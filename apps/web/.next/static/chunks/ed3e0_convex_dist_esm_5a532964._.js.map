{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/values/base64.ts"], "sourcesContent": ["/*\nhttps://github.com/beatgammit/base64-js/blob/88957c9943c7e2a0f03cdf73e71d579e433627d3/index.js\nCopyright (c) 2014 Jameson Little\nThe MIT License (MIT)\n*/\n\n// Vendored because this library has no ESM build, and some environments\n// (SvelteKit) are happiest when all dependencies are ESM.\n\nvar lookup: string[] = [];\nvar revLookup: number[] = [];\nvar Arr = Uint8Array;\n\nvar code = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup[\"-\".charCodeAt(0)] = 62;\nrevLookup[\"_\".charCodeAt(0)] = 63;\n\nfunction getLens(b64: string) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error(\"Invalid string. Length must be a multiple of 4\");\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf(\"=\");\n  if (validLen === -1) validLen = len;\n\n  var placeHoldersLen = validLen === len ? 0 : 4 - (validLen % 4);\n\n  return [validLen, placeHoldersLen];\n}\n\n// base64 is 4/3 + up to two characters of the original data\n/** @public */\nexport function byteLength(b64: string): number {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return ((validLen + placeHoldersLen) * 3) / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(_b64: string, validLen: number, placeHoldersLen: number) {\n  return ((validLen + placeHoldersLen) * 3) / 4 - placeHoldersLen;\n}\n\n/** @public */\nexport function toByteArray(b64: string): Uint8Array {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n\n  var curByte = 0;\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n\n  var i;\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = (tmp >> 16) & 0xff;\n    arr[curByte++] = (tmp >> 8) & 0xff;\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4);\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2);\n    arr[curByte++] = (tmp >> 8) & 0xff;\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num: number) {\n  return (\n    lookup[(num >> 18) & 0x3f] +\n    lookup[(num >> 12) & 0x3f] +\n    lookup[(num >> 6) & 0x3f] +\n    lookup[num & 0x3f]\n  );\n}\n\nfunction encodeChunk(uint8: Uint8Array, start: number, end: number) {\n  var tmp;\n  var output = [];\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xff0000) +\n      ((uint8[i + 1] << 8) & 0xff00) +\n      (uint8[i + 2] & 0xff);\n    output.push(tripletToBase64(tmp));\n  }\n  return output.join(\"\");\n}\n\n/** @public */\nexport function fromByteArray(uint8: Uint8Array): string {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(\n      encodeChunk(\n        uint8,\n        i,\n        i + maxChunkLength > len2 ? len2 : i + maxChunkLength,\n      ),\n    );\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[(tmp << 4) & 0x3f] + \"==\");\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(\n      lookup[tmp >> 10] +\n        lookup[(tmp >> 4) & 0x3f] +\n        lookup[(tmp << 2) & 0x3f] +\n        \"=\",\n    );\n  }\n\n  return parts.join(\"\");\n}\n\nexport function fromByteArrayUrlSafeNoPadding(uint8: Uint8Array): string {\n  return fromByteArray(uint8)\n    .replace(/\\+/g, \"-\")\n    .replace(/\\//g, \"_\")\n    .replace(/=/g, \"\");\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AASA,IAAI,SAAmB,CAAC,CAAA;AACxB,IAAI,YAAsB,CAAC,CAAA;AAC3B,IAAI,MAAM;AAEV,IAAI,OAAO;AACX,IAAA,IAAS,IAAI,GAAG,MAAM,KAAK,MAAA,EAAQ,IAAI,KAAK,EAAE,EAAG;IAC/C,MAAA,CAAO,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;IAClB,SAAA,CAAU,KAAK,UAAA,CAAW,CAAC,CAAC,CAAA,GAAI;AAClC;AAIA,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,GAAI;AAC/B,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,GAAI;AAE/B,SAAS,QAAQ,GAAA,EAAa;IAC5B,IAAI,MAAM,IAAI,MAAA;IAEd,IAAI,MAAM,IAAI,GAAG;QACf,MAAM,IAAI,MAAM,gDAAgD;IAClE;IAIA,IAAI,WAAW,IAAI,OAAA,CAAQ,GAAG;IAC9B,IAAI,aAAa,CAAA,EAAI,CAAA,WAAW;IAEhC,IAAI,kBAAkB,aAAa,MAAM,IAAI,IAAK,WAAW;IAE7D,OAAO;QAAC;QAAU,eAAe;KAAA;AACnC;AAIO,SAAS,WAAW,GAAA,EAAqB;IAC9C,IAAI,OAAO,QAAQ,GAAG;IACtB,IAAI,WAAW,IAAA,CAAK,CAAC,CAAA;IACrB,IAAI,kBAAkB,IAAA,CAAK,CAAC,CAAA;IAC5B,OAAA,CAAS,WAAW,eAAA,IAAmB,IAAK,IAAI;AAClD;AAEA,SAAS,YAAY,IAAA,EAAc,QAAA,EAAkB,eAAA,EAAyB;IAC5E,OAAA,CAAS,WAAW,eAAA,IAAmB,IAAK,IAAI;AAClD;AAGO,SAAS,YAAY,GAAA,EAAyB;IACnD,IAAI;IACJ,IAAI,OAAO,QAAQ,GAAG;IACtB,IAAI,WAAW,IAAA,CAAK,CAAC,CAAA;IACrB,IAAI,kBAAkB,IAAA,CAAK,CAAC,CAAA;IAE5B,IAAI,MAAM,IAAI,IAAI,YAAY,KAAK,UAAU,eAAe,CAAC;IAE7D,IAAI,UAAU;IAGd,IAAI,MAAM,kBAAkB,IAAI,WAAW,IAAI;IAE/C,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QAC3B,MACG,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,IAAK,KAChC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK,KACpC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK,IACrC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA;QACjC,GAAA,CAAI,SAAS,CAAA,GAAK,OAAO,KAAM;QAC/B,GAAA,CAAI,SAAS,CAAA,GAAK,OAAO,IAAK;QAC9B,GAAA,CAAI,SAAS,CAAA,GAAI,MAAM;IACzB;IAEA,IAAI,oBAAoB,GAAG;QACzB,MACG,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,IAAK,IAChC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK;QACvC,GAAA,CAAI,SAAS,CAAA,GAAI,MAAM;IACzB;IAEA,IAAI,oBAAoB,GAAG;QACzB,MACG,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,IAAK,KAChC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK,IACpC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK;QACvC,GAAA,CAAI,SAAS,CAAA,GAAK,OAAO,IAAK;QAC9B,GAAA,CAAI,SAAS,CAAA,GAAI,MAAM;IACzB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,GAAA,EAAa;IACpC,OACE,MAAA,CAAQ,OAAO,KAAM,EAAI,CAAA,GACzB,MAAA,CAAQ,OAAO,KAAM,EAAI,CAAA,GACzB,MAAA,CAAQ,OAAO,IAAK,EAAI,CAAA,GACxB,MAAA,CAAO,MAAM,EAAI,CAAA;AAErB;AAEA,SAAS,YAAY,KAAA,EAAmB,KAAA,EAAe,GAAA,EAAa;IAClE,IAAI;IACJ,IAAI,SAAS,CAAC,CAAA;IACd,IAAA,IAAS,IAAI,OAAO,IAAI,KAAK,KAAK,EAAG;QACnC,MAAA,CACI,KAAA,CAAM,CAAC,CAAA,IAAK,KAAM,QAAA,IAAA,CAClB,KAAA,CAAM,IAAI,CAAC,CAAA,IAAK,IAAK,KAAA,IAAA,CACtB,KAAA,CAAM,IAAI,CAAC,CAAA,GAAI,GAAA;QAClB,OAAO,IAAA,CAAK,gBAAgB,GAAG,CAAC;IAClC;IACA,OAAO,OAAO,IAAA,CAAK,EAAE;AACvB;AAGO,SAAS,cAAc,KAAA,EAA2B;IACvD,IAAI;IACJ,IAAI,MAAM,MAAM,MAAA;IAChB,IAAI,aAAa,MAAM;IACvB,IAAI,QAAQ,CAAC,CAAA;IACb,IAAI,iBAAiB;IAGrB,IAAA,IAAS,IAAI,GAAG,OAAO,MAAM,YAAY,IAAI,MAAM,KAAK,eAAgB;QACtE,MAAM,IAAA,CACJ,YACE,OACA,GACA,IAAI,iBAAiB,OAAO,OAAO,IAAI;IAG7C;IAGA,IAAI,eAAe,GAAG;QACpB,MAAM,KAAA,CAAM,MAAM,CAAC,CAAA;QACnB,MAAM,IAAA,CAAK,MAAA,CAAO,OAAO,CAAC,CAAA,GAAI,MAAA,CAAQ,OAAO,IAAK,EAAI,CAAA,GAAI,IAAI;IAChE,OAAA,IAAW,eAAe,GAAG;QAC3B,MAAA,CAAO,KAAA,CAAM,MAAM,CAAC,CAAA,IAAK,CAAA,IAAK,KAAA,CAAM,MAAM,CAAC,CAAA;QAC3C,MAAM,IAAA,CACJ,MAAA,CAAO,OAAO,EAAE,CAAA,GACd,MAAA,CAAQ,OAAO,IAAK,EAAI,CAAA,GACxB,MAAA,CAAQ,OAAO,IAAK,EAAI,CAAA,GACxB;IAEN;IAEA,OAAO,MAAM,IAAA,CAAK,EAAE;AACtB;AAEO,SAAS,8BAA8B,KAAA,EAA2B;IACvE,OAAO,cAAc,KAAK,EACvB,OAAA,CAAQ,OAAO,GAAG,EAClB,OAAA,CAAQ,OAAO,GAAG,EAClB,OAAA,CAAQ,MAAM,EAAE;AACrB", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/common/index.ts"], "sourcesContent": ["import type { Value } from \"../values/value.js\";\n\n/**\n * Validate that the arguments to a Convex function are an object, defaulting\n * `undefined` to `{}`.\n */\nexport function parseArgs(\n  args: Record<string, Value> | undefined,\n): Record<string, Value> {\n  if (args === undefined) {\n    return {};\n  }\n  if (!isSimpleObject(args)) {\n    throw new Error(\n      `The arguments to a Convex function must be an object. Received: ${\n        args as any\n      }`,\n    );\n  }\n  return args;\n}\n\nexport function validateDeploymentUrl(deploymentUrl: string) {\n  // Don't use things like `new URL(deploymentUrl).hostname` since these aren't\n  // supported by React Native's JS environment\n  if (typeof deploymentUrl === \"undefined\") {\n    throw new Error(\n      `Client created with undefined deployment address. If you used an environment variable, check that it's set.`,\n    );\n  }\n  if (typeof deploymentUrl !== \"string\") {\n    throw new Error(\n      `Invalid deployment address: found ${deploymentUrl as any}\".`,\n    );\n  }\n  if (\n    !(deploymentUrl.startsWith(\"http:\") || deploymentUrl.startsWith(\"https:\"))\n  ) {\n    throw new Error(\n      `Invalid deployment address: Must start with \"https://\" or \"http://\". Found \"${deploymentUrl}\".`,\n    );\n  }\n\n  // Most clients should connect to \".convex.cloud\". But we also support localhost and\n  // custom custom. We validate the deployment url is a valid url, which is the most\n  // common failure pattern.\n  try {\n    new URL(deploymentUrl);\n  } catch {\n    throw new Error(\n      `Invalid deployment address: \"${deploymentUrl}\" is not a valid URL. If you believe this URL is correct, use the \\`skipConvexDeploymentUrlCheck\\` option to bypass this.`,\n    );\n  }\n\n  // If a user uses .convex.site, this is very likely incorrect.\n  if (deploymentUrl.endsWith(\".convex.site\")) {\n    throw new Error(\n      `Invalid deployment address: \"${deploymentUrl}\" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \\`skipConvexDeploymentUrlCheck\\` option to bypass this.`,\n    );\n  }\n}\n\n/**\n * Check whether a value is a plain old JavaScript object.\n */\nexport function isSimpleObject(value: unknown) {\n  const isObject = typeof value === \"object\";\n  const prototype = Object.getPrototypeOf(value);\n  const isSimple =\n    prototype === null ||\n    prototype === Object.prototype ||\n    // Objects generated from other contexts (e.g. across Node.js `vm` modules) will not satisfy the previous\n    // conditions but are still simple objects.\n    prototype?.constructor?.name === \"Object\";\n  return isObject && isSimple;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAMO,SAAS,UACd,IAAA,EACuB;IACvB,IAAI,SAAS,KAAA,GAAW;QACtB,OAAO,CAAC;IACV;IACA,IAAI,CAAC,eAAe,IAAI,GAAG;QACzB,MAAM,IAAI,MACR,mEAEA,OADE,IACF;IAEJ;IACA,OAAO;AACT;AAEO,SAAS,sBAAsB,aAAA,EAAuB;IAG3D,IAAI,OAAO,kBAAkB,aAAa;QACxC,MAAM,IAAI,MACR;IAEJ;IACA,IAAI,OAAO,kBAAkB,UAAU;QACrC,MAAM,IAAI,MACR,qCAAyD,OAApB,aAAoB,EAAA;IAE7D;IACA,IACE,CAAA,CAAE,cAAc,UAAA,CAAW,OAAO,KAAK,cAAc,UAAA,CAAW,QAAQ,CAAA,GACxE;QACA,MAAM,IAAI,MACR,+EAA4F,OAAb,aAAa,EAAA;IAEhG;IAKA,IAAI;QACF,IAAI,IAAI,aAAa;IACvB,EAAA,UAAQ;QACN,MAAM,IAAI,MACR,gCAA6C,OAAb,aAAa,EAAA;IAEjD;IAGA,IAAI,cAAc,QAAA,CAAS,cAAc,GAAG;QAC1C,MAAM,IAAI,MACR,gCAA6C,OAAb,aAAa,EAAA;IAEjD;AACF;AAKO,SAAS,eAAe,KAAA,EAAgB;QAKtB,yGAAA;IAAA,2CAAA;;IAJvB,MAAM,WAAW,OAAO,UAAU;IAClC,MAAM,YAAY,OAAO,cAAA,CAAe,KAAK;IAC7C,MAAM,WACJ,cAAc,QACd,cAAc,OAAO,SAAA,+FAGV,WAAA,2DAAX,uBAAwB,IAAA,MAAS;IACnC,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/values/value.ts"], "sourcesContent": ["/**\n * Utilities for working with values stored in Convex.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n * @module\n */\nimport * as Base64 from \"./base64.js\";\nimport { isSimpleObject } from \"../common/index.js\";\n\nconst LITTLE_ENDIAN = true;\n// This code is used by code that may not have bigint literals.\nconst MIN_INT64 = BigInt(\"-9223372036854775808\");\nconst MAX_INT64 = BigInt(\"9223372036854775807\");\nconst ZERO = BigInt(\"0\");\nconst EIGHT = BigInt(\"8\");\nconst TWOFIFTYSIX = BigInt(\"256\");\n\n/**\n * The type of JavaScript values serializable to JSON.\n *\n * @public\n */\nexport type JSONValue =\n  | null\n  | boolean\n  | number\n  | string\n  | JSONValue[]\n  | { [key: string]: JSONValue };\n\n/**\n * An identifier for a document in Convex.\n *\n * Convex documents are uniquely identified by their `Id`, which is accessible\n * on the `_id` field. To learn more, see [Document IDs](https://docs.convex.dev/database/document-ids).\n *\n * Documents can be loaded using `db.get(id)` in query and mutation functions.\n *\n * IDs are base 32 encoded strings which are URL safe.\n *\n * IDs are just strings at runtime, but this type can be used to distinguish them from other\n * strings at compile time.\n *\n * If you're using code generation, use the `Id` type generated for your data model in\n * `convex/_generated/dataModel.d.ts`.\n *\n * @typeParam TableName - A string literal type of the table name (like \"users\").\n *\n * @public\n */\nexport type Id<TableName extends string> = string & { __tableName: TableName };\n\n/**\n * A value supported by Convex.\n *\n * Values can be:\n * - stored inside of documents.\n * - used as arguments and return types to queries and mutation functions.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n *\n * @public\n */\nexport type Value =\n  | null\n  | bigint\n  | number\n  | boolean\n  | string\n  | ArrayBuffer\n  | Value[]\n  | { [key: string]: undefined | Value };\n\n/**\n * The types of {@link Value} that can be used to represent numbers.\n *\n * @public\n */\nexport type NumericValue = bigint | number;\n\nfunction isSpecial(n: number) {\n  return Number.isNaN(n) || !Number.isFinite(n) || Object.is(n, -0);\n}\n\nexport function slowBigIntToBase64(value: bigint): string {\n  // the conversion is easy if we pretend it's unsigned\n  if (value < ZERO) {\n    value -= MIN_INT64 + MIN_INT64;\n  }\n  let hex = value.toString(16);\n  if (hex.length % 2 === 1) hex = \"0\" + hex;\n\n  const bytes = new Uint8Array(new ArrayBuffer(8));\n  let i = 0;\n  for (const hexByte of hex.match(/.{2}/g)!.reverse()) {\n    bytes.set([parseInt(hexByte, 16)], i++);\n    value >>= EIGHT;\n  }\n  return Base64.fromByteArray(bytes);\n}\n\nexport function slowBase64ToBigInt(encoded: string): bigint {\n  const integerBytes = Base64.toByteArray(encoded);\n  if (integerBytes.byteLength !== 8) {\n    throw new Error(\n      `Received ${integerBytes.byteLength} bytes, expected 8 for $integer`,\n    );\n  }\n  let value = ZERO;\n  let power = ZERO;\n  for (const byte of integerBytes) {\n    value += BigInt(byte) * TWOFIFTYSIX ** power;\n    power++;\n  }\n  if (value > MAX_INT64) {\n    value += MIN_INT64 + MIN_INT64;\n  }\n  return value;\n}\n\nexport function modernBigIntToBase64(value: bigint): string {\n  if (value < MIN_INT64 || MAX_INT64 < value) {\n    throw new Error(\n      `BigInt ${value} does not fit into a 64-bit signed integer.`,\n    );\n  }\n  const buffer = new ArrayBuffer(8);\n  new DataView(buffer).setBigInt64(0, value, true);\n  return Base64.fromByteArray(new Uint8Array(buffer));\n}\n\nexport function modernBase64ToBigInt(encoded: string): bigint {\n  const integerBytes = Base64.toByteArray(encoded);\n  if (integerBytes.byteLength !== 8) {\n    throw new Error(\n      `Received ${integerBytes.byteLength} bytes, expected 8 for $integer`,\n    );\n  }\n  const intBytesView = new DataView(integerBytes.buffer);\n  return intBytesView.getBigInt64(0, true);\n}\n\n// Fall back to a slower version on Safari 14 which lacks these APIs.\nexport const bigIntToBase64 = (DataView.prototype as any).setBigInt64\n  ? modernBigIntToBase64\n  : slowBigIntToBase64;\nexport const base64ToBigInt = (DataView.prototype as any).getBigInt64\n  ? modernBase64ToBigInt\n  : slowBase64ToBigInt;\n\nconst MAX_IDENTIFIER_LEN = 1024;\n\nfunction validateObjectField(k: string) {\n  if (k.length > MAX_IDENTIFIER_LEN) {\n    throw new Error(\n      `Field name ${k} exceeds maximum field name length ${MAX_IDENTIFIER_LEN}.`,\n    );\n  }\n  if (k.startsWith(\"$\")) {\n    throw new Error(`Field name ${k} starts with a '$', which is reserved.`);\n  }\n  for (let i = 0; i < k.length; i += 1) {\n    const charCode = k.charCodeAt(i);\n    // Non-control ASCII characters\n    if (charCode < 32 || charCode >= 127) {\n      throw new Error(\n        `Field name ${k} has invalid character '${k[i]}': Field names can only contain non-control ASCII characters`,\n      );\n    }\n  }\n}\n\n/**\n * Parse a Convex value from its JSON representation.\n *\n * This function will deserialize serialized Int64s to `BigInt`s, Bytes to `ArrayBuffer`s etc.\n *\n * To learn more about Convex values, see [Types](https://docs.convex.dev/using/types).\n *\n * @param value - The JSON representation of a Convex value previously created with {@link convexToJson}.\n * @returns The JavaScript representation of the Convex value.\n *\n * @public\n */\nexport function jsonToConvex(value: JSONValue): Value {\n  if (value === null) {\n    return value;\n  }\n  if (typeof value === \"boolean\") {\n    return value;\n  }\n  if (typeof value === \"number\") {\n    return value;\n  }\n  if (typeof value === \"string\") {\n    return value;\n  }\n  if (Array.isArray(value)) {\n    return value.map((value) => jsonToConvex(value));\n  }\n  if (typeof value !== \"object\") {\n    throw new Error(`Unexpected type of ${value as any}`);\n  }\n  const entries = Object.entries(value);\n  if (entries.length === 1) {\n    const key = entries[0][0];\n    if (key === \"$bytes\") {\n      if (typeof value.$bytes !== \"string\") {\n        throw new Error(`Malformed $bytes field on ${value as any}`);\n      }\n      return Base64.toByteArray(value.$bytes).buffer;\n    }\n    if (key === \"$integer\") {\n      if (typeof value.$integer !== \"string\") {\n        throw new Error(`Malformed $integer field on ${value as any}`);\n      }\n      return base64ToBigInt(value.$integer);\n    }\n    if (key === \"$float\") {\n      if (typeof value.$float !== \"string\") {\n        throw new Error(`Malformed $float field on ${value as any}`);\n      }\n      const floatBytes = Base64.toByteArray(value.$float);\n      if (floatBytes.byteLength !== 8) {\n        throw new Error(\n          `Received ${floatBytes.byteLength} bytes, expected 8 for $float`,\n        );\n      }\n      const floatBytesView = new DataView(floatBytes.buffer);\n      const float = floatBytesView.getFloat64(0, LITTLE_ENDIAN);\n      if (!isSpecial(float)) {\n        throw new Error(`Float ${float} should be encoded as a number`);\n      }\n      return float;\n    }\n    if (key === \"$set\") {\n      throw new Error(\n        `Received a Set which is no longer supported as a Convex type.`,\n      );\n    }\n    if (key === \"$map\") {\n      throw new Error(\n        `Received a Map which is no longer supported as a Convex type.`,\n      );\n    }\n  }\n  const out: { [key: string]: Value } = {};\n  for (const [k, v] of Object.entries(value)) {\n    validateObjectField(k);\n    out[k] = jsonToConvex(v);\n  }\n  return out;\n}\n\nexport function stringifyValueForError(value: any) {\n  return JSON.stringify(value, (_key, value) => {\n    if (value === undefined) {\n      // By default `JSON.stringify` converts undefined, functions, symbols,\n      // Infinity, and NaN to null which produces a confusing error message.\n      // We deal with `undefined` specifically because it's the most common.\n      // Ideally we'd use a pretty-printing library that prints `undefined`\n      // (no quotes), but it might not be worth the bundle size cost.\n      return \"undefined\";\n    }\n    if (typeof value === \"bigint\") {\n      // `JSON.stringify` throws on bigints by default.\n      return `${value.toString()}n`;\n    }\n    return value;\n  });\n}\n\nfunction convexToJsonInternal(\n  value: Value,\n  originalValue: Value,\n  context: string,\n  includeTopLevelUndefined: boolean,\n): JSONValue {\n  if (value === undefined) {\n    const contextText =\n      context &&\n      ` (present at path ${context} in original object ${stringifyValueForError(\n        originalValue,\n      )})`;\n    throw new Error(\n      `undefined is not a valid Convex value${contextText}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`,\n    );\n  }\n  if (value === null) {\n    return value;\n  }\n  if (typeof value === \"bigint\") {\n    if (value < MIN_INT64 || MAX_INT64 < value) {\n      throw new Error(\n        `BigInt ${value} does not fit into a 64-bit signed integer.`,\n      );\n    }\n    return { $integer: bigIntToBase64(value) };\n  }\n  if (typeof value === \"number\") {\n    if (isSpecial(value)) {\n      const buffer = new ArrayBuffer(8);\n      new DataView(buffer).setFloat64(0, value, LITTLE_ENDIAN);\n      return { $float: Base64.fromByteArray(new Uint8Array(buffer)) };\n    } else {\n      return value;\n    }\n  }\n  if (typeof value === \"boolean\") {\n    return value;\n  }\n  if (typeof value === \"string\") {\n    return value;\n  }\n  if (value instanceof ArrayBuffer) {\n    return { $bytes: Base64.fromByteArray(new Uint8Array(value)) };\n  }\n  if (Array.isArray(value)) {\n    return value.map((value, i) =>\n      convexToJsonInternal(value, originalValue, context + `[${i}]`, false),\n    );\n  }\n  if (value instanceof Set) {\n    throw new Error(\n      errorMessageForUnsupportedType(context, \"Set\", [...value], originalValue),\n    );\n  }\n  if (value instanceof Map) {\n    throw new Error(\n      errorMessageForUnsupportedType(context, \"Map\", [...value], originalValue),\n    );\n  }\n\n  if (!isSimpleObject(value)) {\n    const theType = value?.constructor?.name;\n    const typeName = theType ? `${theType} ` : \"\";\n    throw new Error(\n      errorMessageForUnsupportedType(context, typeName, value, originalValue),\n    );\n  }\n\n  const out: { [key: string]: JSONValue } = {};\n  const entries = Object.entries(value);\n  entries.sort(([k1, _v1], [k2, _v2]) => (k1 === k2 ? 0 : k1 < k2 ? -1 : 1));\n  for (const [k, v] of entries) {\n    if (v !== undefined) {\n      validateObjectField(k);\n      out[k] = convexToJsonInternal(v, originalValue, context + `.${k}`, false);\n    } else if (includeTopLevelUndefined) {\n      validateObjectField(k);\n      out[k] = convexOrUndefinedToJsonInternal(\n        v,\n        originalValue,\n        context + `.${k}`,\n      );\n    }\n  }\n  return out;\n}\n\nfunction errorMessageForUnsupportedType(\n  context: string,\n  typeName: string,\n  value: any,\n  originalValue: any,\n) {\n  if (context) {\n    return `${typeName}${stringifyValueForError(\n      value,\n    )} is not a supported Convex type (present at path ${context} in original object ${stringifyValueForError(\n      originalValue,\n    )}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`;\n  } else {\n    return `${typeName}${stringifyValueForError(\n      value,\n    )} is not a supported Convex type.`;\n  }\n}\n\n// convexOrUndefinedToJsonInternal wrapper exists so we can pipe through the\n// `originalValue` and `context` through for better error messaging.\nfunction convexOrUndefinedToJsonInternal(\n  value: Value | undefined,\n  originalValue: Value | undefined,\n  context: string,\n): JSONValue {\n  if (value === undefined) {\n    return { $undefined: null };\n  } else {\n    if (originalValue === undefined) {\n      // This should not happen.\n      throw new Error(\n        `Programming error. Current value is ${stringifyValueForError(\n          value,\n        )} but original value is undefined`,\n      );\n    }\n    return convexToJsonInternal(value, originalValue, context, false);\n  }\n}\n\n/**\n * Convert a Convex value to its JSON representation.\n *\n * Use {@link jsonToConvex} to recreate the original value.\n *\n * To learn more about Convex values, see [Types](https://docs.convex.dev/using/types).\n *\n * @param value - A Convex value to convert into JSON.\n * @returns The JSON representation of `value`.\n *\n * @public\n */\nexport function convexToJson(value: Value): JSONValue {\n  return convexToJsonInternal(value, value, \"\", false);\n}\n\n// Convert a Convex value or `undefined` into its JSON representation.\n// `undefined` is used in filters to represent a missing object field.\nexport function convexOrUndefinedToJson(value: Value | undefined): JSONValue {\n  return convexOrUndefinedToJsonInternal(value, value, \"\");\n}\n\n/**\n * Similar to convexToJson but also serializes top level undefined fields\n * using convexOrUndefinedToJson().\n *\n * @param value - A Convex value to convert into JSON.\n * @returns The JSON representation of `value`.\n */\nexport function patchValueToJson(value: Value): JSONValue {\n  return convexToJsonInternal(value, value, \"\", true);\n}\n"], "names": ["value"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAOA,YAAY,YAAY;AACxB,SAAS,sBAAsB;;;;AAE/B,MAAM,gBAAgB;AAEtB,MAAM,YAAY,OAAO,sBAAsB;AAC/C,MAAM,YAAY,OAAO,qBAAqB;AAC9C,MAAM,OAAO,OAAO,GAAG;AACvB,MAAM,QAAQ,OAAO,GAAG;AACxB,MAAM,cAAc,OAAO,KAAK;AAkEhC,SAAS,UAAU,CAAA,EAAW;IAC5B,OAAO,OAAO,KAAA,CAAM,CAAC,KAAK,CAAC,OAAO,QAAA,CAAS,CAAC,KAAK,OAAO,EAAA,CAAG,GAAG,CAAA,CAAE;AAClE;AAEO,SAAS,mBAAmB,KAAA,EAAuB;IAExD,IAAI,QAAQ,MAAM;QAChB,SAAS,YAAY;IACvB;IACA,IAAI,MAAM,MAAM,QAAA,CAAS,EAAE;IAC3B,IAAI,IAAI,MAAA,GAAS,MAAM,EAAG,CAAA,MAAM,MAAM;IAEtC,MAAM,QAAQ,IAAI,WAAW,IAAI,YAAY,CAAC,CAAC;IAC/C,IAAI,IAAI;IACR,KAAA,MAAW,WAAW,IAAI,KAAA,CAAM,OAAO,EAAG,OAAA,CAAQ,EAAG;QACnD,MAAM,GAAA,CAAI;YAAC,SAAS,SAAS,EAAE,CAAC;SAAA,EAAG,GAAG;QACtC,UAAU;IACZ;IACA,OAAO,OAAO,6OAAA,CAAc,KAAK;AACnC;AAEO,SAAS,mBAAmB,OAAA,EAAyB;IAC1D,MAAM,eAAe,OAAO,2OAAA,CAAY,OAAO;IAC/C,IAAI,aAAa,UAAA,KAAe,GAAG;QACjC,MAAM,IAAI,MACR,YAAmC,OAAvB,aAAa,UAAU,EAAA;IAEvC;IACA,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,KAAA,MAAW,QAAQ,aAAc;QAC/B,SAAS,OAAO,IAAI,IAAI,eAAe;QACvC;IACF;IACA,IAAI,QAAQ,WAAW;QACrB,SAAS,YAAY;IACvB;IACA,OAAO;AACT;AAEO,SAAS,qBAAqB,KAAA,EAAuB;IAC1D,IAAI,QAAQ,aAAa,YAAY,OAAO;QAC1C,MAAM,IAAI,MACR,UAAe,OAAL,KAAK,EAAA;IAEnB;IACA,MAAM,SAAS,IAAI,YAAY,CAAC;IAChC,IAAI,SAAS,MAAM,EAAE,WAAA,CAAY,GAAG,OAAO,IAAI;IAC/C,OAAO,OAAO,6OAAA,CAAc,IAAI,WAAW,MAAM,CAAC;AACpD;AAEO,SAAS,qBAAqB,OAAA,EAAyB;IAC5D,MAAM,eAAe,OAAO,2OAAA,CAAY,OAAO;IAC/C,IAAI,aAAa,UAAA,KAAe,GAAG;QACjC,MAAM,IAAI,MACR,YAAmC,OAAvB,aAAa,UAAU,EAAA;IAEvC;IACA,MAAM,eAAe,IAAI,SAAS,aAAa,MAAM;IACrD,OAAO,aAAa,WAAA,CAAY,GAAG,IAAI;AACzC;AAGO,MAAM,iBAAkB,SAAS,SAAA,CAAkB,WAAA,GACtD,uBACA;AACG,MAAM,iBAAkB,SAAS,SAAA,CAAkB,WAAA,GACtD,uBACA;AAEJ,MAAM,qBAAqB;AAE3B,SAAS,oBAAoB,CAAA,EAAW;IACtC,IAAI,EAAE,MAAA,GAAS,oBAAoB;QACjC,MAAM,IAAI,MACR,qBAAc,CAAC,EAAA,uCAAwD,OAAlB,kBAAkB,EAAA;IAE3E;IACA,IAAI,EAAE,UAAA,CAAW,GAAG,GAAG;QACrB,MAAM,IAAI,MAAM,cAAe,OAAD,CAAC,EAAA,uCAAwC;IACzE;IACA,IAAA,IAAS,IAAI,GAAG,IAAI,EAAE,MAAA,EAAQ,KAAK,EAAG;QACpC,MAAM,WAAW,EAAE,UAAA,CAAW,CAAC;QAE/B,IAAI,WAAW,MAAM,YAAY,KAAK;YACpC,MAAM,IAAI,MACR,qBAAc,CAAC,EAAA,4BAA+B,OAAJ,CAAA,CAAE,CAAC,CAAC,EAAA;QAElD;IACF;AACF;AAcO,SAAS,aAAa,KAAA,EAAyB;IACpD,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,OAAO,UAAU,WAAW;QAC9B,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACxB,OAAO,MAAM,GAAA,CAAI,CAACA,SAAU,aAAaA,MAAK,CAAC;IACjD;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,MAAM,IAAI,MAAM,sBAAkC,CAAE,MAAd,KAAY;IACpD;IACA,MAAM,UAAU,OAAO,OAAA,CAAQ,KAAK;IACpC,IAAI,QAAQ,MAAA,KAAW,GAAG;QACxB,MAAM,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA;QACxB,IAAI,QAAQ,UAAU;YACpB,IAAI,OAAO,MAAM,MAAA,KAAW,UAAU;gBACpC,MAAM,IAAI,MAAM,6BAAyC,CAAE,MAAd,KAAY;YAC3D;YACA,OAAO,OAAO,2OAAA,CAAY,MAAM,MAAM,EAAE,MAAA;QAC1C;QACA,IAAI,QAAQ,YAAY;YACtB,IAAI,OAAO,MAAM,QAAA,KAAa,UAAU;gBACtC,MAAM,IAAI,MAAM,+BAA2C,CAAE,MAAd,KAAY;YAC7D;YACA,OAAO,eAAe,MAAM,QAAQ;QACtC;QACA,IAAI,QAAQ,UAAU;YACpB,IAAI,OAAO,MAAM,MAAA,KAAW,UAAU;gBACpC,MAAM,IAAI,MAAM,6BAAyC,CAAE,MAAd,KAAY;YAC3D;YACA,MAAM,aAAa,OAAO,2OAAA,CAAY,MAAM,MAAM;YAClD,IAAI,WAAW,UAAA,KAAe,GAAG;gBAC/B,MAAM,IAAI,MACR,YAAiC,OAArB,WAAW,UAAU,EAAA;YAErC;YACA,MAAM,iBAAiB,IAAI,SAAS,WAAW,MAAM;YACrD,MAAM,QAAQ,eAAe,UAAA,CAAW,GAAG,aAAa;YACxD,IAAI,CAAC,UAAU,KAAK,GAAG;gBACrB,MAAM,IAAI,MAAM,SAAc,OAAL,KAAK,EAAA,+BAAgC;YAChE;YACA,OAAO;QACT;QACA,IAAI,QAAQ,QAAQ;YAClB,MAAM,IAAI,MACR;QAEJ;QACA,IAAI,QAAQ,QAAQ;YAClB,MAAM,IAAI,MACR;QAEJ;IACF;IACA,MAAM,MAAgC,CAAC;IACvC,KAAA,MAAW,CAAC,GAAG,CAAC,CAAA,IAAK,OAAO,OAAA,CAAQ,KAAK,EAAG;QAC1C,oBAAoB,CAAC;QACrB,GAAA,CAAI,CAAC,CAAA,GAAI,aAAa,CAAC;IACzB;IACA,OAAO;AACT;AAEO,SAAS,uBAAuB,KAAA,EAAY;IACjD,OAAO,KAAK,SAAA,CAAU,OAAO,CAAC,MAAMA,WAAU;QAC5C,IAAIA,WAAU,KAAA,GAAW;YAMvB,OAAO;QACT;QACA,IAAI,OAAOA,WAAU,UAAU;YAE7B,OAAO,GAAmB,OAAhBA,OAAM,QAAA,CAAS,CAAC,EAAA;QAC5B;QACA,OAAOA;IACT,CAAC;AACH;AAEA,SAAS,qBACP,KAAA,EACA,aAAA,EACA,OAAA,EACA,wBAAA,EACW;IACX,IAAI,UAAU,KAAA,GAAW;QACvB,MAAM,cACJ,WACA,4BAAqB,OAAO,EAAA,wBAE3B,OAFkD,uBACjD,gBACD;QACH,MAAM,IAAI,MACR,wCAAmD,OAAX,WAAW,EAAA;IAEvD;IACA,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,QAAQ,aAAa,YAAY,OAAO;YAC1C,MAAM,IAAI,MACR,UAAe,OAAL,KAAK,EAAA;QAEnB;QACA,OAAO;YAAE,UAAU,eAAe,KAAK;QAAE;IAC3C;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,UAAU,KAAK,GAAG;YACpB,MAAM,SAAS,IAAI,YAAY,CAAC;YAChC,IAAI,SAAS,MAAM,EAAE,UAAA,CAAW,GAAG,OAAO,aAAa;YACvD,OAAO;gBAAE,QAAQ,OAAO,6OAAA,CAAc,IAAI,WAAW,MAAM,CAAC;YAAE;QAChE,OAAO;YACL,OAAO;QACT;IACF;IACA,IAAI,OAAO,UAAU,WAAW;QAC9B,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,IAAI,iBAAiB,aAAa;QAChC,OAAO;YAAE,QAAQ,OAAO,6OAAA,CAAc,IAAI,WAAW,KAAK,CAAC;QAAE;IAC/D;IACA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACxB,OAAO,MAAM,GAAA,CAAI,CAACA,QAAO,IACvB,qBAAqBA,QAAO,eAAe,UAAU,IAAK,OAAD,CAAC,EAAA,MAAK,KAAK;IAExE;IACA,IAAI,iBAAiB,KAAK;QACxB,MAAM,IAAI,MACR,+BAA+B,SAAS,OAAO,CAAC;eAAG,KAAK;SAAA,EAAG,aAAa;IAE5E;IACA,IAAI,iBAAiB,KAAK;QACxB,MAAM,IAAI,MACR,+BAA+B,SAAS,OAAO,CAAC;eAAG,KAAK;SAAA,EAAG,aAAa;IAE5E;IAEA,IAAI,KAAC,oPAAA,EAAe,KAAK,GAAG;YACV;QAAhB,MAAM,oFAAiB,WAAA,0EAAa,IAAA;QACpC,MAAM,WAAW,UAAU,GAAU,OAAP,OAAO,EAAA,OAAM;QAC3C,MAAM,IAAI,MACR,+BAA+B,SAAS,UAAU,OAAO,aAAa;IAE1E;IAEA,MAAM,MAAoC,CAAC;IAC3C,MAAM,UAAU,OAAO,OAAA,CAAQ,KAAK;IACpC,QAAQ,IAAA,CAAK;YAAC,CAAC,IAAI,GAAG,CAAA,UAAG,CAAC,IAAI,GAAG,CAAA;eAAO,OAAO,KAAK,IAAI,KAAK,KAAK,CAAA,IAAK,CAAE;;IACzE,KAAA,MAAW,CAAC,GAAG,CAAC,CAAA,IAAK,QAAS;QAC5B,IAAI,MAAM,KAAA,GAAW;YACnB,oBAAoB,CAAC;YACrB,GAAA,CAAI,CAAC,CAAA,GAAI,qBAAqB,GAAG,eAAe,UAAU,IAAK,OAAD,CAAC,GAAI,KAAK;QAC1E,OAAA,IAAW,0BAA0B;YACnC,oBAAoB,CAAC;YACrB,GAAA,CAAI,CAAC,CAAA,GAAI,gCACP,GACA,eACA,UAAU,IAAK,OAAD,CAAC;QAEnB;IACF;IACA,OAAO;AACT;AAEA,SAAS,+BACP,OAAA,EACA,QAAA,EACA,KAAA,EACA,aAAA,EACA;IACA,IAAI,SAAS;QACX,OAAO,UAAG,QAAQ,SAAG,uBACnB,QACD,4DAAoD,OAAO,EAAA,wBAE3D,OAFkF,uBACjF,gBACD;IACH,OAAO;QACL,OAAO,UAAG,QAAQ,EAEjB,OAFoB,uBACnB,QACD;IACH;AACF;AAIA,SAAS,gCACP,KAAA,EACA,aAAA,EACA,OAAA,EACW;IACX,IAAI,UAAU,KAAA,GAAW;QACvB,OAAO;YAAE,YAAY;QAAK;IAC5B,OAAO;QACL,IAAI,kBAAkB,KAAA,GAAW;YAE/B,MAAM,IAAI,MACR,uCAEC,OAFsC,uBACrC,QACD;QAEL;QACA,OAAO,qBAAqB,OAAO,eAAe,SAAS,KAAK;IAClE;AACF;AAcO,SAAS,aAAa,KAAA,EAAyB;IACpD,OAAO,qBAAqB,OAAO,OAAO,IAAI,KAAK;AACrD;AAIO,SAAS,wBAAwB,KAAA,EAAqC;IAC3E,OAAO,gCAAgC,OAAO,OAAO,EAAE;AACzD;AASO,SAAS,iBAAiB,KAAA,EAAyB;IACxD,OAAO,qBAAqB,OAAO,OAAO,IAAI,IAAI;AACpD", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/values/validators.ts"], "sourcesContent": ["import { GenericId } from \"./index.js\";\nimport { GenericValidator } from \"./validator.js\";\nimport { JSONValue, convexToJson } from \"./value.js\";\n\ntype TableNameFromType<T> =\n  T extends GenericId<infer TableName> ? TableName : string;\n\n/**\n * Avoid using `instanceof BaseValidator`; this is inheritence for code reuse\n * not type heirarchy.\n */\nabstract class BaseValidator<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = never,\n> {\n  /**\n   * Only for TypeScript, the TS type of the JS values validated\n   * by this validator.\n   */\n  readonly type!: Type;\n  /**\n   * Only for TypeScript, if this an Object validator, then\n   * this is the TS type of its property names.\n   */\n  readonly fieldPaths!: FieldPaths;\n\n  /**\n   * Whether this is an optional Object property value validator.\n   */\n  readonly isOptional: IsOptional;\n\n  /**\n   * Always `\"true\"`.\n   */\n  readonly isConvexValidator: true;\n\n  constructor({ isOptional }: { isOptional: IsOptional }) {\n    this.isOptional = isOptional;\n    this.isConvexValidator = true;\n  }\n  /** @deprecated - use isOptional instead */\n  get optional(): boolean {\n    return this.isOptional === \"optional\" ? true : false;\n  }\n  /** @internal */\n  abstract get json(): ValidatorJSON;\n  /** @internal */\n  abstract asOptional(): Validator<Type | undefined, \"optional\", FieldPaths>;\n}\n\n/**\n * The type of the `v.id(tableName)` validator.\n */\nexport class VId<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The name of the table that the validated IDs must belong to.\n   */\n  readonly tableName: TableNameFromType<Type>;\n\n  /**\n   * The kind of validator, `\"id\"`.\n   */\n  readonly kind = \"id\" as const;\n\n  /**\n   * Usually you'd use `v.id(tableName)` instead.\n   */\n  constructor({\n    isOptional,\n    tableName,\n  }: {\n    isOptional: IsOptional;\n    tableName: TableNameFromType<Type>;\n  }) {\n    super({ isOptional });\n    if (typeof tableName !== \"string\") {\n      throw new Error(\"v.id(tableName) requires a string\");\n    }\n    this.tableName = tableName;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: \"id\", tableName: this.tableName };\n  }\n  /** @internal */\n  asOptional() {\n    return new VId<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n      tableName: this.tableName,\n    });\n  }\n}\n\n/**\n * The type of the `v.float64()` validator.\n */\nexport class VFloat64<\n  Type = number,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"float64\"`.\n   */\n  readonly kind = \"float64\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    // Server expects the old name `number` string instead of `float64`.\n    return { type: \"number\" };\n  }\n  /** @internal */\n  asOptional() {\n    return new VFloat64<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.int64()` validator.\n */\nexport class VInt64<\n  Type = bigint,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"int64\"`.\n   */\n  readonly kind = \"int64\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    // Server expects the old name `bigint`.\n    return { type: \"bigint\" };\n  }\n  /** @internal */\n  asOptional() {\n    return new VInt64<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.boolean()` validator.\n */\nexport class VBoolean<\n  Type = boolean,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"boolean\"`.\n   */\n  readonly kind = \"boolean\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VBoolean<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.bytes()` validator.\n */\nexport class VBytes<\n  Type = ArrayBuffer,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"bytes\"`.\n   */\n  readonly kind = \"bytes\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VBytes<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.string()` validator.\n */\nexport class VString<\n  Type = string,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"string\"`.\n   */\n  readonly kind = \"string\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VString<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.null()` validator.\n */\nexport class VNull<\n  Type = null,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"null\"`.\n   */\n  readonly kind = \"null\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VNull<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.any()` validator.\n */\nexport class VAny<\n  Type = any,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The kind of validator, `\"any\"`.\n   */\n  readonly kind = \"any\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VAny<Type | undefined, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.object()` validator.\n */\nexport class VObject<\n  Type,\n  Fields extends Record<string, GenericValidator>,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = {\n    [Property in keyof Fields]:\n      | JoinFieldPaths<Property & string, Fields[Property][\"fieldPaths\"]>\n      | Property;\n  }[keyof Fields] &\n    string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * An object with the validator for each property.\n   */\n  readonly fields: Fields;\n\n  /**\n   * The kind of validator, `\"object\"`.\n   */\n  readonly kind = \"object\" as const;\n\n  /**\n   * Usually you'd use `v.object({ ... })` instead.\n   */\n  constructor({\n    isOptional,\n    fields,\n  }: {\n    isOptional: IsOptional;\n    fields: Fields;\n  }) {\n    super({ isOptional });\n    globalThis.Object.values(fields).forEach((v) => {\n      if (!v.isConvexValidator) {\n        throw new Error(\"v.object() entries must be valiators\");\n      }\n    });\n    this.fields = fields;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: globalThis.Object.fromEntries(\n        globalThis.Object.entries(this.fields).map(([k, v]) => [\n          k,\n          {\n            fieldType: v.json,\n            optional: v.isOptional === \"optional\" ? true : false,\n          },\n        ]),\n      ),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VObject<Type | undefined, Fields, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n      fields: this.fields,\n    });\n  }\n}\n\n/**\n * The type of the `v.literal()` validator.\n */\nexport class VLiteral<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The value that the validated values must be equal to.\n   */\n  readonly value: Type;\n\n  /**\n   * The kind of validator, `\"literal\"`.\n   */\n  readonly kind = \"literal\" as const;\n\n  /**\n   * Usually you'd use `v.literal(value)` instead.\n   */\n  constructor({ isOptional, value }: { isOptional: IsOptional; value: Type }) {\n    super({ isOptional });\n    if (\n      typeof value !== \"string\" &&\n      typeof value !== \"boolean\" &&\n      typeof value !== \"number\" &&\n      typeof value !== \"bigint\"\n    ) {\n      throw new Error(\"v.literal(value) must be a string, number, or boolean\");\n    }\n    this.value = value;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: convexToJson(this.value as string | boolean | number | bigint),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VLiteral<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n      value: this.value,\n    });\n  }\n}\n\n/**\n * The type of the `v.array()` validator.\n */\nexport class VArray<\n  Type,\n  Element extends Validator<any, \"required\", any>,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The validator for the elements of the array.\n   */\n  readonly element: Element;\n\n  /**\n   * The kind of validator, `\"array\"`.\n   */\n  readonly kind = \"array\" as const;\n\n  /**\n   * Usually you'd use `v.array(element)` instead.\n   */\n  constructor({\n    isOptional,\n    element,\n  }: {\n    isOptional: IsOptional;\n    element: Element;\n  }) {\n    super({ isOptional });\n    this.element = element;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: this.element.json,\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VArray<Type | undefined, Element, \"optional\">({\n      isOptional: \"optional\",\n      element: this.element,\n    });\n  }\n}\n\n/**\n * The type of the `v.record()` validator.\n */\nexport class VRecord<\n  Type,\n  Key extends Validator<string, \"required\", any>,\n  Value extends Validator<any, \"required\", any>,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The validator for the keys of the record.\n   */\n  readonly key: Key;\n\n  /**\n   * The validator for the values of the record.\n   */\n  readonly value: Value;\n\n  /**\n   * The kind of validator, `\"record\"`.\n   */\n  readonly kind = \"record\" as const;\n\n  /**\n   * Usually you'd use `v.record(key, value)` instead.\n   */\n  constructor({\n    isOptional,\n    key,\n    value,\n  }: {\n    isOptional: IsOptional;\n    key: Key;\n    value: Value;\n  }) {\n    super({ isOptional });\n    if ((key.isOptional as OptionalProperty) === \"optional\") {\n      throw new Error(\"Record validator cannot have optional keys\");\n    }\n    if ((value.isOptional as OptionalProperty) === \"optional\") {\n      throw new Error(\"Record validator cannot have optional values\");\n    }\n    if (!key.isConvexValidator || !value.isConvexValidator) {\n      throw new Error(\"Key and value of v.record() but be validators\");\n    }\n    this.key = key;\n    this.value = value;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      // This cast is needed because TypeScript thinks the key type is too wide\n      keys: this.key.json as RecordKeyValidatorJSON,\n      values: {\n        fieldType: this.value.json,\n        optional: false,\n      },\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VRecord<Type | undefined, Key, Value, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n      key: this.key,\n      value: this.value,\n    });\n  }\n}\n\n/**\n * The type of the `v.union()` validator.\n */\nexport class VUnion<\n  Type,\n  T extends Validator<any, \"required\", any>[],\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = T[number][\"fieldPaths\"],\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The array of validators, one of which must match the value.\n   */\n  readonly members: T;\n\n  /**\n   * The kind of validator, `\"union\"`.\n   */\n  readonly kind = \"union\" as const;\n\n  /**\n   * Usually you'd use `v.union(...members)` instead.\n   */\n  constructor({ isOptional, members }: { isOptional: IsOptional; members: T }) {\n    super({ isOptional });\n    members.forEach((member) => {\n      if (!member.isConvexValidator) {\n        throw new Error(\"All members of v.union() must be validators\");\n      }\n    });\n    this.members = members;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: this.members.map((v) => v.json),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VUnion<Type | undefined, T, \"optional\">({\n      isOptional: \"optional\",\n      members: this.members,\n    });\n  }\n}\n\n// prettier-ignore\nexport type VOptional<T extends Validator<any, OptionalProperty, any>> =\n  T extends VId<infer Type, OptionalProperty> ? VId<Type | undefined, \"optional\">\n  : T extends VString<infer Type, OptionalProperty>\n    ? VString<Type | undefined, \"optional\">\n  : T extends VFloat64<infer Type, OptionalProperty>\n    ? VFloat64<Type | undefined, \"optional\">\n  : T extends VInt64<infer Type, OptionalProperty>\n    ? VInt64<Type | undefined, \"optional\">\n  : T extends VBoolean<infer Type, OptionalProperty>\n    ? VBoolean<Type | undefined, \"optional\">\n  : T extends VNull<infer Type, OptionalProperty>\n    ? VNull<Type | undefined, \"optional\">\n  : T extends VAny<infer Type, OptionalProperty>\n    ? VAny<Type | undefined, \"optional\">\n  : T extends VLiteral<infer Type, OptionalProperty>\n    ? VLiteral<Type | undefined, \"optional\">\n  : T extends VBytes<infer Type, OptionalProperty>\n    ? VBytes<Type | undefined, \"optional\">\n  : T extends VObject< infer Type, infer Fields, OptionalProperty, infer FieldPaths>\n    ? VObject<Type | undefined, Fields, \"optional\", FieldPaths>\n  : T extends VArray<infer Type, infer Element, OptionalProperty>\n    ? VArray<Type | undefined, Element, \"optional\">\n  : T extends VRecord< infer Type, infer Key, infer Value, OptionalProperty, infer FieldPaths>\n    ? VRecord<Type | undefined, Key, Value, \"optional\", FieldPaths>\n  : T extends VUnion<infer Type, infer Members, OptionalProperty, infer FieldPaths>\n    ? VUnion<Type | undefined, Members, \"optional\", FieldPaths>\n  : never\n\n/**\n * Type representing whether a property in an object is optional or required.\n *\n * @public\n */\nexport type OptionalProperty = \"optional\" | \"required\";\n\n/**\n * A validator for a Convex value.\n *\n * This should be constructed using the validator builder, {@link v}.\n *\n * A validator encapsulates:\n * - The TypeScript type of this value.\n * - Whether this field should be optional if it's included in an object.\n * - The TypeScript type for the set of index field paths that can be used to\n * build indexes on this value.\n * - A JSON representation of the validator.\n *\n * Specific types of validators contain additional information: for example\n * an `ArrayValidator` contains an `element` property with the validator\n * used to validate each element of the list. Use the shared 'kind' property\n * to identity the type of validator.\n *\n * More validators can be added in future releases so an exhaustive\n * switch statement on validator `kind` should be expected to break\n * in future releases of Convex.\n *\n * @public\n */\nexport type Validator<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = never,\n> =\n  | VId<Type, IsOptional>\n  | VString<Type, IsOptional>\n  | VFloat64<Type, IsOptional>\n  | VInt64<Type, IsOptional>\n  | VBoolean<Type, IsOptional>\n  | VNull<Type, IsOptional>\n  | VAny<Type, IsOptional>\n  | VLiteral<Type, IsOptional>\n  | VBytes<Type, IsOptional>\n  | VObject<\n      Type,\n      Record<string, Validator<any, OptionalProperty, any>>,\n      IsOptional,\n      FieldPaths\n    >\n  | VArray<Type, Validator<any, \"required\", any>, IsOptional>\n  | VRecord<\n      Type,\n      Validator<string, \"required\", any>,\n      Validator<any, \"required\", any>,\n      IsOptional,\n      FieldPaths\n    >\n  | VUnion<Type, Validator<any, \"required\", any>[], IsOptional, FieldPaths>;\n\n/**\n * Join together two index field paths.\n *\n * This is used within the validator builder, {@link v}.\n * @public\n */\nexport type JoinFieldPaths<\n  Start extends string,\n  End extends string,\n> = `${Start}.${End}`;\n\nexport type ObjectFieldType = { fieldType: ValidatorJSON; optional: boolean };\n\nexport type ValidatorJSON =\n  | { type: \"null\" }\n  | { type: \"number\" }\n  | { type: \"bigint\" }\n  | { type: \"boolean\" }\n  | { type: \"string\" }\n  | { type: \"bytes\" }\n  | { type: \"any\" }\n  | { type: \"literal\"; value: JSONValue }\n  | { type: \"id\"; tableName: string }\n  | { type: \"array\"; value: ValidatorJSON }\n  | {\n      type: \"record\";\n      keys: RecordKeyValidatorJSON;\n      values: RecordValueValidatorJSON;\n    }\n  | { type: \"object\"; value: Record<string, ObjectFieldType> }\n  | { type: \"union\"; value: ValidatorJSON[] };\n\nexport type RecordKeyValidatorJSON =\n  | { type: \"string\" }\n  | { type: \"id\"; tableName: string }\n  | { type: \"union\"; value: RecordKeyValidatorJSON[] };\n\nexport type RecordValueValidatorJSON = ObjectFieldType & { optional: false };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAoB,oBAAoB;;;;;;;;;;;AASxC,MAAe,cAIb;IAyBA,yCAAA,GAEA,IAAI,WAAoB;QACtB,OAAO,IAAA,CAAK,UAAA,KAAe,aAAa,OAAO;IACjD;IAPA,YAAY,EAAE,UAAA,CAAW,CAAA,CAA+B;QAjBxD;;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS;QAGP,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,iBAAA,GAAoB;IAC3B;AASF;AAKO,MAAM,YAGH,cAAgC;IA0BxC,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM;YAAM,WAAW,IAAA,CAAK,SAAA;QAAU;IACjD;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,IAAkC;YAC3C,YAAY;YACZ,WAAW,IAAA,CAAK,SAAA;QAClB,CAAC;IACH;IArCwC;;GAAA,GAcxC,YAAY,EACV,UAAA,EACA,SAAA,EACF,CAGG;QACD,KAAA,CAAM;YAAE;QAAW,CAAC;QAjBtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAad,IAAI,OAAO,cAAc,UAAU;YACjC,MAAM,IAAI,MAAM,mCAAmC;QACrD;QACA,IAAA,CAAK,SAAA,GAAY;IACnB;AAYF;AAKO,MAAM,iBAGH,cAAgC;IAIxB,cAAA,GAGhB,IAAI,OAAsB;QAExB,OAAO;YAAE,MAAM;QAAS;IAC1B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,SAAuC;YAChD,YAAY;QACd,CAAC;IACH;IAnBK,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;AAalB;AAKO,MAAM,eAGH,cAAgC;IAIxB,cAAA,GAGhB,IAAI,OAAsB;QAExB,OAAO;YAAE,MAAM;QAAS;IAC1B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,OAAqC;YAAE,YAAY;QAAW,CAAC;IAC5E;IAjBK,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;AAWlB;AAKO,MAAM,iBAGH,cAAgC;IAIxB,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM,IAAA,CAAK,IAAA;QAAK;IAC3B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,SAAuC;YAChD,YAAY;QACd,CAAC;IACH;IAlBK,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;AAYlB;AAKO,MAAM,eAGH,cAAgC;IAIxB,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM,IAAA,CAAK,IAAA;QAAK;IAC3B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,OAAqC;YAAE,YAAY;QAAW,CAAC;IAC5E;IAhBK,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;AAUlB;AAKO,MAAM,gBAGH,cAAgC;IAIxB,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM,IAAA,CAAK,IAAA;QAAK;IAC3B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,QAAsC;YAC/C,YAAY;QACd,CAAC;IACH;IAlBK,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;AAYlB;AAKO,MAAM,cAGH,cAAgC;IAIxB,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM,IAAA,CAAK,IAAA;QAAK;IAC3B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,MAAoC;YAAE,YAAY;QAAW,CAAC;IAC3E;IAhBK,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;AAUlB;AAKO,MAAM,aAIH,cAA4C;IAIpC,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;QACb;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,KAA+C;YACxD,YAAY;QACd,CAAC;IACH;IArBK,aAAA;QAAA,KAAA,IAAA;QAQL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;AAclB;AAKO,MAAM,gBAUH,cAA4C;IA4BpD,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YACX,OAAO,WAAW,MAAA,CAAO,WAAA,CACvB,WAAW,MAAA,CAAO,OAAA,CAAQ,IAAA,CAAK,MAAM,EAAE,GAAA,CAAI;oBAAC,CAAC,GAAG,CAAC,CAAA;uBAAM;oBACrD;oBACA;wBACE,WAAW,EAAE,IAAA;wBACb,UAAU,EAAE,UAAA,KAAe,aAAa,OAAO;oBACjD;iBACD;;QAEL;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,QAA0D;YACnE,YAAY;YACZ,QAAQ,IAAA,CAAK,MAAA;QACf,CAAC;IACH;IAlDoD;;GAAA,GAcpD,YAAY,EACV,UAAA,EACA,MAAA,EACF,CAGG;QACD,KAAA,CAAM;YAAE;QAAW,CAAC;QAjBtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAad,WAAW,MAAA,CAAO,MAAA,CAAO,MAAM,EAAE,OAAA,CAAQ,CAAC,MAAM;YAC9C,IAAI,CAAC,EAAE,iBAAA,EAAmB;gBACxB,MAAM,IAAI,MAAM,sCAAsC;YACxD;QACF,CAAC;QACD,IAAA,CAAK,MAAA,GAAS;IAChB;AAuBF;AAKO,MAAM,iBAGH,cAAgC;IAyBxC,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YACX,WAAO,kPAAA,EAAa,IAAA,CAAK,KAA2C;QACtE;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,SAAuC;YAChD,YAAY;YACZ,OAAO,IAAA,CAAK,KAAA;QACd,CAAC;IACH;IAvCwC;;GAAA,GAcxC,YAAY,EAAE,UAAA,EAAY,KAAA,CAAM,CAAA,CAA4C;QAC1E,KAAA,CAAM;YAAE;QAAW,CAAC;QAXtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAOd,IACE,OAAO,UAAU,YACjB,OAAO,UAAU,aACjB,OAAO,UAAU,YACjB,OAAO,UAAU,UACjB;YACA,MAAM,IAAI,MAAM,uDAAuD;QACzE;QACA,IAAA,CAAK,KAAA,GAAQ;IACf;AAeF;AAKO,MAAM,eAIH,cAAgC;IAuBxC,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YACX,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA;QACtB;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,OAA8C;YACvD,YAAY;YACZ,SAAS,IAAA,CAAK,OAAA;QAChB,CAAC;IACH;IArCwC;;GAAA,GAcxC,YAAY,EACV,UAAA,EACA,OAAA,EACF,CAGG;QACD,KAAA,CAAM;YAAE;QAAW,CAAC;QAjBtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAad,IAAA,CAAK,OAAA,GAAU;IACjB;AAeF;AAKO,MAAM,gBAMH,cAA4C;IAwCpD,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YAAA,yEAAA;YAEX,MAAM,IAAA,CAAK,GAAA,CAAI,IAAA;YACf,QAAQ;gBACN,WAAW,IAAA,CAAK,KAAA,CAAM,IAAA;gBACtB,UAAU;YACZ;QACF;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,QAA8D;YACvE,YAAY;YACZ,KAAK,IAAA,CAAK,GAAA;YACV,OAAO,IAAA,CAAK,KAAA;QACd,CAAC;IACH;IA5DoD;;GAAA,GAmBpD,YAAY,EACV,UAAA,EACA,GAAA,EACA,KAAA,EACF,CAIG;QACD,KAAA,CAAM;YAAE;QAAW,CAAC;QAxBtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAed,IAAK,IAAI,UAAA,KAAoC,YAAY;YACvD,MAAM,IAAI,MAAM,4CAA4C;QAC9D;QACA,IAAK,MAAM,UAAA,KAAoC,YAAY;YACzD,MAAM,IAAI,MAAM,8CAA8C;QAChE;QACA,IAAI,CAAC,IAAI,iBAAA,IAAqB,CAAC,MAAM,iBAAA,EAAmB;YACtD,MAAM,IAAI,MAAM,+CAA+C;QACjE;QACA,IAAA,CAAK,GAAA,GAAM;QACX,IAAA,CAAK,KAAA,GAAQ;IACf;AAqBF;AAKO,MAAM,eAKH,cAA4C;IAsBpD,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YACX,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,IAAM,EAAE,IAAI;QACvC;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,OAAwC;YACjD,YAAY;YACZ,SAAS,IAAA,CAAK,OAAA;QAChB,CAAC;IACH;IApCoD;;GAAA,GAcpD,YAAY,EAAE,UAAA,EAAY,OAAA,CAAQ,CAAA,CAA2C;QAC3E,KAAA,CAAM;YAAE;QAAW,CAAC;QAXtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAOd,QAAQ,OAAA,CAAQ,CAAC,WAAW;YAC1B,IAAI,CAAC,OAAO,iBAAA,EAAmB;gBAC7B,MAAM,IAAI,MAAM,6CAA6C;YAC/D;QACF,CAAC;QACD,IAAA,CAAK,OAAA,GAAU;IACjB;AAeF", "debugId": null}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/values/validator.ts"], "sourcesContent": ["import { Expand } from \"../type_utils.js\";\nimport { GenericId } from \"./index.js\";\nimport {\n  OptionalProperty,\n  VAny,\n  VArray,\n  VBoolean,\n  VBytes,\n  VFloat64,\n  VId,\n  VInt64,\n  VLiteral,\n  VNull,\n  VObject,\n  VOptional,\n  VRecord,\n  VString,\n  VUnion,\n  Validator,\n} from \"./validators.js\";\n\n/**\n * The type that all validators must extend.\n *\n * @public\n */\nexport type GenericValidator = Validator<any, any, any>;\n\nexport function isValidator(v: any): v is GenericValidator {\n  return !!v.isConvexValidator;\n}\n\n/**\n * Coerce an object with validators as properties to a validator.\n * If a validator is passed, return it.\n *\n * @public\n */\nexport function asObjectValidator<\n  V extends Validator<any, any, any> | PropertyValidators,\n>(\n  obj: V,\n): V extends Validator<any, any, any>\n  ? V\n  : V extends PropertyValidators\n    ? Validator<ObjectType<V>>\n    : never {\n  if (isValidator(obj)) {\n    return obj as any;\n  } else {\n    return v.object(obj as PropertyValidators) as any;\n  }\n}\n\n/**\n * Coerce an object with validators as properties to a validator.\n * If a validator is passed, return it.\n *\n * @public\n */\nexport type AsObjectValidator<\n  V extends Validator<any, any, any> | PropertyValidators,\n> =\n  V extends Validator<any, any, any>\n    ? V\n    : V extends PropertyValidators\n      ? Validator<ObjectType<V>>\n      : never;\n\n/**\n * The validator builder.\n *\n * This builder allows you to build validators for Convex values.\n *\n * Validators can be used in [schema definitions](https://docs.convex.dev/database/schemas)\n * and as input validators for Convex functions.\n *\n * @public\n */\nexport const v = {\n  /**\n   * Validates that the value corresponds to an ID of a document in given table.\n   * @param tableName The name of the table.\n   */\n  id: <TableName extends string>(tableName: TableName) => {\n    return new VId<GenericId<TableName>>({\n      isOptional: \"required\",\n      tableName,\n    });\n  },\n\n  /**\n   * Validates that the value is of type Null.\n   */\n  null: () => {\n    return new VNull({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Float64 (Number in JS).\n   *\n   * Alias for `v.float64()`\n   */\n  number: () => {\n    return new VFloat64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Float64 (Number in JS).\n   */\n  float64: () => {\n    return new VFloat64({ isOptional: \"required\" });\n  },\n\n  /**\n   * @deprecated Use `v.int64()` instead\n   */\n  bigint: () => {\n    return new VInt64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Int64 (BigInt in JS).\n   */\n  int64: () => {\n    return new VInt64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of type Boolean.\n   */\n  boolean: () => {\n    return new VBoolean({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of type String.\n   */\n  string: () => {\n    return new VString({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Bytes (constructed in JS via `ArrayBuffer`).\n   */\n  bytes: () => {\n    return new VBytes({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is equal to the given literal value.\n   * @param literal The literal value to compare against.\n   */\n  literal: <T extends string | number | bigint | boolean>(literal: T) => {\n    return new VLiteral<T>({ isOptional: \"required\", value: literal });\n  },\n\n  /**\n   * Validates that the value is an Array of the given element type.\n   * @param element The validator for the elements of the array.\n   */\n  array: <T extends Validator<any, \"required\", any>>(element: T) => {\n    return new VArray<T[\"type\"][], T>({ isOptional: \"required\", element });\n  },\n\n  /**\n   * Validates that the value is an Object with the given properties.\n   * @param fields An object specifying the validator for each property.\n   */\n  object: <T extends PropertyValidators>(fields: T) => {\n    return new VObject<ObjectType<T>, T>({ isOptional: \"required\", fields });\n  },\n\n  /**\n   * Validates that the value is a Record with keys and values that match the given types.\n   * @param keys The validator for the keys of the record. This cannot contain string literals.\n   * @param values The validator for the values of the record.\n   */\n  record: <\n    Key extends Validator<string, \"required\", any>,\n    Value extends Validator<any, \"required\", any>,\n  >(\n    keys: Key,\n    values: Value,\n  ) => {\n    return new VRecord<Record<Infer<Key>, Value[\"type\"]>, Key, Value>({\n      isOptional: \"required\",\n      key: keys,\n      value: values,\n    });\n  },\n\n  /**\n   * Validates that the value matches one of the given validators.\n   * @param members The validators to match against.\n   */\n  union: <T extends Validator<any, \"required\", any>[]>(...members: T) => {\n    return new VUnion<T[number][\"type\"], T>({\n      isOptional: \"required\",\n      members,\n    });\n  },\n\n  /**\n   * Does not validate the value.\n   */\n  any: () => {\n    return new VAny({ isOptional: \"required\" });\n  },\n\n  /**\n   * Allows not specifying a value for a property in an Object.\n   * @param value The property value validator to make optional.\n   *\n   * ```typescript\n   * const objectWithOptionalFields = v.object({\n   *   requiredField: v.string(),\n   *   optionalField: v.optional(v.string()),\n   * });\n   * ```\n   */\n  optional: <T extends GenericValidator>(value: T) => {\n    return value.asOptional() as VOptional<T>;\n  },\n};\n\n/**\n * Validators for each property of an object.\n *\n * This is represented as an object mapping the property name to its\n * {@link Validator}.\n *\n * @public\n */\nexport type PropertyValidators = Record<\n  string,\n  Validator<any, OptionalProperty, any>\n>;\n\n/**\n * Compute the type of an object from {@link PropertyValidators}.\n *\n * @public\n */\nexport type ObjectType<Fields extends PropertyValidators> = Expand<\n  // Map each key to the corresponding property validator's type making\n  // the optional ones optional.\n  {\n    // This `Exclude<..., undefined>` does nothing unless\n    // the tsconfig.json option `\"exactOptionalPropertyTypes\": true,`\n    // is used. When it is it results in a more accurate type.\n    // When it is not the `Exclude` removes `undefined` but it is\n    // added again by the optional property.\n    [Property in OptionalKeys<Fields>]?: Exclude<\n      Infer<Fields[Property]>,\n      undefined\n    >;\n  } & {\n    [Property in RequiredKeys<Fields>]: Infer<Fields[Property]>;\n  }\n>;\n\ntype OptionalKeys<PropertyValidators extends Record<string, GenericValidator>> =\n  {\n    [Property in keyof PropertyValidators]: PropertyValidators[Property][\"isOptional\"] extends \"optional\"\n      ? Property\n      : never;\n  }[keyof PropertyValidators];\n\ntype RequiredKeys<PropertyValidators extends Record<string, GenericValidator>> =\n  Exclude<keyof PropertyValidators, OptionalKeys<PropertyValidators>>;\n\n/**\n * Extract a TypeScript type from a validator.\n *\n * Example usage:\n * ```ts\n * const objectSchema = v.object({\n *   property: v.string(),\n * });\n * type MyObject = Infer<typeof objectSchema>; // { property: string }\n * ```\n * @typeParam V - The type of a {@link Validator} constructed with {@link v}.\n *\n * @public\n */\nexport type Infer<T extends Validator<any, OptionalProperty, any>> = T[\"type\"];\n"], "names": ["v"], "mappings": ";;;;;;;;AAEA;;;AA0BO,SAAS,YAAYA,EAAAA,EAA+B;IACzD,OAAO,CAAC,CAACA,GAAE,iBAAA;AACb;AAQO,SAAS,kBAGd,GAAA,EAKU;IACV,IAAI,YAAY,GAAG,GAAG;QACpB,OAAO;IACT,OAAO;QACL,OAAO,EAAE,MAAA,CAAO,GAAyB;IAC3C;AACF;AA2BO,MAAM,IAAI;IAAA;;;GAAA,GAKf,IAAI,CAA2B,cAAyB;QACtD,OAAO,IAAI,8OAAA,CAA0B;YACnC,YAAY;YACZ;QACF,CAAC;IACH;IAAA;;GAAA,GAKA,MAAM,MAAM;QACV,OAAO,IAAI,gPAAA,CAAM;YAAE,YAAY;QAAW,CAAC;IAC7C;IAAA;;;;GAAA,GAOA,QAAQ,MAAM;QACZ,OAAO,IAAI,mPAAA,CAAS;YAAE,YAAY;QAAW,CAAC;IAChD;IAAA;;GAAA,GAKA,SAAS,MAAM;QACb,OAAO,IAAI,mPAAA,CAAS;YAAE,YAAY;QAAW,CAAC;IAChD;IAAA;;GAAA,GAKA,QAAQ,MAAM;QACZ,OAAO,IAAI,iPAAA,CAAO;YAAE,YAAY;QAAW,CAAC;IAC9C;IAAA;;GAAA,GAKA,OAAO,MAAM;QACX,OAAO,IAAI,iPAAA,CAAO;YAAE,YAAY;QAAW,CAAC;IAC9C;IAAA;;GAAA,GAKA,SAAS,MAAM;QACb,OAAO,IAAI,mPAAA,CAAS;YAAE,YAAY;QAAW,CAAC;IAChD;IAAA;;GAAA,GAKA,QAAQ,MAAM;QACZ,OAAO,IAAI,kPAAA,CAAQ;YAAE,YAAY;QAAW,CAAC;IAC/C;IAAA;;GAAA,GAKA,OAAO,MAAM;QACX,OAAO,IAAI,iPAAA,CAAO;YAAE,YAAY;QAAW,CAAC;IAC9C;IAAA;;;GAAA,GAMA,SAAS,CAA+C,YAAe;QACrE,OAAO,IAAI,mPAAA,CAAY;YAAE,YAAY;YAAY,OAAO;QAAQ,CAAC;IACnE;IAAA;;;GAAA,GAMA,OAAO,CAA4C,YAAe;QAChE,OAAO,IAAI,iPAAA,CAAuB;YAAE,YAAY;YAAY;QAAQ,CAAC;IACvE;IAAA;;;GAAA,GAMA,QAAQ,CAA+B,WAAc;QACnD,OAAO,IAAI,kPAAA,CAA0B;YAAE,YAAY;YAAY;QAAO,CAAC;IACzE;IAAA;;;;GAAA,GAOA,QAAQ,CAIN,MACA,WACG;QACH,OAAO,IAAI,kPAAA,CAAuD;YAChE,YAAY;YACZ,KAAK;YACL,OAAO;QACT,CAAC;IACH;IAAA;;;GAAA,GAMA,OAAO;;YAAiD,YAAe;;QACrE,OAAO,IAAI,iPAAA,CAA6B;YACtC,YAAY;YACZ;QACF,CAAC;IACH;IAAA;;GAAA,GAKA,KAAK,MAAM;QACT,OAAO,IAAI,+OAAA,CAAK;YAAE,YAAY;QAAW,CAAC;IAC5C;IAAA;;;;;;;;;;GAAA,GAaA,UAAU,CAA6B,UAAa;QAClD,OAAO,MAAM,UAAA,CAAW;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/values/errors.ts"], "sourcesContent": ["import { Value, stringifyValueForError } from \"./value.js\";\n\nconst IDENTIFYING_FIELD = Symbol.for(\"ConvexError\");\n\nexport class ConvexError<TData extends Value> extends Error {\n  name = \"ConvexError\";\n  data: TData;\n  [IDENTIFYING_FIELD] = true;\n\n  constructor(data: TData) {\n    super(typeof data === \"string\" ? data : stringifyValueForError(data));\n    this.data = data;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAgB,8BAA8B;;;;;;;;;;AAA9C,IAAA,IAAA;;AAEA,MAAM,oBAAoB,OAAO,GAAA,CAAI,aAAa;AAE3C,MAAM,oBAAA,CAAyC,KAAA,OAGnD,KAAA,mBAHmD,EAAA,EAAM;IAK1D,YAAY,IAAA,CAAa;QACvB,KAAA,CAAM,OAAO,SAAS,WAAW,WAAO,4PAAA,EAAuB,IAAI,CAAC;QALtE,cAAA,IAAA,EAAA,QAAO;QACP,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAC,IAAqB;QAIpB,IAAA,CAAK,IAAA,GAAO;IACd;AACF", "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/values/compare_utf8.ts"], "sourcesContent": ["/**\n * Taken from https://github.com/rocicorp/compare-utf8/blob/main/LICENSE\n * (Apache Version 2.0, January 2004)\n */\n\n/**\n * This is copied here instead of added as a dependency to avoid bundling issues.\n */\n\n/**\n * Compares two JavaScript strings as if they were UTF-8 encoded byte arrays.\n * @param {string} a\n * @param {string} b\n * @returns {number}\n */\nexport function compareUTF8(a: string, b: string): number {\n  const aLength = a.length;\n  const bLength = b.length;\n  const length = Math.min(aLength, bLength);\n  for (let i = 0; i < length; ) {\n    const aCodePoint = a.codePointAt(i)!;\n    const bCodePoint = b.codePointAt(i)!;\n    if (aCodePoint !== bCodePoint) {\n      // Code points below 0x80 are represented the same way in UTF-8 as in\n      // UTF-16.\n      if (aCodePoint < 0x80 && bCodePoint < 0x80) {\n        return aCodePoint - bCodePoint;\n      }\n\n      // get the UTF-8 bytes for the code points\n      const aLength = utf8Bytes(aCodePoint, aBytes);\n      const bLength = utf8Bytes(bCodePoint, bBytes);\n      return compareArrays(aBytes, aLength, bBytes, bLength);\n    }\n\n    i += utf16LengthForCodePoint(aCodePoint);\n  }\n\n  return aLength - bLength;\n}\n\n/**\n * @param {number[]} a\n * @param {number} aLength\n * @param {number[]} b\n * @param {number} bLength\n * @returns {number}\n */\nfunction compareArrays(\n  a: number[],\n  aLength: number,\n  b: number[],\n  bLength: number,\n) {\n  const length = Math.min(aLength, bLength);\n  for (let i = 0; i < length; i++) {\n    const aValue = a[i];\n    const bValue = b[i];\n    if (aValue !== bValue) {\n      return aValue - bValue;\n    }\n  }\n  return aLength - bLength;\n}\n\n/**\n * @param {number} aCodePoint\n * @returns {number}\n */\nexport function utf16LengthForCodePoint(aCodePoint: number) {\n  return aCodePoint > 0xffff ? 2 : 1;\n}\n\n// 2 preallocated arrays for utf8Bytes.\nconst arr = () => Array.from({ length: 4 }, () => 0);\nconst aBytes = arr();\nconst bBytes = arr();\n\n/**\n * @param {number} codePoint\n * @param {number[]} bytes\n * @returns {number}\n */\nfunction utf8Bytes(codePoint: number, bytes: number[]) {\n  if (codePoint < 0x80) {\n    bytes[0] = codePoint;\n    return 1;\n  }\n\n  let count;\n  let offset;\n\n  if (codePoint <= 0x07ff) {\n    count = 1;\n    offset = 0xc0;\n  } else if (codePoint <= 0xffff) {\n    count = 2;\n    offset = 0xe0;\n  } else if (codePoint <= 0x10ffff) {\n    count = 3;\n    offset = 0xf0;\n  } else {\n    throw new Error(\"Invalid code point\");\n  }\n\n  bytes[0] = (codePoint >> (6 * count)) + offset;\n  let i = 1;\n  for (; count > 0; count--) {\n    const temp = codePoint >> (6 * (count - 1));\n    bytes[i++] = 0x80 | (temp & 0x3f);\n  }\n  return i;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function greaterThan(a: string, b: string) {\n  return compareUTF8(a, b) > 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function greaterThanEq(a: string, b: string) {\n  return compareUTF8(a, b) >= 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function lessThan(a: string, b: string) {\n  return compareUTF8(a, b) < 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function lessThanEq(a: string, b: string) {\n  return compareUTF8(a, b) <= 0;\n}\n"], "names": ["a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;AAeO,SAAS,YAAY,CAAA,EAAW,CAAA,EAAmB;IACxD,MAAM,UAAU,EAAE,MAAA;IAClB,MAAM,UAAU,EAAE,MAAA;IAClB,MAAM,SAAS,KAAK,GAAA,CAAI,SAAS,OAAO;IACxC,IAAA,IAAS,IAAI,GAAG,IAAI,QAAU;QAC5B,MAAM,aAAa,EAAE,WAAA,CAAY,CAAC;QAClC,MAAM,aAAa,EAAE,WAAA,CAAY,CAAC;QAClC,IAAI,eAAe,YAAY;YAG7B,IAAI,aAAa,OAAQ,aAAa,KAAM;gBAC1C,OAAO,aAAa;YACtB;YAGA,MAAMA,WAAU,UAAU,YAAY,MAAM;YAC5C,MAAMC,WAAU,UAAU,YAAY,MAAM;YAC5C,OAAO,cAAc,QAAQD,UAAS,QAAQC,QAAO;QACvD;QAEA,KAAK,wBAAwB,UAAU;IACzC;IAEA,OAAO,UAAU;AACnB;AASA,SAAS,cACP,CAAA,EACA,OAAA,EACA,CAAA,EACA,OAAA,EACA;IACA,MAAM,SAAS,KAAK,GAAA,CAAI,SAAS,OAAO;IACxC,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,SAAS,CAAA,CAAE,CAAC,CAAA;QAClB,MAAM,SAAS,CAAA,CAAE,CAAC,CAAA;QAClB,IAAI,WAAW,QAAQ;YACrB,OAAO,SAAS;QAClB;IACF;IACA,OAAO,UAAU;AACnB;AAMO,SAAS,wBAAwB,UAAA,EAAoB;IAC1D,OAAO,aAAa,QAAS,IAAI;AACnC;AAGA,MAAM,MAAM,IAAM,MAAM,IAAA,CAAK;QAAE,QAAQ;IAAE,GAAG,IAAM,CAAC;AACnD,MAAM,SAAS,IAAI;AACnB,MAAM,SAAS,IAAI;AAOnB,SAAS,UAAU,SAAA,EAAmB,KAAA,EAAiB;IACrD,IAAI,YAAY,KAAM;QACpB,KAAA,CAAM,CAAC,CAAA,GAAI;QACX,OAAO;IACT;IAEA,IAAI;IACJ,IAAI;IAEJ,IAAI,aAAa,MAAQ;QACvB,QAAQ;QACR,SAAS;IACX,OAAA,IAAW,aAAa,OAAQ;QAC9B,QAAQ;QACR,SAAS;IACX,OAAA,IAAW,aAAa,SAAU;QAChC,QAAQ;QACR,SAAS;IACX,OAAO;QACL,MAAM,IAAI,MAAM,oBAAoB;IACtC;IAEA,KAAA,CAAM,CAAC,CAAA,GAAA,CAAK,aAAc,IAAI,KAAA,IAAU;IACxC,IAAI,IAAI;IACR,MAAO,QAAQ,GAAG,QAAS;QACzB,MAAM,OAAO,aAAc,IAAA,CAAK,QAAQ,CAAA;QACxC,KAAA,CAAM,GAAG,CAAA,GAAI,MAAQ,OAAO;IAC9B;IACA,OAAO;AACT;AAOO,SAAS,YAAY,CAAA,EAAW,CAAA,EAAW;IAChD,OAAO,YAAY,GAAG,CAAC,IAAI;AAC7B;AAOO,SAAS,cAAc,CAAA,EAAW,CAAA,EAAW;IAClD,OAAO,YAAY,GAAG,CAAC,KAAK;AAC9B;AAOO,SAAS,SAAS,CAAA,EAAW,CAAA,EAAW;IAC7C,OAAO,YAAY,GAAG,CAAC,IAAI;AAC7B;AAOO,SAAS,WAAW,CAAA,EAAW,CAAA,EAAW;IAC/C,OAAO,YAAY,GAAG,CAAC,KAAK;AAC9B", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/values/compare.ts"], "sourcesContent": ["import { Value } from \"./value.js\";\nimport { compareUTF8 } from \"./compare_utf8.js\";\n\nexport function compareValues(k1: Value | undefined, k2: Value | undefined) {\n  return compareAsTuples(makeComparable(k1), makeComparable(k2));\n}\n\nfunction compareAsTuples<T>(a: [number, T], b: [number, T]): number {\n  if (a[0] === b[0]) {\n    return compareSameTypeValues(a[1], b[1]);\n  } else if (a[0] < b[0]) {\n    return -1;\n  }\n  return 1;\n}\n\nfunction compareSameTypeValues<T>(v1: T, v2: T): number {\n  if (v1 === undefined || v1 === null) {\n    return 0;\n  }\n  if (typeof v1 === \"number\") {\n    if (typeof v2 !== \"number\") {\n      throw new Error(`Unexpected type ${v2 as any}`);\n    }\n    return compareNumbers(v1, v2);\n  }\n  if (typeof v1 === \"string\") {\n    if (typeof v2 !== \"string\") {\n      throw new Error(`Unexpected type ${v2 as any}`);\n    }\n    return compareUTF8(v1, v2);\n  }\n  if (\n    typeof v1 === \"bigint\" ||\n    typeof v1 === \"boolean\" ||\n    typeof v1 === \"string\"\n  ) {\n    return v1 < v2 ? -1 : v1 === v2 ? 0 : 1;\n  }\n  if (!Array.isArray(v1) || !Array.isArray(v2)) {\n    throw new Error(`Unexpected type ${v1 as any}`);\n  }\n  for (let i = 0; i < v1.length && i < v2.length; i++) {\n    const cmp = compareAsTuples(v1[i], v2[i]);\n    if (cmp !== 0) {\n      return cmp;\n    }\n  }\n  if (v1.length < v2.length) {\n    return -1;\n  }\n  if (v1.length > v2.length) {\n    return 1;\n  }\n  return 0;\n}\n\nfunction compareNumbers(v1: number, v2: number): number {\n  // Handle NaN values\n  if (isNaN(v1) || isNaN(v2)) {\n    // Create DataViews for bit-level comparison\n    const buffer1 = new ArrayBuffer(8);\n    const buffer2 = new ArrayBuffer(8);\n    new DataView(buffer1).setFloat64(0, v1, /* little-endian */ true);\n    new DataView(buffer2).setFloat64(0, v2, /* little-endian */ true);\n\n    // Read as BigInt to compare bits\n    const v1Bits = BigInt(\n      new DataView(buffer1).getBigInt64(0, /* little-endian */ true),\n    );\n    const v2Bits = BigInt(\n      new DataView(buffer2).getBigInt64(0, /* little-endian */ true),\n    );\n\n    // The sign bit is the most significant bit (bit 63)\n    const v1Sign = (v1Bits & 0x8000000000000000n) !== 0n;\n    const v2Sign = (v2Bits & 0x8000000000000000n) !== 0n;\n\n    // If one value is NaN and the other isn't, use sign bits first\n    if (isNaN(v1) !== isNaN(v2)) {\n      // If v1 is NaN, compare based on sign bits\n      if (isNaN(v1)) {\n        return v1Sign ? -1 : 1;\n      }\n      // If v2 is NaN, compare based on sign bits\n      return v2Sign ? 1 : -1;\n    }\n\n    // If both are NaN, compare their binary representations\n    if (v1Sign !== v2Sign) {\n      return v1Sign ? -1 : 1; // true means negative\n    }\n    return v1Bits < v2Bits ? -1 : v1Bits === v2Bits ? 0 : 1;\n  }\n\n  if (Object.is(v1, v2)) {\n    return 0;\n  }\n\n  if (Object.is(v1, -0)) {\n    return Object.is(v2, 0) ? -1 : -Math.sign(v2);\n  }\n  if (Object.is(v2, -0)) {\n    return Object.is(v1, 0) ? 1 : Math.sign(v1);\n  }\n\n  // Handle regular number comparison\n  return v1 < v2 ? -1 : 1;\n}\n\n// Returns an array which can be compared to other arrays as if they were tuples.\n// For example, [1, null] < [2, 1n] means null sorts before all bigints\n// And [3, 5] < [3, 6] means floats sort as expected\n// And [7, [[5, \"a\"]]] < [7, [[5, \"a\"], [5, \"b\"]]] means arrays sort as expected\nfunction makeComparable(v: Value | undefined): [number, any] {\n  if (v === undefined) {\n    return [0, undefined];\n  }\n  if (v === null) {\n    return [1, null];\n  }\n  if (typeof v === \"bigint\") {\n    return [2, v];\n  }\n  if (typeof v === \"number\") {\n    return [3, v];\n  }\n  if (typeof v === \"boolean\") {\n    return [4, v];\n  }\n  if (typeof v === \"string\") {\n    return [5, v];\n  }\n  if (v instanceof ArrayBuffer) {\n    return [6, Array.from(new Uint8Array(v)).map(makeComparable)];\n  }\n  if (Array.isArray(v)) {\n    return [7, v.map(makeComparable)];\n  }\n  // Otherwise, it's an POJO.\n  const keys = Object.keys(v).sort();\n  const pojo: Value[] = keys.map((k) => [k, v[k]!]);\n  return [8, pojo.map(makeComparable)];\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,mBAAmB;;;AAErB,SAAS,cAAc,EAAA,EAAuB,EAAA,EAAuB;IAC1E,OAAO,gBAAgB,eAAe,EAAE,GAAG,eAAe,EAAE,CAAC;AAC/D;AAEA,SAAS,gBAAmB,CAAA,EAAgB,CAAA,EAAwB;IAClE,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,CAAA,CAAE,CAAC,CAAA,EAAG;QACjB,OAAO,sBAAsB,CAAA,CAAE,CAAC,CAAA,EAAG,CAAA,CAAE,CAAC,CAAC;IACzC,OAAA,IAAW,CAAA,CAAE,CAAC,CAAA,GAAI,CAAA,CAAE,CAAC,CAAA,EAAG;QACtB,OAAO,CAAA;IACT;IACA,OAAO;AACT;AAEA,SAAS,sBAAyB,EAAA,EAAO,EAAA,EAAe;IACtD,IAAI,OAAO,KAAA,KAAa,OAAO,MAAM;QACnC,OAAO;IACT;IACA,IAAI,OAAO,OAAO,UAAU;QAC1B,IAAI,OAAO,OAAO,UAAU;YAC1B,MAAM,IAAI,MAAM,mBAA4B,CAAE,MAAX,EAAS;QAC9C;QACA,OAAO,eAAe,IAAI,EAAE;IAC9B;IACA,IAAI,OAAO,OAAO,UAAU;QAC1B,IAAI,OAAO,OAAO,UAAU;YAC1B,MAAM,IAAI,MAAM,mBAA4B,CAAE,MAAX,EAAS;QAC9C;QACA,OAAO,4PAAA,EAAY,IAAI,EAAE;IAC3B;IACA,IACE,OAAO,OAAO,YACd,OAAO,OAAO,aACd,OAAO,OAAO,UACd;QACA,OAAO,KAAK,KAAK,CAAA,IAAK,OAAO,KAAK,IAAI;IACxC;IACA,IAAI,CAAC,MAAM,OAAA,CAAQ,EAAE,KAAK,CAAC,MAAM,OAAA,CAAQ,EAAE,GAAG;QAC5C,MAAM,IAAI,MAAM,mBAA4B,CAAE,MAAX,EAAS;IAC9C;IACA,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,MAAA,IAAU,IAAI,GAAG,MAAA,EAAQ,IAAK;QACnD,MAAM,MAAM,gBAAgB,EAAA,CAAG,CAAC,CAAA,EAAG,EAAA,CAAG,CAAC,CAAC;QACxC,IAAI,QAAQ,GAAG;YACb,OAAO;QACT;IACF;IACA,IAAI,GAAG,MAAA,GAAS,GAAG,MAAA,EAAQ;QACzB,OAAO,CAAA;IACT;IACA,IAAI,GAAG,MAAA,GAAS,GAAG,MAAA,EAAQ;QACzB,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,eAAe,EAAA,EAAY,EAAA,EAAoB;IAEtD,IAAI,MAAM,EAAE,KAAK,MAAM,EAAE,GAAG;QAE1B,MAAM,UAAU,IAAI,YAAY,CAAC;QACjC,MAAM,UAAU,IAAI,YAAY,CAAC;QACjC,IAAI,SAAS,OAAO,EAAE,UAAA,CAAW,GAAG,IAAA,iBAAA,GAAwB;QAC5D,IAAI,SAAS,OAAO,EAAE,UAAA,CAAW,GAAG,IAAA,iBAAA,GAAwB;QAG5D,MAAM,SAAS,OACb,IAAI,SAAS,OAAO,EAAE,WAAA,CAAY,GAAA,iBAAA,GAAuB;QAE3D,MAAM,SAAS,OACb,IAAI,SAAS,OAAO,EAAE,WAAA,CAAY,GAAA,iBAAA,GAAuB;QAI3D,MAAM,SAAA,CAAU,SAAS,mBAAA,MAAyB,EAAA;QAClD,MAAM,SAAA,CAAU,SAAS,mBAAA,MAAyB,EAAA;QAGlD,IAAI,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG;YAE3B,IAAI,MAAM,EAAE,GAAG;gBACb,OAAO,SAAS,CAAA,IAAK;YACvB;YAEA,OAAO,SAAS,IAAI,CAAA;QACtB;QAGA,IAAI,WAAW,QAAQ;YACrB,OAAO,SAAS,CAAA,IAAK;QACvB;QACA,OAAO,SAAS,SAAS,CAAA,IAAK,WAAW,SAAS,IAAI;IACxD;IAEA,IAAI,OAAO,EAAA,CAAG,IAAI,EAAE,GAAG;QACrB,OAAO;IACT;IAEA,IAAI,OAAO,EAAA,CAAG,IAAI,CAAA,CAAE,GAAG;QACrB,OAAO,OAAO,EAAA,CAAG,IAAI,CAAC,IAAI,CAAA,IAAK,CAAC,KAAK,IAAA,CAAK,EAAE;IAC9C;IACA,IAAI,OAAO,EAAA,CAAG,IAAI,CAAA,CAAE,GAAG;QACrB,OAAO,OAAO,EAAA,CAAG,IAAI,CAAC,IAAI,IAAI,KAAK,IAAA,CAAK,EAAE;IAC5C;IAGA,OAAO,KAAK,KAAK,CAAA,IAAK;AACxB;AAMA,SAAS,eAAe,CAAA,EAAqC;IAC3D,IAAI,MAAM,KAAA,GAAW;QACnB,OAAO;YAAC;YAAG,KAAA,CAAS;SAAA;IACtB;IACA,IAAI,MAAM,MAAM;QACd,OAAO;YAAC;YAAG,IAAI;SAAA;IACjB;IACA,IAAI,OAAO,MAAM,UAAU;QACzB,OAAO;YAAC;YAAG,CAAC;SAAA;IACd;IACA,IAAI,OAAO,MAAM,UAAU;QACzB,OAAO;YAAC;YAAG,CAAC;SAAA;IACd;IACA,IAAI,OAAO,MAAM,WAAW;QAC1B,OAAO;YAAC;YAAG,CAAC;SAAA;IACd;IACA,IAAI,OAAO,MAAM,UAAU;QACzB,OAAO;YAAC;YAAG,CAAC;SAAA;IACd;IACA,IAAI,aAAa,aAAa;QAC5B,OAAO;YAAC;YAAG,MAAM,IAAA,CAAK,IAAI,WAAW,CAAC,CAAC,EAAE,GAAA,CAAI,cAAc,CAAC;SAAA;IAC9D;IACA,IAAI,MAAM,OAAA,CAAQ,CAAC,GAAG;QACpB,OAAO;YAAC;YAAG,EAAE,GAAA,CAAI,cAAc,CAAC;SAAA;IAClC;IAEA,MAAM,OAAO,OAAO,IAAA,CAAK,CAAC,EAAE,IAAA,CAAK;IACjC,MAAM,OAAgB,KAAK,GAAA,CAAI,CAAC,IAAM;YAAC;YAAG,CAAA,CAAE,CAAC,CAAE;SAAC;IAChD,OAAO;QAAC;QAAG,KAAK,GAAA,CAAI,cAAc,CAAC;KAAA;AACrC", "debugId": null}}, {"offset": {"line": 1286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/values/index.ts"], "sourcesContent": ["/**\n * Utilities for working with values stored in Convex.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n * @module\n */\n\nexport { convexToJson, jsonToConvex } from \"./value.js\";\nexport type {\n  Id as GenericId,\n  JSONValue,\n  Value,\n  NumericValue,\n} from \"./value.js\";\nexport { v, asObjectValidator } from \"./validator.js\";\nexport type {\n  AsObjectValidator,\n  GenericValidator,\n  ObjectType,\n  PropertyValidators,\n} from \"./validator.js\";\nexport type {\n  ValidatorJSON,\n  RecordKeyValidatorJSON,\n  RecordValueValidatorJSON,\n  ObjectFieldType,\n  Validator,\n  OptionalProperty,\n  VId,\n  VFloat64,\n  VInt64,\n  VBoolean,\n  VBytes,\n  VString,\n  VNull,\n  VAny,\n  VObject,\n  VLiteral,\n  VArray,\n  VRecord,\n  VUnion,\n  VOptional,\n} from \"./validators.js\";\nimport * as Base64 from \"./base64.js\";\nexport { Base64 };\nexport type { Infer } from \"./validator.js\";\nexport * from \"./errors.js\";\nexport { compareValues } from \"./compare.js\";\n"], "names": [], "mappings": ";AAQA,SAAS,cAAc,oBAAoB;AAO3C,SAAS,GAAG,yBAAyB;AA6BrC,YAAY,YAAY;AAGxB,cAAc;AACd,SAAS,qBAAqB", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/index.ts"], "sourcesContent": ["export const version = \"1.26.2\";\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,UAAU", "debugId": null}}, {"offset": {"line": 1313, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/logging.ts"], "sourcesContent": ["/* eslint-disable no-console */ // This is the one file where we can `console.log` for the default logger implementation.\nimport { ConvexError, Value } from \"../values/index.js\";\nimport { FunctionFailure } from \"./sync/function_result.js\";\n\n// This is blue #9 from https://www.radix-ui.com/docs/colors/palette-composition/the-scales\n// It must look good in both light and dark mode.\nconst INFO_COLOR = \"color:rgb(0, 145, 255)\";\n\nexport type UdfType = \"query\" | \"mutation\" | \"action\" | \"any\";\n\nfunction prefix_for_source(source: UdfType) {\n  switch (source) {\n    case \"query\":\n      return \"Q\";\n    case \"mutation\":\n      return \"M\";\n    case \"action\":\n      return \"A\";\n    case \"any\":\n      return \"?\";\n  }\n}\n\nexport type LogLevel = \"debug\" | \"info\" | \"warn\" | \"error\";\n\n/**\n * A logger that can be used to log messages. By default, this is a wrapper\n * around `console`, but can be configured to not log at all or to log somewhere\n * else.\n */\nexport type Logger = {\n  logVerbose(...args: any[]): void;\n  log(...args: any[]): void;\n  warn(...args: any[]): void;\n  error(...args: any[]): void;\n};\n\nexport class DefaultLogger implements Logger {\n  private _onLogLineFuncs: Record<\n    string,\n    (level: LogLevel, ...args: any[]) => void\n  >;\n  private _verbose: boolean;\n\n  constructor(options: { verbose: boolean }) {\n    this._onLogLineFuncs = {};\n    this._verbose = options.verbose;\n  }\n\n  addLogLineListener(\n    func: (level: LogLevel, ...args: any[]) => void,\n  ): () => void {\n    let id = Math.random().toString(36).substring(2, 15);\n    for (let i = 0; i < 10; i++) {\n      if (this._onLogLineFuncs[id] === undefined) {\n        break;\n      }\n      id = Math.random().toString(36).substring(2, 15);\n    }\n    this._onLogLineFuncs[id] = func;\n    return () => {\n      delete this._onLogLineFuncs[id];\n    };\n  }\n\n  logVerbose(...args: any[]) {\n    if (this._verbose) {\n      for (const func of Object.values(this._onLogLineFuncs)) {\n        func(\"debug\", `${new Date().toISOString()}`, ...args);\n      }\n    }\n  }\n\n  log(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"info\", ...args);\n    }\n  }\n\n  warn(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"warn\", ...args);\n    }\n  }\n\n  error(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"error\", ...args);\n    }\n  }\n}\n\nexport function instantiateDefaultLogger(options: {\n  verbose: boolean;\n}): Logger {\n  const logger = new DefaultLogger(options);\n  logger.addLogLineListener((level, ...args) => {\n    switch (level) {\n      case \"debug\":\n        console.debug(...args);\n        break;\n      case \"info\":\n        console.log(...args);\n        break;\n      case \"warn\":\n        console.warn(...args);\n        break;\n      case \"error\":\n        console.error(...args);\n        break;\n      default: {\n        level satisfies never;\n        console.log(...args);\n      }\n    }\n  });\n  return logger;\n}\n\nexport function instantiateNoopLogger(options: { verbose: boolean }): Logger {\n  return new DefaultLogger(options);\n}\n\nexport function logForFunction(\n  logger: Logger,\n  type: \"info\" | \"error\",\n  source: UdfType,\n  udfPath: string,\n  message: string | { errorData: Value },\n) {\n  const prefix = prefix_for_source(source);\n\n  if (typeof message === \"object\") {\n    message = `ConvexError ${JSON.stringify(message.errorData, null, 2)}`;\n  }\n  if (type === \"info\") {\n    const match = message.match(/^\\[.*?\\] /);\n    if (match === null) {\n      logger.error(\n        `[CONVEX ${prefix}(${udfPath})] Could not parse console.log`,\n      );\n      return;\n    }\n    const level = message.slice(1, match[0].length - 2);\n    const args = message.slice(match[0].length);\n\n    logger.log(`%c[CONVEX ${prefix}(${udfPath})] [${level}]`, INFO_COLOR, args);\n  } else {\n    logger.error(`[CONVEX ${prefix}(${udfPath})] ${message}`);\n  }\n}\n\nexport function logFatalError(logger: Logger, message: string): Error {\n  const errorMessage = `[CONVEX FATAL ERROR] ${message}`;\n  logger.error(errorMessage);\n  return new Error(errorMessage);\n}\n\nexport function createHybridErrorStacktrace(\n  source: UdfType,\n  udfPath: string,\n  result: FunctionFailure,\n): string {\n  const prefix = prefix_for_source(source);\n  return `[CONVEX ${prefix}(${udfPath})] ${result.errorMessage}\\n  Called by client`;\n}\n\nexport function forwardData(\n  result: FunctionFailure,\n  error: ConvexError<string>,\n) {\n  (error as ConvexError<any>).data = result.errorData;\n  return error;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAMA,MAAM,aAAa;AAInB,SAAS,kBAAkB,MAAA,EAAiB;IAC1C,OAAQ,QAAQ;QACd,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;IACX;AACF;AAgBO,MAAM,cAAgC;IAY3C,mBACE,IAAA,EACY;QACZ,IAAI,KAAK,KAAK,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,EAAE,SAAA,CAAU,GAAG,EAAE;QACnD,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,IAAI,IAAA,CAAK,eAAA,CAAgB,EAAE,CAAA,KAAM,KAAA,GAAW;gBAC1C;YACF;YACA,KAAK,KAAK,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,EAAE,SAAA,CAAU,GAAG,EAAE;QACjD;QACA,IAAA,CAAK,eAAA,CAAgB,EAAE,CAAA,GAAI;QAC3B,OAAO,MAAM;YACX,OAAO,IAAA,CAAK,eAAA,CAAgB,EAAE,CAAA;QAChC;IACF;IAEA,aAA2B;QAA3B,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;YAAc,KAAd,QAAA,SAAA,CAAA,KAAc;;QACZ,IAAI,IAAA,CAAK,QAAA,EAAU;YACjB,KAAA,MAAW,QAAQ,OAAO,MAAA,CAAO,IAAA,CAAK,eAAe,EAAG;gBACtD,KAAK,SAAS,GAA2B,GAAI,IAA/B,AAAG,aAAA,GAAA,IAAI,KAAK,EAAE,WAAA,CAAY,CAAC,MAAO,IAAI;YACtD;QACF;IACF;IAEA,MAAoB;QAApB,IAAA,IAAA,OAAA,UAAA,QAAA,AAAO,OAAP,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;iBAAA,QAAA,SAAA,CAAA,KAAO;;QACL,KAAA,MAAW,QAAQ,OAAO,MAAA,CAAO,IAAA,CAAK,eAAe,EAAG;YACtD,KAAK,QAAQ,GAAG,IAAI;QACtB;IACF;IAEA,OAAqB;QAArB,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;YAAQ,KAAR,QAAA,SAAA,CAAA,KAAQ;;QACN,KAAA,MAAW,QAAQ,OAAO,MAAA,CAAO,IAAA,CAAK,eAAe,EAAG;YACtD,KAAK,QAAQ,GAAG,IAAI;QACtB;IACF;IAEA,QAAsB;QAAtB,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;YAAS,KAAT,QAAA,SAAA,CAAA,KAAS;;QACP,KAAA,MAAW,QAAQ,OAAO,MAAA,CAAO,IAAA,CAAK,eAAe,EAAG;YACtD,KAAK,SAAS,GAAG,IAAI;QACvB;IACF;IA7CA,YAAY,OAAA,CAA+B;QAN3C,cAAA,IAAA,EAAQ;QAIR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,eAAA,GAAkB,CAAC;QACxB,IAAA,CAAK,QAAA,GAAW,QAAQ,OAAA;IAC1B;AA2CF;AAEO,SAAS,yBAAyB,OAAA,EAE9B;IACT,MAAM,SAAS,IAAI,cAAc,OAAO;IACxC,OAAO,kBAAA,CAAmB,SAAC;;YAAU,SAAS;;QAC5C,OAAQ,OAAO;YACb,KAAK;gBACH,QAAQ,KAAA,CAAM,GAAG,IAAI;gBACrB;YACF,KAAK;gBACH,QAAQ,GAAA,CAAI,GAAG,IAAI;gBACnB;YACF,KAAK;gBACH,QAAQ,IAAA,CAAK,GAAG,IAAI;gBACpB;YACF,KAAK;gBACH,QAAQ,KAAA,CAAM,GAAG,IAAI;gBACrB;YACF;gBAAS;oBACP;oBACA,QAAQ,GAAA,CAAI,GAAG,IAAI;gBACrB;QACF;IACF,CAAC;IACD,OAAO;AACT;AAEO,SAAS,sBAAsB,OAAA,EAAuC;IAC3E,OAAO,IAAI,cAAc,OAAO;AAClC;AAEO,SAAS,eACd,MAAA,EACA,IAAA,EACA,MAAA,EACA,OAAA,EACA,OAAA,EACA;IACA,MAAM,SAAS,kBAAkB,MAAM;IAEvC,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU,eAAyD,OAA1C,KAAK,SAAA,CAAU,QAAQ,SAAA,EAAW,MAAM,CAAC,CAAC;IACrE;IACA,IAAI,SAAS,QAAQ;QACnB,MAAM,QAAQ,QAAQ,KAAA,CAAM,WAAW;QACvC,IAAI,UAAU,MAAM;YAClB,OAAO,KAAA,CACL,kBAAW,MAAM,EAAA,KAAW,OAAP,OAAO,EAAA;YAE9B;QACF;QACA,MAAM,QAAQ,QAAQ,KAAA,CAAM,GAAG,KAAA,CAAM,CAAC,CAAA,CAAE,MAAA,GAAS,CAAC;QAClD,MAAM,OAAO,QAAQ,KAAA,CAAM,KAAA,CAAM,CAAC,CAAA,CAAE,MAAM;QAE1C,OAAO,GAAA,CAAI,oBAAa,MAAM,EAAA,YAAI,OAAO,EAAA,QAAY,OAAL,KAAK,EAAA,MAAK,YAAY,IAAI;IAC5E,OAAO;QACL,OAAO,KAAA,CAAM,kBAAW,MAAM,EAAA,YAAI,OAAO,EAAA,OAAa,CAAE,MAAT,OAAO;IACxD;AACF;AAEO,SAAS,cAAc,MAAA,EAAgB,OAAA,EAAwB;IACpE,MAAM,eAAe,wBAA+B,OAAP,OAAO;IACpD,OAAO,KAAA,CAAM,YAAY;IACzB,OAAO,IAAI,MAAM,YAAY;AAC/B;AAEO,SAAS,4BACd,MAAA,EACA,OAAA,EACA,MAAA,EACQ;IACR,MAAM,SAAS,kBAAkB,MAAM;IACvC,OAAO,kBAAW,MAAM,EAAA,YAAI,OAAO,EAAA,OAAyB,OAAnB,OAAO,YAAY,EAAA;AAC9D;AAEO,SAAS,YACd,MAAA,EACA,KAAA,EACA;IACC,MAA2B,IAAA,GAAO,OAAO,SAAA;IAC1C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/udf_path_utils.ts"], "sourcesContent": ["import { convexTo<PERSON>son, Value } from \"../../values/index.js\";\n\nexport function canonicalizeUdfPath(udfPath: string): string {\n  const pieces = udfPath.split(\":\");\n  let moduleName: string;\n  let functionName: string;\n  if (pieces.length === 1) {\n    moduleName = pieces[0];\n    functionName = \"default\";\n  } else {\n    moduleName = pieces.slice(0, pieces.length - 1).join(\":\");\n    functionName = pieces[pieces.length - 1];\n  }\n  if (moduleName.endsWith(\".js\")) {\n    moduleName = moduleName.slice(0, -3);\n  }\n  return `${moduleName}:${functionName}`;\n}\n\n/**\n * A string representing the name and arguments of a query.\n *\n * This is used by the {@link BaseConvexClient}.\n *\n * @public\n */\nexport type QueryToken = string;\n\nexport function serializePathAndArgs(\n  udfPath: string,\n  args: Record<string, Value>,\n): QueryToken {\n  return JSON.stringify({\n    udfPath: canonicalizeUdfPath(udfPath),\n    args: convexT<PERSON><PERSON><PERSON>(args),\n  });\n}\n"], "names": [], "mappings": ";;;;;;AAAA,SAAS,oBAA2B;;;;AAE7B,SAAS,oBAAoB,OAAA,EAAyB;IAC3D,MAAM,SAAS,QAAQ,KAAA,CAAM,GAAG;IAChC,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,MAAA,KAAW,GAAG;QACvB,aAAa,MAAA,CAAO,CAAC,CAAA;QACrB,eAAe;IACjB,OAAO;QACL,aAAa,OAAO,KAAA,CAAM,GAAG,OAAO,MAAA,GAAS,CAAC,EAAE,IAAA,CAAK,GAAG;QACxD,eAAe,MAAA,CAAO,OAAO,MAAA,GAAS,CAAC,CAAA;IACzC;IACA,IAAI,WAAW,QAAA,CAAS,KAAK,GAAG;QAC9B,aAAa,WAAW,KAAA,CAAM,GAAG,CAAA,CAAE;IACrC;IACA,OAAO,UAAG,UAAU,EAAA,KAAgB,OAAZ,YAAY;AACtC;AAWO,SAAS,qBACd,OAAA,EACA,IAAA,EACY;IACZ,OAAO,KAAK,SAAA,CAAU;QACpB,SAAS,oBAAoB,OAAO;QACpC,UAAM,kPAAA,EAAa,IAAI;IACzB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1508, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/local_state.ts"], "sourcesContent": ["import { convexToJson, Value } from \"../../values/index.js\";\nimport {\n  AddQuery,\n  RemoveQuery,\n  QueryId,\n  QuerySetModification,\n  QuerySetVersion,\n  IdentityVersion,\n  Authenticate,\n  QueryJournal,\n  Transition,\n  AdminAuthentication,\n  UserIdentityAttributes,\n} from \"./protocol.js\";\nimport {\n  canonicalizeUdfPath,\n  QueryToken,\n  serializePathAndArgs,\n} from \"./udf_path_utils.js\";\n\ntype LocalQuery = {\n  id: QueryId;\n  canonicalizedUdfPath: string;\n  args: Record<string, Value>;\n  numSubscribers: number;\n  journal?: QueryJournal;\n  componentPath?: string;\n};\n\nexport class LocalSyncState {\n  private nextQueryId: QueryId;\n  private querySetVersion: QuerySetVersion;\n  private readonly querySet: Map<QueryToken, LocalQuery>;\n  private readonly queryIdToToken: Map<QueryId, QueryToken>;\n  private identityVersion: IdentityVersion;\n  private auth?: {\n    tokenType: \"Admin\" | \"User\";\n    value: string;\n    impersonating?: UserIdentityAttributes;\n  };\n  private readonly outstandingQueriesOlderThanRestart: Set<QueryId>;\n  private outstandingAuthOlderThanRestart: boolean;\n  private paused: boolean;\n  private pendingQuerySetModifications: Map<QueryId, AddQuery | RemoveQuery>;\n\n  constructor() {\n    this.nextQueryId = 0;\n    this.querySetVersion = 0;\n    this.identityVersion = 0;\n    this.querySet = new Map();\n    this.queryIdToToken = new Map();\n    this.outstandingQueriesOlderThanRestart = new Set();\n    this.outstandingAuthOlderThanRestart = false;\n    this.paused = false;\n    this.pendingQuerySetModifications = new Map();\n  }\n\n  hasSyncedPastLastReconnect(): boolean {\n    return (\n      this.outstandingQueriesOlderThanRestart.size === 0 &&\n      !this.outstandingAuthOlderThanRestart\n    );\n  }\n\n  markAuthCompletion() {\n    this.outstandingAuthOlderThanRestart = false;\n  }\n\n  subscribe(\n    udfPath: string,\n    args: Record<string, Value>,\n    journal?: QueryJournal,\n    componentPath?: string,\n  ): {\n    queryToken: QueryToken;\n    modification: QuerySetModification | null;\n    unsubscribe: () => QuerySetModification | null;\n  } {\n    const canonicalizedUdfPath = canonicalizeUdfPath(udfPath);\n    const queryToken = serializePathAndArgs(canonicalizedUdfPath, args);\n\n    const existingEntry = this.querySet.get(queryToken);\n\n    if (existingEntry !== undefined) {\n      existingEntry.numSubscribers += 1;\n      return {\n        queryToken,\n        modification: null,\n        unsubscribe: () => this.removeSubscriber(queryToken),\n      };\n    } else {\n      const queryId = this.nextQueryId++;\n      const query: LocalQuery = {\n        id: queryId,\n        canonicalizedUdfPath,\n        args,\n        numSubscribers: 1,\n        journal,\n        componentPath,\n      };\n      this.querySet.set(queryToken, query);\n      this.queryIdToToken.set(queryId, queryToken);\n\n      const baseVersion = this.querySetVersion;\n      const newVersion = this.querySetVersion + 1;\n\n      const add: AddQuery = {\n        type: \"Add\",\n        queryId,\n        udfPath: canonicalizedUdfPath,\n        args: [convexToJson(args)],\n        journal,\n        componentPath,\n      };\n\n      if (this.paused) {\n        this.pendingQuerySetModifications.set(queryId, add);\n      } else {\n        this.querySetVersion = newVersion;\n      }\n\n      const modification: QuerySetModification = {\n        type: \"ModifyQuerySet\",\n        baseVersion,\n        newVersion,\n        modifications: [add],\n      };\n      return {\n        queryToken,\n        modification,\n        unsubscribe: () => this.removeSubscriber(queryToken),\n      };\n    }\n  }\n\n  transition(transition: Transition) {\n    for (const modification of transition.modifications) {\n      switch (modification.type) {\n        case \"QueryUpdated\":\n        case \"QueryFailed\": {\n          this.outstandingQueriesOlderThanRestart.delete(modification.queryId);\n          const journal = modification.journal;\n          if (journal !== undefined) {\n            const queryToken = this.queryIdToToken.get(modification.queryId);\n            // We may have already unsubscribed to this query by the time the server\n            // sends us the journal. If so, just ignore it.\n            if (queryToken !== undefined) {\n              this.querySet.get(queryToken)!.journal = journal;\n            }\n          }\n\n          break;\n        }\n        case \"QueryRemoved\": {\n          this.outstandingQueriesOlderThanRestart.delete(modification.queryId);\n          break;\n        }\n        default: {\n          // Enforce that the switch-case is exhaustive.\n          modification satisfies never;\n          throw new Error(`Invalid modification ${(modification as any).type}`);\n        }\n      }\n    }\n  }\n\n  queryId(udfPath: string, args: Record<string, Value>): QueryId | null {\n    const canonicalizedUdfPath = canonicalizeUdfPath(udfPath);\n    const queryToken = serializePathAndArgs(canonicalizedUdfPath, args);\n    const existingEntry = this.querySet.get(queryToken);\n    if (existingEntry !== undefined) {\n      return existingEntry.id;\n    }\n    return null;\n  }\n\n  isCurrentOrNewerAuthVersion(version: IdentityVersion): boolean {\n    return version >= this.identityVersion;\n  }\n\n  setAuth(value: string): Authenticate {\n    this.auth = {\n      tokenType: \"User\",\n      value: value,\n    };\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      baseVersion: baseVersion,\n      ...this.auth,\n    };\n  }\n\n  setAdminAuth(\n    value: string,\n    actingAs?: UserIdentityAttributes,\n  ): AdminAuthentication {\n    const auth: typeof this.auth & {\n      tokenType: \"Admin\";\n    } = {\n      tokenType: \"Admin\",\n      value,\n      impersonating: actingAs,\n    };\n    this.auth = auth;\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      baseVersion: baseVersion,\n      ...auth,\n    };\n  }\n\n  clearAuth(): Authenticate {\n    this.auth = undefined;\n    this.markAuthCompletion();\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      tokenType: \"None\",\n      baseVersion: baseVersion,\n    };\n  }\n\n  hasAuth(): boolean {\n    return !!this.auth;\n  }\n\n  isNewAuth(value: string): boolean {\n    return this.auth?.value !== value;\n  }\n\n  queryPath(queryId: QueryId): string | null {\n    const pathAndArgs = this.queryIdToToken.get(queryId);\n    if (pathAndArgs) {\n      return this.querySet.get(pathAndArgs)!.canonicalizedUdfPath;\n    }\n    return null;\n  }\n\n  queryArgs(queryId: QueryId): Record<string, Value> | null {\n    const pathAndArgs = this.queryIdToToken.get(queryId);\n    if (pathAndArgs) {\n      return this.querySet.get(pathAndArgs)!.args;\n    }\n    return null;\n  }\n\n  queryToken(queryId: QueryId): string | null {\n    return this.queryIdToToken.get(queryId) ?? null;\n  }\n\n  queryJournal(queryToken: QueryToken): QueryJournal | undefined {\n    return this.querySet.get(queryToken)?.journal;\n  }\n\n  restart(\n    oldRemoteQueryResults: Set<QueryId>,\n  ): [QuerySetModification, Authenticate?] {\n    // Restart works whether we are paused or unpaused.\n    // The `this.pendingQuerySetModifications` is not used\n    // when restarting as the AddQuery and RemoveQuery are computed\n    // from scratch, based on the old remote query results, here.\n    this.unpause();\n\n    this.outstandingQueriesOlderThanRestart.clear();\n    const modifications = [];\n    for (const localQuery of this.querySet.values()) {\n      const add: AddQuery = {\n        type: \"Add\",\n        queryId: localQuery.id,\n        udfPath: localQuery.canonicalizedUdfPath,\n        args: [convexToJson(localQuery.args)],\n        journal: localQuery.journal,\n        componentPath: localQuery.componentPath,\n      };\n      modifications.push(add);\n\n      if (!oldRemoteQueryResults.has(localQuery.id)) {\n        this.outstandingQueriesOlderThanRestart.add(localQuery.id);\n      }\n    }\n    this.querySetVersion = 1;\n    const querySet: QuerySetModification = {\n      type: \"ModifyQuerySet\",\n      baseVersion: 0,\n      newVersion: 1,\n      modifications,\n    };\n    // If there's no auth, no need to send an update as the server will also start with an unknown identity.\n    if (!this.auth) {\n      this.identityVersion = 0;\n      return [querySet, undefined];\n    }\n    this.outstandingAuthOlderThanRestart = true;\n    const authenticate: Authenticate = {\n      type: \"Authenticate\",\n      baseVersion: 0,\n      ...this.auth,\n    };\n    this.identityVersion = 1;\n    return [querySet, authenticate];\n  }\n\n  pause() {\n    this.paused = true;\n  }\n\n  resume(): [QuerySetModification?, Authenticate?] {\n    const querySet: QuerySetModification | undefined =\n      this.pendingQuerySetModifications.size > 0\n        ? {\n            type: \"ModifyQuerySet\",\n            baseVersion: this.querySetVersion,\n            newVersion: ++this.querySetVersion,\n            modifications: Array.from(\n              this.pendingQuerySetModifications.values(),\n            ),\n          }\n        : undefined;\n    const authenticate: Authenticate | undefined =\n      this.auth !== undefined\n        ? {\n            type: \"Authenticate\",\n            baseVersion: this.identityVersion++,\n            ...this.auth,\n          }\n        : undefined;\n\n    this.unpause();\n\n    return [querySet, authenticate];\n  }\n\n  private unpause() {\n    this.paused = false;\n    this.pendingQuerySetModifications.clear();\n  }\n\n  private removeSubscriber(\n    queryToken: QueryToken,\n  ): QuerySetModification | null {\n    const localQuery = this.querySet.get(queryToken)!;\n\n    if (localQuery.numSubscribers > 1) {\n      localQuery.numSubscribers -= 1;\n      return null;\n    } else {\n      this.querySet.delete(queryToken);\n      this.queryIdToToken.delete(localQuery.id);\n      this.outstandingQueriesOlderThanRestart.delete(localQuery.id);\n      const baseVersion = this.querySetVersion;\n      const newVersion = this.querySetVersion + 1;\n      const remove: RemoveQuery = {\n        type: \"Remove\",\n        queryId: localQuery.id,\n      };\n      if (this.paused) {\n        if (this.pendingQuerySetModifications.has(localQuery.id)) {\n          this.pendingQuerySetModifications.delete(localQuery.id);\n        } else {\n          this.pendingQuerySetModifications.set(localQuery.id, remove);\n        }\n      } else {\n        this.querySetVersion = newVersion;\n      }\n      return {\n        type: \"ModifyQuerySet\",\n        baseVersion,\n        newVersion,\n        modifications: [remove],\n      };\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,oBAA2B;AAcpC;;;;;;;;;;;;AAeO,MAAM,eAAe;IA4B1B,6BAAsC;QACpC,OACE,IAAA,CAAK,kCAAA,CAAmC,IAAA,KAAS,KACjD,CAAC,IAAA,CAAK,+BAAA;IAEV;IAEA,qBAAqB;QACnB,IAAA,CAAK,+BAAA,GAAkC;IACzC;IAEA,UACE,OAAA,EACA,IAAA,EACA,OAAA,EACA,aAAA,EAKA;QACA,MAAM,2BAAuB,2QAAA,EAAoB,OAAO;QACxD,MAAM,iBAAa,4QAAA,EAAqB,sBAAsB,IAAI;QAElE,MAAM,gBAAgB,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,UAAU;QAElD,IAAI,kBAAkB,KAAA,GAAW;YAC/B,cAAc,cAAA,IAAkB;YAChC,OAAO;gBACL;gBACA,cAAc;gBACd,aAAa,IAAM,IAAA,CAAK,gBAAA,CAAiB,UAAU;YACrD;QACF,OAAO;YACL,MAAM,UAAU,IAAA,CAAK,WAAA;YACrB,MAAM,QAAoB;gBACxB,IAAI;gBACJ;gBACA;gBACA,gBAAgB;gBAChB;gBACA;YACF;YACA,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,YAAY,KAAK;YACnC,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,SAAS,UAAU;YAE3C,MAAM,cAAc,IAAA,CAAK,eAAA;YACzB,MAAM,aAAa,IAAA,CAAK,eAAA,GAAkB;YAE1C,MAAM,MAAgB;gBACpB,MAAM;gBACN;gBACA,SAAS;gBACT,MAAM;wBAAC,kPAAA,EAAa,IAAI,CAAC;iBAAA;gBACzB;gBACA;YACF;YAEA,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACf,IAAA,CAAK,4BAAA,CAA6B,GAAA,CAAI,SAAS,GAAG;YACpD,OAAO;gBACL,IAAA,CAAK,eAAA,GAAkB;YACzB;YAEA,MAAM,eAAqC;gBACzC,MAAM;gBACN;gBACA;gBACA,eAAe;oBAAC,GAAG;iBAAA;YACrB;YACA,OAAO;gBACL;gBACA;gBACA,aAAa,IAAM,IAAA,CAAK,gBAAA,CAAiB,UAAU;YACrD;QACF;IACF;IAEA,WAAW,UAAA,EAAwB;QACjC,KAAA,MAAW,gBAAgB,WAAW,aAAA,CAAe;YACnD,OAAQ,aAAa,IAAA,EAAM;gBACzB,KAAK;gBACL,KAAK;oBAAe;wBAClB,IAAA,CAAK,kCAAA,CAAmC,MAAA,CAAO,aAAa,OAAO;wBACnE,MAAM,UAAU,aAAa,OAAA;wBAC7B,IAAI,YAAY,KAAA,GAAW;4BACzB,MAAM,aAAa,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,aAAa,OAAO;4BAG/D,IAAI,eAAe,KAAA,GAAW;gCAC5B,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,UAAU,EAAG,OAAA,GAAU;4BAC3C;wBACF;wBAEA;oBACF;gBACA,KAAK;oBAAgB;wBACnB,IAAA,CAAK,kCAAA,CAAmC,MAAA,CAAO,aAAa,OAAO;wBACnE;oBACF;gBACA;oBAAS;wBAEP;wBACA,MAAM,IAAI,MAAM,wBAAkD,CAAE,MAA3B,aAAqB,IAAI;oBACpE;YACF;QACF;IACF;IAEA,QAAQ,OAAA,EAAiB,IAAA,EAA6C;QACpE,MAAM,2BAAuB,2QAAA,EAAoB,OAAO;QACxD,MAAM,iBAAa,4QAAA,EAAqB,sBAAsB,IAAI;QAClE,MAAM,gBAAgB,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,UAAU;QAClD,IAAI,kBAAkB,KAAA,GAAW;YAC/B,OAAO,cAAc,EAAA;QACvB;QACA,OAAO;IACT;IAEA,4BAA4B,OAAA,EAAmC;QAC7D,OAAO,WAAW,IAAA,CAAK,eAAA;IACzB;IAEA,QAAQ,KAAA,EAA6B;QACnC,IAAA,CAAK,IAAA,GAAO;YACV,WAAW;YACX;QACF;QACA,MAAM,cAAc,IAAA,CAAK,eAAA;QACzB,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;YAChB,IAAA,CAAK,eAAA,GAAkB,cAAc;QACvC;QACA,OAAO;YACL,MAAM;YACN;YACA,GAAG,IAAA,CAAK,IAAA;QACV;IACF;IAEA,aACE,KAAA,EACA,QAAA,EACqB;QACrB,MAAM,OAEF;YACF,WAAW;YACX;YACA,eAAe;QACjB;QACA,IAAA,CAAK,IAAA,GAAO;QACZ,MAAM,cAAc,IAAA,CAAK,eAAA;QACzB,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;YAChB,IAAA,CAAK,eAAA,GAAkB,cAAc;QACvC;QACA,OAAO;YACL,MAAM;YACN;YACA,GAAG,IAAA;QACL;IACF;IAEA,YAA0B;QACxB,IAAA,CAAK,IAAA,GAAO,KAAA;QACZ,IAAA,CAAK,kBAAA,CAAmB;QACxB,MAAM,cAAc,IAAA,CAAK,eAAA;QACzB,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;YAChB,IAAA,CAAK,eAAA,GAAkB,cAAc;QACvC;QACA,OAAO;YACL,MAAM;YACN,WAAW;YACX;QACF;IACF;IAEA,UAAmB;QACjB,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA;IAChB;IAEA,UAAU,KAAA,EAAwB;;QAChC,0BAAO,CAAK,IAAA,+CAAL,WAAW,KAAA,MAAU;IAC9B;IAEA,UAAU,OAAA,EAAiC;QACzC,MAAM,cAAc,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,OAAO;QACnD,IAAI,aAAa;YACf,OAAO,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,WAAW,EAAG,oBAAA;QACzC;QACA,OAAO;IACT;IAEA,UAAU,OAAA,EAAgD;QACxD,MAAM,cAAc,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,OAAO;QACnD,IAAI,aAAa;YACf,OAAO,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,WAAW,EAAG,IAAA;QACzC;QACA,OAAO;IACT;IAEA,WAAW,OAAA,EAAiC;;QAC1C,mCAAO,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,OAAO,gFAAK;IAC7C;IAEA,aAAa,UAAA,EAAkD;;QAC7D,iCAAO,CAAK,QAAA,CAAS,GAAA,CAAI,UAAU,wDAA5B,mBAA+B,OAAA;IACxC;IAEA,QACE,qBAAA,EACuC;QAKvC,IAAA,CAAK,OAAA,CAAQ;QAEb,IAAA,CAAK,kCAAA,CAAmC,KAAA,CAAM;QAC9C,MAAM,gBAAgB,CAAC,CAAA;QACvB,KAAA,MAAW,cAAc,IAAA,CAAK,QAAA,CAAS,MAAA,CAAO,EAAG;YAC/C,MAAM,MAAgB;gBACpB,MAAM;gBACN,SAAS,WAAW,EAAA;gBACpB,SAAS,WAAW,oBAAA;gBACpB,MAAM;wBAAC,kPAAA,EAAa,WAAW,IAAI,CAAC;iBAAA;gBACpC,SAAS,WAAW,OAAA;gBACpB,eAAe,WAAW,aAAA;YAC5B;YACA,cAAc,IAAA,CAAK,GAAG;YAEtB,IAAI,CAAC,sBAAsB,GAAA,CAAI,WAAW,EAAE,GAAG;gBAC7C,IAAA,CAAK,kCAAA,CAAmC,GAAA,CAAI,WAAW,EAAE;YAC3D;QACF;QACA,IAAA,CAAK,eAAA,GAAkB;QACvB,MAAM,WAAiC;YACrC,MAAM;YACN,aAAa;YACb,YAAY;YACZ;QACF;QAEA,IAAI,CAAC,IAAA,CAAK,IAAA,EAAM;YACd,IAAA,CAAK,eAAA,GAAkB;YACvB,OAAO;gBAAC;gBAAU,KAAA,CAAS;aAAA;QAC7B;QACA,IAAA,CAAK,+BAAA,GAAkC;QACvC,MAAM,eAA6B;YACjC,MAAM;YACN,aAAa;YACb,GAAG,IAAA,CAAK,IAAA;QACV;QACA,IAAA,CAAK,eAAA,GAAkB;QACvB,OAAO;YAAC;YAAU,YAAY;SAAA;IAChC;IAEA,QAAQ;QACN,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,SAAiD;QAC/C,MAAM,WACJ,IAAA,CAAK,4BAAA,CAA6B,IAAA,GAAO,IACrC;YACE,MAAM;YACN,aAAa,IAAA,CAAK,eAAA;YAClB,YAAY,EAAE,IAAA,CAAK,eAAA;YACnB,eAAe,MAAM,IAAA,CACnB,IAAA,CAAK,4BAAA,CAA6B,MAAA,CAAO;QAE7C,IACA,KAAA;QACN,MAAM,eACJ,IAAA,CAAK,IAAA,KAAS,KAAA,IACV;YACE,MAAM;YACN,aAAa,IAAA,CAAK,eAAA;YAClB,GAAG,IAAA,CAAK,IAAA;QACV,IACA,KAAA;QAEN,IAAA,CAAK,OAAA,CAAQ;QAEb,OAAO;YAAC;YAAU,YAAY;SAAA;IAChC;IAEQ,UAAU;QAChB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,4BAAA,CAA6B,KAAA,CAAM;IAC1C;IAEQ,iBACN,UAAA,EAC6B;QAC7B,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,UAAU;QAE/C,IAAI,WAAW,cAAA,GAAiB,GAAG;YACjC,WAAW,cAAA,IAAkB;YAC7B,OAAO;QACT,OAAO;YACL,IAAA,CAAK,QAAA,CAAS,MAAA,CAAO,UAAU;YAC/B,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,WAAW,EAAE;YACxC,IAAA,CAAK,kCAAA,CAAmC,MAAA,CAAO,WAAW,EAAE;YAC5D,MAAM,cAAc,IAAA,CAAK,eAAA;YACzB,MAAM,aAAa,IAAA,CAAK,eAAA,GAAkB;YAC1C,MAAM,SAAsB;gBAC1B,MAAM;gBACN,SAAS,WAAW,EAAA;YACtB;YACA,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACf,IAAI,IAAA,CAAK,4BAAA,CAA6B,GAAA,CAAI,WAAW,EAAE,GAAG;oBACxD,IAAA,CAAK,4BAAA,CAA6B,MAAA,CAAO,WAAW,EAAE;gBACxD,OAAO;oBACL,IAAA,CAAK,4BAAA,CAA6B,GAAA,CAAI,WAAW,EAAA,EAAI,MAAM;gBAC7D;YACF,OAAO;gBACL,IAAA,CAAK,eAAA,GAAkB;YACzB;YACA,OAAO;gBACL,MAAM;gBACN;gBACA;gBACA,eAAe;oBAAC,MAAM;iBAAA;YACxB;QACF;IACF;IAjVA,aAAc;QAfd,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAKR,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,QAAA,GAAW,aAAA,GAAA,IAAI,IAAI;QACxB,IAAA,CAAK,cAAA,GAAiB,aAAA,GAAA,IAAI,IAAI;QAC9B,IAAA,CAAK,kCAAA,GAAqC,aAAA,GAAA,IAAI,IAAI;QAClD,IAAA,CAAK,+BAAA,GAAkC;QACvC,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,4BAAA,GAA+B,aAAA,GAAA,IAAI,IAAI;IAC9C;AAwUF", "debugId": null}}, {"offset": {"line": 1834, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/request_manager.ts"], "sourcesContent": ["import { jsonToConvex } from \"../../values/index.js\";\nimport { logForFunction, Logger } from \"../logging.js\";\nimport { Long } from \"../long.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport {\n  ActionRequest,\n  ActionResponse,\n  ClientMessage,\n  MutationRequest,\n  MutationResponse,\n  RequestId,\n} from \"./protocol.js\";\n\ntype RequestStatus =\n  | {\n      status: \"Requested\" | \"NotSent\";\n      onResult: (result: FunctionResult) => void;\n      requestedAt: Date;\n    }\n  | {\n      status: \"Completed\";\n      result: FunctionResult;\n      onResolve: () => void;\n      ts: Long;\n    };\n\nexport class RequestManager {\n  private inflightRequests: Map<\n    RequestId,\n    {\n      message: MutationRequest | ActionRequest;\n      status: RequestStatus;\n    }\n  >;\n  private requestsOlderThanRestart: Set<RequestId>;\n  private inflightMutationsCount: number = 0;\n  private inflightActionsCount: number = 0;\n  constructor(\n    private readonly logger: Logger,\n    private readonly markConnectionStateDirty: () => void,\n  ) {\n    this.inflightRequests = new Map();\n    this.requestsOlderThanRestart = new Set();\n  }\n\n  request(\n    message: MutationRequest | ActionRequest,\n    sent: boolean,\n  ): Promise<FunctionResult> {\n    const result = new Promise<FunctionResult>((resolve) => {\n      const status = sent ? \"Requested\" : \"NotSent\";\n      this.inflightRequests.set(message.requestId, {\n        message,\n        status: { status, requestedAt: new Date(), onResult: resolve },\n      });\n\n      if (message.type === \"Mutation\") {\n        this.inflightMutationsCount++;\n      } else if (message.type === \"Action\") {\n        this.inflightActionsCount++;\n      }\n    });\n\n    this.markConnectionStateDirty();\n    return result;\n  }\n\n  /**\n   * Update the state after receiving a response.\n   *\n   * @returns A RequestId if the request is complete and its optimistic update\n   * can be dropped, null otherwise.\n   */\n  onResponse(\n    response: MutationResponse | ActionResponse,\n  ): { requestId: RequestId; result: FunctionResult } | null {\n    const requestInfo = this.inflightRequests.get(response.requestId);\n    if (requestInfo === undefined) {\n      // Annoyingly we can occasionally get responses to mutations that we're no\n      // longer tracking. One flow where this happens is:\n      // 1. Client sends mutation 1\n      // 2. Client gets response for mutation 1. The sever says that it was committed at ts=10.\n      // 3. Client is disconnected\n      // 4. Client reconnects and re-issues queries and this mutation.\n      // 5. Server sends transition message to ts=20\n      // 6. Client drops mutation because it's already been observed.\n      // 7. Client receives a second response for mutation 1 but doesn't know about it anymore.\n\n      // The right fix for this is probably to add a reconciliation phase on\n      // reconnection where we receive responses to all the mutations before\n      // the transition message so this flow could never happen (CX-1513).\n\n      // For now though, we can just ignore this message.\n      return null;\n    }\n\n    // Because `.restart()` re-requests completed requests, we may get some\n    // responses for requests that are already in the \"Completed\" state.\n    // We can safely ignore those because we've already notified the UI about\n    // their results.\n    if (requestInfo.status.status === \"Completed\") {\n      return null;\n    }\n\n    const udfType =\n      requestInfo.message.type === \"Mutation\" ? \"mutation\" : \"action\";\n    const udfPath = requestInfo.message.udfPath;\n\n    for (const line of response.logLines) {\n      logForFunction(this.logger, \"info\", udfType, udfPath, line);\n    }\n\n    const status = requestInfo.status;\n    let result: FunctionResult;\n    let onResolve;\n    if (response.success) {\n      result = {\n        success: true,\n        logLines: response.logLines,\n        value: jsonToConvex(response.result),\n      };\n      onResolve = () => status.onResult(result);\n    } else {\n      const errorMessage = response.result as string;\n      const { errorData } = response;\n      logForFunction(this.logger, \"error\", udfType, udfPath, errorMessage);\n      result = {\n        success: false,\n        errorMessage,\n        errorData:\n          errorData !== undefined ? jsonToConvex(errorData) : undefined,\n        logLines: response.logLines,\n      };\n      onResolve = () => status.onResult(result);\n    }\n\n    // We can resolve Mutation failures immediately since they don't have any\n    // side effects. Actions are intentionally decoupled from\n    // queries/mutations here on the sync protocol since they have different\n    // guarantees.\n    if (response.type === \"ActionResponse\" || !response.success) {\n      onResolve();\n      this.inflightRequests.delete(response.requestId);\n      this.requestsOlderThanRestart.delete(response.requestId);\n\n      if (requestInfo.message.type === \"Action\") {\n        this.inflightActionsCount--;\n      } else if (requestInfo.message.type === \"Mutation\") {\n        this.inflightMutationsCount--;\n      }\n\n      this.markConnectionStateDirty();\n      return { requestId: response.requestId, result };\n    }\n\n    // We have to wait to resolve the request promise until after we transition\n    // past this timestamp so clients can read their own writes.\n    requestInfo.status = {\n      status: \"Completed\",\n      result,\n      ts: response.ts,\n      onResolve,\n    };\n\n    return null;\n  }\n\n  // Remove and returns completed requests.\n  removeCompleted(ts: Long): Map<RequestId, FunctionResult> {\n    const completeRequests: Map<RequestId, FunctionResult> = new Map();\n    for (const [requestId, requestInfo] of this.inflightRequests.entries()) {\n      const status = requestInfo.status;\n      if (status.status === \"Completed\" && status.ts.lessThanOrEqual(ts)) {\n        status.onResolve();\n        completeRequests.set(requestId, status.result);\n\n        if (requestInfo.message.type === \"Mutation\") {\n          this.inflightMutationsCount--;\n        } else if (requestInfo.message.type === \"Action\") {\n          this.inflightActionsCount--;\n        }\n\n        this.inflightRequests.delete(requestId);\n        this.requestsOlderThanRestart.delete(requestId);\n      }\n    }\n    if (completeRequests.size > 0) {\n      this.markConnectionStateDirty();\n    }\n    return completeRequests;\n  }\n\n  restart(): ClientMessage[] {\n    // When we reconnect to the backend, re-request all requests that are safe\n    // to be resend.\n\n    this.requestsOlderThanRestart = new Set(this.inflightRequests.keys());\n    const allMessages = [];\n    for (const [requestId, value] of this.inflightRequests) {\n      if (value.status.status === \"NotSent\") {\n        value.status.status = \"Requested\";\n        allMessages.push(value.message);\n        continue;\n      }\n\n      if (value.message.type === \"Mutation\") {\n        // This includes ones that have already been completed because we still\n        // want to tell the backend to transition the client past the completed\n        // timestamp. This is safe since mutations are idempotent.\n        allMessages.push(value.message);\n      } else if (value.message.type === \"Action\") {\n        // Unlike mutations, actions are not idempotent. When we reconnect to the\n        // backend, we don't know if it is safe to resend in-flight actions, so we\n        // cancel them and consider them failed.\n        this.inflightRequests.delete(requestId);\n        this.requestsOlderThanRestart.delete(requestId);\n        this.inflightActionsCount--;\n        if (value.status.status === \"Completed\") {\n          throw new Error(\"Action should never be in 'Completed' state\");\n        }\n        value.status.onResult({\n          success: false,\n          errorMessage: \"Connection lost while action was in flight\",\n          logLines: [],\n        });\n      }\n    }\n    this.markConnectionStateDirty();\n    return allMessages;\n  }\n\n  resume(): ClientMessage[] {\n    const allMessages = [];\n    for (const [, value] of this.inflightRequests) {\n      if (value.status.status === \"NotSent\") {\n        value.status.status = \"Requested\";\n        allMessages.push(value.message);\n        continue;\n      }\n    }\n    return allMessages;\n  }\n\n  /**\n   * @returns true if there are any requests that have been requested but have\n   * not be completed yet.\n   */\n  hasIncompleteRequests(): boolean {\n    for (const requestInfo of this.inflightRequests.values()) {\n      if (requestInfo.status.status === \"Requested\") {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @returns true if there are any inflight requests, including ones that have\n   * completed on the server, but have not been applied.\n   */\n  hasInflightRequests(): boolean {\n    return this.inflightRequests.size > 0;\n  }\n\n  /**\n   * @returns true if there are any inflight requests, that have been hanging around\n   * since prior to the most recent restart.\n   */\n  hasSyncedPastLastReconnect(): boolean {\n    return this.requestsOlderThanRestart.size === 0;\n  }\n\n  timeOfOldestInflightRequest(): Date | null {\n    if (this.inflightRequests.size === 0) {\n      return null;\n    }\n    let oldestInflightRequest = Date.now();\n    for (const request of this.inflightRequests.values()) {\n      if (request.status.status !== \"Completed\") {\n        if (request.status.requestedAt.getTime() < oldestInflightRequest) {\n          oldestInflightRequest = request.status.requestedAt.getTime();\n        }\n      }\n    }\n    return new Date(oldestInflightRequest);\n  }\n\n  /**\n   * @returns The number of mutations currently in flight.\n   */\n  inflightMutations(): number {\n    return this.inflightMutationsCount;\n  }\n\n  /**\n   * @returns The number of actions currently in flight.\n   */\n  inflightActions(): number {\n    return this.inflightActionsCount;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,sBAA8B;;;;;;;;;;;;AAyBhC,MAAM,eAAe;IAmB1B,QACE,OAAA,EACA,IAAA,EACyB;QACzB,MAAM,SAAS,IAAI,QAAwB,CAAC,YAAY;YACtD,MAAM,SAAS,OAAO,cAAc;YACpC,IAAA,CAAK,gBAAA,CAAiB,GAAA,CAAI,QAAQ,SAAA,EAAW;gBAC3C;gBACA,QAAQ;oBAAE;oBAAQ,aAAa,aAAA,GAAA,IAAI,KAAK;oBAAG,UAAU;gBAAQ;YAC/D,CAAC;YAED,IAAI,QAAQ,IAAA,KAAS,YAAY;gBAC/B,IAAA,CAAK,sBAAA;YACP,OAAA,IAAW,QAAQ,IAAA,KAAS,UAAU;gBACpC,IAAA,CAAK,oBAAA;YACP;QACF,CAAC;QAED,IAAA,CAAK,wBAAA,CAAyB;QAC9B,OAAO;IACT;IAAA;;;;;GAAA,GAQA,WACE,QAAA,EACyD;QACzD,MAAM,cAAc,IAAA,CAAK,gBAAA,CAAiB,GAAA,CAAI,SAAS,SAAS;QAChE,IAAI,gBAAgB,KAAA,GAAW;YAgB7B,OAAO;QACT;QAMA,IAAI,YAAY,MAAA,CAAO,MAAA,KAAW,aAAa;YAC7C,OAAO;QACT;QAEA,MAAM,UACJ,YAAY,OAAA,CAAQ,IAAA,KAAS,aAAa,aAAa;QACzD,MAAM,UAAU,YAAY,OAAA,CAAQ,OAAA;QAEpC,KAAA,MAAW,QAAQ,SAAS,QAAA,CAAU;YACpC,IAAA,uPAAA,EAAe,IAAA,CAAK,MAAA,EAAQ,QAAQ,SAAS,SAAS,IAAI;QAC5D;QAEA,MAAM,SAAS,YAAY,MAAA;QAC3B,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS,OAAA,EAAS;YACpB,SAAS;gBACP,SAAS;gBACT,UAAU,SAAS,QAAA;gBACnB,OAAO,sPAAA,EAAa,SAAS,MAAM;YACrC;YACA,YAAY,IAAM,OAAO,QAAA,CAAS,MAAM;QAC1C,OAAO;YACL,MAAM,eAAe,SAAS,MAAA;YAC9B,MAAM,EAAE,SAAA,CAAU,CAAA,GAAI;YACtB,IAAA,uPAAA,EAAe,IAAA,CAAK,MAAA,EAAQ,SAAS,SAAS,SAAS,YAAY;YACnE,SAAS;gBACP,SAAS;gBACT;gBACA,WACE,cAAc,KAAA,QAAY,kPAAA,EAAa,SAAS,IAAI,KAAA;gBACtD,UAAU,SAAS,QAAA;YACrB;YACA,YAAY,IAAM,OAAO,QAAA,CAAS,MAAM;QAC1C;QAMA,IAAI,SAAS,IAAA,KAAS,oBAAoB,CAAC,SAAS,OAAA,EAAS;YAC3D,UAAU;YACV,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO,SAAS,SAAS;YAC/C,IAAA,CAAK,wBAAA,CAAyB,MAAA,CAAO,SAAS,SAAS;YAEvD,IAAI,YAAY,OAAA,CAAQ,IAAA,KAAS,UAAU;gBACzC,IAAA,CAAK,oBAAA;YACP,OAAA,IAAW,YAAY,OAAA,CAAQ,IAAA,KAAS,YAAY;gBAClD,IAAA,CAAK,sBAAA;YACP;YAEA,IAAA,CAAK,wBAAA,CAAyB;YAC9B,OAAO;gBAAE,WAAW,SAAS,SAAA;gBAAW;YAAO;QACjD;QAIA,YAAY,MAAA,GAAS;YACnB,QAAQ;YACR;YACA,IAAI,SAAS,EAAA;YACb;QACF;QAEA,OAAO;IACT;IAAA,yCAAA;IAGA,gBAAgB,EAAA,EAA0C;QACxD,MAAM,mBAAmD,aAAA,GAAA,IAAI,IAAI;QACjE,KAAA,MAAW,CAAC,WAAW,WAAW,CAAA,IAAK,IAAA,CAAK,gBAAA,CAAiB,OAAA,CAAQ,EAAG;YACtE,MAAM,SAAS,YAAY,MAAA;YAC3B,IAAI,OAAO,MAAA,KAAW,eAAe,OAAO,EAAA,CAAG,eAAA,CAAgB,EAAE,GAAG;gBAClE,OAAO,SAAA,CAAU;gBACjB,iBAAiB,GAAA,CAAI,WAAW,OAAO,MAAM;gBAE7C,IAAI,YAAY,OAAA,CAAQ,IAAA,KAAS,YAAY;oBAC3C,IAAA,CAAK,sBAAA;gBACP,OAAA,IAAW,YAAY,OAAA,CAAQ,IAAA,KAAS,UAAU;oBAChD,IAAA,CAAK,oBAAA;gBACP;gBAEA,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO,SAAS;gBACtC,IAAA,CAAK,wBAAA,CAAyB,MAAA,CAAO,SAAS;YAChD;QACF;QACA,IAAI,iBAAiB,IAAA,GAAO,GAAG;YAC7B,IAAA,CAAK,wBAAA,CAAyB;QAChC;QACA,OAAO;IACT;IAEA,UAA2B;QAIzB,IAAA,CAAK,wBAAA,GAA2B,IAAI,IAAI,IAAA,CAAK,gBAAA,CAAiB,IAAA,CAAK,CAAC;QACpE,MAAM,cAAc,CAAC,CAAA;QACrB,KAAA,MAAW,CAAC,WAAW,KAAK,CAAA,IAAK,IAAA,CAAK,gBAAA,CAAkB;YACtD,IAAI,MAAM,MAAA,CAAO,MAAA,KAAW,WAAW;gBACrC,MAAM,MAAA,CAAO,MAAA,GAAS;gBACtB,YAAY,IAAA,CAAK,MAAM,OAAO;gBAC9B;YACF;YAEA,IAAI,MAAM,OAAA,CAAQ,IAAA,KAAS,YAAY;gBAIrC,YAAY,IAAA,CAAK,MAAM,OAAO;YAChC,OAAA,IAAW,MAAM,OAAA,CAAQ,IAAA,KAAS,UAAU;gBAI1C,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO,SAAS;gBACtC,IAAA,CAAK,wBAAA,CAAyB,MAAA,CAAO,SAAS;gBAC9C,IAAA,CAAK,oBAAA;gBACL,IAAI,MAAM,MAAA,CAAO,MAAA,KAAW,aAAa;oBACvC,MAAM,IAAI,MAAM,6CAA6C;gBAC/D;gBACA,MAAM,MAAA,CAAO,QAAA,CAAS;oBACpB,SAAS;oBACT,cAAc;oBACd,UAAU,CAAC,CAAA;gBACb,CAAC;YACH;QACF;QACA,IAAA,CAAK,wBAAA,CAAyB;QAC9B,OAAO;IACT;IAEA,SAA0B;QACxB,MAAM,cAAc,CAAC,CAAA;QACrB,KAAA,MAAW,CAAC,EAAE,KAAK,CAAA,IAAK,IAAA,CAAK,gBAAA,CAAkB;YAC7C,IAAI,MAAM,MAAA,CAAO,MAAA,KAAW,WAAW;gBACrC,MAAM,MAAA,CAAO,MAAA,GAAS;gBACtB,YAAY,IAAA,CAAK,MAAM,OAAO;gBAC9B;YACF;QACF;QACA,OAAO;IACT;IAAA;;;GAAA,GAMA,wBAAiC;QAC/B,KAAA,MAAW,eAAe,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO,EAAG;YACxD,IAAI,YAAY,MAAA,CAAO,MAAA,KAAW,aAAa;gBAC7C,OAAO;YACT;QACF;QACA,OAAO;IACT;IAAA;;;GAAA,GAMA,sBAA+B;QAC7B,OAAO,IAAA,CAAK,gBAAA,CAAiB,IAAA,GAAO;IACtC;IAAA;;;GAAA,GAMA,6BAAsC;QACpC,OAAO,IAAA,CAAK,wBAAA,CAAyB,IAAA,KAAS;IAChD;IAEA,8BAA2C;QACzC,IAAI,IAAA,CAAK,gBAAA,CAAiB,IAAA,KAAS,GAAG;YACpC,OAAO;QACT;QACA,IAAI,wBAAwB,KAAK,GAAA,CAAI;QACrC,KAAA,MAAW,WAAW,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO,EAAG;YACpD,IAAI,QAAQ,MAAA,CAAO,MAAA,KAAW,aAAa;gBACzC,IAAI,QAAQ,MAAA,CAAO,WAAA,CAAY,OAAA,CAAQ,IAAI,uBAAuB;oBAChE,wBAAwB,QAAQ,MAAA,CAAO,WAAA,CAAY,OAAA,CAAQ;gBAC7D;YACF;QACF;QACA,OAAO,IAAI,KAAK,qBAAqB;IACvC;IAAA;;GAAA,GAKA,oBAA4B;QAC1B,OAAO,IAAA,CAAK,sBAAA;IACd;IAAA;;GAAA,GAKA,kBAA0B;QACxB,OAAO,IAAA,CAAK,oBAAA;IACd;IAtQA,YACmB,MAAA,EACA,wBAAA,CACjB;QAFiB,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,wBAAA,GAAA;QAZnB,cAAA,IAAA,EAAQ;QAOR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ,0BAAiC;QACzC,cAAA,IAAA,EAAQ,wBAA+B;QAKrC,IAAA,CAAK,gBAAA,GAAmB,aAAA,GAAA,IAAI,IAAI;QAChC,IAAA,CAAK,wBAAA,GAA2B,aAAA,GAAA,IAAI,IAAI;IAC1C;AAiQF", "debugId": null}}, {"offset": {"line": 2059, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/server/functionName.ts"], "sourcesContent": ["/**\n * A symbol for accessing the name of a {@link FunctionReference} at runtime.\n */\nexport const functionName = Symbol.for(\"functionName\");\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,eAAe,OAAO,GAAA,CAAI,cAAc", "debugId": null}}, {"offset": {"line": 2069, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/server/components/paths.ts"], "sourcesContent": ["import { functionName } from \"../functionName.js\";\n\nexport const toReferencePath = Symbol.for(\"toReferencePath\");\n\n// Multiple instances of the same Symbol.for() are equal at runtime but not\n// at type-time, so `[toReferencePath]` properties aren't used in types.\n// Use this function to set the property invisibly.\nexport function setReferencePath<T>(obj: T, value: string) {\n  (obj as any)[toReferencePath] = value;\n}\n\nexport function extractReferencePath(reference: any): string | null {\n  return reference[toReferencePath] ?? null;\n}\n\nexport function isFunctionHandle(s: string): boolean {\n  return s.startsWith(\"function://\");\n}\n\nexport function getFunctionAddress(functionReference: any) {\n  // The `run*` syscalls expect either a UDF path at \"name\" or a serialized\n  // reference at \"reference\". Dispatch on `functionReference` to coerce\n  // it to one or the other.\n  let functionAddress;\n\n  // Legacy path for passing in UDF paths directly as function references.\n  if (typeof functionReference === \"string\") {\n    if (isFunctionHandle(functionReference)) {\n      functionAddress = { functionHandle: functionReference };\n    } else {\n      functionAddress = { name: functionReference };\n    }\n  }\n  // Path for passing in a `FunctionReference`, either from `api` or directly\n  // created from a UDF path with `makeFunctionReference`.\n  else if (functionReference[functionName]) {\n    functionAddress = { name: functionReference[functionName] };\n  }\n  // Reference to a component's function derived from `app` or `component`.\n  else {\n    const referencePath = extractReferencePath(functionReference);\n    if (!referencePath) {\n      throw new Error(`${functionReference} is not a functionReference`);\n    }\n    functionAddress = { reference: referencePath };\n  }\n  return functionAddress;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,SAAS,oBAAoB;;;AAEtB,MAAM,kBAAkB,OAAO,GAAA,CAAI,iBAAiB;AAKpD,SAAS,iBAAoB,GAAA,EAAQ,KAAA,EAAe;IACxD,GAAA,CAAY,eAAe,CAAA,GAAI;AAClC;AAEO,SAAS,qBAAqB,SAAA,EAA+B;;IAClE,8CAAO,CAAU,eAAe,CAAA,sDAAzB,6BAA8B;AACvC;AAEO,SAAS,iBAAiB,CAAA,EAAoB;IACnD,OAAO,EAAE,UAAA,CAAW,aAAa;AACnC;AAEO,SAAS,mBAAmB,iBAAA,EAAwB;IAIzD,IAAI;IAGJ,IAAI,OAAO,sBAAsB,UAAU;QACzC,IAAI,iBAAiB,iBAAiB,GAAG;YACvC,kBAAkB;gBAAE,gBAAgB;YAAkB;QACxD,OAAO;YACL,kBAAkB;gBAAE,MAAM;YAAkB;QAC9C;IACF,OAAA,IAGS,iBAAA,CAAkB,yPAAY,CAAA,EAAG;QACxC,kBAAkB;YAAE,MAAM,iBAAA,CAAkB,yPAAY,CAAA;QAAE;IAC5D,OAEK;QACH,MAAM,gBAAgB,qBAAqB,iBAAiB;QAC5D,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM,GAAoB,OAAjB,iBAAiB,EAAA,4BAA6B;QACnE;QACA,kBAAkB;YAAE,WAAW;QAAc;IAC/C;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/server/api.ts"], "sourcesContent": ["import {\n  EmptyObject,\n  DefaultFunctionArgs,\n  FunctionVisibility,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"./registration.js\";\nimport { Expand, UnionToIntersection } from \"../type_utils.js\";\nimport { PaginationOptions, PaginationResult } from \"./pagination.js\";\nimport { functionName } from \"./functionName.js\";\nimport { getFunctionAddress } from \"./components/paths.js\";\n\n/**\n * The type of a Convex function.\n *\n * @public\n */\nexport type FunctionType = \"query\" | \"mutation\" | \"action\";\n\n/**\n * A reference to a registered Convex function.\n *\n * You can create a {@link FunctionReference} using the generated `api` utility:\n * ```js\n * import { api } from \"../convex/_generated/api\";\n *\n * const reference = api.myModule.myFunction;\n * ```\n *\n * If you aren't using code generation, you can create references using\n * {@link anyApi}:\n * ```js\n * import { anyApi } from \"convex/server\";\n *\n * const reference = anyApi.myModule.myFunction;\n * ```\n *\n * Function references can be used to invoke functions from the client. For\n * example, in React you can pass references to the {@link react.useQuery} hook:\n * ```js\n * const result = useQuery(api.myModule.myFunction);\n * ```\n *\n * @typeParam Type - The type of the function (\"query\", \"mutation\", or \"action\").\n * @typeParam Visibility - The visibility of the function (\"public\" or \"internal\").\n * @typeParam Args - The arguments to this function. This is an object mapping\n * argument names to their types.\n * @typeParam ReturnType - The return type of this function.\n * @public\n */\nexport type FunctionReference<\n  Type extends FunctionType,\n  Visibility extends FunctionVisibility = \"public\",\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n  ComponentPath = string | undefined,\n> = {\n  _type: Type;\n  _visibility: Visibility;\n  _args: Args;\n  _returnType: ReturnType;\n  _componentPath: ComponentPath;\n};\n\n/**\n * Get the name of a function from a {@link FunctionReference}.\n *\n * The name is a string like \"myDir/myModule:myFunction\". If the exported name\n * of the function is `\"default\"`, the function name is omitted\n * (e.g. \"myDir/myModule\").\n *\n * @param functionReference - A {@link FunctionReference} to get the name of.\n * @returns A string of the function's name.\n *\n * @public\n */\nexport function getFunctionName(\n  functionReference: AnyFunctionReference,\n): string {\n  const address = getFunctionAddress(functionReference);\n\n  if (address.name === undefined) {\n    if (address.functionHandle !== undefined) {\n      throw new Error(\n        `Expected function reference like \"api.file.func\" or \"internal.file.func\", but received function handle ${address.functionHandle}`,\n      );\n    } else if (address.reference !== undefined) {\n      throw new Error(\n        `Expected function reference in the current component like \"api.file.func\" or \"internal.file.func\", but received reference ${address.reference}`,\n      );\n    }\n    throw new Error(\n      `Expected function reference like \"api.file.func\" or \"internal.file.func\", but received ${JSON.stringify(address)}`,\n    );\n  }\n  // Both a legacy thing and also a convenience for interactive use:\n  // the types won't check but a string is always allowed at runtime.\n  if (typeof functionReference === \"string\") return functionReference;\n\n  // Two different runtime values for FunctionReference implement this\n  // interface: api objects returned from `createApi()` and standalone\n  // function reference objects returned from makeFunctionReference.\n  const name = (functionReference as any)[functionName];\n  if (!name) {\n    throw new Error(`${functionReference as any} is not a functionReference`);\n  }\n  return name;\n}\n\n/**\n * FunctionReferences generally come from generated code, but in custom clients\n * it may be useful to be able to build one manually.\n *\n * Real function references are empty objects at runtime, but the same interface\n * can be implemented with an object for tests and clients which don't use\n * code generation.\n *\n * @param name - The identifier of the function. E.g. `path/to/file:functionName`\n * @public\n */\nexport function makeFunctionReference<\n  type extends FunctionType,\n  args extends DefaultFunctionArgs = any,\n  ret = any,\n>(name: string): FunctionReference<type, \"public\", args, ret> {\n  return { [functionName]: name } as unknown as FunctionReference<\n    type,\n    \"public\",\n    args,\n    ret\n  >;\n}\n\n/**\n * Create a runtime API object that implements {@link AnyApi}.\n *\n * This allows accessing any path regardless of what directories, modules,\n * or functions are defined.\n *\n * @param pathParts - The path to the current node in the API.\n * @returns An {@link AnyApi}\n * @public\n */\nfunction createApi(pathParts: string[] = []): AnyApi {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createApi(newParts);\n      } else if (prop === functionName) {\n        if (pathParts.length < 2) {\n          const found = [\"api\", ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`api.moduleName.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        const path = pathParts.slice(0, -1).join(\"/\");\n        const exportName = pathParts[pathParts.length - 1];\n        if (exportName === \"default\") {\n          return path;\n        } else {\n          return path + \":\" + exportName;\n        }\n      } else if (prop === Symbol.toStringTag) {\n        return \"FunctionReference\";\n      } else {\n        return undefined;\n      }\n    },\n  };\n\n  return new Proxy({}, handler);\n}\n\n/**\n * Given an export from a module, convert it to a {@link FunctionReference}\n * if it is a Convex function.\n */\nexport type FunctionReferenceFromExport<Export> =\n  Export extends RegisteredQuery<\n    infer Visibility,\n    infer Args,\n    infer ReturnValue\n  >\n    ? FunctionReference<\n        \"query\",\n        Visibility,\n        Args,\n        ConvertReturnType<ReturnValue>\n      >\n    : Export extends RegisteredMutation<\n          infer Visibility,\n          infer Args,\n          infer ReturnValue\n        >\n      ? FunctionReference<\n          \"mutation\",\n          Visibility,\n          Args,\n          ConvertReturnType<ReturnValue>\n        >\n      : Export extends RegisteredAction<\n            infer Visibility,\n            infer Args,\n            infer ReturnValue\n          >\n        ? FunctionReference<\n            \"action\",\n            Visibility,\n            Args,\n            ConvertReturnType<ReturnValue>\n          >\n        : never;\n\n/**\n * Given a module, convert all the Convex functions into\n * {@link FunctionReference}s and remove the other exports.\n *\n * BE CAREFUL WHEN EDITING THIS!\n *\n * This is written carefully to preserve jumping to function definitions using\n * cmd+click. If you edit it, please test that cmd+click still works.\n */\ntype FunctionReferencesInModule<Module extends Record<string, any>> = {\n  -readonly [ExportName in keyof Module as Module[ExportName][\"isConvexFunction\"] extends true\n    ? ExportName\n    : never]: FunctionReferenceFromExport<Module[ExportName]>;\n};\n\n/**\n * Given a path to a module and it's type, generate an API type for this module.\n *\n * This is a nested object according to the module's path.\n */\ntype ApiForModule<\n  ModulePath extends string,\n  Module extends object,\n> = ModulePath extends `${infer First}/${infer Second}`\n  ? {\n      [_ in First]: ApiForModule<Second, Module>;\n    }\n  : { [_ in ModulePath]: FunctionReferencesInModule<Module> };\n\n/**\n * Given the types of all modules in the `convex/` directory, construct the type\n * of `api`.\n *\n * `api` is a utility for constructing {@link FunctionReference}s.\n *\n * @typeParam AllModules - A type mapping module paths (like `\"dir/myModule\"`) to\n * the types of the modules.\n * @public\n */\nexport type ApiFromModules<AllModules extends Record<string, object>> =\n  FilterApi<\n    ApiFromModulesAllowEmptyNodes<AllModules>,\n    FunctionReference<any, any, any, any>\n  >;\n\ntype ApiFromModulesAllowEmptyNodes<AllModules extends Record<string, object>> =\n  ExpandModulesAndDirs<\n    UnionToIntersection<\n      {\n        [ModulePath in keyof AllModules]: ApiForModule<\n          ModulePath & string,\n          AllModules[ModulePath]\n        >;\n      }[keyof AllModules]\n    >\n  >;\n\n/**\n * @public\n *\n * Filter a Convex deployment api object for functions which meet criteria,\n * for example all public queries.\n */\nexport type FilterApi<API, Predicate> = Expand<{\n  [mod in keyof API as API[mod] extends Predicate\n    ? mod\n    : API[mod] extends FunctionReference<any, any, any, any>\n      ? never\n      : FilterApi<API[mod], Predicate> extends Record<string, never>\n        ? never\n        : mod]: API[mod] extends Predicate\n    ? API[mod]\n    : FilterApi<API[mod], Predicate>;\n}>;\n\n/**\n * Given an api of type API and a FunctionReference subtype, return an api object\n * containing only the function references that match.\n *\n * ```ts\n * const q = filterApi<typeof api, FunctionReference<\"query\">>(api)\n * ```\n *\n * @public\n */\nexport function filterApi<API, Predicate>(api: API): FilterApi<API, Predicate> {\n  return api as any;\n}\n\n// These just* API filter helpers require no type parameters so are useable from JavaScript.\n/** @public */\nexport function justInternal<API>(\n  api: API,\n): FilterApi<API, FunctionReference<any, \"internal\", any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justPublic<API>(\n  api: API,\n): FilterApi<API, FunctionReference<any, \"public\", any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justQueries<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"query\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justMutations<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"mutation\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justActions<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"action\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justPaginatedQueries<API>(\n  api: API,\n): FilterApi<\n  API,\n  FunctionReference<\n    \"query\",\n    any,\n    { paginationOpts: PaginationOptions },\n    PaginationResult<any>\n  >\n> {\n  return api as any;\n}\n\n/** @public */\nexport function justSchedulable<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"mutation\" | \"action\", any, any, any>> {\n  return api as any;\n}\n\n/**\n * Like {@link Expand}, this simplifies how TypeScript displays object types.\n * The differences are:\n * 1. This version is recursive.\n * 2. This stops recursing when it hits a {@link FunctionReference}.\n */\ntype ExpandModulesAndDirs<ObjectType> = ObjectType extends AnyFunctionReference\n  ? ObjectType\n  : {\n      [Key in keyof ObjectType]: ExpandModulesAndDirs<ObjectType[Key]>;\n    };\n\n/**\n * A {@link FunctionReference} of any type and any visibility with any\n * arguments and any return type.\n *\n * @public\n */\nexport type AnyFunctionReference = FunctionReference<any, any>;\n\ntype AnyModuleDirOrFunc = {\n  [key: string]: AnyModuleDirOrFunc;\n} & AnyFunctionReference;\n\n/**\n * The type that Convex api objects extend. If you were writing an api from\n * scratch it should extend this type.\n *\n * @public\n */\nexport type AnyApi = Record<string, Record<string, AnyModuleDirOrFunc>>;\n\n/**\n * Recursive partial API, useful for defining a subset of an API when mocking\n * or building custom api objects.\n *\n * @public\n */\nexport type PartialApi<API> = {\n  [mod in keyof API]?: API[mod] extends FunctionReference<any, any, any, any>\n    ? API[mod]\n    : PartialApi<API[mod]>;\n};\n\n/**\n * A utility for constructing {@link FunctionReference}s in projects that\n * are not using code generation.\n *\n * You can create a reference to a function like:\n * ```js\n * const reference = anyApi.myModule.myFunction;\n * ```\n *\n * This supports accessing any path regardless of what directories and modules\n * are in your project. All function references are typed as\n * {@link AnyFunctionReference}.\n *\n *\n * If you're using code generation, use `api` from `convex/_generated/api`\n * instead. It will be more type-safe and produce better auto-complete\n * in your editor.\n *\n * @public\n */\nexport const anyApi: AnyApi = createApi() as any;\n\n/**\n * Given a {@link FunctionReference}, get the return type of the function.\n *\n * This is represented as an object mapping argument names to values.\n * @public\n */\nexport type FunctionArgs<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_args\"];\n\n/**\n * A tuple type of the (maybe optional) arguments to `FuncRef`.\n *\n * This type is used to make methods involving arguments type safe while allowing\n * skipping the arguments for functions that don't require arguments.\n *\n * @public\n */\nexport type OptionalRestArgs<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_args\"] extends EmptyObject\n    ? [args?: EmptyObject]\n    : [args: FuncRef[\"_args\"]];\n\n/**\n * A tuple type of the (maybe optional) arguments to `FuncRef`, followed by an options\n * object of type `Options`.\n *\n * This type is used to make methods like `useQuery` type-safe while allowing\n * 1. Skipping arguments for functions that don't require arguments.\n * 2. Skipping the options object.\n * @public\n */\nexport type ArgsAndOptions<\n  FuncRef extends AnyFunctionReference,\n  Options,\n> = FuncRef[\"_args\"] extends EmptyObject\n  ? [args?: EmptyObject, options?: Options]\n  : [args: FuncRef[\"_args\"], options?: Options];\n\n/**\n * Given a {@link FunctionReference}, get the return type of the function.\n *\n * @public\n */\nexport type FunctionReturnType<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_returnType\"];\n\ntype UndefinedToNull<T> = T extends void ? null : T;\n\ntype NullToUndefinedOrNull<T> = T extends null ? T | undefined | void : T;\n\n/**\n * Convert the return type of a function to it's client-facing format.\n *\n * This means:\n * - Converting `undefined` and `void` to `null`\n * - Removing all `Promise` wrappers\n */\nexport type ConvertReturnType<T> = UndefinedToNull<Awaited<T>>;\n\nexport type ValidatorTypeToReturnType<T> =\n  | Promise<NullToUndefinedOrNull<T>>\n  | NullToUndefinedOrNull<T>;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAS,oBAAoB;AAC7B,SAAS,0BAA0B;;;;AAkE5B,SAAS,gBACd,iBAAA,EACQ;IACR,MAAM,cAAU,sQAAA,EAAmB,iBAAiB;IAEpD,IAAI,QAAQ,IAAA,KAAS,KAAA,GAAW;QAC9B,IAAI,QAAQ,cAAA,KAAmB,KAAA,GAAW;YACxC,MAAM,IAAI,MACR,0GAAgI,OAAtB,QAAQ,cAAc;QAEpI,OAAA,IAAW,QAAQ,SAAA,KAAc,KAAA,GAAW;YAC1C,MAAM,IAAI,MACR,6HAA8I,OAAjB,QAAQ,SAAS;QAElJ;QACA,MAAM,IAAI,MACR,0FAAiH,OAAvB,KAAK,SAAA,CAAU,OAAO,CAAC;IAErH;IAGA,IAAI,OAAO,sBAAsB,SAAU,CAAA,OAAO;IAKlD,MAAM,OAAQ,iBAAA,CAA0B,yPAAY,CAAA;IACpD,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,GAA2B,OAAxB,iBAAwB,EAAA,EAA6B;IAC1E;IACA,OAAO;AACT;AAaO,SAAS,sBAId,IAAA,EAA4D;IAC5D,OAAO;QAAE,CAAC,yPAAY,CAAA,EAAG;IAAK;AAMhC;AAYA,SAAS;oBAAU,iEAAsB,CAAC,CAAA,EAAW;IACnD,MAAM,UAAgC;QACpC,KAAI,CAAA,EAAG,IAAA,EAAuB;YAC5B,IAAI,OAAO,SAAS,UAAU;gBAC5B,MAAM,WAAW,CAAC;uBAAG;oBAAW,IAAI;iBAAA;gBACpC,OAAO,UAAU,QAAQ;YAC3B,OAAA,IAAW,SAAS,yPAAA,EAAc;gBAChC,IAAI,UAAU,MAAA,GAAS,GAAG;oBACxB,MAAM,QAAQ;wBAAC,OAAO;2BAAG,SAAS;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC5C,MAAM,IAAI,MACR,iFAAyF,OAAL,KAAK,EAAA;gBAE7F;gBACA,MAAM,OAAO,UAAU,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,GAAG;gBAC5C,MAAM,aAAa,SAAA,CAAU,UAAU,MAAA,GAAS,CAAC,CAAA;gBACjD,IAAI,eAAe,WAAW;oBAC5B,OAAO;gBACT,OAAO;oBACL,OAAO,OAAO,MAAM;gBACtB;YACF,OAAA,IAAW,SAAS,OAAO,WAAA,EAAa;gBACtC,OAAO;YACT,OAAO;gBACL,OAAO,KAAA;YACT;QACF;IACF;IAEA,OAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AA+HO,SAAS,UAA0B,GAAA,EAAqC;IAC7E,OAAO;AACT;AAIO,SAAS,aACd,GAAA,EAC8D;IAC9D,OAAO;AACT;AAGO,SAAS,WACd,GAAA,EAC4D;IAC5D,OAAO;AACT;AAGO,SAAS,YACd,GAAA,EAC2D;IAC3D,OAAO;AACT;AAGO,SAAS,cACd,GAAA,EAC8D;IAC9D,OAAO;AACT;AAGO,SAAS,YACd,GAAA,EAC4D;IAC5D,OAAO;AACT;AAGO,SAAS,qBACd,GAAA,EASA;IACA,OAAO;AACT;AAGO,SAAS,gBACd,GAAA,EACyE;IACzE,OAAO;AACT;AAkEO,MAAM,SAAiB,UAAU", "debugId": null}}, {"offset": {"line": 2240, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/optimistic_updates_impl.ts"], "sourcesContent": ["import {\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  getFunctionName,\n} from \"../../server/api.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { Value } from \"../../values/index.js\";\nimport { createHybridErrorStacktrace, forwardData } from \"../logging.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport { OptimisticLocalStore } from \"./optimistic_updates.js\";\nimport { RequestId } from \"./protocol.js\";\nimport {\n  canonicalizeUdfPath,\n  QueryToken,\n  serializePathAndArgs,\n} from \"./udf_path_utils.js\";\nimport { ConvexError } from \"../../values/errors.js\";\n\n/**\n * An optimistic update function that has been curried over its arguments.\n */\ntype WrappedOptimisticUpdate = (locaQueryStore: OptimisticLocalStore) => void;\n\n/**\n * The implementation of `OptimisticLocalStore`.\n *\n * This class provides the interface for optimistic updates to modify query results.\n */\nclass OptimisticLocalStoreImpl implements OptimisticLocalStore {\n  // A references of the query results in OptimisticQueryResults\n  private readonly queryResults: QueryResultsMap;\n\n  // All of the queries modified by this class\n  readonly modifiedQueries: QueryToken[];\n\n  constructor(queryResults: QueryResultsMap) {\n    this.queryResults = queryResults;\n    this.modifiedQueries = [];\n  }\n\n  getQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): undefined | FunctionReturnType<Query> {\n    const queryArgs = parseArgs(args[0]);\n    const name = getFunctionName(query);\n    const queryResult = this.queryResults.get(\n      serializePathAndArgs(name, queryArgs),\n    );\n    if (queryResult === undefined) {\n      return undefined;\n    }\n    return OptimisticLocalStoreImpl.queryValue(queryResult.result);\n  }\n\n  getAllQueries<Query extends FunctionReference<\"query\">>(\n    query: Query,\n  ): {\n    args: FunctionArgs<Query>;\n    value: undefined | FunctionReturnType<Query>;\n  }[] {\n    const queriesWithName: {\n      args: FunctionArgs<Query>;\n      value: undefined | FunctionReturnType<Query>;\n    }[] = [];\n    const name = getFunctionName(query);\n    for (const queryResult of this.queryResults.values()) {\n      if (queryResult.udfPath === canonicalizeUdfPath(name)) {\n        queriesWithName.push({\n          args: queryResult.args as FunctionArgs<Query>,\n          value: OptimisticLocalStoreImpl.queryValue(queryResult.result),\n        });\n      }\n    }\n    return queriesWithName;\n  }\n\n  setQuery<QueryReference extends FunctionReference<\"query\">>(\n    queryReference: QueryReference,\n    args: FunctionArgs<QueryReference>,\n    value: undefined | FunctionReturnType<QueryReference>,\n  ): void {\n    const queryArgs = parseArgs(args);\n    const name = getFunctionName(queryReference);\n    const queryToken = serializePathAndArgs(name, queryArgs);\n\n    let result: FunctionResult | undefined;\n    if (value === undefined) {\n      result = undefined;\n    } else {\n      result = {\n        success: true,\n        value,\n        // It's an optimistic update, so there are no function logs to show.\n        logLines: [],\n      };\n    }\n    const query: Query = {\n      udfPath: name,\n      args: queryArgs,\n      result,\n    };\n    this.queryResults.set(queryToken, query);\n    this.modifiedQueries.push(queryToken);\n  }\n\n  private static queryValue(\n    result: FunctionResult | undefined,\n  ): Value | undefined {\n    if (result === undefined) {\n      return undefined;\n    } else if (result.success) {\n      return result.value;\n    } else {\n      // If the query is an error state, just return `undefined` as though\n      // it's loading. Optimistic updates should already handle `undefined` well\n      // and there isn't a need to break the whole update because it tried\n      // to load a single query that errored.\n      return undefined;\n    }\n  }\n}\n\ntype OptimisticUpdateAndId = {\n  update: WrappedOptimisticUpdate;\n  mutationId: RequestId;\n};\n\ntype Query = {\n  // undefined means the query was set to be loading (undefined) in an optimistic update.\n  // Note that we can also have queries not present in the QueryResultMap\n  // at all because they are still loading from the server and have no optimistic update\n  // setting an optimistic value in advance.\n  result: FunctionResult | undefined;\n  udfPath: string;\n  args: Record<string, Value>;\n};\nexport type QueryResultsMap = Map<QueryToken, Query>;\n\ntype ChangedQueries = QueryToken[];\n\n/**\n * A view of all of our query results with optimistic updates applied on top.\n */\nexport class OptimisticQueryResults {\n  private queryResults: QueryResultsMap;\n  private optimisticUpdates: OptimisticUpdateAndId[];\n\n  constructor() {\n    this.queryResults = new Map();\n    this.optimisticUpdates = [];\n  }\n\n  /**\n   * Apply all optimistic updates on top of server query results\n   */\n  ingestQueryResultsFromServer(\n    serverQueryResults: QueryResultsMap,\n    optimisticUpdatesToDrop: Set<RequestId>,\n  ): ChangedQueries {\n    this.optimisticUpdates = this.optimisticUpdates.filter((updateAndId) => {\n      return !optimisticUpdatesToDrop.has(updateAndId.mutationId);\n    });\n\n    const oldQueryResults = this.queryResults;\n    this.queryResults = new Map(serverQueryResults);\n    const localStore = new OptimisticLocalStoreImpl(this.queryResults);\n    for (const updateAndId of this.optimisticUpdates) {\n      updateAndId.update(localStore);\n    }\n\n    // To find the changed queries, just do a shallow comparison\n    // TODO(CX-733): Change this so we avoid unnecessary rerenders\n    const changedQueries: ChangedQueries = [];\n    for (const [queryToken, query] of this.queryResults) {\n      const oldQuery = oldQueryResults.get(queryToken);\n      if (oldQuery === undefined || oldQuery.result !== query.result) {\n        changedQueries.push(queryToken);\n      }\n    }\n\n    return changedQueries;\n  }\n\n  applyOptimisticUpdate(\n    update: WrappedOptimisticUpdate,\n    mutationId: RequestId,\n  ): ChangedQueries {\n    // Apply the update to our store\n    this.optimisticUpdates.push({\n      update,\n      mutationId,\n    });\n    const localStore = new OptimisticLocalStoreImpl(this.queryResults);\n    update(localStore);\n\n    // Notify about any query results that changed\n    // TODO(CX-733): Change this so we avoid unnecessary rerenders\n    return localStore.modifiedQueries;\n  }\n\n  /**\n   * @internal\n   */\n  rawQueryResult(queryToken: QueryToken): Query | undefined {\n    return this.queryResults.get(queryToken);\n  }\n\n  queryResult(queryToken: QueryToken): Value | undefined {\n    const query = this.queryResults.get(queryToken);\n    if (query === undefined) {\n      return undefined;\n    }\n    const result = query.result;\n    if (result === undefined) {\n      return undefined;\n    } else if (result.success) {\n      return result.value;\n    } else {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(\n            createHybridErrorStacktrace(\"query\", query.udfPath, result),\n          ),\n        );\n      }\n      throw new Error(\n        createHybridErrorStacktrace(\"query\", query.udfPath, result),\n      );\n    }\n  }\n\n  hasQueryResult(queryToken: QueryToken): boolean {\n    return this.queryResults.get(queryToken) !== undefined;\n  }\n\n  /**\n   * @internal\n   */\n  queryLogs(queryToken: QueryToken): string[] | undefined {\n    const query = this.queryResults.get(queryToken);\n    return query?.result?.logLines;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAOA,SAAS,iBAAiB;AAE1B,SAAS,6BAA6B,mBAAmB;AAIzD;AAKA,SAAS,mBAAmB;;;;;;;;;;;;;;;AAY5B,MAAM,yBAAyD;IAY7D,SACE,KAAA,EAEuC;QAFvC,IAAA,IAAA,OAAA,UAAA,QAAA,AACG,OADH,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;iBAAA,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,gBAAY,+OAAA,EAAU,IAAA,CAAK,CAAC,CAAC;QACnC,MAAM,WAAO,mPAAA,EAAgB,KAAK;QAClC,MAAM,cAAc,IAAA,CAAK,YAAA,CAAa,GAAA,KACpC,4QAAA,EAAqB,MAAM,SAAS;QAEtC,IAAI,gBAAgB,KAAA,GAAW;YAC7B,OAAO,KAAA;QACT;QACA,OAAO,yBAAyB,UAAA,CAAW,YAAY,MAAM;IAC/D;IAEA,cACE,KAAA,EAIE;QACF,MAAM,kBAGA,CAAC,CAAA;QACP,MAAM,WAAO,mPAAA,EAAgB,KAAK;QAClC,KAAA,MAAW,eAAe,IAAA,CAAK,YAAA,CAAa,MAAA,CAAO,EAAG;YACpD,IAAI,YAAY,OAAA,KAAY,+QAAA,EAAoB,IAAI,GAAG;gBACrD,gBAAgB,IAAA,CAAK;oBACnB,MAAM,YAAY,IAAA;oBAClB,OAAO,yBAAyB,UAAA,CAAW,YAAY,MAAM;gBAC/D,CAAC;YACH;QACF;QACA,OAAO;IACT;IAEA,SACE,cAAA,EACA,IAAA,EACA,KAAA,EACM;QACN,MAAM,gBAAY,+OAAA,EAAU,IAAI;QAChC,MAAM,OAAO,uPAAA,EAAgB,cAAc;QAC3C,MAAM,aAAa,gRAAA,EAAqB,MAAM,SAAS;QAEvD,IAAI;QACJ,IAAI,UAAU,KAAA,GAAW;YACvB,SAAS,KAAA;QACX,OAAO;YACL,SAAS;gBACP,SAAS;gBACT;gBAAA,oEAAA;gBAEA,UAAU,CAAC,CAAA;YACb;QACF;QACA,MAAM,QAAe;YACnB,SAAS;YACT,MAAM;YACN;QACF;QACA,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,YAAY,KAAK;QACvC,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,UAAU;IACtC;IAEA,OAAe,WACb,MAAA,EACmB;QACnB,IAAI,WAAW,KAAA,GAAW;YACxB,OAAO,KAAA;QACT,OAAA,IAAW,OAAO,OAAA,EAAS;YACzB,OAAO,OAAO,KAAA;QAChB,OAAO;YAKL,OAAO,KAAA;QACT;IACF;IArFA,YAAY,YAAA,CAA+B;QAL3C,8DAAA;QAAA,cAAA,IAAA,EAAiB;QAGjB,4CAAA;QAAA,cAAA,IAAA,EAAS;QAGP,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,eAAA,GAAkB,CAAC,CAAA;IAC1B;AAmFF;AAuBO,MAAM,uBAAuB;IAOlC;;GAAA,GAKA,6BACE,kBAAA,EACA,uBAAA,EACgB;QAChB,IAAA,CAAK,iBAAA,GAAoB,IAAA,CAAK,iBAAA,CAAkB,MAAA,CAAO,CAAC,gBAAgB;YACtE,OAAO,CAAC,wBAAwB,GAAA,CAAI,YAAY,UAAU;QAC5D,CAAC;QAED,MAAM,kBAAkB,IAAA,CAAK,YAAA;QAC7B,IAAA,CAAK,YAAA,GAAe,IAAI,IAAI,kBAAkB;QAC9C,MAAM,aAAa,IAAI,yBAAyB,IAAA,CAAK,YAAY;QACjE,KAAA,MAAW,eAAe,IAAA,CAAK,iBAAA,CAAmB;YAChD,YAAY,MAAA,CAAO,UAAU;QAC/B;QAIA,MAAM,iBAAiC,CAAC,CAAA;QACxC,KAAA,MAAW,CAAC,YAAY,KAAK,CAAA,IAAK,IAAA,CAAK,YAAA,CAAc;YACnD,MAAM,WAAW,gBAAgB,GAAA,CAAI,UAAU;YAC/C,IAAI,aAAa,KAAA,KAAa,SAAS,MAAA,KAAW,MAAM,MAAA,EAAQ;gBAC9D,eAAe,IAAA,CAAK,UAAU;YAChC;QACF;QAEA,OAAO;IACT;IAEA,sBACE,MAAA,EACA,UAAA,EACgB;QAEhB,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK;YAC1B;YACA;QACF,CAAC;QACD,MAAM,aAAa,IAAI,yBAAyB,IAAA,CAAK,YAAY;QACjE,OAAO,UAAU;QAIjB,OAAO,WAAW,eAAA;IACpB;IAAA;;GAAA,GAKA,eAAe,UAAA,EAA2C;QACxD,OAAO,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,UAAU;IACzC;IAEA,YAAY,UAAA,EAA2C;QACrD,MAAM,QAAQ,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,UAAU;QAC9C,IAAI,UAAU,KAAA,GAAW;YACvB,OAAO,KAAA;QACT;QACA,MAAM,SAAS,MAAM,MAAA;QACrB,IAAI,WAAW,KAAA,GAAW;YACxB,OAAO,KAAA;QACT,OAAA,IAAW,OAAO,OAAA,EAAS;YACzB,OAAO,OAAO,KAAA;QAChB,OAAO;YACL,IAAI,OAAO,SAAA,KAAc,KAAA,GAAW;gBAClC,UAAM,oPAAA,EACJ,QACA,IAAI,kPAAA,KACF,oQAAA,EAA4B,SAAS,MAAM,OAAA,EAAS,MAAM;YAGhE;YACA,MAAM,IAAI,UACR,oQAAA,EAA4B,SAAS,MAAM,OAAA,EAAS,MAAM;QAE9D;IACF;IAEA,eAAe,UAAA,EAAiC;QAC9C,OAAO,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,UAAU,MAAM,KAAA;IAC/C;IAAA;;GAAA,GAKA,UAAU,UAAA,EAA8C;;QACtD,MAAM,QAAQ,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,UAAU;QAC9C,4EAAc,MAAA,cAAP,kDAAe,QAAA;IACxB;IA/FA,aAAc;QAHd,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,YAAA,GAAe,aAAA,GAAA,IAAI,IAAI;QAC5B,IAAA,CAAK,iBAAA,GAAoB,CAAC,CAAA;IAC5B;AA6FF", "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/long.ts"], "sourcesContent": ["// Implements an unsigned long.\n// This is a subset of https://github.com/dcodeIO/Long.js,\n// vendored to decrease bundle size.\n// Copyright <PERSON> <<EMAIL>>\n// License: Apache Version 2.0\n/*\n\n                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"[]\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright 2023 Daniel Wirtz <<EMAIL>>\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n*/\n\n// This works... but don't try to compare one to a real Long.js Long!\n// For internal use only.\n// `| 0` assures the runtime that we are using integer arithmetic\nexport class Long {\n  low: number;\n  high: number;\n  __isUnsignedLong__: boolean;\n\n  static isLong(obj: Long) {\n    return (obj && obj.__isUnsignedLong__) === true;\n  }\n\n  constructor(low: number, high: number) {\n    this.low = low | 0;\n    this.high = high | 0;\n    this.__isUnsignedLong__ = true;\n  }\n\n  // prettier-ignore\n  static fromBytesLE(bytes: number[]): Long {\n    return new Long(\n      bytes[0] |\n      bytes[1] << 8 |\n      bytes[2] << 16 |\n      bytes[3] << 24,\n      bytes[4] |\n      bytes[5] << 8 |\n      bytes[6] << 16 |\n      bytes[7] << 24,\n    );\n  }\n\n  // prettier-ignore\n  toBytesLE() {\n    const hi = this.high;\n    const lo = this.low;\n    return [\n      lo & 0xff,\n      lo >>> 8 & 0xff,\n      lo >>> 16 & 0xff,\n      lo >>> 24,\n      hi & 0xff,\n      hi >>> 8 & 0xff,\n      hi >>> 16 & 0xff,\n      hi >>> 24\n    ];\n  }\n\n  static fromNumber(value: number) {\n    if (isNaN(value)) return UZERO;\n    if (value < 0) return UZERO;\n    if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n    return new Long(value % TWO_PWR_32_DBL | 0, (value / TWO_PWR_32_DBL) | 0);\n  }\n\n  toString() {\n    return (\n      BigInt(this.high) * BigInt(TWO_PWR_32_DBL) +\n      BigInt(this.low)\n    ).toString();\n  }\n\n  equals(other: Long) {\n    if (!Long.isLong(other)) other = Long.fromValue(other);\n    if (this.high >>> 31 === 1 && other.high >>> 31 === 1) return false;\n    return this.high === other.high && this.low === other.low;\n  }\n\n  notEquals(other: Long) {\n    return !this.equals(other);\n  }\n\n  comp(other: Long) {\n    if (!Long.isLong(other)) other = Long.fromValue(other);\n    if (this.equals(other)) return 0;\n    return other.high >>> 0 > this.high >>> 0 ||\n      (other.high === this.high && other.low >>> 0 > this.low >>> 0)\n      ? -1\n      : 1;\n  }\n\n  lessThanOrEqual(other: Long) {\n    return this.comp(/* validates */ other) <= 0;\n  }\n\n  static fromValue(val: any) {\n    if (typeof val === \"number\") return Long.fromNumber(val);\n    // Throws for non-objects, converts non-instanceof Long:\n    return new Long(val.low, val.high);\n  }\n}\n\nconst UZERO = new Long(0, 0);\nconst TWO_PWR_16_DBL = 1 << 16;\nconst TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\nconst TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\nconst MAX_UNSIGNED_VALUE = new Long(0xffffffff | 0, 0xffffffff | 0);\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAqNO,MAAM,KAAK;IAKhB,OAAO,OAAO,GAAA,EAAW;QACvB,OAAA,CAAQ,OAAO,IAAI,kBAAA,MAAwB;IAC7C;IAAA,kBAAA;IASA,OAAO,YAAY,KAAA,EAAuB;QACxC,OAAO,IAAI,KACT,KAAA,CAAM,CAAC,CAAA,GACP,KAAA,CAAM,CAAC,CAAA,IAAK,IACZ,KAAA,CAAM,CAAC,CAAA,IAAK,KACZ,KAAA,CAAM,CAAC,CAAA,IAAK,IACZ,KAAA,CAAM,CAAC,CAAA,GACP,KAAA,CAAM,CAAC,CAAA,IAAK,IACZ,KAAA,CAAM,CAAC,CAAA,IAAK,KACZ,KAAA,CAAM,CAAC,CAAA,IAAK;IAEhB;IAAA,kBAAA;IAGA,YAAY;QACV,MAAM,KAAK,IAAA,CAAK,IAAA;QAChB,MAAM,KAAK,IAAA,CAAK,GAAA;QAChB,OAAO;YACL,KAAK;YACL,OAAO,IAAI;YACX,OAAO,KAAK;YACZ,OAAO;YACP,KAAK;YACL,OAAO,IAAI;YACX,OAAO,KAAK;YACZ,OAAO;SACT;IACF;IAEA,OAAO,WAAW,KAAA,EAAe;QAC/B,IAAI,MAAM,KAAK,EAAG,CAAA,OAAO;QACzB,IAAI,QAAQ,EAAG,CAAA,OAAO;QACtB,IAAI,SAAS,eAAgB,CAAA,OAAO;QACpC,OAAO,IAAI,KAAK,QAAQ,iBAAiB,GAAI,QAAQ,iBAAkB,CAAC;IAC1E;IAEA,WAAW;QACT,OAAA,CACE,OAAO,IAAA,CAAK,IAAI,IAAI,OAAO,cAAc,IACzC,OAAO,IAAA,CAAK,GAAG,CAAA,EACf,QAAA,CAAS;IACb;IAEA,OAAO,KAAA,EAAa;QAClB,IAAI,CAAC,KAAK,MAAA,CAAO,KAAK,EAAG,CAAA,QAAQ,KAAK,SAAA,CAAU,KAAK;QACrD,IAAI,IAAA,CAAK,IAAA,KAAS,OAAO,KAAK,MAAM,IAAA,KAAS,OAAO,EAAG,CAAA,OAAO;QAC9D,OAAO,IAAA,CAAK,IAAA,KAAS,MAAM,IAAA,IAAQ,IAAA,CAAK,GAAA,KAAQ,MAAM,GAAA;IACxD;IAEA,UAAU,KAAA,EAAa;QACrB,OAAO,CAAC,IAAA,CAAK,MAAA,CAAO,KAAK;IAC3B;IAEA,KAAK,KAAA,EAAa;QAChB,IAAI,CAAC,KAAK,MAAA,CAAO,KAAK,EAAG,CAAA,QAAQ,KAAK,SAAA,CAAU,KAAK;QACrD,IAAI,IAAA,CAAK,MAAA,CAAO,KAAK,EAAG,CAAA,OAAO;QAC/B,OAAO,MAAM,IAAA,KAAS,IAAI,IAAA,CAAK,IAAA,KAAS,KACrC,MAAM,IAAA,KAAS,IAAA,CAAK,IAAA,IAAQ,MAAM,GAAA,KAAQ,IAAI,IAAA,CAAK,GAAA,KAAQ,IAC1D,CAAA,IACA;IACN;IAEA,gBAAgB,KAAA,EAAa;QAC3B,OAAO,IAAA,CAAK,IAAA,CAAA,aAAA,GAAqB,UAAU;IAC7C;IAEA,OAAO,UAAU,GAAA,EAAU;QACzB,IAAI,OAAO,QAAQ,SAAU,CAAA,OAAO,KAAK,UAAA,CAAW,GAAG;QAEvD,OAAO,IAAI,KAAK,IAAI,GAAA,EAAK,IAAI,IAAI;IACnC;IA7EA,YAAY,GAAA,EAAa,IAAA,CAAc;QARvC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAOE,IAAA,CAAK,GAAA,GAAM,MAAM;QACjB,IAAA,CAAK,IAAA,GAAO,OAAO;QACnB,IAAA,CAAK,kBAAA,GAAqB;IAC5B;AA0EF;AAEA,MAAM,QAAQ,IAAI,KAAK,GAAG,CAAC;AAC3B,MAAM,iBAAiB,KAAK;AAC5B,MAAM,iBAAiB,iBAAiB;AACxC,MAAM,iBAAiB,iBAAiB;AACxC,MAAM,qBAAqB,IAAI,KAAK,aAAa,GAAG,aAAa,CAAC", "debugId": null}}, {"offset": {"line": 2487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/remote_query_set.ts"], "sourcesContent": ["import { jsonToConvex } from \"../../values/index.js\";\nimport { Long } from \"../long.js\";\nimport { logForFunction, Logger } from \"../logging.js\";\nimport { QueryId, StateVersion, Transition } from \"./protocol.js\";\nimport { FunctionResult } from \"./function_result.js\";\n\n/**\n * A represention of the query results we've received on the current WebSocket\n * connection.\n *\n * Queries you won't find here include:\n * - queries which have been requested, but no query transition has been received yet for\n * - queries which are populated only though active optimistic updates, but are not subscribed to\n * - queries which have already been removed by the server (which it shouldn't do unless that's\n *   been requested by the client)\n */\nexport class RemoteQuerySet {\n  private version: StateVersion;\n  private readonly remoteQuerySet: Map<QueryId, FunctionResult>;\n  private readonly queryPath: (queryId: QueryId) => string | null;\n  private readonly logger: Logger;\n\n  constructor(queryPath: (queryId: QueryId) => string | null, logger: Logger) {\n    this.version = { querySet: 0, ts: Long.fromNumber(0), identity: 0 };\n    this.remoteQuerySet = new Map();\n    this.queryPath = queryPath;\n    this.logger = logger;\n  }\n\n  transition(transition: Transition): void {\n    const start = transition.startVersion;\n    if (\n      this.version.querySet !== start.querySet ||\n      this.version.ts.notEquals(start.ts) ||\n      this.version.identity !== start.identity\n    ) {\n      throw new Error(\n        `Invalid start version: ${start.ts.toString()}:${start.querySet}`,\n      );\n    }\n    for (const modification of transition.modifications) {\n      switch (modification.type) {\n        case \"QueryUpdated\": {\n          const queryPath = this.queryPath(modification.queryId);\n          if (queryPath) {\n            for (const line of modification.logLines) {\n              logForFunction(this.logger, \"info\", \"query\", queryPath, line);\n            }\n          }\n          const value = jsonToConvex(modification.value ?? null);\n          this.remoteQuerySet.set(modification.queryId, {\n            success: true,\n            value,\n            logLines: modification.logLines,\n          });\n          break;\n        }\n        case \"QueryFailed\": {\n          const queryPath = this.queryPath(modification.queryId);\n          if (queryPath) {\n            for (const line of modification.logLines) {\n              logForFunction(this.logger, \"info\", \"query\", queryPath, line);\n            }\n          }\n          const { errorData } = modification;\n          this.remoteQuerySet.set(modification.queryId, {\n            success: false,\n            errorMessage: modification.errorMessage,\n            errorData:\n              errorData !== undefined ? jsonToConvex(errorData) : undefined,\n            logLines: modification.logLines,\n          });\n          break;\n        }\n        case \"QueryRemoved\": {\n          this.remoteQuerySet.delete(modification.queryId);\n          break;\n        }\n        default: {\n          // Enforce that the switch-case is exhaustive.\n          modification satisfies never;\n          throw new Error(`Invalid modification ${(modification as any).type}`);\n        }\n      }\n    }\n    this.version = transition.endVersion;\n  }\n\n  remoteQueryResults(): Map<QueryId, FunctionResult> {\n    return this.remoteQuerySet;\n  }\n\n  timestamp(): Long {\n    return this.version.ts;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,oBAAoB;;AAC7B,SAAS,YAAY;AACrB,SAAS,sBAA8B;;;;;;;;;;;;;AAchC,MAAM,eAAe;IAa1B,WAAW,UAAA,EAA8B;QACvC,MAAM,QAAQ,WAAW,YAAA;QACzB,IACE,IAAA,CAAK,OAAA,CAAQ,QAAA,KAAa,MAAM,QAAA,IAChC,IAAA,CAAK,OAAA,CAAQ,EAAA,CAAG,SAAA,CAAU,MAAM,EAAE,KAClC,IAAA,CAAK,OAAA,CAAQ,QAAA,KAAa,MAAM,QAAA,EAChC;YACA,MAAM,IAAI,MACR,0BAAiD,OAAvB,MAAM,EAAA,CAAG,QAAA,CAAS,CAAC,EAAA,KAAkB,aAAR,QAAQ;QAEnE;QACA,KAAA,MAAW,gBAAgB,WAAW,aAAA,CAAe;YACnD,OAAQ,aAAa,IAAA,EAAM;gBACzB,KAAK;oBAAgB;wBACnB,MAAM,YAAY,IAAA,CAAK,SAAA,CAAU,aAAa,OAAO;wBACrD,IAAI,WAAW;4BACb,KAAA,MAAW,QAAQ,aAAa,QAAA,CAAU;gCACxC,IAAA,uPAAA,EAAe,IAAA,CAAK,MAAA,EAAQ,QAAQ,SAAS,WAAW,IAAI;4BAC9D;wBACF;4BAC2B;wBAA3B,MAAM,QAAQ,sPAAA,sCAA0B,KAAA,qEAAS,IAAI;wBACrD,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,aAAa,OAAA,EAAS;4BAC5C,SAAS;4BACT;4BACA,UAAU,aAAa,QAAA;wBACzB,CAAC;wBACD;oBACF;gBACA,KAAK;oBAAe;wBAClB,MAAM,YAAY,IAAA,CAAK,SAAA,CAAU,aAAa,OAAO;wBACrD,IAAI,WAAW;4BACb,KAAA,MAAW,QAAQ,aAAa,QAAA,CAAU;gCACxC,IAAA,uPAAA,EAAe,IAAA,CAAK,MAAA,EAAQ,QAAQ,SAAS,WAAW,IAAI;4BAC9D;wBACF;wBACA,MAAM,EAAE,SAAA,CAAU,CAAA,GAAI;wBACtB,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,aAAa,OAAA,EAAS;4BAC5C,SAAS;4BACT,cAAc,aAAa,YAAA;4BAC3B,WACE,cAAc,KAAA,QAAY,kPAAA,EAAa,SAAS,IAAI,KAAA;4BACtD,UAAU,aAAa,QAAA;wBACzB,CAAC;wBACD;oBACF;gBACA,KAAK;oBAAgB;wBACnB,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,aAAa,OAAO;wBAC/C;oBACF;gBACA;oBAAS;wBAEP;wBACA,MAAM,IAAI,MAAM,wBAAkD,CAAE,MAA3B,aAAqB,IAAI;oBACpE;YACF;QACF;QACA,IAAA,CAAK,OAAA,GAAU,WAAW,UAAA;IAC5B;IAEA,qBAAmD;QACjD,OAAO,IAAA,CAAK,cAAA;IACd;IAEA,YAAkB;QAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,EAAA;IACtB;IAxEA,YAAY,SAAA,EAAgD,MAAA,CAAgB;QAL5E,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QAGf,IAAA,CAAK,OAAA,GAAU;YAAE,UAAU;YAAG,IAAI,0OAAA,CAAK,UAAA,CAAW,CAAC;YAAG,UAAU;QAAE;QAClE,IAAA,CAAK,cAAA,GAAiB,aAAA,GAAA,IAAI,IAAI;QAC9B,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,MAAA,GAAS;IAChB;AAoEF", "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/protocol.ts"], "sourcesContent": ["import type { UserIdentityAttributes } from \"../../server/authentication.js\";\nexport type { UserIdentityAttributes } from \"../../server/authentication.js\";\nimport { JSONValue, Base64 } from \"../../values/index.js\";\nimport { Long } from \"../long.js\";\n\n/**\n * Shared schema\n */\n\nexport function u64ToLong(encoded: EncodedU64): U64 {\n  const integerBytes = Base64.toByteArray(encoded);\n  return Long.fromBytesLE(Array.from(integerBytes));\n}\n\nexport function longToU64(raw: U64): EncodedU64 {\n  const integerBytes = new Uint8Array(raw.toBytesLE());\n  return Base64.fromByteArray(integerBytes);\n}\n\nexport function parseServerMessage(\n  encoded: EncodedServerMessage,\n): ServerMessage {\n  switch (encoded.type) {\n    case \"FatalError\":\n    case \"AuthError\":\n    case \"ActionResponse\":\n    case \"Ping\": {\n      return { ...encoded };\n    }\n    case \"MutationResponse\": {\n      if (encoded.success) {\n        return { ...encoded, ts: u64ToLong(encoded.ts) };\n      } else {\n        return { ...encoded };\n      }\n    }\n    case \"Transition\": {\n      return {\n        ...encoded,\n        startVersion: {\n          ...encoded.startVersion,\n          ts: u64ToLong(encoded.startVersion.ts),\n        },\n        endVersion: {\n          ...encoded.endVersion,\n          ts: u64ToLong(encoded.endVersion.ts),\n        },\n      };\n    }\n    default: {\n      encoded satisfies never;\n    }\n  }\n  return undefined as never;\n}\n\nexport function encodeClientMessage(\n  message: ClientMessage,\n): EncodedClientMessage {\n  switch (message.type) {\n    case \"Authenticate\":\n    case \"ModifyQuerySet\":\n    case \"Mutation\":\n    case \"Action\":\n    case \"Event\": {\n      return { ...message };\n    }\n    case \"Connect\": {\n      if (message.maxObservedTimestamp !== undefined) {\n        return {\n          ...message,\n          maxObservedTimestamp: longToU64(message.maxObservedTimestamp),\n        };\n      } else {\n        return { ...message, maxObservedTimestamp: undefined };\n      }\n    }\n    default: {\n      message satisfies never;\n    }\n  }\n  return undefined as never;\n}\n\ntype U64 = Long;\ntype EncodedU64 = string;\n\n/**\n * Unique nonnegative integer identifying a single query.\n */\nexport type QueryId = number; // nonnegative int\n\nexport type QuerySetVersion = number; // nonnegative int\n\nexport type RequestId = number; // nonnegative int\n\nexport type IdentityVersion = number; // nonnegative int\n\n/**\n * A serialized representation of decisions made during a query's execution.\n *\n * A journal is produced when a query function first executes and is re-used\n * when a query is re-executed.\n *\n * Currently this is used to store pagination end cursors to ensure\n * that pages of paginated queries will always end at the same cursor. This\n * enables gapless, reactive pagination.\n *\n * `null` is used to represent empty journals.\n * @public\n */\nexport type QueryJournal = string | null;\n\n/**\n * Client message schema\n */\n\ntype Connect = {\n  type: \"Connect\";\n  sessionId: string;\n  connectionCount: number;\n  lastCloseReason: string | null;\n  maxObservedTimestamp?: TS;\n};\n\nexport type AddQuery = {\n  type: \"Add\";\n  queryId: QueryId;\n  udfPath: string;\n  args: JSONValue[];\n  journal?: QueryJournal;\n  /**\n   * @internal\n   */\n  componentPath?: string;\n};\n\nexport type RemoveQuery = {\n  type: \"Remove\";\n  queryId: QueryId;\n};\n\nexport type QuerySetModification = {\n  type: \"ModifyQuerySet\";\n  baseVersion: QuerySetVersion;\n  newVersion: QuerySetVersion;\n  modifications: (AddQuery | RemoveQuery)[];\n};\n\nexport type MutationRequest = {\n  type: \"Mutation\";\n  requestId: RequestId;\n  udfPath: string;\n  args: JSONValue[];\n  // Execute the mutation on a specific component.\n  // Only admin auth is allowed to run mutations on non-root components.\n  componentPath?: string;\n};\n\nexport type ActionRequest = {\n  type: \"Action\";\n  requestId: RequestId;\n  udfPath: string;\n  args: JSONValue[];\n  // Execute the action on a specific component.\n  // Only admin auth is allowed to run actions on non-root components.\n  componentPath?: string;\n};\n\nexport type AdminAuthentication = {\n  type: \"Authenticate\";\n  tokenType: \"Admin\";\n  value: string;\n  baseVersion: IdentityVersion;\n  impersonating?: UserIdentityAttributes;\n};\n\nexport type Authenticate =\n  | AdminAuthentication\n  | {\n      type: \"Authenticate\";\n      tokenType: \"User\";\n      value: string;\n      baseVersion: IdentityVersion;\n    }\n  | {\n      type: \"Authenticate\";\n      tokenType: \"None\";\n      baseVersion: IdentityVersion;\n    };\n\nexport type Event = {\n  type: \"Event\";\n  eventType: string;\n  event: any;\n};\nexport type ClientMessage =\n  | Connect\n  | Authenticate\n  | QuerySetModification\n  | MutationRequest\n  | ActionRequest\n  | Event;\n\ntype EncodedConnect = Omit<Connect, \"maxObservedTimestamp\"> & {\n  maxObservedTimestamp?: EncodedTS;\n};\n\ntype EncodedClientMessage =\n  | EncodedConnect\n  | Authenticate\n  | QuerySetModification\n  | MutationRequest\n  | ActionRequest\n  | Event;\n\n/**\n * Server message schema\n */\nexport type TS = U64;\ntype EncodedTS = EncodedU64;\ntype LogLines = string[];\n\nexport type StateVersion = {\n  querySet: QuerySetVersion;\n  ts: TS;\n  identity: IdentityVersion;\n};\ntype EncodedStateVersion = Omit<StateVersion, \"ts\"> & { ts: EncodedTS };\n\ntype StateModification =\n  | {\n      type: \"QueryUpdated\";\n      queryId: QueryId;\n      value: JSONValue;\n      logLines: LogLines;\n      journal: QueryJournal;\n    }\n  | {\n      type: \"QueryFailed\";\n      queryId: QueryId;\n      errorMessage: string;\n      logLines: LogLines;\n      errorData: JSONValue;\n      journal: QueryJournal;\n    }\n  | {\n      type: \"QueryRemoved\";\n      queryId: QueryId;\n    };\n\nexport type Transition = {\n  type: \"Transition\";\n  startVersion: StateVersion;\n  endVersion: StateVersion;\n  modifications: StateModification[];\n};\n\ntype MutationSuccess = {\n  type: \"MutationResponse\";\n  requestId: RequestId;\n  success: true;\n  result: JSONValue;\n  ts: TS;\n  logLines: LogLines;\n};\ntype MutationFailed = {\n  type: \"MutationResponse\";\n  requestId: RequestId;\n  success: false;\n  result: string;\n  logLines: LogLines;\n  errorData?: JSONValue;\n};\nexport type MutationResponse = MutationSuccess | MutationFailed;\ntype ActionSuccess = {\n  type: \"ActionResponse\";\n  requestId: RequestId;\n  success: true;\n  result: JSONValue;\n  logLines: LogLines;\n};\ntype ActionFailed = {\n  type: \"ActionResponse\";\n  requestId: RequestId;\n  success: false;\n  result: string;\n  logLines: LogLines;\n  errorData?: JSONValue;\n};\nexport type ActionResponse = ActionSuccess | ActionFailed;\nexport type AuthError = {\n  type: \"AuthError\";\n  error: string;\n  baseVersion: IdentityVersion;\n  // True if this error is in response to processing a new `Authenticate` message.\n  // Other AuthErrors may occur due to executing a function with expired auth and\n  // should be handled differently.\n  authUpdateAttempted: boolean;\n};\ntype FatalError = {\n  type: \"FatalError\";\n  error: string;\n};\ntype Ping = {\n  type: \"Ping\";\n};\n\nexport type ServerMessage =\n  | Transition\n  | MutationResponse\n  | ActionResponse\n  | FatalError\n  | AuthError\n  | Ping;\n\ntype EncodedTransition = Omit<Transition, \"startVersion\" | \"endVersion\"> & {\n  startVersion: EncodedStateVersion;\n  endVersion: EncodedStateVersion;\n};\ntype EncodedMutationSuccess = Omit<MutationSuccess, \"ts\"> & { ts: EncodedTS };\ntype EncodedMutationResponse = MutationFailed | EncodedMutationSuccess;\n\ntype EncodedServerMessage =\n  | EncodedTransition\n  | EncodedMutationResponse\n  | ActionResponse\n  | FatalError\n  | AuthError\n  | Ping;\n"], "names": [], "mappings": ";;;;;;;;;;AAEA,SAAoB,cAAc;;AAClC,SAAS,YAAY;;;;AAMd,SAAS,UAAU,OAAA,EAA0B;IAClD,MAAM,eAAe,+QAAA,CAAO,WAAA,CAAY,OAAO;IAC/C,OAAO,0OAAA,CAAK,WAAA,CAAY,MAAM,IAAA,CAAK,YAAY,CAAC;AAClD;AAEO,SAAS,UAAU,GAAA,EAAsB;IAC9C,MAAM,eAAe,IAAI,WAAW,IAAI,SAAA,CAAU,CAAC;IACnD,OAAO,+QAAA,CAAO,aAAA,CAAc,YAAY;AAC1C;AAEO,SAAS,mBACd,OAAA,EACe;IACf,OAAQ,QAAQ,IAAA,EAAM;QACpB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;oBAAE,GAAG,OAAA;gBAAQ;YACtB;QACA,KAAK;YAAoB;gBACvB,IAAI,QAAQ,OAAA,EAAS;oBACnB,OAAO;wBAAE,GAAG,OAAA;wBAAS,IAAI,UAAU,QAAQ,EAAE;oBAAE;gBACjD,OAAO;oBACL,OAAO;wBAAE,GAAG,OAAA;oBAAQ;gBACtB;YACF;QACA,KAAK;YAAc;gBACjB,OAAO;oBACL,GAAG,OAAA;oBACH,cAAc;wBACZ,GAAG,QAAQ,YAAA;wBACX,IAAI,UAAU,QAAQ,YAAA,CAAa,EAAE;oBACvC;oBACA,YAAY;wBACV,GAAG,QAAQ,UAAA;wBACX,IAAI,UAAU,QAAQ,UAAA,CAAW,EAAE;oBACrC;gBACF;YACF;QACA;YAAS;gBACP;YACF;IACF;IACA,OAAO,KAAA;AACT;AAEO,SAAS,oBACd,OAAA,EACsB;IACtB,OAAQ,QAAQ,IAAA,EAAM;QACpB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACZ,OAAO;oBAAE,GAAG,OAAA;gBAAQ;YACtB;QACA,KAAK;YAAW;gBACd,IAAI,QAAQ,oBAAA,KAAyB,KAAA,GAAW;oBAC9C,OAAO;wBACL,GAAG,OAAA;wBACH,sBAAsB,UAAU,QAAQ,oBAAoB;oBAC9D;gBACF,OAAO;oBACL,OAAO;wBAAE,GAAG,OAAA;wBAAS,sBAAsB,KAAA;oBAAU;gBACvD;YACF;QACA;YAAS;gBACP;YACF;IACF;IACA,OAAO,KAAA;AACT", "debugId": null}}, {"offset": {"line": 2703, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/web_socket_manager.ts"], "sourcesContent": ["import { Logger } from \"../logging.js\";\nimport {\n  ClientMessage,\n  encodeClientMessage,\n  parseServerMessage,\n  ServerMessage,\n} from \"./protocol.js\";\n\nconst CLOSE_NORMAL = 1000;\nconst CLOSE_GOING_AWAY = 1001;\nconst CLOSE_NO_STATUS = 1005;\n/** Convex-specific close code representing a \"404 Not Found\".\n * The edge Onramp accepts websocket upgrades before confirming that the\n * intended destination exists, so this code is sent once we've discovered that\n * the destination does not exist.\n */\nconst CLOSE_NOT_FOUND = 4040;\n\n/**\n * The various states our WebSocket can be in:\n *\n * - \"disconnected\": We don't have a WebSocket, but plan to create one.\n * - \"connecting\": We have created the WebSocket and are waiting for the\n *   `onOpen` callback.\n * - \"ready\": We have an open WebSocket.\n * - \"stopped\": The WebSocket was closed and a new one can be created via `.restart()`.\n * - \"terminated\": We have closed the WebSocket and will never create a new one.\n *\n *\n * WebSocket State Machine\n * -----------------------\n * initialState: disconnected\n * validTransitions:\n *   disconnected:\n *     new WebSocket() -> connecting\n *     terminate() -> terminated\n *   connecting:\n *     onopen -> ready\n *     close() -> disconnected\n *     terminate() -> terminated\n *   ready:\n *     close() -> disconnected\n *     stop() -> stopped\n *     terminate() -> terminated\n *   stopped:\n *     restart() -> connecting\n *     terminate() -> terminated\n * terminalStates:\n *   terminated\n *\n *\n *\n *                                        ┌────────────────┐\n *                ┌────terminate()────────│  disconnected  │◀─┐\n *                │                       └────────────────┘  │\n *                ▼                            │       ▲      │\n *       ┌────────────────┐           new WebSocket()  │      │\n *    ┌─▶│   terminated   │◀──────┐            │       │      │\n *    │  └────────────────┘       │            │       │      │\n *    │           ▲          terminate()       │    close() close()\n *    │      terminate()          │            │       │      │\n *    │           │               │            ▼       │      │\n *    │  ┌────────────────┐       └───────┌────────────────┐  │\n *    │  │    stopped     │──restart()───▶│   connecting   │  │\n *    │  └────────────────┘               └────────────────┘  │\n *    │           ▲                                │          │\n *    │           │                               onopen      │\n *    │           │                                │          │\n *    │           │                                ▼          │\n * terminate()    │                       ┌────────────────┐  │\n *    │           └────────stop()─────────│     ready      │──┘\n *    │                                   └────────────────┘\n *    │                                            │\n *    │                                            │\n *    └────────────────────────────────────────────┘\n *\n * The `connecting` and `ready` state have a sub-state-machine for pausing.\n */\n\ntype Socket =\n  | { state: \"disconnected\" }\n  | { state: \"connecting\"; ws: WebSocket; paused: \"yes\" | \"no\" }\n  | { state: \"ready\"; ws: WebSocket; paused: \"yes\" | \"no\" | \"uninitialized\" }\n  | { state: \"stopped\" }\n  | { state: \"terminated\" };\n\nexport type ReconnectMetadata = {\n  connectionCount: number;\n  lastCloseReason: string | null;\n};\n\nexport type OnMessageResponse = {\n  hasSyncedPastLastReconnect: boolean;\n};\n\nconst serverDisconnectErrors = {\n  // A known error, e.g. during a restart or push\n  InternalServerError: { timeout: 1000 },\n  // ErrorMetadata::overloaded() messages that we realy should back off\n  SubscriptionsWorkerFullError: { timeout: 3000 },\n  TooManyConcurrentRequests: { timeout: 3000 },\n  CommitterFullError: { timeout: 3000 },\n  AwsTooManyRequestsException: { timeout: 3000 },\n  ExecuteFullError: { timeout: 3000 },\n  SystemTimeoutError: { timeout: 3000 },\n  ExpiredInQueue: { timeout: 3000 },\n  // ErrorMetadata::feature_temporarily_unavailable() that typically indicate a deploy just happened\n  VectorIndexesUnavailable: { timeout: 1000 },\n  SearchIndexesUnavailable: { timeout: 1000 },\n  TableSummariesUnavailable: { timeout: 1000 },\n  // More ErrorMeatadata::overloaded()\n  VectorIndexTooLarge: { timeout: 3000 },\n  SearchIndexTooLarge: { timeout: 3000 },\n  TooManyWritesInTimePeriod: { timeout: 3000 },\n} as const satisfies Record<string, { timeout: number }>;\n\ntype ServerDisconnectError = keyof typeof serverDisconnectErrors | \"Unknown\";\n\nfunction classifyDisconnectError(s?: string): ServerDisconnectError {\n  if (s === undefined) return \"Unknown\";\n  // startsWith so more info could be at the end (although currently there isn't)\n\n  for (const prefix of Object.keys(\n    serverDisconnectErrors,\n  ) as ServerDisconnectError[]) {\n    if (s.startsWith(prefix)) {\n      return prefix;\n    }\n  }\n  return \"Unknown\";\n}\n\n/**\n * A wrapper around a websocket that handles errors, reconnection, and message\n * parsing.\n */\nexport class WebSocketManager {\n  private socket: Socket;\n\n  private connectionCount: number;\n  private _hasEverConnected: boolean = false;\n  private lastCloseReason:\n    | \"InitialConnect\"\n    | \"OnCloseInvoked\"\n    | (string & {}) // a full serverErrorReason (not just the prefix) or a new one\n    | null;\n\n  /** Upon HTTPS/WSS failure, the first jittered backoff duration, in ms. */\n  private readonly defaultInitialBackoff: number;\n\n  /** We backoff exponentially, but we need to cap that--this is the jittered max. */\n  private readonly maxBackoff: number;\n\n  /** How many times have we failed consecutively? */\n  private retries: number;\n\n  /** How long before lack of server response causes us to initiate a reconnect,\n   * in ms */\n  private readonly serverInactivityThreshold: number;\n\n  private reconnectDueToServerInactivityTimeout: ReturnType<\n    typeof setTimeout\n  > | null;\n\n  private readonly uri: string;\n  private readonly onOpen: (reconnectMetadata: ReconnectMetadata) => void;\n  private readonly onResume: () => void;\n  private readonly onMessage: (message: ServerMessage) => OnMessageResponse;\n  private readonly webSocketConstructor: typeof WebSocket;\n  private readonly logger: Logger;\n  private readonly onServerDisconnectError:\n    | ((message: string) => void)\n    | undefined;\n\n  constructor(\n    uri: string,\n    callbacks: {\n      onOpen: (reconnectMetadata: ReconnectMetadata) => void;\n      onResume: () => void;\n      onMessage: (message: ServerMessage) => OnMessageResponse;\n      onServerDisconnectError?: (message: string) => void;\n    },\n    webSocketConstructor: typeof WebSocket,\n    logger: Logger,\n    private readonly markConnectionStateDirty: () => void,\n  ) {\n    this.webSocketConstructor = webSocketConstructor;\n    this.socket = { state: \"disconnected\" };\n    this.connectionCount = 0;\n    this.lastCloseReason = \"InitialConnect\";\n\n    // backoff for unknown errors\n    this.defaultInitialBackoff = 1000;\n    this.maxBackoff = 16000;\n    this.retries = 0;\n\n    // Ping messages (sync protocol Pings, not WebSocket protocol Pings) are\n    // sent every 15s in the absence of other messages. But a single large\n    // Transition or other downstream message can hog the line so this\n    // threshold is set higher to prevent clients from giving up.\n    this.serverInactivityThreshold = 60000;\n    this.reconnectDueToServerInactivityTimeout = null;\n\n    this.uri = uri;\n    this.onOpen = callbacks.onOpen;\n    this.onResume = callbacks.onResume;\n    this.onMessage = callbacks.onMessage;\n    this.onServerDisconnectError = callbacks.onServerDisconnectError;\n    this.logger = logger;\n\n    this.connect();\n  }\n\n  private setSocketState(state: Socket) {\n    this.socket = state;\n    this._logVerbose(\n      `socket state changed: ${this.socket.state}, paused: ${\n        \"paused\" in this.socket ? this.socket.paused : undefined\n      }`,\n    );\n    this.markConnectionStateDirty();\n  }\n\n  private connect() {\n    if (this.socket.state === \"terminated\") {\n      return;\n    }\n    if (\n      this.socket.state !== \"disconnected\" &&\n      this.socket.state !== \"stopped\"\n    ) {\n      throw new Error(\n        \"Didn't start connection from disconnected state: \" + this.socket.state,\n      );\n    }\n\n    const ws = new this.webSocketConstructor(this.uri);\n    this._logVerbose(\"constructed WebSocket\");\n    this.setSocketState({\n      state: \"connecting\",\n      ws,\n      paused: \"no\",\n    });\n\n    // Kick off server inactivity timer before WebSocket connection is established\n    // so we can detect cases where handshake fails.\n    // The `onopen` event only fires after the connection is established:\n    // Source: https://datatracker.ietf.org/doc/html/rfc6455#page-19:~:text=_The%20WebSocket%20Connection%20is%20Established_,-and\n    this.resetServerInactivityTimeout();\n\n    ws.onopen = () => {\n      this.logger.logVerbose(\"begin ws.onopen\");\n      if (this.socket.state !== \"connecting\") {\n        throw new Error(\"onopen called with socket not in connecting state\");\n      }\n      this.setSocketState({\n        state: \"ready\",\n        ws,\n        paused: this.socket.paused === \"yes\" ? \"uninitialized\" : \"no\",\n      });\n      this.resetServerInactivityTimeout();\n      if (this.socket.paused === \"no\") {\n        this._hasEverConnected = true;\n        this.onOpen({\n          connectionCount: this.connectionCount,\n          lastCloseReason: this.lastCloseReason,\n        });\n      }\n\n      if (this.lastCloseReason !== \"InitialConnect\") {\n        this.logger.log(\"WebSocket reconnected\");\n      }\n\n      this.connectionCount += 1;\n      this.lastCloseReason = null;\n    };\n    // NB: The WebSocket API calls `onclose` even if connection fails, so we can route all error paths through `onclose`.\n    ws.onerror = (error) => {\n      const message = (error as ErrorEvent).message;\n      this.logger.log(`WebSocket error: ${message}`);\n    };\n    ws.onmessage = (message) => {\n      this.resetServerInactivityTimeout();\n      const serverMessage = parseServerMessage(JSON.parse(message.data));\n      this._logVerbose(`received ws message with type ${serverMessage.type}`);\n      const response = this.onMessage(serverMessage);\n      if (response.hasSyncedPastLastReconnect) {\n        // Reset backoff to 0 once all outstanding requests are complete.\n        this.retries = 0;\n        this.markConnectionStateDirty();\n      }\n    };\n    ws.onclose = (event) => {\n      this._logVerbose(\"begin ws.onclose\");\n      if (this.lastCloseReason === null) {\n        this.lastCloseReason = event.reason ?? \"OnCloseInvoked\";\n      }\n      if (\n        event.code !== CLOSE_NORMAL &&\n        event.code !== CLOSE_GOING_AWAY && // This commonly gets fired on mobile apps when the app is backgrounded\n        event.code !== CLOSE_NO_STATUS &&\n        event.code !== CLOSE_NOT_FOUND // Note that we want to retry on a 404, as it can be transient during a push.\n      ) {\n        let msg = `WebSocket closed with code ${event.code}`;\n        if (event.reason) {\n          msg += `: ${event.reason}`;\n        }\n        this.logger.log(msg);\n        if (this.onServerDisconnectError && event.reason) {\n          // This callback is a unstable API, InternalServerErrors in particular may be removed\n          // since they reflect expected temporary downtime. But until a quantitative measure\n          // of uptime is reported this unstable API errs on the inclusive side.\n          this.onServerDisconnectError(msg);\n        }\n      }\n      const reason = classifyDisconnectError(event.reason);\n      this.scheduleReconnect(reason);\n      return;\n    };\n  }\n\n  /**\n   * @returns The state of the {@link Socket}.\n   */\n  socketState(): string {\n    return this.socket.state;\n  }\n\n  /**\n   * @param message - A ClientMessage to send.\n   * @returns Whether the message (might have been) sent.\n   */\n  sendMessage(message: ClientMessage) {\n    const messageForLog = {\n      type: message.type,\n      ...(message.type === \"Authenticate\" && message.tokenType === \"User\"\n        ? {\n            value: `...${message.value.slice(-7)}`,\n          }\n        : {}),\n    };\n    if (this.socket.state === \"ready\" && this.socket.paused === \"no\") {\n      const encodedMessage = encodeClientMessage(message);\n      const request = JSON.stringify(encodedMessage);\n      try {\n        this.socket.ws.send(request);\n      } catch (error: any) {\n        this.logger.log(\n          `Failed to send message on WebSocket, reconnecting: ${error}`,\n        );\n        this.closeAndReconnect(\"FailedToSendMessage\");\n      }\n      // We are not sure if this was sent or not.\n      this._logVerbose(\n        `sent message with type ${message.type}: ${JSON.stringify(\n          messageForLog,\n        )}`,\n      );\n      return true;\n    }\n    this._logVerbose(\n      `message not sent (socket state: ${this.socket.state}, paused: ${\"paused\" in this.socket ? this.socket.paused : undefined}): ${JSON.stringify(\n        messageForLog,\n      )}`,\n    );\n\n    return false;\n  }\n\n  private resetServerInactivityTimeout() {\n    if (this.socket.state === \"terminated\") {\n      // Don't reset any timers if we were trying to terminate.\n      return;\n    }\n    if (this.reconnectDueToServerInactivityTimeout !== null) {\n      clearTimeout(this.reconnectDueToServerInactivityTimeout);\n      this.reconnectDueToServerInactivityTimeout = null;\n    }\n    this.reconnectDueToServerInactivityTimeout = setTimeout(() => {\n      this.closeAndReconnect(\"InactiveServer\");\n    }, this.serverInactivityThreshold);\n  }\n\n  private scheduleReconnect(reason: \"client\" | ServerDisconnectError) {\n    this.socket = { state: \"disconnected\" };\n    const backoff = this.nextBackoff(reason);\n    this.markConnectionStateDirty();\n    this.logger.log(`Attempting reconnect in ${backoff}ms`);\n    setTimeout(() => this.connect(), backoff);\n  }\n\n  /**\n   * Close the WebSocket and schedule a reconnect.\n   *\n   * This should be used when we hit an error and would like to restart the session.\n   */\n  private closeAndReconnect(closeReason: string) {\n    this._logVerbose(`begin closeAndReconnect with reason ${closeReason}`);\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"terminated\":\n      case \"stopped\":\n        // Nothing to do if we don't have a WebSocket.\n        return;\n      case \"connecting\":\n      case \"ready\": {\n        this.lastCloseReason = closeReason;\n        // Close the old socket asynchronously, we'll open a new socket in reconnect.\n        void this.close();\n        this.scheduleReconnect(\"client\");\n        return;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n  }\n\n  /**\n   * Close the WebSocket, being careful to clear the onclose handler to avoid re-entrant\n   * calls. Use this instead of directly calling `ws.close()`\n   *\n   * It is the callers responsibility to update the state after this method is called so that the\n   * closed socket is not accessible or used again after this method is called\n   */\n  private close(): Promise<void> {\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"terminated\":\n      case \"stopped\":\n        // Nothing to do if we don't have a WebSocket.\n        return Promise.resolve();\n      case \"connecting\": {\n        const ws = this.socket.ws;\n        return new Promise((r) => {\n          ws.onclose = () => {\n            this._logVerbose(\"Closed after connecting\");\n            r();\n          };\n          ws.onopen = () => {\n            this._logVerbose(\"Opened after connecting\");\n            ws.close();\n          };\n        });\n      }\n      case \"ready\": {\n        this._logVerbose(\"ws.close called\");\n        const ws = this.socket.ws;\n        const result: Promise<void> = new Promise((r) => {\n          ws.onclose = () => {\n            r();\n          };\n        });\n        ws.close();\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return Promise.resolve();\n      }\n    }\n  }\n\n  /**\n   * Close the WebSocket and do not reconnect.\n   * @returns A Promise that resolves when the WebSocket `onClose` callback is called.\n   */\n  terminate(): Promise<void> {\n    if (this.reconnectDueToServerInactivityTimeout) {\n      clearTimeout(this.reconnectDueToServerInactivityTimeout);\n    }\n    switch (this.socket.state) {\n      case \"terminated\":\n      case \"stopped\":\n      case \"disconnected\":\n      case \"connecting\":\n      case \"ready\": {\n        const result = this.close();\n        this.setSocketState({ state: \"terminated\" });\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        throw new Error(\n          `Invalid websocket state: ${(this.socket as any).state}`,\n        );\n      }\n    }\n  }\n\n  stop(): Promise<void> {\n    switch (this.socket.state) {\n      case \"terminated\":\n        // If we're terminating we ignore stop\n        return Promise.resolve();\n      case \"connecting\":\n      case \"stopped\":\n      case \"disconnected\":\n      case \"ready\": {\n        const result = this.close();\n        this.socket = { state: \"stopped\" };\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return Promise.resolve();\n      }\n    }\n  }\n\n  /**\n   * Create a new WebSocket after a previous `stop()`, unless `terminate()` was\n   * called before.\n   */\n  tryRestart(): void {\n    switch (this.socket.state) {\n      case \"stopped\":\n        break;\n      case \"terminated\":\n      case \"connecting\":\n      case \"ready\":\n      case \"disconnected\":\n        this.logger.logVerbose(\"Restart called without stopping first\");\n        return;\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n    this.connect();\n  }\n\n  pause(): void {\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"stopped\":\n      case \"terminated\":\n        // If already stopped or stopping ignore.\n        return;\n      case \"connecting\":\n      case \"ready\": {\n        this.socket = { ...this.socket, paused: \"yes\" };\n        return;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return;\n      }\n    }\n  }\n\n  /**\n   * Resume the state machine if previously paused.\n   */\n  resume(): void {\n    switch (this.socket.state) {\n      case \"connecting\":\n        this.socket = { ...this.socket, paused: \"no\" };\n        return;\n      case \"ready\":\n        if (this.socket.paused === \"uninitialized\") {\n          this.socket = { ...this.socket, paused: \"no\" };\n          this.onOpen({\n            connectionCount: this.connectionCount,\n            lastCloseReason: this.lastCloseReason,\n          });\n        } else if (this.socket.paused === \"yes\") {\n          this.socket = { ...this.socket, paused: \"no\" };\n          this.onResume();\n        }\n        return;\n      case \"terminated\":\n      case \"stopped\":\n      case \"disconnected\":\n        // Ignore resume if not paused, perhaps we already resumed.\n        return;\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n    this.connect();\n  }\n\n  connectionState(): {\n    isConnected: boolean;\n    hasEverConnected: boolean;\n    connectionCount: number;\n    connectionRetries: number;\n  } {\n    return {\n      isConnected: this.socket.state === \"ready\",\n      hasEverConnected: this._hasEverConnected,\n      connectionCount: this.connectionCount,\n      connectionRetries: this.retries,\n    };\n  }\n\n  private _logVerbose(message: string) {\n    this.logger.logVerbose(message);\n  }\n\n  private nextBackoff(reason: \"client\" | ServerDisconnectError): number {\n    const initialBackoff: number =\n      reason === \"client\"\n        ? 100 // There's no evidence of a server problem, retry quickly\n        : reason === \"Unknown\"\n          ? this.defaultInitialBackoff\n          : serverDisconnectErrors[reason].timeout;\n\n    const baseBackoff = initialBackoff * Math.pow(2, this.retries);\n    this.retries += 1;\n    const actualBackoff = Math.min(baseBackoff, this.maxBackoff);\n    const jitter = actualBackoff * (Math.random() - 0.5);\n    return actualBackoff + jitter;\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;AAOA,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,kBAAkB;AAMxB,MAAM,kBAAkB;AA+ExB,MAAM,yBAAyB;IAAA,+CAAA;IAE7B,qBAAqB;QAAE,SAAS;IAAK;IAAA,qEAAA;IAErC,8BAA8B;QAAE,SAAS;IAAK;IAC9C,2BAA2B;QAAE,SAAS;IAAK;IAC3C,oBAAoB;QAAE,SAAS;IAAK;IACpC,6BAA6B;QAAE,SAAS;IAAK;IAC7C,kBAAkB;QAAE,SAAS;IAAK;IAClC,oBAAoB;QAAE,SAAS;IAAK;IACpC,gBAAgB;QAAE,SAAS;IAAK;IAAA,kGAAA;IAEhC,0BAA0B;QAAE,SAAS;IAAK;IAC1C,0BAA0B;QAAE,SAAS;IAAK;IAC1C,2BAA2B;QAAE,SAAS;IAAK;IAAA,oCAAA;IAE3C,qBAAqB;QAAE,SAAS;IAAK;IACrC,qBAAqB;QAAE,SAAS;IAAK;IACrC,2BAA2B;QAAE,SAAS;IAAK;AAC7C;AAIA,SAAS,wBAAwB,CAAA,EAAmC;IAClE,IAAI,MAAM,KAAA,EAAW,CAAA,OAAO;IAG5B,KAAA,MAAW,UAAU,OAAO,IAAA,CAC1B,wBAC4B;QAC5B,IAAI,EAAE,UAAA,CAAW,MAAM,GAAG;YACxB,OAAO;QACT;IACF;IACA,OAAO;AACT;AAMO,MAAM,iBAAiB;IA6EpB,eAAe,KAAA,EAAe;QACpC,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,WAAA,CACH,yBACE,OADuB,IAAA,CAAK,MAAA,CAAO,KAAK,EAAA,cAE1C,mBADc,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,KAAA,CACjD;QAEF,IAAA,CAAK,wBAAA,CAAyB;IAChC;IAEQ,UAAU;QAChB,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU,cAAc;YACtC;QACF;QACA,IACE,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU,kBACtB,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU,WACtB;YACA,MAAM,IAAI,MACR,sDAAsD,IAAA,CAAK,MAAA,CAAO,KAAA;QAEtE;QAEA,MAAM,KAAK,IAAI,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,GAAG;QACjD,IAAA,CAAK,WAAA,CAAY,uBAAuB;QACxC,IAAA,CAAK,cAAA,CAAe;YAClB,OAAO;YACP;YACA,QAAQ;QACV,CAAC;QAMD,IAAA,CAAK,4BAAA,CAA6B;QAElC,GAAG,MAAA,GAAS,MAAM;YAChB,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,iBAAiB;YACxC,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU,cAAc;gBACtC,MAAM,IAAI,MAAM,mDAAmD;YACrE;YACA,IAAA,CAAK,cAAA,CAAe;gBAClB,OAAO;gBACP;gBACA,QAAQ,IAAA,CAAK,MAAA,CAAO,MAAA,KAAW,QAAQ,kBAAkB;YAC3D,CAAC;YACD,IAAA,CAAK,4BAAA,CAA6B;YAClC,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA,KAAW,MAAM;gBAC/B,IAAA,CAAK,iBAAA,GAAoB;gBACzB,IAAA,CAAK,MAAA,CAAO;oBACV,iBAAiB,IAAA,CAAK,eAAA;oBACtB,iBAAiB,IAAA,CAAK,eAAA;gBACxB,CAAC;YACH;YAEA,IAAI,IAAA,CAAK,eAAA,KAAoB,kBAAkB;gBAC7C,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,uBAAuB;YACzC;YAEA,IAAA,CAAK,eAAA,IAAmB;YACxB,IAAA,CAAK,eAAA,GAAkB;QACzB;QAEA,GAAG,OAAA,GAAU,CAAC,UAAU;YACtB,MAAM,UAAW,MAAqB,OAAA;YACtC,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,oBAA2B,CAAE,MAAT,OAAO;QAC7C;QACA,GAAG,SAAA,GAAY,CAAC,YAAY;YAC1B,IAAA,CAAK,4BAAA,CAA6B;YAClC,MAAM,gBAAgB,wQAAA,EAAmB,KAAK,KAAA,CAAM,QAAQ,IAAI,CAAC;YACjE,IAAA,CAAK,WAAA,CAAY,iCAAmD,CAAE,MAApB,cAAc,IAAI;YACpE,MAAM,WAAW,IAAA,CAAK,SAAA,CAAU,aAAa;YAC7C,IAAI,SAAS,0BAAA,EAA4B;gBAEvC,IAAA,CAAK,OAAA,GAAU;gBACf,IAAA,CAAK,wBAAA,CAAyB;YAChC;QACF;QACA,GAAG,OAAA,GAAU,CAAC,UAAU;YACtB,IAAA,CAAK,WAAA,CAAY,kBAAkB;YACnC,IAAI,IAAA,CAAK,eAAA,KAAoB,MAAM;;gBACjC,IAAA,CAAK,eAAA,0BAAwB,MAAA,yCAAN,gBAAgB;YACzC;YACA,IACE,MAAM,IAAA,KAAS,gBACf,MAAM,IAAA,KAAS,oBAAA,uEAAA;YACf,MAAM,IAAA,KAAS,mBACf,MAAM,IAAA,KAAS,iBACf;gBACA,IAAI,MAAM,8BAAwC,OAAV,MAAM,IAAI;gBAClD,IAAI,MAAM,MAAA,EAAQ;oBAChB,OAAO,KAAiB,OAAZ,MAAM,MAAM;gBAC1B;gBACA,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,GAAG;gBACnB,IAAI,IAAA,CAAK,uBAAA,IAA2B,MAAM,MAAA,EAAQ;oBAIhD,IAAA,CAAK,uBAAA,CAAwB,GAAG;gBAClC;YACF;YACA,MAAM,SAAS,wBAAwB,MAAM,MAAM;YACnD,IAAA,CAAK,iBAAA,CAAkB,MAAM;YAC7B;QACF;IACF;IAAA;;GAAA,GAKA,cAAsB;QACpB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;IACrB;IAAA;;;GAAA,GAMA,YAAY,OAAA,EAAwB;QAClC,MAAM,gBAAgB;YACpB,MAAM,QAAQ,IAAA;YACd,GAAI,QAAQ,IAAA,KAAS,kBAAkB,QAAQ,SAAA,KAAc,SACzD;gBACE,OAAO,MAA6B,OAAvB,QAAQ,KAAA,CAAM,KAAA,CAAM,CAAA,CAAE,CAAC;YACtC,IACA,CAAC,CAAA;QACP;QACA,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU,WAAW,IAAA,CAAK,MAAA,CAAO,MAAA,KAAW,MAAM;YAChE,MAAM,iBAAiB,yQAAA,EAAoB,OAAO;YAClD,MAAM,UAAU,KAAK,SAAA,CAAU,cAAc;YAC7C,IAAI;gBACF,IAAA,CAAK,MAAA,CAAO,EAAA,CAAG,IAAA,CAAK,OAAO;YAC7B,EAAA,OAAS,OAAY;gBACnB,IAAA,CAAK,MAAA,CAAO,GAAA,CACV,sDAA2D,OAAL,KAAK;gBAE7D,IAAA,CAAK,iBAAA,CAAkB,qBAAqB;YAC9C;YAEA,IAAA,CAAK,WAAA,CACH,0BAA2C,OAAjB,QAAQ,IAAI,EAAA,MAErC,YAF+C,SAAA,CAC9C;YAGJ,OAAO;QACT;QACA,IAAA,CAAK,WAAA,CACH,0CAAmC,IAAA,CAAK,MAAA,CAAO,KAAK,EAAA,cAA2E,OAA9D,YAAY,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,KAAA,CAAS,EAAA,OAExH,YAFmI,SAAA,CAClI;QAIJ,OAAO;IACT;IAEQ,+BAA+B;QACrC,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU,cAAc;YAEtC;QACF;QACA,IAAI,IAAA,CAAK,qCAAA,KAA0C,MAAM;YACvD,aAAa,IAAA,CAAK,qCAAqC;YACvD,IAAA,CAAK,qCAAA,GAAwC;QAC/C;QACA,IAAA,CAAK,qCAAA,GAAwC,WAAW,MAAM;YAC5D,IAAA,CAAK,iBAAA,CAAkB,gBAAgB;QACzC,GAAG,IAAA,CAAK,yBAAyB;IACnC;IAEQ,kBAAkB,MAAA,EAA0C;QAClE,IAAA,CAAK,MAAA,GAAS;YAAE,OAAO;QAAe;QACtC,MAAM,UAAU,IAAA,CAAK,WAAA,CAAY,MAAM;QACvC,IAAA,CAAK,wBAAA,CAAyB;QAC9B,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,2BAAkC,OAAP,OAAO,EAAA,GAAI;QACtD,WAAW,IAAM,IAAA,CAAK,OAAA,CAAQ,GAAG,OAAO;IAC1C;IAAA;;;;GAAA,GAOQ,kBAAkB,WAAA,EAAqB;QAC7C,IAAA,CAAK,WAAA,CAAY,uCAAkD,CAAE,MAAb,WAAW;QACnE,OAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACzB,KAAK;YACL,KAAK;YACL,KAAK;gBAEH;YACF,KAAK;YACL,KAAK;gBAAS;oBACZ,IAAA,CAAK,eAAA,GAAkB;oBAEvB,KAAK,IAAA,CAAK,KAAA,CAAM;oBAChB,IAAA,CAAK,iBAAA,CAAkB,QAAQ;oBAC/B;gBACF;YACA;gBAAS;oBAEP,IAAA,CAAK,MAAA;gBACP;QACF;IACF;IAAA;;;;;;GAAA,GASQ,QAAuB;QAC7B,OAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACzB,KAAK;YACL,KAAK;YACL,KAAK;gBAEH,OAAO,QAAQ,OAAA,CAAQ;YACzB,KAAK;gBAAc;oBACjB,MAAM,KAAK,IAAA,CAAK,MAAA,CAAO,EAAA;oBACvB,OAAO,IAAI,QAAQ,CAAC,MAAM;wBACxB,GAAG,OAAA,GAAU,MAAM;4BACjB,IAAA,CAAK,WAAA,CAAY,yBAAyB;4BAC1C,EAAE;wBACJ;wBACA,GAAG,MAAA,GAAS,MAAM;4BAChB,IAAA,CAAK,WAAA,CAAY,yBAAyB;4BAC1C,GAAG,KAAA,CAAM;wBACX;oBACF,CAAC;gBACH;YACA,KAAK;gBAAS;oBACZ,IAAA,CAAK,WAAA,CAAY,iBAAiB;oBAClC,MAAM,KAAK,IAAA,CAAK,MAAA,CAAO,EAAA;oBACvB,MAAM,SAAwB,IAAI,QAAQ,CAAC,MAAM;wBAC/C,GAAG,OAAA,GAAU,MAAM;4BACjB,EAAE;wBACJ;oBACF,CAAC;oBACD,GAAG,KAAA,CAAM;oBACT,OAAO;gBACT;YACA;gBAAS;oBAEP,IAAA,CAAK,MAAA;oBACL,OAAO,QAAQ,OAAA,CAAQ;gBACzB;QACF;IACF;IAAA;;;GAAA,GAMA,YAA2B;QACzB,IAAI,IAAA,CAAK,qCAAA,EAAuC;YAC9C,aAAa,IAAA,CAAK,qCAAqC;QACzD;QACA,OAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACzB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAS;oBACZ,MAAM,SAAS,IAAA,CAAK,KAAA,CAAM;oBAC1B,IAAA,CAAK,cAAA,CAAe;wBAAE,OAAO;oBAAa,CAAC;oBAC3C,OAAO;gBACT;YACA;gBAAS;oBAEP,IAAA,CAAK,MAAA;oBACL,MAAM,IAAI,MACR,4BAAsD,OAAzB,IAAA,CAAK,MAAA,CAAe,KAAK;gBAE1D;QACF;IACF;IAEA,OAAsB;QACpB,OAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACzB,KAAK;gBAEH,OAAO,QAAQ,OAAA,CAAQ;YACzB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAS;oBACZ,MAAM,SAAS,IAAA,CAAK,KAAA,CAAM;oBAC1B,IAAA,CAAK,MAAA,GAAS;wBAAE,OAAO;oBAAU;oBACjC,OAAO;gBACT;YACA;gBAAS;oBAEP,IAAA,CAAK,MAAA;oBACL,OAAO,QAAQ,OAAA,CAAQ;gBACzB;QACF;IACF;IAAA;;;GAAA,GAMA,aAAmB;QACjB,OAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACzB,KAAK;gBACH;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,uCAAuC;gBAC9D;YACF;gBAAS;oBAEP,IAAA,CAAK,MAAA;gBACP;QACF;QACA,IAAA,CAAK,OAAA,CAAQ;IACf;IAEA,QAAc;QACZ,OAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACzB,KAAK;YACL,KAAK;YACL,KAAK;gBAEH;YACF,KAAK;YACL,KAAK;gBAAS;oBACZ,IAAA,CAAK,MAAA,GAAS;wBAAE,GAAG,IAAA,CAAK,MAAA;wBAAQ,QAAQ;oBAAM;oBAC9C;gBACF;YACA;gBAAS;oBAEP,IAAA,CAAK,MAAA;oBACL;gBACF;QACF;IACF;IAAA;;GAAA,GAKA,SAAe;QACb,OAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACzB,KAAK;gBACH,IAAA,CAAK,MAAA,GAAS;oBAAE,GAAG,IAAA,CAAK,MAAA;oBAAQ,QAAQ;gBAAK;gBAC7C;YACF,KAAK;gBACH,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA,KAAW,iBAAiB;oBAC1C,IAAA,CAAK,MAAA,GAAS;wBAAE,GAAG,IAAA,CAAK,MAAA;wBAAQ,QAAQ;oBAAK;oBAC7C,IAAA,CAAK,MAAA,CAAO;wBACV,iBAAiB,IAAA,CAAK,eAAA;wBACtB,iBAAiB,IAAA,CAAK,eAAA;oBACxB,CAAC;gBACH,OAAA,IAAW,IAAA,CAAK,MAAA,CAAO,MAAA,KAAW,OAAO;oBACvC,IAAA,CAAK,MAAA,GAAS;wBAAE,GAAG,IAAA,CAAK,MAAA;wBAAQ,QAAQ;oBAAK;oBAC7C,IAAA,CAAK,QAAA,CAAS;gBAChB;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBAEH;YACF;gBAAS;oBAEP,IAAA,CAAK,MAAA;gBACP;QACF;QACA,IAAA,CAAK,OAAA,CAAQ;IACf;IAEA,kBAKE;QACA,OAAO;YACL,aAAa,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU;YACnC,kBAAkB,IAAA,CAAK,iBAAA;YACvB,iBAAiB,IAAA,CAAK,eAAA;YACtB,mBAAmB,IAAA,CAAK,OAAA;QAC1B;IACF;IAEQ,YAAY,OAAA,EAAiB;QACnC,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,OAAO;IAChC;IAEQ,YAAY,MAAA,EAAkD;QACpE,MAAM,iBACJ,WAAW,WACP,MACA,WAAW,YACT,IAAA,CAAK,qBAAA,GACL,sBAAA,CAAuB,MAAM,CAAA,CAAE,OAAA;QAEvC,MAAM,cAAc,iBAAiB,KAAK,GAAA,CAAI,GAAG,IAAA,CAAK,OAAO;QAC7D,IAAA,CAAK,OAAA,IAAW;QAChB,MAAM,gBAAgB,KAAK,GAAA,CAAI,aAAa,IAAA,CAAK,UAAU;QAC3D,MAAM,SAAS,gBAAA,CAAiB,KAAK,MAAA,CAAO,IAAI,GAAA;QAChD,OAAO,gBAAgB;IACzB;IA9bA,YACE,GAAA,EACA,SAAA,EAMA,oBAAA,EACA,MAAA,EACiB,wBAAA,CACjB;QADiB,IAAA,CAAA,wBAAA,GAAA;QA/CnB,cAAA,IAAA,EAAQ;QAER,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ,qBAA6B;QACrC,cAAA,IAAA,EAAQ;QAOR,wEAAA,GAAA,cAAA,IAAA,EAAiB;QAGjB,iFAAA,GAAA,cAAA,IAAA,EAAiB;QAGjB,iDAAA,GAAA,cAAA,IAAA,EAAQ;QAIR;aAAA,GAAA,cAAA,IAAA,EAAiB;QAEjB,cAAA,IAAA,EAAQ;QAIR,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QAgBf,IAAA,CAAK,oBAAA,GAAuB;QAC5B,IAAA,CAAK,MAAA,GAAS;YAAE,OAAO;QAAe;QACtC,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,eAAA,GAAkB;QAGvB,IAAA,CAAK,qBAAA,GAAwB;QAC7B,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,OAAA,GAAU;QAMf,IAAA,CAAK,yBAAA,GAA4B;QACjC,IAAA,CAAK,qCAAA,GAAwC;QAE7C,IAAA,CAAK,GAAA,GAAM;QACX,IAAA,CAAK,MAAA,GAAS,UAAU,MAAA;QACxB,IAAA,CAAK,QAAA,GAAW,UAAU,QAAA;QAC1B,IAAA,CAAK,SAAA,GAAY,UAAU,SAAA;QAC3B,IAAA,CAAK,uBAAA,GAA0B,UAAU,uBAAA;QACzC,IAAA,CAAK,MAAA,GAAS;QAEd,IAAA,CAAK,OAAA,CAAQ;IACf;AA0ZF", "debugId": null}}, {"offset": {"line": 3173, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/session.ts"], "sourcesContent": ["export function newSessionId() {\n  return uuidv4();\n}\n\n// From https://stackoverflow.com/a/2117523\nfunction uuidv4() {\n  return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16) | 0,\n      v = c === \"x\" ? r : (r & 0x3) | 0x8;\n    return v.toString(16);\n  });\n}\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,eAAe;IAC7B,OAAO,OAAO;AAChB;AAGA,SAAS,SAAS;IAChB,OAAO,uCAAuC,OAAA,CAAQ,SAAS,CAAC,MAAM;QACpE,MAAM,IAAK,KAAK,MAAA,CAAO,IAAI,KAAM,GAC/B,IAAI,MAAM,MAAM,IAAK,IAAI,IAAO;QAClC,OAAO,EAAE,QAAA,CAAS,EAAE;IACtB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 3191, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/authentication_manager.ts"], "sourcesContent": ["import { Logger } from \"../logging.js\";\nimport { LocalSyncState } from \"./local_state.js\";\nimport { AuthError, IdentityVersion, Transition } from \"./protocol.js\";\nimport { jwtDecode } from \"jwt-decode\";\n\n// setTimout uses 32 bit integer, so it can only\n// schedule about 24 days in the future.\nconst MAXIMUM_REFRESH_DELAY = 20 * 24 * 60 * 60 * 1000; // 20 days\n\nconst MAX_TOKEN_CONFIRMATION_ATTEMPTS = 2;\n\n/**\n * An async function returning a JWT. Depending on the auth providers\n * configured in convex/auth.config.ts, this may be a JWT-encoded OpenID\n * Connect Identity Token or a traditional JWT.\n *\n * `forceRefreshToken` is `true` if the server rejected a previously\n * returned token or the token is anticipated to expiring soon\n * based on its `exp` time.\n *\n * See {@link ConvexReactClient.setAuth}.\n *\n * @public\n */\nexport type AuthTokenFetcher = (args: {\n  forceRefreshToken: boolean;\n}) => Promise<string | null | undefined>;\n\n/**\n * What is provided to the client.\n */\ntype AuthConfig = {\n  fetchToken: AuthTokenFetcher;\n  onAuthChange: (isAuthenticated: boolean) => void;\n};\n\n/**\n * In general we take 3 steps:\n *   1. Fetch a possibly cached token\n *   2. Immediately fetch a fresh token without using a cache\n *   3. Repeat step 2 before the end of the fresh token's lifetime\n *\n * When we fetch without using a cache we know when the token\n * will expire, and can schedule refetching it.\n *\n * If we get an error before a scheduled refetch, we go back\n * to step 2.\n */\ntype AuthState =\n  | { state: \"noAuth\" }\n  | {\n      state: \"waitingForServerConfirmationOfCachedToken\";\n      config: AuthConfig;\n      hasRetried: boolean;\n    }\n  | {\n      state: \"initialRefetch\";\n      config: AuthConfig;\n    }\n  | {\n      state: \"waitingForServerConfirmationOfFreshToken\";\n      config: AuthConfig;\n      hadAuth: boolean;\n      token: string;\n    }\n  | {\n      state: \"waitingForScheduledRefetch\";\n      config: AuthConfig;\n      refetchTokenTimeoutId: ReturnType<typeof setTimeout>;\n    }\n  // Special/weird state when we got a valid token\n  // but could not fetch a new one.\n  | {\n      state: \"notRefetching\";\n      config: AuthConfig;\n    };\n\n/**\n * Handles the state transitions for auth. The server is the source\n * of truth.\n */\nexport class AuthenticationManager {\n  private authState: AuthState = { state: \"noAuth\" };\n  // Used to detect races involving `setConfig` calls\n  // while a token is being fetched.\n  private configVersion = 0;\n  // Shared by the BaseClient so that the auth manager can easily inspect it\n  private readonly syncState: LocalSyncState;\n  // Passed down by BaseClient, sends a message to the server\n  private readonly authenticate: (token: string) => IdentityVersion;\n  private readonly stopSocket: () => Promise<void>;\n  private readonly tryRestartSocket: () => void;\n  private readonly pauseSocket: () => void;\n  private readonly resumeSocket: () => void;\n  // Passed down by BaseClient, sends a message to the server\n  private readonly clearAuth: () => void;\n  private readonly logger: Logger;\n  private readonly refreshTokenLeewaySeconds: number;\n  // Number of times we have attempted to confirm the latest token. We retry up\n  // to `MAX_TOKEN_CONFIRMATION_ATTEMPTS` times.\n  private tokenConfirmationAttempts = 0;\n  constructor(\n    syncState: LocalSyncState,\n    callbacks: {\n      authenticate: (token: string) => IdentityVersion;\n      stopSocket: () => Promise<void>;\n      tryRestartSocket: () => void;\n      pauseSocket: () => void;\n      resumeSocket: () => void;\n      clearAuth: () => void;\n    },\n    config: {\n      refreshTokenLeewaySeconds: number;\n      logger: Logger;\n    },\n  ) {\n    this.syncState = syncState;\n    this.authenticate = callbacks.authenticate;\n    this.stopSocket = callbacks.stopSocket;\n    this.tryRestartSocket = callbacks.tryRestartSocket;\n    this.pauseSocket = callbacks.pauseSocket;\n    this.resumeSocket = callbacks.resumeSocket;\n    this.clearAuth = callbacks.clearAuth;\n    this.logger = config.logger;\n    this.refreshTokenLeewaySeconds = config.refreshTokenLeewaySeconds;\n  }\n\n  async setConfig(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ) {\n    this.resetAuthState();\n    this._logVerbose(\"pausing WS for auth token fetch\");\n    this.pauseSocket();\n    const token = await this.fetchTokenAndGuardAgainstRace(fetchToken, {\n      forceRefreshToken: false,\n    });\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n    if (token.value) {\n      this.setAuthState({\n        state: \"waitingForServerConfirmationOfCachedToken\",\n        config: { fetchToken, onAuthChange: onChange },\n        hasRetried: false,\n      });\n      this.authenticate(token.value);\n    } else {\n      this.setAuthState({\n        state: \"initialRefetch\",\n        config: { fetchToken, onAuthChange: onChange },\n      });\n      // Try again with `forceRefreshToken: true`\n      await this.refetchToken();\n    }\n    this._logVerbose(\"resuming WS after auth token fetch\");\n    this.resumeSocket();\n  }\n\n  onTransition(serverMessage: Transition) {\n    if (\n      !this.syncState.isCurrentOrNewerAuthVersion(\n        serverMessage.endVersion.identity,\n      )\n    ) {\n      // This is a stale transition - client has moved on to\n      // a newer auth version.\n      return;\n    }\n    if (\n      serverMessage.endVersion.identity <= serverMessage.startVersion.identity\n    ) {\n      // This transition did not change auth - it is not a response to Authenticate.\n      return;\n    }\n\n    if (this.authState.state === \"waitingForServerConfirmationOfCachedToken\") {\n      this._logVerbose(\"server confirmed auth token is valid\");\n      void this.refetchToken();\n      this.authState.config.onAuthChange(true);\n      return;\n    }\n    if (this.authState.state === \"waitingForServerConfirmationOfFreshToken\") {\n      this._logVerbose(\"server confirmed new auth token is valid\");\n      this.scheduleTokenRefetch(this.authState.token);\n      this.tokenConfirmationAttempts = 0;\n      if (!this.authState.hadAuth) {\n        this.authState.config.onAuthChange(true);\n      }\n    }\n  }\n\n  onAuthError(serverMessage: AuthError) {\n    // If the AuthError is not due to updating the token, and we're currently\n    // waiting on the result of a token update, ignore.\n    if (\n      serverMessage.authUpdateAttempted === false &&\n      (this.authState.state === \"waitingForServerConfirmationOfFreshToken\" ||\n        this.authState.state === \"waitingForServerConfirmationOfCachedToken\")\n    ) {\n      this._logVerbose(\"ignoring non-auth token expired error\");\n      return;\n    }\n    const { baseVersion } = serverMessage;\n    // Versioned AuthErrors are ignored if the client advanced to\n    // a newer auth identity\n    // Error are reporting the previous version, since the server\n    // didn't advance, hence `+ 1`.\n    if (!this.syncState.isCurrentOrNewerAuthVersion(baseVersion + 1)) {\n      this._logVerbose(\"ignoring auth error for previous auth attempt\");\n      return;\n    }\n    void this.tryToReauthenticate(serverMessage);\n    return;\n  }\n\n  // This is similar to `refetchToken` defined below, in fact we\n  // don't represent them as different states, but it is different\n  // in that we pause the WebSocket so that mutations\n  // don't retry with bad auth.\n  private async tryToReauthenticate(serverMessage: AuthError) {\n    this._logVerbose(`attempting to reauthenticate: ${serverMessage.error}`);\n    if (\n      // No way to fetch another token, kaboom\n      this.authState.state === \"noAuth\" ||\n      // We failed on a fresh token. After a small number of retries, we give up\n      // and clear the auth state to avoid infinite retries.\n      (this.authState.state === \"waitingForServerConfirmationOfFreshToken\" &&\n        this.tokenConfirmationAttempts >= MAX_TOKEN_CONFIRMATION_ATTEMPTS)\n    ) {\n      this.logger.error(\n        `Failed to authenticate: \"${serverMessage.error}\", check your server auth config`,\n      );\n      if (this.syncState.hasAuth()) {\n        this.syncState.clearAuth();\n      }\n      if (this.authState.state !== \"noAuth\") {\n        this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n      }\n      return;\n    }\n    if (this.authState.state === \"waitingForServerConfirmationOfFreshToken\") {\n      this.tokenConfirmationAttempts++;\n      this._logVerbose(\n        `retrying reauthentication, ${MAX_TOKEN_CONFIRMATION_ATTEMPTS - this.tokenConfirmationAttempts} attempts remaining`,\n      );\n    }\n\n    await this.stopSocket();\n    const token = await this.fetchTokenAndGuardAgainstRace(\n      this.authState.config.fetchToken,\n      {\n        forceRefreshToken: true,\n      },\n    );\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n\n    if (token.value && this.syncState.isNewAuth(token.value)) {\n      this.authenticate(token.value);\n      this.setAuthState({\n        state: \"waitingForServerConfirmationOfFreshToken\",\n        config: this.authState.config,\n        token: token.value,\n        hadAuth:\n          this.authState.state === \"notRefetching\" ||\n          this.authState.state === \"waitingForScheduledRefetch\",\n      });\n    } else {\n      this._logVerbose(\"reauthentication failed, could not fetch a new token\");\n      if (this.syncState.hasAuth()) {\n        this.syncState.clearAuth();\n      }\n      this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n    }\n    this.tryRestartSocket();\n  }\n\n  // Force refetch the token and schedule another refetch\n  // before the token expires - an active client should never\n  // need to reauthenticate.\n  private async refetchToken() {\n    if (this.authState.state === \"noAuth\") {\n      return;\n    }\n    this._logVerbose(\"refetching auth token\");\n    const token = await this.fetchTokenAndGuardAgainstRace(\n      this.authState.config.fetchToken,\n      {\n        forceRefreshToken: true,\n      },\n    );\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n\n    if (token.value) {\n      if (this.syncState.isNewAuth(token.value)) {\n        this.setAuthState({\n          state: \"waitingForServerConfirmationOfFreshToken\",\n          hadAuth: this.syncState.hasAuth(),\n          token: token.value,\n          config: this.authState.config,\n        });\n        this.authenticate(token.value);\n      } else {\n        this.setAuthState({\n          state: \"notRefetching\",\n          config: this.authState.config,\n        });\n      }\n    } else {\n      this._logVerbose(\"refetching token failed\");\n      if (this.syncState.hasAuth()) {\n        this.clearAuth();\n      }\n      this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n    }\n    // Restart in case this refetch was triggered via schedule during\n    // a reauthentication attempt.\n    this._logVerbose(\n      \"restarting WS after auth token fetch (if currently stopped)\",\n    );\n    this.tryRestartSocket();\n  }\n\n  private scheduleTokenRefetch(token: string) {\n    if (this.authState.state === \"noAuth\") {\n      return;\n    }\n    const decodedToken = this.decodeToken(token);\n    if (!decodedToken) {\n      // This is no longer really possible, because\n      // we wait on server response before scheduling token refetch,\n      // and the server currently requires JWT tokens.\n      this.logger.error(\n        \"Auth token is not a valid JWT, cannot refetch the token\",\n      );\n      return;\n    }\n    // iat: issued at time, UTC seconds timestamp at which the JWT was issued\n    // exp: expiration time, UTC seconds timestamp at which the JWT will expire\n    const { iat, exp } = decodedToken as { iat?: number; exp?: number };\n    if (!iat || !exp) {\n      this.logger.error(\n        \"Auth token does not have required fields, cannot refetch the token\",\n      );\n      return;\n    }\n    // Because the client and server clocks may be out of sync,\n    // we only know that the token will expire after `exp - iat`,\n    // and since we just fetched a fresh one we know when that\n    // will happen.\n    const tokenValiditySeconds = exp - iat;\n    if (tokenValiditySeconds <= 2) {\n      this.logger.error(\n        \"Auth token does not live long enough, cannot refetch the token\",\n      );\n      return;\n    }\n    // Attempt to refresh the token `refreshTokenLeewaySeconds` before it expires,\n    // or immediately if the token is already expiring soon.\n    let delay = Math.min(\n      MAXIMUM_REFRESH_DELAY,\n      (tokenValiditySeconds - this.refreshTokenLeewaySeconds) * 1000,\n    );\n    if (delay <= 0) {\n      // Refetch immediately, but this might be due to configuring a `refreshTokenLeewaySeconds`\n      // that is too large compared to the token's actual lifetime.\n      this.logger.warn(\n        `Refetching auth token immediately, configured leeway ${this.refreshTokenLeewaySeconds}s is larger than the token's lifetime ${tokenValiditySeconds}s`,\n      );\n      delay = 0;\n    }\n    const refetchTokenTimeoutId = setTimeout(() => {\n      this._logVerbose(\"running scheduled token refetch\");\n      void this.refetchToken();\n    }, delay);\n    this.setAuthState({\n      state: \"waitingForScheduledRefetch\",\n      refetchTokenTimeoutId,\n      config: this.authState.config,\n    });\n    this._logVerbose(\n      `scheduled preemptive auth token refetching in ${delay}ms`,\n    );\n  }\n\n  // Protects against simultaneous calls to `setConfig`\n  // while we're fetching a token\n  private async fetchTokenAndGuardAgainstRace(\n    fetchToken: AuthTokenFetcher,\n    fetchArgs: {\n      forceRefreshToken: boolean;\n    },\n  ) {\n    const originalConfigVersion = ++this.configVersion;\n    this._logVerbose(\n      `fetching token with config version ${originalConfigVersion}`,\n    );\n    const token = await fetchToken(fetchArgs);\n    if (this.configVersion !== originalConfigVersion) {\n      // This is a stale config\n      this._logVerbose(\n        `stale config version, expected ${originalConfigVersion}, got ${this.configVersion}`,\n      );\n      return { isFromOutdatedConfig: true };\n    }\n    return { isFromOutdatedConfig: false, value: token };\n  }\n\n  stop() {\n    this.resetAuthState();\n    // Bump this in case we are mid-token-fetch when we get stopped\n    this.configVersion++;\n    this._logVerbose(`config version bumped to ${this.configVersion}`);\n  }\n\n  private setAndReportAuthFailed(\n    onAuthChange: (authenticated: boolean) => void,\n  ) {\n    onAuthChange(false);\n    this.resetAuthState();\n  }\n\n  private resetAuthState() {\n    this.setAuthState({ state: \"noAuth\" });\n  }\n\n  private setAuthState(newAuth: AuthState) {\n    const authStateForLog =\n      newAuth.state === \"waitingForServerConfirmationOfFreshToken\"\n        ? {\n            hadAuth: newAuth.hadAuth,\n            state: newAuth.state,\n            token: `...${newAuth.token.slice(-7)}`,\n          }\n        : { state: newAuth.state };\n    this._logVerbose(\n      `setting auth state to ${JSON.stringify(authStateForLog)}`,\n    );\n    switch (newAuth.state) {\n      case \"waitingForScheduledRefetch\":\n      case \"notRefetching\":\n      case \"noAuth\":\n        this.tokenConfirmationAttempts = 0;\n        break;\n      case \"waitingForServerConfirmationOfFreshToken\":\n      case \"waitingForServerConfirmationOfCachedToken\":\n      case \"initialRefetch\":\n        break;\n      default: {\n        newAuth satisfies never;\n      }\n    }\n    if (this.authState.state === \"waitingForScheduledRefetch\") {\n      clearTimeout(this.authState.refetchTokenTimeoutId);\n\n      // The waitingForScheduledRefetch state is the most quiesced authed state.\n      // Let the syncState know that auth is in a good state, so it can reset failure backoffs\n      this.syncState.markAuthCompletion();\n    }\n    this.authState = newAuth;\n  }\n\n  private decodeToken(token: string) {\n    try {\n      return jwtDecode(token);\n    } catch (e) {\n      this._logVerbose(\n        `Error decoding token: ${e instanceof Error ? e.message : \"Unknown error\"}`,\n      );\n      return null;\n    }\n  }\n\n  private _logVerbose(message: string) {\n    this.logger.logVerbose(`${message} [v${this.configVersion}]`);\n  }\n}\n"], "names": [], "mappings": ";;;;AAGA,SAAS,iBAAiB;;;;;;;;;;;AAI1B,MAAM,wBAAwB,KAAK,KAAK,KAAK,KAAK;AAElD,MAAM,kCAAkC;AAwEjC,MAAM,sBAAsB;IA8CjC,MAAM,UACJ,UAAA,EACA,QAAA,EACA;QACA,IAAA,CAAK,cAAA,CAAe;QACpB,IAAA,CAAK,WAAA,CAAY,iCAAiC;QAClD,IAAA,CAAK,WAAA,CAAY;QACjB,MAAM,QAAQ,MAAM,IAAA,CAAK,6BAAA,CAA8B,YAAY;YACjE,mBAAmB;QACrB,CAAC;QACD,IAAI,MAAM,oBAAA,EAAsB;YAC9B;QACF;QACA,IAAI,MAAM,KAAA,EAAO;YACf,IAAA,CAAK,YAAA,CAAa;gBAChB,OAAO;gBACP,QAAQ;oBAAE;oBAAY,cAAc;gBAAS;gBAC7C,YAAY;YACd,CAAC;YACD,IAAA,CAAK,YAAA,CAAa,MAAM,KAAK;QAC/B,OAAO;YACL,IAAA,CAAK,YAAA,CAAa;gBAChB,OAAO;gBACP,QAAQ;oBAAE;oBAAY,cAAc;gBAAS;YAC/C,CAAC;YAED,MAAM,IAAA,CAAK,YAAA,CAAa;QAC1B;QACA,IAAA,CAAK,WAAA,CAAY,oCAAoC;QACrD,IAAA,CAAK,YAAA,CAAa;IACpB;IAEA,aAAa,aAAA,EAA2B;QACtC,IACE,CAAC,IAAA,CAAK,SAAA,CAAU,2BAAA,CACd,cAAc,UAAA,CAAW,QAAA,GAE3B;YAGA;QACF;QACA,IACE,cAAc,UAAA,CAAW,QAAA,IAAY,cAAc,YAAA,CAAa,QAAA,EAChE;YAEA;QACF;QAEA,IAAI,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,6CAA6C;YACxE,IAAA,CAAK,WAAA,CAAY,sCAAsC;YACvD,KAAK,IAAA,CAAK,YAAA,CAAa;YACvB,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,YAAA,CAAa,IAAI;YACvC;QACF;QACA,IAAI,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,4CAA4C;YACvE,IAAA,CAAK,WAAA,CAAY,0CAA0C;YAC3D,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,SAAA,CAAU,KAAK;YAC9C,IAAA,CAAK,yBAAA,GAA4B;YACjC,IAAI,CAAC,IAAA,CAAK,SAAA,CAAU,OAAA,EAAS;gBAC3B,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,YAAA,CAAa,IAAI;YACzC;QACF;IACF;IAEA,YAAY,aAAA,EAA0B;QAGpC,IACE,cAAc,mBAAA,KAAwB,SAAA,CACrC,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,8CACxB,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,2CAAA,GAC3B;YACA,IAAA,CAAK,WAAA,CAAY,uCAAuC;YACxD;QACF;QACA,MAAM,EAAE,WAAA,CAAY,CAAA,GAAI;QAKxB,IAAI,CAAC,IAAA,CAAK,SAAA,CAAU,2BAAA,CAA4B,cAAc,CAAC,GAAG;YAChE,IAAA,CAAK,WAAA,CAAY,+CAA+C;YAChE;QACF;QACA,KAAK,IAAA,CAAK,mBAAA,CAAoB,aAAa;QAC3C;IACF;IAAA,8DAAA;IAAA,gEAAA;IAAA,mDAAA;IAAA,6BAAA;IAMA,MAAc,oBAAoB,aAAA,EAA0B;QAC1D,IAAA,CAAK,WAAA,CAAY,iCAAoD,CAAE,MAArB,cAAc,KAAK;QACrE,IAAA,wCAAA;QAEE,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,YAAA,0EAAA;QAAA,sDAAA;QAGxB,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,8CACxB,IAAA,CAAK,yBAAA,IAA6B,iCACpC;YACA,IAAA,CAAK,MAAA,CAAO,KAAA,CACV,4BAA+C,OAAnB,cAAc,KAAK,EAAA;YAEjD,IAAI,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,GAAG;gBAC5B,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU;YAC3B;YACA,IAAI,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,UAAU;gBACrC,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,YAAY;YAChE;YACA;QACF;QACA,IAAI,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,4CAA4C;YACvE,IAAA,CAAK,yBAAA;YACL,IAAA,CAAK,WAAA,CACH,8BAA8F,OAAhE,kCAAkC,IAAA,CAAK,yBAAyB,EAAA;QAElG;QAEA,MAAM,IAAA,CAAK,UAAA,CAAW;QACtB,MAAM,QAAQ,MAAM,IAAA,CAAK,6BAAA,CACvB,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,UAAA,EACtB;YACE,mBAAmB;QACrB;QAEF,IAAI,MAAM,oBAAA,EAAsB;YAC9B;QACF;QAEA,IAAI,MAAM,KAAA,IAAS,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,MAAM,KAAK,GAAG;YACxD,IAAA,CAAK,YAAA,CAAa,MAAM,KAAK;YAC7B,IAAA,CAAK,YAAA,CAAa;gBAChB,OAAO;gBACP,QAAQ,IAAA,CAAK,SAAA,CAAU,MAAA;gBACvB,OAAO,MAAM,KAAA;gBACb,SACE,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,mBACzB,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU;YAC7B,CAAC;QACH,OAAO;YACL,IAAA,CAAK,WAAA,CAAY,sDAAsD;YACvE,IAAI,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,GAAG;gBAC5B,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU;YAC3B;YACA,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,YAAY;QAChE;QACA,IAAA,CAAK,gBAAA,CAAiB;IACxB;IAAA,uDAAA;IAAA,2DAAA;IAAA,0BAAA;IAKA,MAAc,eAAe;QAC3B,IAAI,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,UAAU;YACrC;QACF;QACA,IAAA,CAAK,WAAA,CAAY,uBAAuB;QACxC,MAAM,QAAQ,MAAM,IAAA,CAAK,6BAAA,CACvB,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,UAAA,EACtB;YACE,mBAAmB;QACrB;QAEF,IAAI,MAAM,oBAAA,EAAsB;YAC9B;QACF;QAEA,IAAI,MAAM,KAAA,EAAO;YACf,IAAI,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,MAAM,KAAK,GAAG;gBACzC,IAAA,CAAK,YAAA,CAAa;oBAChB,OAAO;oBACP,SAAS,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ;oBAChC,OAAO,MAAM,KAAA;oBACb,QAAQ,IAAA,CAAK,SAAA,CAAU,MAAA;gBACzB,CAAC;gBACD,IAAA,CAAK,YAAA,CAAa,MAAM,KAAK;YAC/B,OAAO;gBACL,IAAA,CAAK,YAAA,CAAa;oBAChB,OAAO;oBACP,QAAQ,IAAA,CAAK,SAAA,CAAU,MAAA;gBACzB,CAAC;YACH;QACF,OAAO;YACL,IAAA,CAAK,WAAA,CAAY,yBAAyB;YAC1C,IAAI,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,GAAG;gBAC5B,IAAA,CAAK,SAAA,CAAU;YACjB;YACA,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,YAAY;QAChE;QAGA,IAAA,CAAK,WAAA,CACH;QAEF,IAAA,CAAK,gBAAA,CAAiB;IACxB;IAEQ,qBAAqB,KAAA,EAAe;QAC1C,IAAI,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,UAAU;YACrC;QACF;QACA,MAAM,eAAe,IAAA,CAAK,WAAA,CAAY,KAAK;QAC3C,IAAI,CAAC,cAAc;YAIjB,IAAA,CAAK,MAAA,CAAO,KAAA,CACV;YAEF;QACF;QAGA,MAAM,EAAE,GAAA,EAAK,GAAA,CAAI,CAAA,GAAI;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK;YAChB,IAAA,CAAK,MAAA,CAAO,KAAA,CACV;YAEF;QACF;QAKA,MAAM,uBAAuB,MAAM;QACnC,IAAI,wBAAwB,GAAG;YAC7B,IAAA,CAAK,MAAA,CAAO,KAAA,CACV;YAEF;QACF;QAGA,IAAI,QAAQ,KAAK,GAAA,CACf,uBAAA,CACC,uBAAuB,IAAA,CAAK,yBAAA,IAA6B;QAE5D,IAAI,SAAS,GAAG;YAGd,IAAA,CAAK,MAAA,CAAO,IAAA,CACV,+DAAwD,IAAA,CAAK,yBAAyB,EAAA,0CAA6D,OAApB,oBAAoB,EAAA;YAErJ,QAAQ;QACV;QACA,MAAM,wBAAwB,WAAW,MAAM;YAC7C,IAAA,CAAK,WAAA,CAAY,iCAAiC;YAClD,KAAK,IAAA,CAAK,YAAA,CAAa;QACzB,GAAG,KAAK;QACR,IAAA,CAAK,YAAA,CAAa;YAChB,OAAO;YACP;YACA,QAAQ,IAAA,CAAK,SAAA,CAAU,MAAA;QACzB,CAAC;QACD,IAAA,CAAK,WAAA,CACH,iDAAsD,OAAL,KAAK,EAAA;IAE1D;IAAA,qDAAA;IAAA,+BAAA;IAIA,MAAc,8BACZ,UAAA,EACA,SAAA,EAGA;QACA,MAAM,wBAAwB,EAAE,IAAA,CAAK,aAAA;QACrC,IAAA,CAAK,WAAA,CACH,sCAA2D,OAArB,qBAAqB;QAE7D,MAAM,QAAQ,MAAM,WAAW,SAAS;QACxC,IAAI,IAAA,CAAK,aAAA,KAAkB,uBAAuB;YAEhD,IAAA,CAAK,WAAA,CACH,yCAAkC,qBAAqB,EAAA,UAA2B,OAAlB,IAAA,CAAK,aAAa;YAEpF,OAAO;gBAAE,sBAAsB;YAAK;QACtC;QACA,OAAO;YAAE,sBAAsB;YAAO,OAAO;QAAM;IACrD;IAEA,OAAO;QACL,IAAA,CAAK,cAAA,CAAe;QAEpB,IAAA,CAAK,aAAA;QACL,IAAA,CAAK,WAAA,CAAY,4BAA8C,CAAE,MAApB,IAAA,CAAK,aAAa;IACjE;IAEQ,uBACN,YAAA,EACA;QACA,aAAa,KAAK;QAClB,IAAA,CAAK,cAAA,CAAe;IACtB;IAEQ,iBAAiB;QACvB,IAAA,CAAK,YAAA,CAAa;YAAE,OAAO;QAAS,CAAC;IACvC;IAEQ,aAAa,OAAA,EAAoB;QACvC,MAAM,kBACJ,QAAQ,KAAA,KAAU,6CACd;YACE,SAAS,QAAQ,OAAA;YACjB,OAAO,QAAQ,KAAA;YACf,OAAO,MAA6B,OAAvB,QAAQ,KAAA,CAAM,KAAA,CAAM,CAAA,CAAE,CAAC;QACtC,IACA;YAAE,OAAO,QAAQ,KAAA;QAAM;QAC7B,IAAA,CAAK,WAAA,CACH,yBAAwD,OAA/B,KAAK,SAAA,CAAU,eAAe,CAAC;QAE1D,OAAQ,QAAQ,KAAA,EAAO;YACrB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAA,CAAK,yBAAA,GAA4B;gBACjC;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBAAS;oBACP;gBACF;QACF;QACA,IAAI,IAAA,CAAK,SAAA,CAAU,KAAA,KAAU,8BAA8B;YACzD,aAAa,IAAA,CAAK,SAAA,CAAU,qBAAqB;YAIjD,IAAA,CAAK,SAAA,CAAU,kBAAA,CAAmB;QACpC;QACA,IAAA,CAAK,SAAA,GAAY;IACnB;IAEQ,YAAY,KAAA,EAAe;QACjC,IAAI;YACF,WAAO,+NAAA,EAAU,KAAK;QACxB,EAAA,OAAS,GAAG;YACV,IAAA,CAAK,WAAA,CACH,yBAAyE,OAAhD,aAAa,QAAQ,EAAE,OAAA,GAAU,eAAe;YAE3E,OAAO;QACT;IACF;IAEQ,YAAY,OAAA,EAAiB;QACnC,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,UAAG,OAAO,EAAA,OAAwB,EAAG,KAArB,IAAA,CAAK,aAAa,EAAA;IAC3D;IA1XA,YACE,SAAA,EACA,SAAA,EAQA,MAAA,CAIA;QAjCF,cAAA,IAAA,EAAQ,aAAuB;YAAE,OAAO;QAAS;QAGjD,mDAAA;QAAA,kCAAA;QAAA,cAAA,IAAA,EAAQ,iBAAgB;QAExB,0EAAA;QAAA,cAAA,IAAA,EAAiB;QAEjB,2DAAA;QAAA,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QAEjB,2DAAA;QAAA,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QAGjB,6EAAA;QAAA,8CAAA;QAAA,cAAA,IAAA,EAAQ,6BAA4B;QAgBlC,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,YAAA,GAAe,UAAU,YAAA;QAC9B,IAAA,CAAK,UAAA,GAAa,UAAU,UAAA;QAC5B,IAAA,CAAK,gBAAA,GAAmB,UAAU,gBAAA;QAClC,IAAA,CAAK,WAAA,GAAc,UAAU,WAAA;QAC7B,IAAA,CAAK,YAAA,GAAe,UAAU,YAAA;QAC9B,IAAA,CAAK,SAAA,GAAY,UAAU,SAAA;QAC3B,IAAA,CAAK,MAAA,GAAS,OAAO,MAAA;QACrB,IAAA,CAAK,yBAAA,GAA4B,OAAO,yBAAA;IAC1C;AAmWF", "debugId": null}}, {"offset": {"line": 3507, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/metrics.ts"], "sourcesContent": ["// Marks share a global namespace with other developer code.\nconst markNames = [\n  \"convexClientConstructed\",\n  \"convexWebSocketOpen\",\n  \"convexFirstMessageReceived\",\n] as const;\nexport type MarkName = (typeof markNames)[number];\n\n// Mark details are not reported to the server.\ntype MarkDetail = {\n  sessionId: string;\n};\n\n// `PerformanceMark`s are efficient and show up in browser's performance\n// timeline. They can be cleared with `performance.clearMarks()`.\n// This is a memory leak, but a worthwhile one: automatic\n// cleanup would make in-browser debugging more difficult.\nexport function mark(name: MarkName, sessionId: string) {\n  const detail: MarkDetail = { sessionId };\n  // `performance` APIs exists in browsers, Node.js, Deno, and more but it\n  // is not required by the Convex client.\n  if (typeof performance === \"undefined\" || !performance.mark) return;\n  performance.mark(name, { detail });\n}\n\n// `PerfomanceMark` has a built-in toJSON() but the return type varies\n// between implementations, e.g. Node.js returns details but Chrome does not.\nfunction performanceMarkToJson(mark: PerformanceMark): Mark<PERSON><PERSON> {\n  // Remove \"convex\" prefix\n  let name = mark.name.slice(\"convex\".length);\n  // lowercase the first letter\n  name = name.charAt(0).toLowerCase() + name.slice(1);\n  return {\n    name,\n    startTime: mark.startTime,\n  };\n}\n\n// Similar to the return type of `PerformanceMark.toJson()`.\nexport type MarkJson = {\n  name: string;\n  // `startTime` is in milliseconds since the time origin like `performance.now()`.\n  // https://developer.mozilla.org/en-US/docs/Web/API/DOMHighResTimeStamp#the_time_origin\n  startTime: number;\n};\n\nexport function getMarksReport(sessionId: string): MarkJson[] {\n  if (typeof performance === \"undefined\" || !performance.getEntriesByName) {\n    return [];\n  }\n  const allMarks: PerformanceMark[] = [];\n  for (const name of markNames) {\n    const marks = (\n      performance\n        .getEntriesByName(name)\n        .filter((entry) => entry.entryType === \"mark\") as PerformanceMark[]\n    ).filter((mark) => mark.detail.sessionId === sessionId);\n    allMarks.push(...marks);\n  }\n  return allMarks.map(performanceMarkToJson);\n}\n"], "names": ["mark"], "mappings": ";;;;;;;AACA,MAAM,YAAY;IAChB;IACA;IACA;CACF;AAYO,SAAS,KAAK,IAAA,EAAgB,SAAA,EAAmB;IACtD,MAAM,SAAqB;QAAE;IAAU;IAGvC,IAAI,OAAO,gBAAgB,eAAe,CAAC,YAAY,IAAA,CAAM,CAAA;IAC7D,YAAY,IAAA,CAAK,MAAM;QAAE;IAAO,CAAC;AACnC;AAIA,SAAS,sBAAsBA,KAAAA,EAAiC;IAE9D,IAAI,OAAOA,MAAK,IAAA,CAAK,KAAA,CAAM,SAAS,MAAM;IAE1C,OAAO,KAAK,MAAA,CAAO,CAAC,EAAE,WAAA,CAAY,IAAI,KAAK,KAAA,CAAM,CAAC;IAClD,OAAO;QACL;QACA,WAAWA,MAAK,SAAA;IAClB;AACF;AAUO,SAAS,eAAe,SAAA,EAA+B;IAC5D,IAAI,OAAO,gBAAgB,eAAe,CAAC,YAAY,gBAAA,EAAkB;QACvE,OAAO,CAAC,CAAA;IACV;IACA,MAAM,WAA8B,CAAC,CAAA;IACrC,KAAA,MAAW,QAAQ,UAAW;QAC5B,MAAM,QACJ,YACG,gBAAA,CAAiB,IAAI,EACrB,MAAA,CAAO,CAAC,QAAU,MAAM,SAAA,KAAc,MAAM,EAC/C,MAAA,CAAO,CAACA,QAASA,MAAK,MAAA,CAAO,SAAA,KAAc,SAAS;QACtD,SAAS,IAAA,CAAK,GAAG,KAAK;IACxB;IACA,OAAO,SAAS,GAAA,CAAI,qBAAqB;AAC3C", "debugId": null}}, {"offset": {"line": 3551, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/sync/client.ts"], "sourcesContent": ["import { version } from \"../../index.js\";\nimport { convexToJson, Value } from \"../../values/index.js\";\nimport {\n  createHybridErrorStacktrace,\n  forwardData,\n  instantiateDefaultLogger,\n  instantiateNoopLogger,\n  logFatalError,\n  Logger,\n} from \"../logging.js\";\nimport { LocalSyncState } from \"./local_state.js\";\nimport { RequestManager } from \"./request_manager.js\";\nimport {\n  OptimisticLocalStore,\n  OptimisticUpdate,\n} from \"./optimistic_updates.js\";\nimport {\n  OptimisticQueryResults,\n  QueryResultsMap,\n} from \"./optimistic_updates_impl.js\";\nimport {\n  ActionRequest,\n  MutationRequest,\n  QueryId,\n  QueryJournal,\n  RequestId,\n  ServerMessage,\n  TS,\n  UserIdentityAttributes,\n} from \"./protocol.js\";\nimport { RemoteQuerySet } from \"./remote_query_set.js\";\nimport { QueryToken, serializePathAndArgs } from \"./udf_path_utils.js\";\nimport { ReconnectMetadata, WebSocketManager } from \"./web_socket_manager.js\";\nimport { newSessionId } from \"./session.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport {\n  AuthenticationManager,\n  AuthTokenFetcher,\n} from \"./authentication_manager.js\";\nexport { type AuthTokenFetcher } from \"./authentication_manager.js\";\nimport { getMarksReport, mark, MarkName } from \"./metrics.js\";\nimport { parseArgs, validateDeploymentUrl } from \"../../common/index.js\";\nimport { ConvexError } from \"../../values/errors.js\";\n\n/**\n * Options for {@link BaseConvexClient}.\n *\n * @public\n */\nexport interface BaseConvexClientOptions {\n  /**\n   * Whether to prompt the user if they have unsaved changes pending\n   * when navigating away or closing a web page.\n   *\n   * This is only possible when the `window` object exists, i.e. in a browser.\n   *\n   * The default value is `true` in browsers.\n   */\n  unsavedChangesWarning?: boolean;\n  /**\n   * Specifies an alternate\n   * [WebSocket](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)\n   * constructor to use for client communication with the Convex cloud.\n   * The default behavior is to use `WebSocket` from the global environment.\n   */\n  webSocketConstructor?: typeof WebSocket;\n  /**\n   * Adds additional logging for debugging purposes.\n   *\n   * The default value is `false`.\n   */\n  verbose?: boolean;\n  /**\n   * A logger, `true`, or `false`. If not provided or `true`, logs to the console.\n   * If `false`, logs are not printed anywhere.\n   *\n   * You can construct your own logger to customize logging to log elsewhere.\n   * A logger is an object with 4 methods: log(), warn(), error(), and logVerbose().\n   * These methods can receive multiple arguments of any types, like console.log().\n   */\n  logger?: Logger | boolean;\n  /**\n   * Sends additional metrics to Convex for debugging purposes.\n   *\n   * The default value is `false`.\n   */\n  reportDebugInfoToConvex?: boolean;\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * A function to call on receiving abnormal WebSocket close messages from the\n   * connected Convex deployment. The content of these messages is not stable,\n   * it is an implementation detail that may change.\n   *\n   * Consider this API an observability stopgap until higher level codes with\n   * recommendations on what to do are available, which could be a more stable\n   * interface instead of `string`.\n   *\n   * Check `connectionState` for more quantitative metrics about connection status.\n   */\n  onServerDisconnectError?: (message: string) => void;\n  /**\n   * Skip validating that the Convex deployment URL looks like\n   * `https://happy-animal-123.convex.cloud` or localhost.\n   *\n   * This can be useful if running a self-hosted Convex backend that uses a different\n   * URL.\n   *\n   * The default value is `false`\n   */\n  skipConvexDeploymentUrlCheck?: boolean;\n  /**\n   * If using auth, the number of seconds before a token expires that we should refresh it.\n   *\n   * The default value is `2`.\n   */\n  authRefreshTokenLeewaySeconds?: number;\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * Whether query, mutation, and action requests should be held back\n   * until the first auth token can be sent.\n   *\n   * Opting into this behavior works well for pages that should\n   * only be viewed by authenticated clients.\n   *\n   * Defaults to false, not waiting for an auth token.\n   */\n  expectAuth?: boolean;\n}\n\n/**\n * State describing the client's connection with the Convex backend.\n *\n * @public\n */\nexport type ConnectionState = {\n  hasInflightRequests: boolean;\n  isWebSocketConnected: boolean;\n  timeOfOldestInflightRequest: Date | null;\n  /**\n   * True if the client has ever opened a WebSocket to the \"ready\" state.\n   */\n  hasEverConnected: boolean;\n  /**\n   * The number of times this client has connected to the Convex backend.\n   *\n   * A number of things can cause the client to reconnect -- server errors,\n   * bad internet, auth expiring. But this number being high is an indication\n   * that the client is having trouble keeping a stable connection.\n   */\n  connectionCount: number;\n  /**\n   * The number of times this client has tried (and failed) to connect to the Convex backend.\n   */\n  connectionRetries: number;\n  /**\n   * The number of mutations currently in flight.\n   */\n  inflightMutations: number;\n  /**\n   * The number of actions currently in flight.\n   */\n  inflightActions: number;\n};\n\n/**\n * Options for {@link BaseConvexClient.subscribe}.\n *\n * @public\n */\nexport interface SubscribeOptions {\n  /**\n   * An (optional) journal produced from a previous execution of this query\n   * function.\n   *\n   * If there is an existing subscription to a query function with the same\n   * name and arguments, this journal will have no effect.\n   */\n  journal?: QueryJournal;\n\n  /**\n   * @internal\n   */\n  componentPath?: string;\n}\n\n/**\n * Options for {@link BaseConvexClient.mutation}.\n *\n * @public\n */\nexport interface MutationOptions {\n  /**\n   * An optimistic update to apply along with this mutation.\n   *\n   * An optimistic update locally updates queries while a mutation is pending.\n   * Once the mutation completes, the update will be rolled back.\n   */\n  optimisticUpdate?: OptimisticUpdate<any>;\n}\n\n/**\n * Type describing updates to a query within a `Transition`.\n *\n * @public\n */\nexport type QueryModification =\n  // `undefined` generally comes from an optimistic update setting the query to be loading\n  { kind: \"Updated\"; result: FunctionResult | undefined } | { kind: \"Removed\" };\n\n/**\n * Object describing a transition passed into the `onTransition` handler.\n *\n * These can be from receiving a transition from the server, or from applying an\n * optimistic update locally.\n *\n * @public\n */\nexport type Transition = {\n  queries: Array<{ token: QueryToken; modification: QueryModification }>;\n  reflectedMutations: Array<{ requestId: RequestId; result: FunctionResult }>;\n  timestamp: TS;\n};\n\n/**\n * Low-level client for directly integrating state management libraries\n * with Convex.\n *\n * Most developers should use higher level clients, like\n * the {@link ConvexHttpClient} or the React hook based {@link react.ConvexReactClient}.\n *\n * @public\n */\nexport class BaseConvexClient {\n  private readonly address: string;\n  private readonly state: LocalSyncState;\n  private readonly requestManager: RequestManager;\n  private readonly webSocketManager: WebSocketManager;\n  private readonly authenticationManager: AuthenticationManager;\n  private remoteQuerySet: RemoteQuerySet;\n  private readonly optimisticQueryResults: OptimisticQueryResults;\n  private _transitionHandlerCounter = 0;\n  private _nextRequestId: RequestId;\n  private _onTransitionFns: Map<number, (transition: Transition) => void> =\n    new Map();\n  private readonly _sessionId: string;\n  private firstMessageReceived = false;\n  private readonly debug: boolean;\n  private readonly logger: Logger;\n  private maxObservedTimestamp: TS | undefined;\n  private connectionStateSubscribers = new Map<\n    number,\n    (connectionState: ConnectionState) => void\n  >();\n  private nextConnectionStateSubscriberId: number = 0;\n  private _lastPublishedConnectionState: ConnectionState | undefined;\n\n  /**\n   * @param address - The url of your Convex deployment, often provided\n   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.\n   * @param onTransition - A callback receiving an array of query tokens\n   * corresponding to query results that have changed -- additional handlers\n   * can be added via `addOnTransitionHandler`.\n   * @param options - See {@link BaseConvexClientOptions} for a full description.\n   */\n  constructor(\n    address: string,\n    onTransition: (updatedQueries: QueryToken[]) => void,\n    options?: BaseConvexClientOptions,\n  ) {\n    if (typeof address === \"object\") {\n      throw new Error(\n        \"Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.\",\n      );\n    }\n    if (options?.skipConvexDeploymentUrlCheck !== true) {\n      validateDeploymentUrl(address);\n    }\n    options = { ...options };\n    const authRefreshTokenLeewaySeconds =\n      options.authRefreshTokenLeewaySeconds ?? 2;\n    let webSocketConstructor = options.webSocketConstructor;\n    if (!webSocketConstructor && typeof WebSocket === \"undefined\") {\n      throw new Error(\n        \"No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient\",\n      );\n    }\n    webSocketConstructor = webSocketConstructor || WebSocket;\n    this.debug = options.reportDebugInfoToConvex ?? false;\n    this.address = address;\n    this.logger =\n      options.logger === false\n        ? instantiateNoopLogger({ verbose: options.verbose ?? false })\n        : options.logger !== true && options.logger\n          ? options.logger\n          : instantiateDefaultLogger({ verbose: options.verbose ?? false });\n    // Substitute http(s) with ws(s)\n    const i = address.search(\"://\");\n    if (i === -1) {\n      throw new Error(\"Provided address was not an absolute URL.\");\n    }\n    const origin = address.substring(i + 3); // move past the double slash\n    const protocol = address.substring(0, i);\n    let wsProtocol;\n    if (protocol === \"http\") {\n      wsProtocol = \"ws\";\n    } else if (protocol === \"https\") {\n      wsProtocol = \"wss\";\n    } else {\n      throw new Error(`Unknown parent protocol ${protocol}`);\n    }\n    const wsUri = `${wsProtocol}://${origin}/api/${version}/sync`;\n\n    this.state = new LocalSyncState();\n    this.remoteQuerySet = new RemoteQuerySet(\n      (queryId) => this.state.queryPath(queryId),\n      this.logger,\n    );\n    this.requestManager = new RequestManager(\n      this.logger,\n      this.markConnectionStateDirty,\n    );\n\n    // This is a callback for AuthenticationManager (which can't call\n    // this synchronously, the callback wouldn't work) so the initial\n    // pause for expectAuth we call it at the end of this constructor.\n    const pauseSocket = () => {\n      this.webSocketManager.pause();\n      this.state.pause();\n    };\n    this.authenticationManager = new AuthenticationManager(\n      this.state,\n      {\n        authenticate: (token) => {\n          const message = this.state.setAuth(token);\n          this.webSocketManager.sendMessage(message);\n          return message.baseVersion;\n        },\n        stopSocket: () => this.webSocketManager.stop(),\n        tryRestartSocket: () => this.webSocketManager.tryRestart(),\n        pauseSocket,\n        resumeSocket: () => this.webSocketManager.resume(),\n        clearAuth: () => {\n          this.clearAuth();\n        },\n      },\n      {\n        logger: this.logger,\n        refreshTokenLeewaySeconds: authRefreshTokenLeewaySeconds,\n      },\n    );\n    this.optimisticQueryResults = new OptimisticQueryResults();\n    this.addOnTransitionHandler((transition) => {\n      onTransition(transition.queries.map((q) => q.token));\n    });\n    this._nextRequestId = 0;\n    this._sessionId = newSessionId();\n\n    const { unsavedChangesWarning } = options;\n    if (\n      typeof window === \"undefined\" ||\n      typeof window.addEventListener === \"undefined\"\n    ) {\n      if (unsavedChangesWarning === true) {\n        throw new Error(\n          \"unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.\",\n        );\n      }\n    } else if (unsavedChangesWarning !== false) {\n      // Listen for tab close events and notify the user on unsaved changes.\n      window.addEventListener(\"beforeunload\", (e) => {\n        if (this.requestManager.hasIncompleteRequests()) {\n          // There are 3 different ways to trigger this pop up so just try all of\n          // them.\n\n          e.preventDefault();\n          // This confirmation message doesn't actually appear in most modern\n          // browsers but we tried.\n          const confirmationMessage =\n            \"Are you sure you want to leave? Your changes may not be saved.\";\n          (e || window.event).returnValue = confirmationMessage;\n          return confirmationMessage;\n        }\n      });\n    }\n\n    this.webSocketManager = new WebSocketManager(\n      wsUri,\n      {\n        onOpen: (reconnectMetadata: ReconnectMetadata) => {\n          // We have a new WebSocket!\n          this.mark(\"convexWebSocketOpen\");\n          this.webSocketManager.sendMessage({\n            ...reconnectMetadata,\n            type: \"Connect\",\n            sessionId: this._sessionId,\n            maxObservedTimestamp: this.maxObservedTimestamp,\n          });\n\n          // Throw out our remote query, reissue queries\n          // and outstanding mutations, and reauthenticate.\n          const oldRemoteQueryResults = new Set(\n            this.remoteQuerySet.remoteQueryResults().keys(),\n          );\n          this.remoteQuerySet = new RemoteQuerySet(\n            (queryId) => this.state.queryPath(queryId),\n            this.logger,\n          );\n          const [querySetModification, authModification] = this.state.restart(\n            oldRemoteQueryResults,\n          );\n          if (authModification) {\n            this.webSocketManager.sendMessage(authModification);\n          }\n          this.webSocketManager.sendMessage(querySetModification);\n          for (const message of this.requestManager.restart()) {\n            this.webSocketManager.sendMessage(message);\n          }\n        },\n        onResume: () => {\n          const [querySetModification, authModification] = this.state.resume();\n          if (authModification) {\n            this.webSocketManager.sendMessage(authModification);\n          }\n          if (querySetModification) {\n            this.webSocketManager.sendMessage(querySetModification);\n          }\n          for (const message of this.requestManager.resume()) {\n            this.webSocketManager.sendMessage(message);\n          }\n        },\n        onMessage: (serverMessage: ServerMessage) => {\n          // Metrics events grow linearly with reconnection attempts so this\n          // conditional prevents n^2 metrics reporting.\n          if (!this.firstMessageReceived) {\n            this.firstMessageReceived = true;\n            this.mark(\"convexFirstMessageReceived\");\n            this.reportMarks();\n          }\n          switch (serverMessage.type) {\n            case \"Transition\": {\n              this.observedTimestamp(serverMessage.endVersion.ts);\n              this.authenticationManager.onTransition(serverMessage);\n              this.remoteQuerySet.transition(serverMessage);\n              this.state.transition(serverMessage);\n              const completedRequests = this.requestManager.removeCompleted(\n                this.remoteQuerySet.timestamp(),\n              );\n              this.notifyOnQueryResultChanges(completedRequests);\n              break;\n            }\n            case \"MutationResponse\": {\n              if (serverMessage.success) {\n                this.observedTimestamp(serverMessage.ts);\n              }\n              const completedMutationInfo =\n                this.requestManager.onResponse(serverMessage);\n              if (completedMutationInfo !== null) {\n                this.notifyOnQueryResultChanges(\n                  new Map([\n                    [\n                      completedMutationInfo.requestId,\n                      completedMutationInfo.result,\n                    ],\n                  ]),\n                );\n              }\n              break;\n            }\n            case \"ActionResponse\": {\n              this.requestManager.onResponse(serverMessage);\n              break;\n            }\n            case \"AuthError\": {\n              this.authenticationManager.onAuthError(serverMessage);\n              break;\n            }\n            case \"FatalError\": {\n              const error = logFatalError(this.logger, serverMessage.error);\n              void this.webSocketManager.terminate();\n              throw error;\n            }\n            case \"Ping\":\n              break; // do nothing\n            default: {\n              serverMessage satisfies never;\n            }\n          }\n\n          return {\n            hasSyncedPastLastReconnect: this.hasSyncedPastLastReconnect(),\n          };\n        },\n        onServerDisconnectError: options.onServerDisconnectError,\n      },\n      webSocketConstructor,\n      this.logger,\n      this.markConnectionStateDirty,\n    );\n    this.mark(\"convexClientConstructed\");\n\n    // Begin client in a paused state waiting for an auth token.\n    if (options.expectAuth) {\n      pauseSocket();\n    }\n  }\n\n  /**\n   * Return true if there is outstanding work from prior to the time of the most recent restart.\n   * This indicates that the client has not proven itself to have gotten past the issue that\n   * potentially led to the restart. Use this to influence when to reset backoff after a failure.\n   */\n  private hasSyncedPastLastReconnect() {\n    const hasSyncedPastLastReconnect =\n      this.requestManager.hasSyncedPastLastReconnect() ||\n      this.state.hasSyncedPastLastReconnect();\n    return hasSyncedPastLastReconnect;\n  }\n\n  private observedTimestamp(observedTs: TS) {\n    if (\n      this.maxObservedTimestamp === undefined ||\n      this.maxObservedTimestamp.lessThanOrEqual(observedTs)\n    ) {\n      this.maxObservedTimestamp = observedTs;\n    }\n  }\n\n  getMaxObservedTimestamp() {\n    return this.maxObservedTimestamp;\n  }\n\n  /**\n   * Compute the current query results based on the remoteQuerySet and the\n   * current optimistic updates and call `onTransition` for all the changed\n   * queries.\n   *\n   * @param completedMutations - A set of mutation IDs whose optimistic updates\n   * are no longer needed.\n   */\n  private notifyOnQueryResultChanges(\n    completedRequests: Map<RequestId, FunctionResult>,\n  ) {\n    const remoteQueryResults: Map<QueryId, FunctionResult> =\n      this.remoteQuerySet.remoteQueryResults();\n    const queryTokenToValue: QueryResultsMap = new Map();\n    for (const [queryId, result] of remoteQueryResults) {\n      const queryToken = this.state.queryToken(queryId);\n      // It's possible that we've already unsubscribed to this query but\n      // the server hasn't learned about that yet. If so, ignore this one.\n\n      if (queryToken !== null) {\n        const query = {\n          result,\n          udfPath: this.state.queryPath(queryId)!,\n          args: this.state.queryArgs(queryId)!,\n        };\n        queryTokenToValue.set(queryToken, query);\n      }\n    }\n\n    // Query tokens that are new (because of new server results or new local optimistic updates)\n    // or differ from old values (because of changes from local optimistic updates or new results\n    // from the server).\n    const changedQueryTokens =\n      this.optimisticQueryResults.ingestQueryResultsFromServer(\n        queryTokenToValue,\n        new Set(completedRequests.keys()),\n      );\n\n    this.handleTransition({\n      queries: changedQueryTokens.map((token) => {\n        const optimisticResult =\n          this.optimisticQueryResults.rawQueryResult(token);\n        return {\n          token,\n          modification: {\n            kind: \"Updated\",\n            result: optimisticResult!.result,\n          },\n        };\n      }),\n      reflectedMutations: Array.from(completedRequests).map(\n        ([requestId, result]) => ({\n          requestId,\n          result,\n        }),\n      ),\n      timestamp: this.remoteQuerySet.timestamp(),\n    });\n  }\n\n  private handleTransition(transition: Transition) {\n    for (const fn of this._onTransitionFns.values()) {\n      fn(transition);\n    }\n  }\n\n  /**\n   * Add a handler that will be called on a transition.\n   *\n   * Any external side effects (e.g. setting React state) should be handled here.\n   *\n   * @param fn\n   *\n   * @returns\n   */\n  addOnTransitionHandler(fn: (transition: Transition) => void) {\n    const id = this._transitionHandlerCounter++;\n    this._onTransitionFns.set(id, fn);\n    return () => this._onTransitionFns.delete(id);\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   * `fetchToken` will be called automatically again if a token expires.\n   * `fetchToken` should return `null` if the token cannot be retrieved, for example\n   * when the user's rights were permanently revoked.\n   * @param fetchToken - an async function returning the JWT-encoded OpenID Connect Identity Token\n   * @param onChange - a callback that will be called when the authentication status changes\n   */\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ) {\n    void this.authenticationManager.setConfig(fetchToken, onChange);\n  }\n\n  hasAuth() {\n    return this.state.hasAuth();\n  }\n\n  /** @internal */\n  setAdminAuth(value: string, fakeUserIdentity?: UserIdentityAttributes) {\n    const message = this.state.setAdminAuth(value, fakeUserIdentity);\n    this.webSocketManager.sendMessage(message);\n  }\n\n  clearAuth() {\n    const message = this.state.clearAuth();\n    this.webSocketManager.sendMessage(message);\n  }\n\n  /**\n   * Subscribe to a query function.\n   *\n   * Whenever this query's result changes, the `onTransition` callback\n   * passed into the constructor will be called.\n   *\n   * @param name - The name of the query.\n   * @param args - An arguments object for the query. If this is omitted, the\n   * arguments will be `{}`.\n   * @param options - A {@link SubscribeOptions} options object for this query.\n\n   * @returns An object containing a {@link QueryToken} corresponding to this\n   * query and an `unsubscribe` callback.\n   */\n  subscribe(\n    name: string,\n    args?: Record<string, Value>,\n    options?: SubscribeOptions,\n  ): { queryToken: QueryToken; unsubscribe: () => void } {\n    const argsObject = parseArgs(args);\n\n    const { modification, queryToken, unsubscribe } = this.state.subscribe(\n      name,\n      argsObject,\n      options?.journal,\n      options?.componentPath,\n    );\n    if (modification !== null) {\n      this.webSocketManager.sendMessage(modification);\n    }\n    return {\n      queryToken,\n      unsubscribe: () => {\n        const modification = unsubscribe();\n        if (modification) {\n          this.webSocketManager.sendMessage(modification);\n        }\n      },\n    };\n  }\n\n  /**\n   * A query result based only on the current, local state.\n   *\n   * The only way this will return a value is if we're already subscribed to the\n   * query or its value has been set optimistically.\n   */\n  localQueryResult(\n    udfPath: string,\n    args?: Record<string, Value>,\n  ): Value | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(udfPath, argsObject);\n    return this.optimisticQueryResults.queryResult(queryToken);\n  }\n\n  /**\n   * Get query result by query token based on current, local state\n   *\n   * The only way this will return a value is if we're already subscribed to the\n   * query or its value has been set optimistically.\n   *\n   * @internal\n   */\n  localQueryResultByToken(queryToken: QueryToken): Value | undefined {\n    return this.optimisticQueryResults.queryResult(queryToken);\n  }\n\n  /**\n   * Whether local query result is available for a toke.\n   *\n   * This method does not throw if the result is an error.\n   *\n   * @internal\n   */\n  hasLocalQueryResultByToken(queryToken: QueryToken): boolean {\n    return this.optimisticQueryResults.hasQueryResult(queryToken);\n  }\n\n  /**\n   * @internal\n   */\n  localQueryLogs(\n    udfPath: string,\n    args?: Record<string, Value>,\n  ): string[] | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(udfPath, argsObject);\n    return this.optimisticQueryResults.queryLogs(queryToken);\n  }\n\n  /**\n   * Retrieve the current {@link QueryJournal} for this query function.\n   *\n   * If we have not yet received a result for this query, this will be `undefined`.\n   *\n   * @param name - The name of the query.\n   * @param args - The arguments object for this query.\n   * @returns The query's {@link QueryJournal} or `undefined`.\n   */\n  queryJournal(\n    name: string,\n    args?: Record<string, Value>,\n  ): QueryJournal | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(name, argsObject);\n    return this.state.queryJournal(queryToken);\n  }\n\n  /**\n   * Get the current {@link ConnectionState} between the client and the Convex\n   * backend.\n   *\n   * @returns The {@link ConnectionState} with the Convex backend.\n   */\n  connectionState(): ConnectionState {\n    const wsConnectionState = this.webSocketManager.connectionState();\n    return {\n      hasInflightRequests: this.requestManager.hasInflightRequests(),\n      isWebSocketConnected: wsConnectionState.isConnected,\n      hasEverConnected: wsConnectionState.hasEverConnected,\n      connectionCount: wsConnectionState.connectionCount,\n      connectionRetries: wsConnectionState.connectionRetries,\n      timeOfOldestInflightRequest:\n        this.requestManager.timeOfOldestInflightRequest(),\n      inflightMutations: this.requestManager.inflightMutations(),\n      inflightActions: this.requestManager.inflightActions(),\n    };\n  }\n\n  /**\n   * Call this whenever the connection state may have changed in a way that could\n   * require publishing it. Schedules a possibly update.\n   */\n  private markConnectionStateDirty = () => {\n    void Promise.resolve().then(() => {\n      const curConnectionState = this.connectionState();\n      if (\n        JSON.stringify(curConnectionState) !==\n        JSON.stringify(this._lastPublishedConnectionState)\n      ) {\n        this._lastPublishedConnectionState = curConnectionState;\n        for (const cb of this.connectionStateSubscribers.values()) {\n          // One of these callback throwing will prevent other callbacks\n          // from running but will not leave the client in a undefined state.\n          cb(curConnectionState);\n        }\n      }\n    });\n  };\n\n  /**\n   * Subscribe to the {@link ConnectionState} between the client and the Convex\n   * backend, calling a callback each time it changes.\n   *\n   * Subscribed callbacks will be called when any part of ConnectionState changes.\n   * ConnectionState may grow in future versions (e.g. to provide a array of\n   * inflight requests) in which case callbacks would be called more frequently.\n   *\n   * @returns An unsubscribe function to stop listening.\n   */\n  subscribeToConnectionState(\n    cb: (connectionState: ConnectionState) => void,\n  ): () => void {\n    const id = this.nextConnectionStateSubscriberId++;\n    this.connectionStateSubscribers.set(id, cb);\n    return () => {\n      this.connectionStateSubscribers.delete(id);\n    };\n  }\n\n  /**\n   * Execute a mutation function.\n   *\n   * @param name - The name of the mutation.\n   * @param args - An arguments object for the mutation. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link MutationOptions} options object for this mutation.\n\n   * @returns - A promise of the mutation's result.\n   */\n  async mutation(\n    name: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n  ): Promise<any> {\n    const result = await this.mutationInternal(name, args, options);\n    if (!result.success) {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(\n            createHybridErrorStacktrace(\"mutation\", name, result),\n          ),\n        );\n      }\n      throw new Error(createHybridErrorStacktrace(\"mutation\", name, result));\n    }\n    return result.value;\n  }\n\n  /**\n   * @internal\n   */\n  async mutationInternal(\n    udfPath: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n    componentPath?: string,\n  ): Promise<FunctionResult> {\n    const { mutationPromise } = this.enqueueMutation(\n      udfPath,\n      args,\n      options,\n      componentPath,\n    );\n    return mutationPromise;\n  }\n\n  /**\n   * @internal\n   */\n  enqueueMutation(\n    udfPath: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n    componentPath?: string,\n  ): { requestId: RequestId; mutationPromise: Promise<FunctionResult> } {\n    const mutationArgs = parseArgs(args);\n    this.tryReportLongDisconnect();\n    const requestId = this.nextRequestId;\n    this._nextRequestId++;\n\n    if (options !== undefined) {\n      const optimisticUpdate = options.optimisticUpdate;\n      if (optimisticUpdate !== undefined) {\n        const wrappedUpdate = (localQueryStore: OptimisticLocalStore) => {\n          const result: unknown = optimisticUpdate(\n            localQueryStore,\n            mutationArgs,\n          );\n          if (result instanceof Promise) {\n            this.logger.warn(\n              \"Optimistic update handler returned a Promise. Optimistic updates should be synchronous.\",\n            );\n          }\n        };\n\n        const changedQueryTokens =\n          this.optimisticQueryResults.applyOptimisticUpdate(\n            wrappedUpdate,\n            requestId,\n          );\n\n        const changedQueries = changedQueryTokens.map((token) => {\n          const localResult = this.localQueryResultByToken(token);\n          return {\n            token,\n            modification: {\n              kind: \"Updated\" as const,\n              result:\n                localResult === undefined\n                  ? undefined\n                  : {\n                      success: true as const,\n                      value: localResult,\n                      logLines: [],\n                    },\n            },\n          };\n        });\n        this.handleTransition({\n          queries: changedQueries,\n          reflectedMutations: [],\n          timestamp: this.remoteQuerySet.timestamp(),\n        });\n      }\n    }\n\n    const message: MutationRequest = {\n      type: \"Mutation\",\n      requestId,\n      udfPath,\n      componentPath,\n      args: [convexToJson(mutationArgs)],\n    };\n    const mightBeSent = this.webSocketManager.sendMessage(message);\n    const mutationPromise = this.requestManager.request(message, mightBeSent);\n    return {\n      requestId,\n      mutationPromise,\n    };\n  }\n\n  /**\n   * Execute an action function.\n   *\n   * @param name - The name of the action.\n   * @param args - An arguments object for the action. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the action's result.\n   */\n  async action(name: string, args?: Record<string, Value>): Promise<any> {\n    const result = await this.actionInternal(name, args);\n    if (!result.success) {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(createHybridErrorStacktrace(\"action\", name, result)),\n        );\n      }\n      throw new Error(createHybridErrorStacktrace(\"action\", name, result));\n    }\n    return result.value;\n  }\n\n  /**\n   * @internal\n   */\n  async actionInternal(\n    udfPath: string,\n    args?: Record<string, Value>,\n    componentPath?: string,\n  ): Promise<FunctionResult> {\n    const actionArgs = parseArgs(args);\n    const requestId = this.nextRequestId;\n    this._nextRequestId++;\n    this.tryReportLongDisconnect();\n\n    const message: ActionRequest = {\n      type: \"Action\",\n      requestId,\n      udfPath,\n      componentPath,\n      args: [convexToJson(actionArgs)],\n    };\n\n    const mightBeSent = this.webSocketManager.sendMessage(message);\n    return this.requestManager.request(message, mightBeSent);\n  }\n\n  /**\n   * Close any network handles associated with this client and stop all subscriptions.\n   *\n   * Call this method when you're done with an {@link BaseConvexClient} to\n   * dispose of its sockets and resources.\n   *\n   * @returns A `Promise` fulfilled when the connection has been completely closed.\n   */\n  async close(): Promise<void> {\n    this.authenticationManager.stop();\n    return this.webSocketManager.terminate();\n  }\n\n  /**\n   * Return the address for this client, useful for creating a new client.\n   *\n   * Not guaranteed to match the address with which this client was constructed:\n   * it may be canonicalized.\n   */\n  get url() {\n    return this.address;\n  }\n\n  /**\n   * @internal\n   */\n  get nextRequestId() {\n    return this._nextRequestId;\n  }\n\n  /**\n   * @internal\n   */\n  get sessionId() {\n    return this._sessionId;\n  }\n\n  // Instance property so that `mark()` doesn't need to be called as a method.\n  private mark = (name: MarkName) => {\n    if (this.debug) {\n      mark(name, this.sessionId);\n    }\n  };\n\n  /**\n   * Reports performance marks to the server. This should only be called when\n   * we have a functional websocket.\n   */\n  private reportMarks() {\n    if (this.debug) {\n      const report = getMarksReport(this.sessionId);\n      this.webSocketManager.sendMessage({\n        type: \"Event\",\n        eventType: \"ClientConnect\",\n        event: report,\n      });\n    }\n  }\n\n  private tryReportLongDisconnect() {\n    if (!this.debug) {\n      return;\n    }\n    const timeOfOldestRequest =\n      this.connectionState().timeOfOldestInflightRequest;\n    if (\n      timeOfOldestRequest === null ||\n      Date.now() - timeOfOldestRequest.getTime() <= 60 * 1000\n    ) {\n      return;\n    }\n    const endpoint = `${this.address}/api/debug_event`;\n    fetch(endpoint, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Convex-Client\": `npm-${version}`,\n      },\n      body: JSON.stringify({ event: \"LongWebsocketDisconnect\" }),\n    })\n      .then((response) => {\n        if (!response.ok) {\n          this.logger.warn(\n            \"Analytics request failed with response:\",\n            response.body,\n          );\n        }\n      })\n      .catch((error) => {\n        this.logger.warn(\"Analytics response failed with error:\", error);\n      });\n  }\n}\n"], "names": ["modification"], "mappings": ";;;;AAAA,SAAS,eAAe;AACxB,SAAS,oBAA2B;;AACpC;AAQA,SAAS,sBAAsB;AAC/B,SAAS,sBAAsB;AAK/B;AAcA,SAAS,sBAAsB;AAC/B,SAAqB,4BAA4B;AACjD,SAA4B,wBAAwB;AACpD,SAAS,oBAAoB;AAE7B;AAKA,SAAS,gBAAgB,YAAsB;AAC/C,SAAS,WAAW,6BAA6B;AACjD,SAAS,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;AAgMrB,MAAM,iBAAiB;IAgR5B;;;;GAAA,GAOQ,6BAA6B;QACnC,MAAM,6BACJ,IAAA,CAAK,cAAA,CAAe,0BAAA,CAA2B,KAC/C,IAAA,CAAK,KAAA,CAAM,0BAAA,CAA2B;QACxC,OAAO;IACT;IAEQ,kBAAkB,UAAA,EAAgB;QACxC,IACE,IAAA,CAAK,oBAAA,KAAyB,KAAA,KAC9B,IAAA,CAAK,oBAAA,CAAqB,eAAA,CAAgB,UAAU,GACpD;YACA,IAAA,CAAK,oBAAA,GAAuB;QAC9B;IACF;IAEA,0BAA0B;QACxB,OAAO,IAAA,CAAK,oBAAA;IACd;IAAA;;;;;;;GAAA,GAUQ,2BACN,iBAAA,EACA;QACA,MAAM,qBACJ,IAAA,CAAK,cAAA,CAAe,kBAAA,CAAmB;QACzC,MAAM,oBAAqC,aAAA,GAAA,IAAI,IAAI;QACnD,KAAA,MAAW,CAAC,SAAS,MAAM,CAAA,IAAK,mBAAoB;YAClD,MAAM,aAAa,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,OAAO;YAIhD,IAAI,eAAe,MAAM;gBACvB,MAAM,QAAQ;oBACZ;oBACA,SAAS,IAAA,CAAK,KAAA,CAAM,SAAA,CAAU,OAAO;oBACrC,MAAM,IAAA,CAAK,KAAA,CAAM,SAAA,CAAU,OAAO;gBACpC;gBACA,kBAAkB,GAAA,CAAI,YAAY,KAAK;YACzC;QACF;QAKA,MAAM,qBACJ,IAAA,CAAK,sBAAA,CAAuB,4BAAA,CAC1B,mBACA,IAAI,IAAI,kBAAkB,IAAA,CAAK,CAAC;QAGpC,IAAA,CAAK,gBAAA,CAAiB;YACpB,SAAS,mBAAmB,GAAA,CAAI,CAAC,UAAU;gBACzC,MAAM,mBACJ,IAAA,CAAK,sBAAA,CAAuB,cAAA,CAAe,KAAK;gBAClD,OAAO;oBACL;oBACA,cAAc;wBACZ,MAAM;wBACN,QAAQ,iBAAkB,MAAA;oBAC5B;gBACF;YACF,CAAC;YACD,oBAAoB,MAAM,IAAA,CAAK,iBAAiB,EAAE,GAAA,CAChD;oBAAC,CAAC,WAAW,MAAM,CAAA;uBAAO;oBACxB;oBACA;gBACF;;YAEF,WAAW,IAAA,CAAK,cAAA,CAAe,SAAA,CAAU;QAC3C,CAAC;IACH;IAEQ,iBAAiB,UAAA,EAAwB;QAC/C,KAAA,MAAW,MAAM,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO,EAAG;YAC/C,GAAG,UAAU;QACf;IACF;IAAA;;;;;;;;GAAA,GAWA,uBAAuB,EAAA,EAAsC;QAC3D,MAAM,KAAK,IAAA,CAAK,yBAAA;QAChB,IAAA,CAAK,gBAAA,CAAiB,GAAA,CAAI,IAAI,EAAE;QAChC,OAAO,IAAM,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO,EAAE;IAC9C;IAAA;;;;;;;GAAA,GAUA,QACE,UAAA,EACA,QAAA,EACA;QACA,KAAK,IAAA,CAAK,qBAAA,CAAsB,SAAA,CAAU,YAAY,QAAQ;IAChE;IAEA,UAAU;QACR,OAAO,IAAA,CAAK,KAAA,CAAM,OAAA,CAAQ;IAC5B;IAAA,cAAA,GAGA,aAAa,KAAA,EAAe,gBAAA,EAA2C;QACrE,MAAM,UAAU,IAAA,CAAK,KAAA,CAAM,YAAA,CAAa,OAAO,gBAAgB;QAC/D,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,OAAO;IAC3C;IAEA,YAAY;QACV,MAAM,UAAU,IAAA,CAAK,KAAA,CAAM,SAAA,CAAU;QACrC,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,OAAO;IAC3C;IAAA;;;;;;;;;;;;;KAAA,GAgBA,UACE,IAAA,EACA,IAAA,EACA,OAAA,EACqD;QACrD,MAAM,iBAAa,+OAAA,EAAU,IAAI;QAEjC,MAAM,EAAE,YAAA,EAAc,UAAA,EAAY,WAAA,CAAY,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,SAAA,CAC3D,MACA,8DACA,QAAS,OAAA,oDACT,QAAS,aAAA;QAEX,IAAI,iBAAiB,MAAM;YACzB,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,YAAY;QAChD;QACA,OAAO;YACL;YACA,aAAa,MAAM;gBACjB,MAAMA,gBAAe,YAAY;gBACjC,IAAIA,eAAc;oBAChB,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAYA,aAAY;gBAChD;YACF;QACF;IACF;IAAA;;;;;GAAA,GAQA,iBACE,OAAA,EACA,IAAA,EACmB;QACnB,MAAM,iBAAa,+OAAA,EAAU,IAAI;QACjC,MAAM,iBAAa,4QAAA,EAAqB,SAAS,UAAU;QAC3D,OAAO,IAAA,CAAK,sBAAA,CAAuB,WAAA,CAAY,UAAU;IAC3D;IAAA;;;;;;;GAAA,GAUA,wBAAwB,UAAA,EAA2C;QACjE,OAAO,IAAA,CAAK,sBAAA,CAAuB,WAAA,CAAY,UAAU;IAC3D;IAAA;;;;;;GAAA,GASA,2BAA2B,UAAA,EAAiC;QAC1D,OAAO,IAAA,CAAK,sBAAA,CAAuB,cAAA,CAAe,UAAU;IAC9D;IAAA;;GAAA,GAKA,eACE,OAAA,EACA,IAAA,EACsB;QACtB,MAAM,iBAAa,+OAAA,EAAU,IAAI;QACjC,MAAM,iBAAa,4QAAA,EAAqB,SAAS,UAAU;QAC3D,OAAO,IAAA,CAAK,sBAAA,CAAuB,SAAA,CAAU,UAAU;IACzD;IAAA;;;;;;;;GAAA,GAWA,aACE,IAAA,EACA,IAAA,EAC0B;QAC1B,MAAM,iBAAa,+OAAA,EAAU,IAAI;QACjC,MAAM,aAAa,gRAAA,EAAqB,MAAM,UAAU;QACxD,OAAO,IAAA,CAAK,KAAA,CAAM,YAAA,CAAa,UAAU;IAC3C;IAAA;;;;;GAAA,GAQA,kBAAmC;QACjC,MAAM,oBAAoB,IAAA,CAAK,gBAAA,CAAiB,eAAA,CAAgB;QAChE,OAAO;YACL,qBAAqB,IAAA,CAAK,cAAA,CAAe,mBAAA,CAAoB;YAC7D,sBAAsB,kBAAkB,WAAA;YACxC,kBAAkB,kBAAkB,gBAAA;YACpC,iBAAiB,kBAAkB,eAAA;YACnC,mBAAmB,kBAAkB,iBAAA;YACrC,6BACE,IAAA,CAAK,cAAA,CAAe,2BAAA,CAA4B;YAClD,mBAAmB,IAAA,CAAK,cAAA,CAAe,iBAAA,CAAkB;YACzD,iBAAiB,IAAA,CAAK,cAAA,CAAe,eAAA,CAAgB;QACvD;IACF;IAAA;;;;;;;;;GAAA,GAiCA,2BACE,EAAA,EACY;QACZ,MAAM,KAAK,IAAA,CAAK,+BAAA;QAChB,IAAA,CAAK,0BAAA,CAA2B,GAAA,CAAI,IAAI,EAAE;QAC1C,OAAO,MAAM;YACX,IAAA,CAAK,0BAAA,CAA2B,MAAA,CAAO,EAAE;QAC3C;IACF;IAAA;;;;;;;;;KAAA,GAYA,MAAM,SACJ,IAAA,EACA,IAAA,EACA,OAAA,EACc;QACd,MAAM,SAAS,MAAM,IAAA,CAAK,gBAAA,CAAiB,MAAM,MAAM,OAAO;QAC9D,IAAI,CAAC,OAAO,OAAA,EAAS;YACnB,IAAI,OAAO,SAAA,KAAc,KAAA,GAAW;gBAClC,UAAM,oPAAA,EACJ,QACA,IAAI,kPAAA,KACF,oQAAA,EAA4B,YAAY,MAAM,MAAM;YAG1D;YACA,MAAM,IAAI,UAAM,oQAAA,EAA4B,YAAY,MAAM,MAAM,CAAC;QACvE;QACA,OAAO,OAAO,KAAA;IAChB;IAAA;;GAAA,GAKA,MAAM,iBACJ,OAAA,EACA,IAAA,EACA,OAAA,EACA,aAAA,EACyB;QACzB,MAAM,EAAE,eAAA,CAAgB,CAAA,GAAI,IAAA,CAAK,eAAA,CAC/B,SACA,MACA,SACA;QAEF,OAAO;IACT;IAAA;;GAAA,GAKA,gBACE,OAAA,EACA,IAAA,EACA,OAAA,EACA,aAAA,EACoE;QACpE,MAAM,mBAAe,+OAAA,EAAU,IAAI;QACnC,IAAA,CAAK,uBAAA,CAAwB;QAC7B,MAAM,YAAY,IAAA,CAAK,aAAA;QACvB,IAAA,CAAK,cAAA;QAEL,IAAI,YAAY,KAAA,GAAW;YACzB,MAAM,mBAAmB,QAAQ,gBAAA;YACjC,IAAI,qBAAqB,KAAA,GAAW;gBAClC,MAAM,gBAAgB,CAAC,oBAA0C;oBAC/D,MAAM,SAAkB,iBACtB,iBACA;oBAEF,IAAI,kBAAkB,SAAS;wBAC7B,IAAA,CAAK,MAAA,CAAO,IAAA,CACV;oBAEJ;gBACF;gBAEA,MAAM,qBACJ,IAAA,CAAK,sBAAA,CAAuB,qBAAA,CAC1B,eACA;gBAGJ,MAAM,iBAAiB,mBAAmB,GAAA,CAAI,CAAC,UAAU;oBACvD,MAAM,cAAc,IAAA,CAAK,uBAAA,CAAwB,KAAK;oBACtD,OAAO;wBACL;wBACA,cAAc;4BACZ,MAAM;4BACN,QACE,gBAAgB,KAAA,IACZ,KAAA,IACA;gCACE,SAAS;gCACT,OAAO;gCACP,UAAU,CAAC,CAAA;4BACb;wBACR;oBACF;gBACF,CAAC;gBACD,IAAA,CAAK,gBAAA,CAAiB;oBACpB,SAAS;oBACT,oBAAoB,CAAC,CAAA;oBACrB,WAAW,IAAA,CAAK,cAAA,CAAe,SAAA,CAAU;gBAC3C,CAAC;YACH;QACF;QAEA,MAAM,UAA2B;YAC/B,MAAM;YACN;YACA;YACA;YACA,MAAM;oBAAC,kPAAA,EAAa,YAAY,CAAC;aAAA;QACnC;QACA,MAAM,cAAc,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,OAAO;QAC7D,MAAM,kBAAkB,IAAA,CAAK,cAAA,CAAe,OAAA,CAAQ,SAAS,WAAW;QACxE,OAAO;YACL;YACA;QACF;IACF;IAAA;;;;;;;GAAA,GAUA,MAAM,OAAO,IAAA,EAAc,IAAA,EAA4C;QACrE,MAAM,SAAS,MAAM,IAAA,CAAK,cAAA,CAAe,MAAM,IAAI;QACnD,IAAI,CAAC,OAAO,OAAA,EAAS;YACnB,IAAI,OAAO,SAAA,KAAc,KAAA,GAAW;gBAClC,UAAM,oPAAA,EACJ,QACA,IAAI,kPAAA,KAAY,oQAAA,EAA4B,UAAU,MAAM,MAAM,CAAC;YAEvE;YACA,MAAM,IAAI,MAAM,wQAAA,EAA4B,UAAU,MAAM,MAAM,CAAC;QACrE;QACA,OAAO,OAAO,KAAA;IAChB;IAAA;;GAAA,GAKA,MAAM,eACJ,OAAA,EACA,IAAA,EACA,aAAA,EACyB;QACzB,MAAM,iBAAa,+OAAA,EAAU,IAAI;QACjC,MAAM,YAAY,IAAA,CAAK,aAAA;QACvB,IAAA,CAAK,cAAA;QACL,IAAA,CAAK,uBAAA,CAAwB;QAE7B,MAAM,UAAyB;YAC7B,MAAM;YACN;YACA;YACA;YACA,MAAM;oBAAC,kPAAA,EAAa,UAAU,CAAC;aAAA;QACjC;QAEA,MAAM,cAAc,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,OAAO;QAC7D,OAAO,IAAA,CAAK,cAAA,CAAe,OAAA,CAAQ,SAAS,WAAW;IACzD;IAAA;;;;;;;GAAA,GAUA,MAAM,QAAuB;QAC3B,IAAA,CAAK,qBAAA,CAAsB,IAAA,CAAK;QAChC,OAAO,IAAA,CAAK,gBAAA,CAAiB,SAAA,CAAU;IACzC;IAAA;;;;;GAAA,GAQA,IAAI,MAAM;QACR,OAAO,IAAA,CAAK,OAAA;IACd;IAAA;;GAAA,GAKA,IAAI,gBAAgB;QAClB,OAAO,IAAA,CAAK,cAAA;IACd;IAAA;;GAAA,GAKA,IAAI,YAAY;QACd,OAAO,IAAA,CAAK,UAAA;IACd;IAAA;;;GAAA,GAaQ,cAAc;QACpB,IAAI,IAAA,CAAK,KAAA,EAAO;YACd,MAAM,aAAS,+PAAA,EAAe,IAAA,CAAK,SAAS;YAC5C,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY;gBAChC,MAAM;gBACN,WAAW;gBACX,OAAO;YACT,CAAC;QACH;IACF;IAEQ,0BAA0B;QAChC,IAAI,CAAC,IAAA,CAAK,KAAA,EAAO;YACf;QACF;QACA,MAAM,sBACJ,IAAA,CAAK,eAAA,CAAgB,EAAE,2BAAA;QACzB,IACE,wBAAwB,QACxB,KAAK,GAAA,CAAI,IAAI,oBAAoB,OAAA,CAAQ,KAAK,KAAK,KACnD;YACA;QACF;QACA,MAAM,WAAW,GAAe,OAAZ,IAAA,CAAK,OAAO,EAAA;QAChC,MAAM,UAAU;YACd,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,OAAc,OAAP,mOAAO;YACjC;YACA,MAAM,KAAK,SAAA,CAAU;gBAAE,OAAO;YAA0B,CAAC;QAC3D,CAAC,EACE,IAAA,CAAK,CAAC,aAAa;YAClB,IAAI,CAAC,SAAS,EAAA,EAAI;gBAChB,IAAA,CAAK,MAAA,CAAO,IAAA,CACV,2CACA,SAAS,IAAA;YAEb;QACF,CAAC,EACA,KAAA,CAAM,CAAC,UAAU;YAChB,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,yCAAyC,KAAK;QACjE,CAAC;IACL;IA10B4B;;;;;;;GAAA,GAgC5B,YACE,OAAA,EACA,YAAA,EACA,OAAA,CACA;QAnCF,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAQ,6BAA4B;QACpC,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ,oBACN,aAAA,GAAA,IAAI,IAAI;QACV,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAQ,wBAAuB;QAC/B,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ,8BAA6B,aAAA,GAAA,IAAI,IAGvC;QACF,cAAA,IAAA,EAAQ,mCAA0C;QAClD,cAAA,IAAA,EAAQ;QA0gBR;;;KAAA,GAAA,cAAA,IAAA,EAAQ,4BAA2B,MAAM;YACvC,KAAK,QAAQ,OAAA,CAAQ,EAAE,IAAA,CAAK,MAAM;gBAChC,MAAM,qBAAqB,IAAA,CAAK,eAAA,CAAgB;gBAChD,IACE,KAAK,SAAA,CAAU,kBAAkB,MACjC,KAAK,SAAA,CAAU,IAAA,CAAK,6BAA6B,GACjD;oBACA,IAAA,CAAK,6BAAA,GAAgC;oBACrC,KAAA,MAAW,MAAM,IAAA,CAAK,0BAAA,CAA2B,MAAA,CAAO,EAAG;wBAGzD,GAAG,kBAAkB;oBACvB;gBACF;YACF,CAAC;QACH;QAsOA,4EAAA;QAAA,cAAA,IAAA,EAAQ,QAAO,CAAC,SAAmB;YACjC,IAAI,IAAA,CAAK,KAAA,EAAO;gBACd,IAAA,qPAAA,EAAK,MAAM,IAAA,CAAK,SAAS;YAC3B;QACF;QApvBE,IAAI,OAAO,YAAY,UAAU;YAC/B,MAAM,IAAI,MACR;QAEJ;QACA,KAAI,0DAAS,4BAAA,MAAiC,MAAM;YAClD,IAAA,2PAAA,EAAsB,OAAO;QAC/B;QACA,UAAU;YAAE,GAAG,OAAA;QAAQ;;QACvB,MAAM,kFACI,6BAAA,kEAAR,yCAAyC;QAC3C,IAAI,uBAAuB,QAAQ,oBAAA;QACnC,IAAI,CAAC,wBAAwB,OAAO,cAAc,aAAa;YAC7D,MAAM,IAAI,MACR;QAEJ;QACA,uBAAuB,wBAAwB;;QAC/C,IAAA,CAAK,KAAA,IAAQ,2CAAQ,uBAAA,+FAA2B;QAChD,IAAA,CAAK,OAAA,GAAU;8BAM6B;QAL5C,IAAA,CAAK,MAAA,GACH,QAAQ,MAAA,KAAW,YACf,8PAAA,EAAsB;YAAE,6BAAS,QAAQ,OAAA,+DAAW;QAAM,CAAC,IAC3D,QAAQ,MAAA,KAAW,QAAQ,QAAQ,MAAA,GACjC,QAAQ,MAAA,GACR,qQAAA,EAAyB;YAAE,sCAAiB,OAAA,iEAAW;QAAM,CAAC;QAEtE,MAAM,IAAI,QAAQ,MAAA,CAAO,KAAK;QAC9B,IAAI,MAAM,CAAA,GAAI;YACZ,MAAM,IAAI,MAAM,2CAA2C;QAC7D;QACA,MAAM,SAAS,QAAQ,SAAA,CAAU,IAAI,CAAC;QACtC,MAAM,WAAW,QAAQ,SAAA,CAAU,GAAG,CAAC;QACvC,IAAI;QACJ,IAAI,aAAa,QAAQ;YACvB,aAAa;QACf,OAAA,IAAW,aAAa,SAAS;YAC/B,aAAa;QACf,OAAO;YACL,MAAM,IAAI,MAAM,2BAAmC,CAAE,MAAV,QAAQ;QACrD;QACA,MAAM,QAAQ,UAAG,UAAU,EAAA,cAAM,MAAM,EAAA,SAAe,OAAP,mOAAO,EAAA;QAEtD,IAAA,CAAK,KAAA,GAAQ,IAAI,mQAAA,CAAe;QAChC,IAAA,CAAK,cAAA,GAAiB,IAAI,wQAAA,CACxB,CAAC,UAAY,IAAA,CAAK,KAAA,CAAM,SAAA,CAAU,OAAO,GACzC,IAAA,CAAK,MAAA;QAEP,IAAA,CAAK,cAAA,GAAiB,IAAI,uQAAA,CACxB,IAAA,CAAK,MAAA,EACL,IAAA,CAAK,wBAAA;QAMP,MAAM,cAAc,MAAM;YACxB,IAAA,CAAK,gBAAA,CAAiB,KAAA,CAAM;YAC5B,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM;QACnB;QACA,IAAA,CAAK,qBAAA,GAAwB,IAAI,qRAAA,CAC/B,IAAA,CAAK,KAAA,EACL;YACE,cAAc,CAAC,UAAU;gBACvB,MAAM,UAAU,IAAA,CAAK,KAAA,CAAM,OAAA,CAAQ,KAAK;gBACxC,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,OAAO;gBACzC,OAAO,QAAQ,WAAA;YACjB;YACA,YAAY,IAAM,IAAA,CAAK,gBAAA,CAAiB,IAAA,CAAK;YAC7C,kBAAkB,IAAM,IAAA,CAAK,gBAAA,CAAiB,UAAA,CAAW;YACzD;YACA,cAAc,IAAM,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YACjD,WAAW,MAAM;gBACf,IAAA,CAAK,SAAA,CAAU;YACjB;QACF,GACA;YACE,QAAQ,IAAA,CAAK,MAAA;YACb,2BAA2B;QAC7B;QAEF,IAAA,CAAK,sBAAA,GAAyB,IAAI,uRAAA,CAAuB;QACzD,IAAA,CAAK,sBAAA,CAAuB,CAAC,eAAe;YAC1C,aAAa,WAAW,OAAA,CAAQ,GAAA,CAAI,CAAC,IAAM,EAAE,KAAK,CAAC;QACrD,CAAC;QACD,IAAA,CAAK,cAAA,GAAiB;QACtB,IAAA,CAAK,UAAA,GAAa,iQAAA,CAAa;QAE/B,MAAM,EAAE,qBAAA,CAAsB,CAAA,GAAI;QAClC,IACE,OAAO,WAAW,eAClB,OAAO,OAAO,gBAAA,KAAqB,aACnC;YACA,IAAI,0BAA0B,MAAM;gBAClC,MAAM,IAAI,MACR;YAEJ;QACF,OAAA,IAAW,0BAA0B,OAAO;YAE1C,OAAO,gBAAA,CAAiB,gBAAgB,CAAC,MAAM;gBAC7C,IAAI,IAAA,CAAK,cAAA,CAAe,qBAAA,CAAsB,GAAG;oBAI/C,EAAE,cAAA,CAAe;oBAGjB,MAAM,sBACJ;oBACF,CAAC,KAAK,OAAO,KAAA,EAAO,WAAA,GAAc;oBAClC,OAAO;gBACT;YACF,CAAC;QACH;QAEA,IAAA,CAAK,gBAAA,GAAmB,IAAI,4QAAA,CAC1B,OACA;YACE,QAAQ,CAAC,sBAAyC;gBAEhD,IAAA,CAAK,IAAA,CAAK,qBAAqB;gBAC/B,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY;oBAChC,GAAG,iBAAA;oBACH,MAAM;oBACN,WAAW,IAAA,CAAK,UAAA;oBAChB,sBAAsB,IAAA,CAAK,oBAAA;gBAC7B,CAAC;gBAID,MAAM,wBAAwB,IAAI,IAChC,IAAA,CAAK,cAAA,CAAe,kBAAA,CAAmB,EAAE,IAAA,CAAK;gBAEhD,IAAA,CAAK,cAAA,GAAiB,IAAI,wQAAA,CACxB,CAAC,UAAY,IAAA,CAAK,KAAA,CAAM,SAAA,CAAU,OAAO,GACzC,IAAA,CAAK,MAAA;gBAEP,MAAM,CAAC,sBAAsB,gBAAgB,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,OAAA,CAC1D;gBAEF,IAAI,kBAAkB;oBACpB,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,gBAAgB;gBACpD;gBACA,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,oBAAoB;gBACtD,KAAA,MAAW,WAAW,IAAA,CAAK,cAAA,CAAe,OAAA,CAAQ,EAAG;oBACnD,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,OAAO;gBAC3C;YACF;YACA,UAAU,MAAM;gBACd,MAAM,CAAC,sBAAsB,gBAAgB,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO;gBACnE,IAAI,kBAAkB;oBACpB,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,gBAAgB;gBACpD;gBACA,IAAI,sBAAsB;oBACxB,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,oBAAoB;gBACxD;gBACA,KAAA,MAAW,WAAW,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,EAAG;oBAClD,IAAA,CAAK,gBAAA,CAAiB,WAAA,CAAY,OAAO;gBAC3C;YACF;YACA,WAAW,CAAC,kBAAiC;gBAG3C,IAAI,CAAC,IAAA,CAAK,oBAAA,EAAsB;oBAC9B,IAAA,CAAK,oBAAA,GAAuB;oBAC5B,IAAA,CAAK,IAAA,CAAK,4BAA4B;oBACtC,IAAA,CAAK,WAAA,CAAY;gBACnB;gBACA,OAAQ,cAAc,IAAA,EAAM;oBAC1B,KAAK;wBAAc;4BACjB,IAAA,CAAK,iBAAA,CAAkB,cAAc,UAAA,CAAW,EAAE;4BAClD,IAAA,CAAK,qBAAA,CAAsB,YAAA,CAAa,aAAa;4BACrD,IAAA,CAAK,cAAA,CAAe,UAAA,CAAW,aAAa;4BAC5C,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,aAAa;4BACnC,MAAM,oBAAoB,IAAA,CAAK,cAAA,CAAe,eAAA,CAC5C,IAAA,CAAK,cAAA,CAAe,SAAA,CAAU;4BAEhC,IAAA,CAAK,0BAAA,CAA2B,iBAAiB;4BACjD;wBACF;oBACA,KAAK;wBAAoB;4BACvB,IAAI,cAAc,OAAA,EAAS;gCACzB,IAAA,CAAK,iBAAA,CAAkB,cAAc,EAAE;4BACzC;4BACA,MAAM,wBACJ,IAAA,CAAK,cAAA,CAAe,UAAA,CAAW,aAAa;4BAC9C,IAAI,0BAA0B,MAAM;gCAClC,IAAA,CAAK,0BAAA,CACH,aAAA,GAAA,IAAI,IAAI;oCACN;wCACE,sBAAsB,SAAA;wCACtB,sBAAsB,MAAA;qCACxB;iCACD;4BAEL;4BACA;wBACF;oBACA,KAAK;wBAAkB;4BACrB,IAAA,CAAK,cAAA,CAAe,UAAA,CAAW,aAAa;4BAC5C;wBACF;oBACA,KAAK;wBAAa;4BAChB,IAAA,CAAK,qBAAA,CAAsB,WAAA,CAAY,aAAa;4BACpD;wBACF;oBACA,KAAK;wBAAc;4BACjB,MAAM,QAAQ,0PAAA,EAAc,IAAA,CAAK,MAAA,EAAQ,cAAc,KAAK;4BAC5D,KAAK,IAAA,CAAK,gBAAA,CAAiB,SAAA,CAAU;4BACrC,MAAM;wBACR;oBACA,KAAK;wBACH;oBAAA,aAAA;oBACF;wBAAS;4BACP;wBACF;gBACF;gBAEA,OAAO;oBACL,4BAA4B,IAAA,CAAK,0BAAA,CAA2B;gBAC9D;YACF;YACA,yBAAyB,QAAQ,uBAAA;QACnC,GACA,sBACA,IAAA,CAAK,MAAA,EACL,IAAA,CAAK,wBAAA;QAEP,IAAA,CAAK,IAAA,CAAK,yBAAyB;QAGnC,IAAI,QAAQ,UAAA,EAAY;YACtB,YAAY;QACd;IACF;AA2jBF", "debugId": null}}, {"offset": {"line": 4223, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/simple_client.ts"], "sourcesContent": ["import { validateDeploymentUrl } from \"../common/index.js\";\nimport {\n  BaseConvexClient,\n  BaseConvexClientOptions,\n  MutationOptions,\n  QueryToken,\n  UserIdentityAttributes,\n} from \"./index.js\";\nimport {\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n} from \"../server/index.js\";\nimport { getFunctionName } from \"../server/api.js\";\nimport { AuthTokenFetcher } from \"./sync/authentication_manager.js\";\nimport { ConnectionState } from \"./sync/client.js\";\n\n// In Node.js builds this points to a bundled WebSocket implementation. If no\n// WebSocket implementation is manually specified or globally available,\n// this one is used.\nlet defaultWebSocketConstructor: typeof WebSocket | undefined;\n\n/** internal */\nexport function setDefaultWebSocketConstructor(ws: typeof WebSocket) {\n  defaultWebSocketConstructor = ws;\n}\n\nexport type ConvexClientOptions = BaseConvexClientOptions & {\n  /**\n   * `disabled` makes onUpdate callback registration a no-op and actions,\n   * mutations and one-shot queries throw. Setting disabled to true may be\n   * useful for server-side rendering, where subscriptions don't make sense.\n   */\n  disabled?: boolean;\n  /**\n   * Whether to prompt users in browsers about queued or in-flight mutations.\n   * This only works in environments where `window.onbeforeunload` is available.\n   *\n   * Defaults to true when `window` is defined, otherwise false.\n   */\n  unsavedChangesWarning?: boolean;\n};\n\n/**\n * Stops callbacks from running.\n *\n * @public\n */\nexport type Unsubscribe<T> = {\n  /** Stop calling callback when query results changes. If this is the last listener on this query, stop received updates. */\n  (): void;\n  /** Stop calling callback when query results changes. If this is the last listener on this query, stop received updates. */\n  unsubscribe(): void;\n  /** Get the last known value, possibly with local optimistic updates applied. */\n  getCurrentValue(): T | undefined;\n  /** @internal */\n  getQueryLogs(): string[] | undefined;\n};\n\n/**\n * Subscribes to Convex query functions and executes mutations and actions over a WebSocket.\n *\n * Optimistic updates for mutations are not provided for this client.\n * Third party clients may choose to wrap {@link browser.BaseConvexClient} for additional control.\n *\n * ```ts\n * const client = new ConvexClient(\"https://happy-otter-123.convex.cloud\");\n * const unsubscribe = client.onUpdate(api.messages.list, {}, (messages) => {\n *   console.log(messages[0].body);\n * });\n * ```\n *\n * @public\n */\nexport class ConvexClient {\n  private listeners: Set<QueryInfo>;\n  private _client: BaseConvexClient | undefined;\n  // A synthetic server event to run callbacks the first time\n  private callNewListenersWithCurrentValuesTimer:\n    | ReturnType<typeof setTimeout>\n    | undefined;\n  private _closed: boolean;\n  private _disabled: boolean;\n  /**\n   * Once closed no registered callbacks will fire again.\n   */\n  get closed(): boolean {\n    return this._closed;\n  }\n  get client(): BaseConvexClient {\n    if (this._client) return this._client;\n    throw new Error(\"ConvexClient is disabled\");\n  }\n  get disabled(): boolean {\n    return this._disabled;\n  }\n\n  /**\n   * Construct a client and immediately initiate a WebSocket connection to the passed address.\n   *\n   * @public\n   */\n  constructor(address: string, options: ConvexClientOptions = {}) {\n    if (options.skipConvexDeploymentUrlCheck !== true) {\n      validateDeploymentUrl(address);\n    }\n    const { disabled, ...baseOptions } = options;\n    this._closed = false;\n    this._disabled = !!disabled;\n    if (\n      defaultWebSocketConstructor &&\n      !(\"webSocketConstructor\" in baseOptions) &&\n      typeof WebSocket === \"undefined\"\n    ) {\n      baseOptions.webSocketConstructor = defaultWebSocketConstructor;\n    }\n    if (\n      typeof window === \"undefined\" &&\n      !(\"unsavedChangesWarning\" in baseOptions)\n    ) {\n      baseOptions.unsavedChangesWarning = false;\n    }\n    if (!this.disabled) {\n      this._client = new BaseConvexClient(\n        address,\n        (updatedQueries) => this._transition(updatedQueries),\n        baseOptions,\n      );\n    }\n    this.listeners = new Set();\n  }\n\n  /**\n   * Call a callback whenever a new result for a query is received. The callback\n   * will run soon after being registered if a result for the query is already\n   * in memory.\n   *\n   * The return value is an {@link Unsubscribe} object which is both a function\n   * an an object with properties. Both of the patterns below work with this object:\n   *\n   *```ts\n   * // call the return value as a function\n   * const unsubscribe = client.onUpdate(api.messages.list, {}, (messages) => {\n   *   console.log(messages);\n   * });\n   * unsubscribe();\n   *\n   * // unpack the return value into its properties\n   * const {\n   *   getCurrentValue,\n   *   unsubscribe,\n   * } = client.onUpdate(api.messages.list, {}, (messages) => {\n   *   console.log(messages);\n   * });\n   *```\n   *\n   * @param query - A {@link server.FunctionReference} for the public query to run.\n   * @param args - The arguments to run the query with.\n   * @param callback - Function to call when the query result updates.\n   * @param onError - Function to call when the query result updates with an error.\n   * If not provided, errors will be thrown instead of calling the callback.\n   *\n   * @return an {@link Unsubscribe} function to stop calling the onUpdate function.\n   */\n  onUpdate<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    args: FunctionArgs<Query>,\n    callback: (result: FunctionReturnType<Query>) => unknown,\n    onError?: (e: Error) => unknown,\n  ): Unsubscribe<Query[\"_returnType\"]> {\n    if (this.disabled) {\n      const disabledUnsubscribe = (() => {}) as Unsubscribe<\n        Query[\"_returnType\"]\n      >;\n      const unsubscribeProps: RemoveCallSignature<\n        Unsubscribe<Query[\"_returnType\"]>\n      > = {\n        unsubscribe: disabledUnsubscribe,\n        getCurrentValue: () => undefined,\n        getQueryLogs: () => undefined,\n      };\n      Object.assign(disabledUnsubscribe, unsubscribeProps);\n      return disabledUnsubscribe;\n    }\n\n    // BaseConvexClient takes care of deduplicating queries subscriptions...\n    const { queryToken, unsubscribe } = this.client.subscribe(\n      getFunctionName(query),\n      args,\n    );\n\n    // ...but we still need to bookkeep callbacks to actually call them.\n    const queryInfo: QueryInfo = {\n      queryToken,\n      callback,\n      onError,\n      unsubscribe,\n      hasEverRun: false,\n      query,\n      args,\n    };\n    this.listeners.add(queryInfo);\n\n    // If the callback is registered for a query with a result immediately available\n    // schedule a fake transition to call the callback soon instead of waiting for\n    // a new server update (which could take seconds or days).\n    if (\n      this.queryResultReady(queryToken) &&\n      this.callNewListenersWithCurrentValuesTimer === undefined\n    ) {\n      this.callNewListenersWithCurrentValuesTimer = setTimeout(\n        () => this.callNewListenersWithCurrentValues(),\n        0,\n      );\n    }\n\n    const unsubscribeProps: RemoveCallSignature<\n      Unsubscribe<Query[\"_returnType\"]>\n    > = {\n      unsubscribe: () => {\n        if (this.closed) {\n          // all unsubscribes already ran\n          return;\n        }\n        this.listeners.delete(queryInfo);\n        unsubscribe();\n      },\n      getCurrentValue: () => this.client.localQueryResultByToken(queryToken),\n      getQueryLogs: () => this.client.localQueryLogs(queryToken),\n    };\n    const ret = unsubscribeProps.unsubscribe as Unsubscribe<\n      Query[\"_returnType\"]\n    >;\n    Object.assign(ret, unsubscribeProps);\n    return ret;\n  }\n\n  // Run all callbacks that have never been run before if they have a query\n  // result available now.\n  private callNewListenersWithCurrentValues() {\n    this.callNewListenersWithCurrentValuesTimer = undefined;\n    this._transition([], true);\n  }\n\n  private queryResultReady(queryToken: QueryToken): boolean {\n    return this.client.hasLocalQueryResultByToken(queryToken);\n  }\n\n  async close() {\n    if (this.disabled) return;\n    // prevent pending updates\n    this.listeners.clear();\n    this._closed = true;\n    return this.client.close();\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   * `fetchToken` will be called automatically again if a token expires.\n   * `fetchToken` should return `null` if the token cannot be retrieved, for example\n   * when the user's rights were permanently revoked.\n   * @param fetchToken - an async function returning the JWT (typically an OpenID Connect Identity Token)\n   * @param onChange - a callback that will be called when the authentication status changes\n   */\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange?: (isAuthenticated: boolean) => void,\n  ) {\n    if (this.disabled) return;\n    this.client.setAuth(\n      fetchToken,\n      onChange ??\n        (() => {\n          // Do nothing\n        }),\n    );\n  }\n\n  /**\n   * @internal\n   */\n  setAdminAuth(token: string, identity?: UserIdentityAttributes) {\n    if (this.closed) {\n      throw new Error(\"ConvexClient has already been closed.\");\n    }\n    if (this.disabled) return;\n    this.client.setAdminAuth(token, identity);\n  }\n\n  /**\n   * @internal\n   */\n  _transition(updatedQueries: QueryToken[], callNewListeners = false) {\n    // Deduping subscriptions happens in the BaseConvexClient, so not much to do here.\n\n    // Call all callbacks in the order they were registered\n    for (const queryInfo of this.listeners) {\n      const { callback, queryToken, onError, hasEverRun } = queryInfo;\n      if (\n        updatedQueries.includes(queryToken) ||\n        (callNewListeners &&\n          !hasEverRun &&\n          this.client.hasLocalQueryResultByToken(queryToken))\n      ) {\n        queryInfo.hasEverRun = true;\n        let newValue;\n        try {\n          newValue = this.client.localQueryResultByToken(queryToken);\n        } catch (error) {\n          if (!(error instanceof Error)) throw error;\n          if (onError) {\n            onError(\n              error,\n              \"Second argument to onUpdate onError is reserved for later use\",\n            );\n          } else {\n            // Make some noise without unsubscribing or failing to call other callbacks.\n            void Promise.reject(error);\n          }\n          continue;\n        }\n        callback(\n          newValue,\n          \"Second argument to onUpdate callback is reserved for later use\",\n        );\n      }\n    }\n  }\n\n  /**\n   * Execute a mutation function.\n   *\n   * @param mutation - A {@link server.FunctionReference} for the public mutation\n   * to run.\n   * @param args - An arguments object for the mutation.\n   * @param options - A {@link MutationOptions} options object for the mutation.\n   * @returns A promise of the mutation's result.\n   */\n  async mutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    args: FunctionArgs<Mutation>,\n    options?: MutationOptions,\n  ): Promise<Awaited<FunctionReturnType<Mutation>>> {\n    if (this.disabled) throw new Error(\"ConvexClient is disabled\");\n    return await this.client.mutation(getFunctionName(mutation), args, options);\n  }\n\n  /**\n   * Execute an action function.\n   *\n   * @param action - A {@link server.FunctionReference} for the public action\n   * to run.\n   * @param args - An arguments object for the action.\n   * @returns A promise of the action's result.\n   */\n  async action<Action extends FunctionReference<\"action\">>(\n    action: Action,\n    args: FunctionArgs<Action>,\n  ): Promise<Awaited<FunctionReturnType<Action>>> {\n    if (this.disabled) throw new Error(\"ConvexClient is disabled\");\n    return await this.client.action(getFunctionName(action), args);\n  }\n\n  /**\n   * Fetch a query result once.\n   *\n   * @param query - A {@link server.FunctionReference} for the public query\n   * to run.\n   * @param args - An arguments object for the query.\n   * @returns A promise of the query's result.\n   */\n  async query<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    args: Query[\"_args\"],\n  ): Promise<Awaited<Query[\"_returnType\"]>> {\n    if (this.disabled) throw new Error(\"ConvexClient is disabled\");\n    const value = this.client.localQueryResult(getFunctionName(query), args) as\n      | Awaited<Query[\"_returnType\"]>\n      | undefined;\n    if (value !== undefined) return Promise.resolve(value);\n\n    return new Promise((resolve, reject) => {\n      const { unsubscribe } = this.onUpdate(\n        query,\n        args,\n        (value) => {\n          unsubscribe();\n          resolve(value);\n        },\n        (e: Error) => {\n          unsubscribe();\n          reject(e);\n        },\n      );\n    });\n  }\n\n  /**\n   * Get the current {@link ConnectionState} between the client and the Convex\n   * backend.\n   *\n   * @returns The {@link ConnectionState} with the Convex backend.\n   */\n  connectionState(): ConnectionState {\n    if (this.disabled) throw new Error(\"ConvexClient is disabled\");\n    return this.client.connectionState();\n  }\n\n  /**\n   * Subscribe to the {@link ConnectionState} between the client and the Convex\n   * backend, calling a callback each time it changes.\n   *\n   * Subscribed callbacks will be called when any part of ConnectionState changes.\n   * ConnectionState may grow in future versions (e.g. to provide a array of\n   * inflight requests) in which case callbacks would be called more frequently.\n   *\n   * @returns An unsubscribe function to stop listening.\n   */\n  subscribeToConnectionState(\n    cb: (connectionState: ConnectionState) => void,\n  ): () => void {\n    if (this.disabled) return () => {};\n    return this.client.subscribeToConnectionState(cb);\n  }\n}\n\n// internal information tracked about each registered callback\ntype QueryInfo = {\n  callback: (result: any, meta: unknown) => unknown;\n  onError: ((e: Error, meta: unknown) => unknown) | undefined;\n  unsubscribe: () => void;\n  queryToken: QueryToken;\n  hasEverRun: boolean;\n  // query and args are just here for debugging, the queryToken is authoritative\n  query: FunctionReference<\"query\">;\n  args: any;\n};\n\ntype RemoveCallSignature<T> = Omit<T, never>;\n"], "names": ["unsubscribeProps", "value"], "mappings": ";;;;;;AAAA,SAAS,6BAA6B;AACtC;;AAYA,SAAS,uBAAuB;;;;;;;;;;;;;AAOhC,IAAI;AAGG,SAAS,+BAA+B,EAAA,EAAsB;IACnE,8BAA8B;AAChC;AAiDO,MAAM,aAAa;IAwDxB;;GAAA,GA5CA,IAAI,SAAkB;QACpB,OAAO,IAAA,CAAK,OAAA;IACd;IACA,IAAI,SAA2B;QAC7B,IAAI,IAAA,CAAK,OAAA,CAAS,CAAA,OAAO,IAAA,CAAK,OAAA;QAC9B,MAAM,IAAI,MAAM,0BAA0B;IAC5C;IACA,IAAI,WAAoB;QACtB,OAAO,IAAA,CAAK,SAAA;IACd;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GAqEA,SACE,KAAA,EACA,IAAA,EACA,QAAA,EACA,OAAA,EACmC;QACnC,IAAI,IAAA,CAAK,QAAA,EAAU;YACjB,MAAM,sBAAuB,KAAO,CAAD;YAGnC,MAAMA,oBAEF;gBACF,aAAa;gBACb,iBAAiB,IAAM,KAAA;gBACvB,cAAc,IAAM,KAAA;YACtB;YACA,OAAO,MAAA,CAAO,qBAAqBA,iBAAgB;YACnD,OAAO;QACT;QAGA,MAAM,EAAE,UAAA,EAAY,WAAA,CAAY,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO,SAAA,KAC9C,mPAAA,EAAgB,KAAK,GACrB;QAIF,MAAM,YAAuB;YAC3B;YACA;YACA;YACA;YACA,YAAY;YACZ;YACA;QACF;QACA,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,SAAS;QAK5B,IACE,IAAA,CAAK,gBAAA,CAAiB,UAAU,KAChC,IAAA,CAAK,sCAAA,KAA2C,KAAA,GAChD;YACA,IAAA,CAAK,sCAAA,GAAyC,WAC5C,IAAM,IAAA,CAAK,iCAAA,CAAkC,GAC7C;QAEJ;QAEA,MAAM,mBAEF;YACF,aAAa,MAAM;gBACjB,IAAI,IAAA,CAAK,MAAA,EAAQ;oBAEf;gBACF;gBACA,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,SAAS;gBAC/B,YAAY;YACd;YACA,iBAAiB,IAAM,IAAA,CAAK,MAAA,CAAO,uBAAA,CAAwB,UAAU;YACrE,cAAc,IAAM,IAAA,CAAK,MAAA,CAAO,cAAA,CAAe,UAAU;QAC3D;QACA,MAAM,MAAM,iBAAiB,WAAA;QAG7B,OAAO,MAAA,CAAO,KAAK,gBAAgB;QACnC,OAAO;IACT;IAAA,yEAAA;IAAA,wBAAA;IAIQ,oCAAoC;QAC1C,IAAA,CAAK,sCAAA,GAAyC,KAAA;QAC9C,IAAA,CAAK,WAAA,CAAY,CAAC,CAAA,EAAG,IAAI;IAC3B;IAEQ,iBAAiB,UAAA,EAAiC;QACxD,OAAO,IAAA,CAAK,MAAA,CAAO,0BAAA,CAA2B,UAAU;IAC1D;IAEA,MAAM,QAAQ;QACZ,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA;QAEnB,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM;QACrB,IAAA,CAAK,OAAA,GAAU;QACf,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM;IAC3B;IAAA;;;;;;;GAAA,GAUA,QACE,UAAA,EACA,QAAA,EACA;QACA,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA;QACnB,IAAA,CAAK,MAAA,CAAO,OAAA,CACV,YACA,sDACG,KAED,CAFO;IAIb;IAAA;;GAAA,GAKA,aAAa,KAAA,EAAe,QAAA,EAAmC;QAC7D,IAAI,IAAA,CAAK,MAAA,EAAQ;YACf,MAAM,IAAI,MAAM,uCAAuC;QACzD;QACA,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA;QACnB,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,OAAO,QAAQ;IAC1C;IAAA;;GAAA,GAKA,YAAY,cAAA,EAAwD;+BAA1B,iEAAmB;QAI3D,KAAA,MAAW,aAAa,IAAA,CAAK,SAAA,CAAW;YACtC,MAAM,EAAE,QAAA,EAAU,UAAA,EAAY,OAAA,EAAS,UAAA,CAAW,CAAA,GAAI;YACtD,IACE,eAAe,QAAA,CAAS,UAAU,KACjC,oBACC,CAAC,cACD,IAAA,CAAK,MAAA,CAAO,0BAAA,CAA2B,UAAU,GACnD;gBACA,UAAU,UAAA,GAAa;gBACvB,IAAI;gBACJ,IAAI;oBACF,WAAW,IAAA,CAAK,MAAA,CAAO,uBAAA,CAAwB,UAAU;gBAC3D,EAAA,OAAS,OAAO;oBACd,IAAI,CAAA,CAAE,iBAAiB,KAAA,EAAQ,CAAA,MAAM;oBACrC,IAAI,SAAS;wBACX,QACE,OACA;oBAEJ,OAAO;wBAEL,KAAK,QAAQ,MAAA,CAAO,KAAK;oBAC3B;oBACA;gBACF;gBACA,SACE,UACA;YAEJ;QACF;IACF;IAAA;;;;;;;;GAAA,GAWA,MAAM,SACJ,QAAA,EACA,IAAA,EACA,OAAA,EACgD;QAChD,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA,MAAM,IAAI,MAAM,0BAA0B;QAC7D,OAAO,MAAM,IAAA,CAAK,MAAA,CAAO,QAAA,KAAS,mPAAA,EAAgB,QAAQ,GAAG,MAAM,OAAO;IAC5E;IAAA;;;;;;;GAAA,GAUA,MAAM,OACJ,MAAA,EACA,IAAA,EAC8C;QAC9C,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA,MAAM,IAAI,MAAM,0BAA0B;QAC7D,OAAO,MAAM,IAAA,CAAK,MAAA,CAAO,MAAA,KAAO,mPAAA,EAAgB,MAAM,GAAG,IAAI;IAC/D;IAAA;;;;;;;GAAA,GAUA,MAAM,MACJ,KAAA,EACA,IAAA,EACwC;QACxC,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA,MAAM,IAAI,MAAM,0BAA0B;QAC7D,MAAM,QAAQ,IAAA,CAAK,MAAA,CAAO,gBAAA,KAAiB,mPAAA,EAAgB,KAAK,GAAG,IAAI;QAGvE,IAAI,UAAU,KAAA,EAAW,CAAA,OAAO,QAAQ,OAAA,CAAQ,KAAK;QAErD,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;YACtC,MAAM,EAAE,WAAA,CAAY,CAAA,GAAI,IAAA,CAAK,QAAA,CAC3B,OACA,MACA,CAACC,WAAU;gBACT,YAAY;gBACZ,QAAQA,MAAK;YACf,GACA,CAAC,MAAa;gBACZ,YAAY;gBACZ,OAAO,CAAC;YACV;QAEJ,CAAC;IACH;IAAA;;;;;GAAA,GAQA,kBAAmC;QACjC,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA,MAAM,IAAI,MAAM,0BAA0B;QAC7D,OAAO,IAAA,CAAK,MAAA,CAAO,eAAA,CAAgB;IACrC;IAAA;;;;;;;;;GAAA,GAYA,2BACE,EAAA,EACY;QACZ,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA,OAAO,KAAO,CAAD;QAChC,OAAO,IAAA,CAAK,MAAA,CAAO,0BAAA,CAA2B,EAAE;IAClD;IA7VwB;;;;GAAA,GA4BxB,YAAY,OAAA,EAAiB,UAA+B,CAAC,CAAA,CAAG;QA3BhE,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAER,2DAAA;QAAA,cAAA,IAAA,EAAQ;QAGR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAqBN,IAAI,QAAQ,4BAAA,KAAiC,MAAM;YACjD,IAAA,2PAAA,EAAsB,OAAO;QAC/B;QACA,MAAM,EAAE,QAAA,EAAU,GAAG,YAAY,CAAA,GAAI;QACrC,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,SAAA,GAAY,CAAC,CAAC;QACnB,IACE,+BACA,CAAA,CAAE,0BAA0B,WAAA,KAC5B,OAAO,cAAc,aACrB;YACA,YAAY,oBAAA,GAAuB;QACrC;QACA,IACE,OAAO,WAAW,eAClB,CAAA,CAAE,2BAA2B,WAAA,GAC7B;YACA,YAAY,qBAAA,GAAwB;QACtC;QACA,IAAI,CAAC,IAAA,CAAK,QAAA,EAAU;YAClB,IAAA,CAAK,OAAA,GAAU,IAAI,gQAAA,CACjB,SACA,CAAC,iBAAmB,IAAA,CAAK,WAAA,CAAY,cAAc,GACnD;QAEJ;QACA,IAAA,CAAK,SAAA,GAAY,aAAA,GAAA,IAAI,IAAI;IAC3B;AAsSF", "debugId": null}}, {"offset": {"line": 4491, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/http_client.ts"], "sourcesContent": ["import {\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  getFunctionName,\n} from \"../server/api.js\";\nimport { parseArgs, validateDeploymentUrl } from \"../common/index.js\";\nimport { version } from \"../index.js\";\nimport {\n  ConvexError,\n  JSONValue,\n  convexToJson,\n  jsonToConvex,\n} from \"../values/index.js\";\nimport {\n  instantiateDefaultLogger,\n  instantiateNoopLogger,\n  logForFunction,\n  Logger,\n} from \"./logging.js\";\nimport {\n  ArgsAndOptions,\n  FunctionArgs,\n  UserIdentityAttributes,\n} from \"../server/index.js\";\n\nexport const STATUS_CODE_OK = 200;\nexport const STATUS_CODE_BAD_REQUEST = 400;\n// Special custom 5xx HTTP status code to mean that the UDF returned an error.\n//\n// Must match the constant of the same name in the backend.\nexport const STATUS_CODE_UDF_FAILED = 560;\n\n// Allow fetch to be shimmed in for Node.js < 18\nlet specifiedFetch: typeof globalThis.fetch | undefined = undefined;\nexport function setFetch(f: typeof globalThis.fetch) {\n  specifiedFetch = f;\n}\n\nexport type HttpMutationOptions = {\n  /**\n   * Skip the default queue of mutations and run this immediately.\n   *\n   * This allows the same HttpConvexClient to be used to request multiple\n   * mutations in parallel, something not possible with WebSocket-based clients.\n   */\n  skipQueue: boolean;\n};\n\n/**\n * A Convex client that runs queries and mutations over HTTP.\n *\n * This client is stateful (it has user credentials and queues mutations)\n * so take care to avoid sharing it between requests in a server.\n *\n * This is appropriate for server-side code (like Netlify Lambdas) or non-reactive\n * webapps.\n *\n * @public\n */\nexport class ConvexHttpClient {\n  private readonly address: string;\n  private auth?: string;\n  private adminAuth?: string;\n  private encodedTsPromise?: Promise<string>;\n  private debug: boolean;\n  private fetchOptions?: FetchOptions;\n  private logger: Logger;\n  private mutationQueue: Array<{\n    mutation: FunctionReference<\"mutation\">;\n    args: FunctionArgs<any>;\n    resolve: (value: any) => void;\n    reject: (error: any) => void;\n  }> = [];\n  private isProcessingQueue: boolean = false;\n\n  /**\n   * Create a new {@link ConvexHttpClient}.\n   *\n   * @param address - The url of your Convex deployment, often provided\n   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.\n   * @param options - An object of options.\n   * - `skipConvexDeploymentUrlCheck` - Skip validating that the Convex deployment URL looks like\n   * `https://happy-animal-123.convex.cloud` or localhost. This can be useful if running a self-hosted\n   * Convex backend that uses a different URL.\n   * - `logger` - A logger or a boolean. If not provided, logs to the console.\n   * You can construct your own logger to customize logging to log elsewhere\n   * or not log at all, or use `false` as a shorthand for a no-op logger.\n   * A logger is an object with 4 methods: log(), warn(), error(), and logVerbose().\n   * These methods can receive multiple arguments of any types, like console.log().\n   * - `auth` - A JWT containing identity claims accessible in Convex functions.\n   * This identity may expire so it may be necessary to call `setAuth()` later,\n   * but for short-lived clients it's convenient to specify this value here.\n   */\n  constructor(\n    address: string,\n    options?: {\n      skipConvexDeploymentUrlCheck?: boolean;\n      logger?: Logger | boolean;\n      auth?: string;\n    },\n  ) {\n    if (typeof options === \"boolean\") {\n      throw new Error(\n        \"skipConvexDeploymentUrlCheck as the second argument is no longer supported. Please pass an options object, `{ skipConvexDeploymentUrlCheck: true }`.\",\n      );\n    }\n    const opts = options ?? {};\n    if (opts.skipConvexDeploymentUrlCheck !== true) {\n      validateDeploymentUrl(address);\n    }\n    this.logger =\n      options?.logger === false\n        ? instantiateNoopLogger({ verbose: false })\n        : options?.logger !== true && options?.logger\n          ? options.logger\n          : instantiateDefaultLogger({ verbose: false });\n    this.address = address;\n    this.debug = true;\n    if (options?.auth) {\n      this.setAuth(options.auth);\n    }\n  }\n\n  /**\n   * Obtain the {@link ConvexHttpClient}'s URL to its backend.\n   * @deprecated Use url, which returns the url without /api at the end.\n   *\n   * @returns The URL to the Convex backend, including the client's API version.\n   */\n  backendUrl(): string {\n    return `${this.address}/api`;\n  }\n\n  /**\n   * Return the address for this client, useful for creating a new client.\n   *\n   * Not guaranteed to match the address with which this client was constructed:\n   * it may be canonicalized.\n   */\n  get url() {\n    return this.address;\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   *\n   * Should be called whenever the token changes (i.e. due to expiration and refresh).\n   *\n   * @param value - JWT-encoded OpenID Connect identity token.\n   */\n  setAuth(value: string) {\n    this.clearAuth();\n    this.auth = value;\n  }\n\n  /**\n   * Set admin auth token to allow calling internal queries, mutations, and actions\n   * and acting as an identity.\n   *\n   * @internal\n   */\n  setAdminAuth(token: string, actingAsIdentity?: UserIdentityAttributes) {\n    this.clearAuth();\n    if (actingAsIdentity !== undefined) {\n      // Encode the identity to a base64 string\n      const bytes = new TextEncoder().encode(JSON.stringify(actingAsIdentity));\n      const actingAsIdentityEncoded = btoa(String.fromCodePoint(...bytes));\n      this.adminAuth = `${token}:${actingAsIdentityEncoded}`;\n    } else {\n      this.adminAuth = token;\n    }\n  }\n\n  /**\n   * Clear the current authentication token if set.\n   */\n  clearAuth() {\n    this.auth = undefined;\n    this.adminAuth = undefined;\n  }\n\n  /**\n   * Sets whether the result log lines should be printed on the console or not.\n   *\n   * @internal\n   */\n  setDebug(debug: boolean) {\n    this.debug = debug;\n  }\n\n  /**\n   * Used to customize the fetch behavior in some runtimes.\n   *\n   * @internal\n   */\n  setFetchOptions(fetchOptions: FetchOptions) {\n    this.fetchOptions = fetchOptions;\n  }\n\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * Execute a Convex query function at the same timestamp as every other\n   * consistent query execution run by this HTTP client.\n   *\n   * This doesn't make sense for long-lived ConvexHttpClients as Convex\n   * backends can read a limited amount into the past: beyond 30 seconds\n   * in the past may not be available.\n   *\n   * Create a new client to use a consistent time.\n   *\n   * @param name - The name of the query.\n   * @param args - The arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the query's result.\n   *\n   * @deprecated This API is experimental: it may change or disappear.\n   */\n  async consistentQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): Promise<FunctionReturnType<Query>> {\n    const queryArgs = parseArgs(args[0]);\n\n    const timestampPromise = this.getTimestamp();\n    return await this.queryInner(query, queryArgs, { timestampPromise });\n  }\n\n  private async getTimestamp() {\n    if (this.encodedTsPromise) {\n      return this.encodedTsPromise;\n    }\n    return (this.encodedTsPromise = this.getTimestampInner());\n  }\n\n  private async getTimestampInner() {\n    const localFetch = specifiedFetch || fetch;\n\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    const response = await localFetch(`${this.address}/api/query_ts`, {\n      ...this.fetchOptions,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok) {\n      throw new Error(await response.text());\n    }\n    const { ts } = (await response.json()) as { ts: string };\n    return ts;\n  }\n\n  /**\n   * Execute a Convex query function.\n   *\n   * @param name - The name of the query.\n   * @param args - The arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the query's result.\n   */\n  async query<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): Promise<FunctionReturnType<Query>> {\n    const queryArgs = parseArgs(args[0]);\n    return await this.queryInner(query, queryArgs, {});\n  }\n\n  private async queryInner<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    queryArgs: FunctionArgs<Query>,\n    options: { timestampPromise?: Promise<string> },\n  ): Promise<FunctionReturnType<Query>> {\n    const name = getFunctionName(query);\n    const args = [convexToJson(queryArgs)];\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    if (this.adminAuth) {\n      headers[\"Authorization\"] = `Convex ${this.adminAuth}`;\n    } else if (this.auth) {\n      headers[\"Authorization\"] = `Bearer ${this.auth}`;\n    }\n    const localFetch = specifiedFetch || fetch;\n\n    const timestamp = options.timestampPromise\n      ? await options.timestampPromise\n      : undefined;\n\n    const body = JSON.stringify({\n      path: name,\n      format: \"convex_encoded_json\",\n      args,\n      ...(timestamp ? { ts: timestamp } : {}),\n    });\n    const endpoint = timestamp\n      ? `${this.address}/api/query_at_ts`\n      : `${this.address}/api/query`;\n\n    const response = await localFetch(endpoint, {\n      ...this.fetchOptions,\n      body,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {\n      throw new Error(await response.text());\n    }\n    const respJSON = await response.json();\n\n    if (this.debug) {\n      for (const line of respJSON.logLines ?? []) {\n        logForFunction(this.logger, \"info\", \"query\", name, line);\n      }\n    }\n    switch (respJSON.status) {\n      case \"success\":\n        return jsonToConvex(respJSON.value);\n      case \"error\":\n        if (respJSON.errorData !== undefined) {\n          throw forwardErrorData(\n            respJSON.errorData,\n            new ConvexError(respJSON.errorMessage),\n          );\n        }\n        throw new Error(respJSON.errorMessage);\n      default:\n        throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);\n    }\n  }\n\n  private async mutationInner<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    mutationArgs: FunctionArgs<Mutation>,\n  ): Promise<FunctionReturnType<Mutation>> {\n    const name = getFunctionName(mutation);\n    const body = JSON.stringify({\n      path: name,\n      format: \"convex_encoded_json\",\n      args: [convexToJson(mutationArgs)],\n    });\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    if (this.adminAuth) {\n      headers[\"Authorization\"] = `Convex ${this.adminAuth}`;\n    } else if (this.auth) {\n      headers[\"Authorization\"] = `Bearer ${this.auth}`;\n    }\n    const localFetch = specifiedFetch || fetch;\n    const response = await localFetch(`${this.address}/api/mutation`, {\n      ...this.fetchOptions,\n      body,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {\n      throw new Error(await response.text());\n    }\n    const respJSON = await response.json();\n    if (this.debug) {\n      for (const line of respJSON.logLines ?? []) {\n        logForFunction(this.logger, \"info\", \"mutation\", name, line);\n      }\n    }\n    switch (respJSON.status) {\n      case \"success\":\n        return jsonToConvex(respJSON.value);\n      case \"error\":\n        if (respJSON.errorData !== undefined) {\n          throw forwardErrorData(\n            respJSON.errorData,\n            new ConvexError(respJSON.errorMessage),\n          );\n        }\n        throw new Error(respJSON.errorMessage);\n      default:\n        throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);\n    }\n  }\n\n  private async processMutationQueue() {\n    if (this.isProcessingQueue) {\n      return;\n    }\n\n    this.isProcessingQueue = true;\n    while (this.mutationQueue.length > 0) {\n      const { mutation, args, resolve, reject } = this.mutationQueue.shift()!;\n      try {\n        const result = await this.mutationInner(mutation, args);\n        resolve(result);\n      } catch (error) {\n        reject(error);\n      }\n    }\n    this.isProcessingQueue = false;\n  }\n\n  private enqueueMutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    args: FunctionArgs<Mutation>,\n  ): Promise<FunctionReturnType<Mutation>> {\n    return new Promise((resolve, reject) => {\n      this.mutationQueue.push({ mutation, args, resolve, reject });\n      void this.processMutationQueue();\n    });\n  }\n\n  /**\n   * Execute a Convex mutation function. Mutations are queued by default.\n   *\n   * @param name - The name of the mutation.\n   * @param args - The arguments object for the mutation. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - An optional object containing\n   * @returns A promise of the mutation's result.\n   */\n  async mutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    ...args: ArgsAndOptions<Mutation, HttpMutationOptions>\n  ): Promise<FunctionReturnType<Mutation>> {\n    const [fnArgs, options] = args;\n    const mutationArgs = parseArgs(fnArgs);\n    const queued = !options?.skipQueue;\n\n    if (queued) {\n      return await this.enqueueMutation(mutation, mutationArgs);\n    } else {\n      return await this.mutationInner(mutation, mutationArgs);\n    }\n  }\n\n  /**\n   * Execute a Convex action function. Actions are not queued.\n   *\n   * @param name - The name of the action.\n   * @param args - The arguments object for the action. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the action's result.\n   */\n  async action<Action extends FunctionReference<\"action\">>(\n    action: Action,\n    ...args: OptionalRestArgs<Action>\n  ): Promise<FunctionReturnType<Action>> {\n    const actionArgs = parseArgs(args[0]);\n    const name = getFunctionName(action);\n    const body = JSON.stringify({\n      path: name,\n      format: \"convex_encoded_json\",\n      args: [convexToJson(actionArgs)],\n    });\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    if (this.adminAuth) {\n      headers[\"Authorization\"] = `Convex ${this.adminAuth}`;\n    } else if (this.auth) {\n      headers[\"Authorization\"] = `Bearer ${this.auth}`;\n    }\n    const localFetch = specifiedFetch || fetch;\n    const response = await localFetch(`${this.address}/api/action`, {\n      ...this.fetchOptions,\n      body,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {\n      throw new Error(await response.text());\n    }\n    const respJSON = await response.json();\n    if (this.debug) {\n      for (const line of respJSON.logLines ?? []) {\n        logForFunction(this.logger, \"info\", \"action\", name, line);\n      }\n    }\n    switch (respJSON.status) {\n      case \"success\":\n        return jsonToConvex(respJSON.value);\n      case \"error\":\n        if (respJSON.errorData !== undefined) {\n          throw forwardErrorData(\n            respJSON.errorData,\n            new ConvexError(respJSON.errorMessage),\n          );\n        }\n        throw new Error(respJSON.errorMessage);\n      default:\n        throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);\n    }\n  }\n\n  /**\n   * Execute a Convex function of an unknown type. These function calls are not queued.\n   *\n   * @param name - The name of the function.\n   * @param args - The arguments object for the function. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the function's result.\n   *\n   * @internal\n   */\n  async function<\n    AnyFunction extends FunctionReference<\"query\" | \"mutation\" | \"action\">,\n  >(\n    anyFunction: AnyFunction | string,\n    componentPath?: string,\n    ...args: OptionalRestArgs<AnyFunction>\n  ): Promise<FunctionReturnType<AnyFunction>> {\n    const functionArgs = parseArgs(args[0]);\n    const name =\n      typeof anyFunction === \"string\"\n        ? anyFunction\n        : getFunctionName(anyFunction);\n    const body = JSON.stringify({\n      componentPath: componentPath,\n      path: name,\n      format: \"convex_encoded_json\",\n      args: convexToJson(functionArgs),\n    });\n    const headers: Record<string, string> = {\n      \"Content-Type\": \"application/json\",\n      \"Convex-Client\": `npm-${version}`,\n    };\n    if (this.adminAuth) {\n      headers[\"Authorization\"] = `Convex ${this.adminAuth}`;\n    } else if (this.auth) {\n      headers[\"Authorization\"] = `Bearer ${this.auth}`;\n    }\n    const localFetch = specifiedFetch || fetch;\n    const response = await localFetch(`${this.address}/api/function`, {\n      ...this.fetchOptions,\n      body,\n      method: \"POST\",\n      headers: headers,\n    });\n    if (!response.ok && response.status !== STATUS_CODE_UDF_FAILED) {\n      throw new Error(await response.text());\n    }\n    const respJSON = await response.json();\n    if (this.debug) {\n      for (const line of respJSON.logLines ?? []) {\n        logForFunction(this.logger, \"info\", \"any\", name, line);\n      }\n    }\n    switch (respJSON.status) {\n      case \"success\":\n        return jsonToConvex(respJSON.value);\n      case \"error\":\n        if (respJSON.errorData !== undefined) {\n          throw forwardErrorData(\n            respJSON.errorData,\n            new ConvexError(respJSON.errorMessage),\n          );\n        }\n        throw new Error(respJSON.errorMessage);\n      default:\n        throw new Error(`Invalid response: ${JSON.stringify(respJSON)}`);\n    }\n  }\n}\n\nfunction forwardErrorData(errorData: JSONValue, error: ConvexError<string>) {\n  (error as ConvexError<any>).data = jsonToConvex(errorData);\n  return error;\n}\n\n/**\n * @internal\n */\ntype FetchOptions = { cache: \"force-cache\" | \"no-store\" };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAMA,SAAS,WAAW,6BAA6B;AACjD,SAAS,eAAe;AACxB;;;AAMA;;;;;;;;;;;;;;;AAYO,MAAM,iBAAiB;AACvB,MAAM,0BAA0B;AAIhC,MAAM,yBAAyB;AAGtC,IAAI,iBAAsD,KAAA;AACnD,SAAS,SAAS,CAAA,EAA4B;IACnD,iBAAiB;AACnB;AAuBO,MAAM,iBAAiB;IA8D5B;;;;;GAAA,GAQA,aAAqB;QACnB,OAAO,GAAe,OAAZ,IAAA,CAAK,OAAO,EAAA;IACxB;IAAA;;;;;GAAA,GAQA,IAAI,MAAM;QACR,OAAO,IAAA,CAAK,OAAA;IACd;IAAA;;;;;;GAAA,GASA,QAAQ,KAAA,EAAe;QACrB,IAAA,CAAK,SAAA,CAAU;QACf,IAAA,CAAK,IAAA,GAAO;IACd;IAAA;;;;;GAAA,GAQA,aAAa,KAAA,EAAe,gBAAA,EAA2C;QACrE,IAAA,CAAK,SAAA,CAAU;QACf,IAAI,qBAAqB,KAAA,GAAW;YAElC,MAAM,QAAQ,IAAI,YAAY,EAAE,MAAA,CAAO,KAAK,SAAA,CAAU,gBAAgB,CAAC;YACvE,MAAM,0BAA0B,KAAK,OAAO,aAAA,CAAc,GAAG,KAAK,CAAC;YACnE,IAAA,CAAK,SAAA,GAAY,UAAG,KAAK,EAAA,KAA2B,OAAvB,uBAAuB;QACtD,OAAO;YACL,IAAA,CAAK,SAAA,GAAY;QACnB;IACF;IAAA;;GAAA,GAKA,YAAY;QACV,IAAA,CAAK,IAAA,GAAO,KAAA;QACZ,IAAA,CAAK,SAAA,GAAY,KAAA;IACnB;IAAA;;;;GAAA,GAOA,SAAS,KAAA,EAAgB;QACvB,IAAA,CAAK,KAAA,GAAQ;IACf;IAAA;;;;GAAA,GAOA,gBAAgB,YAAA,EAA4B;QAC1C,IAAA,CAAK,YAAA,GAAe;IACtB;IAAA;;;;;;;;;;;;;;;;;;GAAA,GAqBA,MAAM,gBACJ,KAAA,EAEoC;QAFpC,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,KADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,gBAAY,+OAAA,EAAU,IAAA,CAAK,CAAC,CAAC;QAEnC,MAAM,mBAAmB,IAAA,CAAK,YAAA,CAAa;QAC3C,OAAO,MAAM,IAAA,CAAK,UAAA,CAAW,OAAO,WAAW;YAAE;QAAiB,CAAC;IACrE;IAEA,MAAc,eAAe;QAC3B,IAAI,IAAA,CAAK,gBAAA,EAAkB;YACzB,OAAO,IAAA,CAAK,gBAAA;QACd;QACA,OAAQ,IAAA,CAAK,gBAAA,GAAmB,IAAA,CAAK,iBAAA,CAAkB;IACzD;IAEA,MAAc,oBAAoB;QAChC,MAAM,aAAa,kBAAkB;QAErC,MAAM,UAAkC;YACtC,gBAAgB;YAChB,iBAAiB,OAAc,OAAP,mOAAO;QACjC;QACA,MAAM,WAAW,MAAM,WAAW,GAAe,OAAZ,IAAA,CAAK,OAAO,EAAA,kBAAiB;YAChE,GAAG,IAAA,CAAK,YAAA;YACR,QAAQ;YACR;QACF,CAAC;QACD,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,MAAM,IAAI,MAAM,MAAM,SAAS,IAAA,CAAK,CAAC;QACvC;QACA,MAAM,EAAE,EAAA,CAAG,CAAA,GAAK,MAAM,SAAS,IAAA,CAAK;QACpC,OAAO;IACT;IAAA;;;;;;;GAAA,GAUA,MAAM,MACJ,KAAA,EAEoC;QAFpC,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,KADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,gBAAY,+OAAA,EAAU,IAAA,CAAK,CAAC,CAAC;QACnC,OAAO,MAAM,IAAA,CAAK,UAAA,CAAW,OAAO,WAAW,CAAC,CAAC;IACnD;IAEA,MAAc,WACZ,KAAA,EACA,SAAA,EACA,OAAA,EACoC;QACpC,MAAM,WAAO,mPAAA,EAAgB,KAAK;QAClC,MAAM,OAAO;gBAAC,kPAAA,EAAa,SAAS,CAAC;SAAA;QACrC,MAAM,UAAkC;YACtC,gBAAgB;YAChB,iBAAiB,OAAc,OAAP,mOAAO;QACjC;QACA,IAAI,IAAA,CAAK,SAAA,EAAW;YAClB,OAAA,CAAQ,eAAe,CAAA,GAAI,UAAwB,OAAd,IAAA,CAAK,SAAS;QACrD,OAAA,IAAW,IAAA,CAAK,IAAA,EAAM;YACpB,OAAA,CAAQ,eAAe,CAAA,GAAI,UAAmB,OAAT,IAAA,CAAK,IAAI;QAChD;QACA,MAAM,aAAa,kBAAkB;QAErC,MAAM,YAAY,QAAQ,gBAAA,GACtB,MAAM,QAAQ,gBAAA,GACd,KAAA;QAEJ,MAAM,OAAO,KAAK,SAAA,CAAU;YAC1B,MAAM;YACN,QAAQ;YACR;YACA,GAAI,YAAY;gBAAE,IAAI;YAAU,IAAI,CAAC,CAAA;QACvC,CAAC;QACD,MAAM,WAAW,YACb,GAAe,OAAZ,IAAA,CAAK,OAAO,EAAA,sBACf,GAAe,OAAZ,IAAA,CAAK,OAAO,EAAA;QAEnB,MAAM,WAAW,MAAM,WAAW,UAAU;YAC1C,GAAG,IAAA,CAAK,YAAA;YACR;YACA,QAAQ;YACR;QACF,CAAC;QACD,IAAI,CAAC,SAAS,EAAA,IAAM,SAAS,MAAA,KAAW,wBAAwB;YAC9D,MAAM,IAAI,MAAM,MAAM,SAAS,IAAA,CAAK,CAAC;QACvC;QACA,MAAM,WAAW,MAAM,SAAS,IAAA,CAAK;QAErC,IAAI,IAAA,CAAK,KAAA,EAAO;;YACd,KAAA,MAAW,uCAAiB,QAAA,8CAAT,qBAAqB,CAAC,CAAA,CAAG;gBAC1C,IAAA,uPAAA,EAAe,IAAA,CAAK,MAAA,EAAQ,QAAQ,SAAS,MAAM,IAAI;YACzD;QACF;QACA,OAAQ,SAAS,MAAA,EAAQ;YACvB,KAAK;gBACH,WAAO,kPAAA,EAAa,SAAS,KAAK;YACpC,KAAK;gBACH,IAAI,SAAS,SAAA,KAAc,KAAA,GAAW;oBACpC,MAAM,iBACJ,SAAS,SAAA,EACT,IAAI,kPAAA,CAAY,SAAS,YAAY;gBAEzC;gBACA,MAAM,IAAI,MAAM,SAAS,YAAY;YACvC;gBACE,MAAM,IAAI,MAAM,qBAA6C,CAAE,MAA1B,KAAK,SAAA,CAAU,QAAQ,CAAC;QACjE;IACF;IAEA,MAAc,cACZ,QAAA,EACA,YAAA,EACuC;QACvC,MAAM,WAAO,mPAAA,EAAgB,QAAQ;QACrC,MAAM,OAAO,KAAK,SAAA,CAAU;YAC1B,MAAM;YACN,QAAQ;YACR,MAAM;oBAAC,kPAAA,EAAa,YAAY,CAAC;aAAA;QACnC,CAAC;QACD,MAAM,UAAkC;YACtC,gBAAgB;YAChB,iBAAiB,OAAc,OAAP,mOAAO;QACjC;QACA,IAAI,IAAA,CAAK,SAAA,EAAW;YAClB,OAAA,CAAQ,eAAe,CAAA,GAAI,UAAwB,OAAd,IAAA,CAAK,SAAS;QACrD,OAAA,IAAW,IAAA,CAAK,IAAA,EAAM;YACpB,OAAA,CAAQ,eAAe,CAAA,GAAI,UAAmB,OAAT,IAAA,CAAK,IAAI;QAChD;QACA,MAAM,aAAa,kBAAkB;QACrC,MAAM,WAAW,MAAM,WAAW,GAAe,OAAZ,IAAA,CAAK,OAAO,EAAA,kBAAiB;YAChE,GAAG,IAAA,CAAK,YAAA;YACR;YACA,QAAQ;YACR;QACF,CAAC;QACD,IAAI,CAAC,SAAS,EAAA,IAAM,SAAS,MAAA,KAAW,wBAAwB;YAC9D,MAAM,IAAI,MAAM,MAAM,SAAS,IAAA,CAAK,CAAC;QACvC;QACA,MAAM,WAAW,MAAM,SAAS,IAAA,CAAK;QACrC,IAAI,IAAA,CAAK,KAAA,EAAO;gBACK;YAAnB,KAAA,MAAW,uCAAiB,QAAA,mEAAY,CAAC,CAAA,CAAG;gBAC1C,IAAA,uPAAA,EAAe,IAAA,CAAK,MAAA,EAAQ,QAAQ,YAAY,MAAM,IAAI;YAC5D;QACF;QACA,OAAQ,SAAS,MAAA,EAAQ;YACvB,KAAK;gBACH,WAAO,kPAAA,EAAa,SAAS,KAAK;YACpC,KAAK;gBACH,IAAI,SAAS,SAAA,KAAc,KAAA,GAAW;oBACpC,MAAM,iBACJ,SAAS,SAAA,EACT,IAAI,kPAAA,CAAY,SAAS,YAAY;gBAEzC;gBACA,MAAM,IAAI,MAAM,SAAS,YAAY;YACvC;gBACE,MAAM,IAAI,MAAM,qBAA6C,CAAE,MAA1B,KAAK,SAAA,CAAU,QAAQ,CAAC;QACjE;IACF;IAEA,MAAc,uBAAuB;QACnC,IAAI,IAAA,CAAK,iBAAA,EAAmB;YAC1B;QACF;QAEA,IAAA,CAAK,iBAAA,GAAoB;QACzB,MAAO,IAAA,CAAK,aAAA,CAAc,MAAA,GAAS,EAAG;YACpC,MAAM,EAAE,QAAA,EAAU,IAAA,EAAM,OAAA,EAAS,MAAA,CAAO,CAAA,GAAI,IAAA,CAAK,aAAA,CAAc,KAAA,CAAM;YACrE,IAAI;gBACF,MAAM,SAAS,MAAM,IAAA,CAAK,aAAA,CAAc,UAAU,IAAI;gBACtD,QAAQ,MAAM;YAChB,EAAA,OAAS,OAAO;gBACd,OAAO,KAAK;YACd;QACF;QACA,IAAA,CAAK,iBAAA,GAAoB;IAC3B;IAEQ,gBACN,QAAA,EACA,IAAA,EACuC;QACvC,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;YACtC,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK;gBAAE;gBAAU;gBAAM;gBAAS;YAAO,CAAC;YAC3D,KAAK,IAAA,CAAK,oBAAA,CAAqB;QACjC,CAAC;IACH;IAAA;;;;;;;;GAAA,GAWA,MAAM,SACJ,QAAA,EAEuC;QAFvC,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,KADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,CAAC,QAAQ,OAAO,CAAA,GAAI;QAC1B,MAAM,mBAAe,+OAAA,EAAU,MAAM;QACrC,MAAM,SAAS,oDAAC,QAAS,SAAA;QAEzB,IAAI,QAAQ;YACV,OAAO,MAAM,IAAA,CAAK,eAAA,CAAgB,UAAU,YAAY;QAC1D,OAAO;YACL,OAAO,MAAM,IAAA,CAAK,aAAA,CAAc,UAAU,YAAY;QACxD;IACF;IAAA;;;;;;;GAAA,GAUA,MAAM,OACJ,MAAA,EAEqC;QAFrC,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,KADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,iBAAa,+OAAA,EAAU,IAAA,CAAK,CAAC,CAAC;QACpC,MAAM,WAAO,mPAAA,EAAgB,MAAM;QACnC,MAAM,OAAO,KAAK,SAAA,CAAU;YAC1B,MAAM;YACN,QAAQ;YACR,MAAM;oBAAC,kPAAA,EAAa,UAAU,CAAC;aAAA;QACjC,CAAC;QACD,MAAM,UAAkC;YACtC,gBAAgB;YAChB,iBAAiB,OAAc,OAAP,mOAAO;QACjC;QACA,IAAI,IAAA,CAAK,SAAA,EAAW;YAClB,OAAA,CAAQ,eAAe,CAAA,GAAI,UAAwB,OAAd,IAAA,CAAK,SAAS;QACrD,OAAA,IAAW,IAAA,CAAK,IAAA,EAAM;YACpB,OAAA,CAAQ,eAAe,CAAA,GAAI,UAAmB,OAAT,IAAA,CAAK,IAAI;QAChD;QACA,MAAM,aAAa,kBAAkB;QACrC,MAAM,WAAW,MAAM,WAAW,GAAe,OAAZ,IAAA,CAAK,OAAO,EAAA,gBAAe;YAC9D,GAAG,IAAA,CAAK,YAAA;YACR;YACA,QAAQ;YACR;QACF,CAAC;QACD,IAAI,CAAC,SAAS,EAAA,IAAM,SAAS,MAAA,KAAW,wBAAwB;YAC9D,MAAM,IAAI,MAAM,MAAM,SAAS,IAAA,CAAK,CAAC;QACvC;QACA,MAAM,WAAW,MAAM,SAAS,IAAA,CAAK;QACrC,IAAI,IAAA,CAAK,KAAA,EAAO;;YACd,KAAA,MAAW,uCAAiB,QAAA,cAAT,qDAAqB,CAAC,CAAA,CAAG;gBAC1C,IAAA,uPAAA,EAAe,IAAA,CAAK,MAAA,EAAQ,QAAQ,UAAU,MAAM,IAAI;YAC1D;QACF;QACA,OAAQ,SAAS,MAAA,EAAQ;YACvB,KAAK;gBACH,WAAO,kPAAA,EAAa,SAAS,KAAK;YACpC,KAAK;gBACH,IAAI,SAAS,SAAA,KAAc,KAAA,GAAW;oBACpC,MAAM,iBACJ,SAAS,SAAA,EACT,IAAI,kPAAA,CAAY,SAAS,YAAY;gBAEzC;gBACA,MAAM,IAAI,MAAM,SAAS,YAAY;YACvC;gBACE,MAAM,IAAI,MAAM,qBAA6C,CAAE,MAA1B,KAAK,SAAA,CAAU,QAAQ,CAAC;QACjE;IACF;IAAA;;;;;;;;;GAAA,GAYA,MAAM,SAGJ,WAAA,EACA,aAAA,EAE0C;QAF1C,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,KADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,mBAAe,+OAAA,EAAU,IAAA,CAAK,CAAC,CAAC;QACtC,MAAM,OACJ,OAAO,gBAAgB,WACnB,kBACA,mPAAA,EAAgB,WAAW;QACjC,MAAM,OAAO,KAAK,SAAA,CAAU;YAC1B;YACA,MAAM;YACN,QAAQ;YACR,UAAM,kPAAA,EAAa,YAAY;QACjC,CAAC;QACD,MAAM,UAAkC;YACtC,gBAAgB;YAChB,iBAAiB,OAAc,OAAP,mOAAO;QACjC;QACA,IAAI,IAAA,CAAK,SAAA,EAAW;YAClB,OAAA,CAAQ,eAAe,CAAA,GAAI,UAAwB,OAAd,IAAA,CAAK,SAAS;QACrD,OAAA,IAAW,IAAA,CAAK,IAAA,EAAM;YACpB,OAAA,CAAQ,eAAe,CAAA,GAAI,UAAmB,OAAT,IAAA,CAAK,IAAI;QAChD;QACA,MAAM,aAAa,kBAAkB;QACrC,MAAM,WAAW,MAAM,WAAW,GAAe,OAAZ,IAAA,CAAK,OAAO,EAAA,kBAAiB;YAChE,GAAG,IAAA,CAAK,YAAA;YACR;YACA,QAAQ;YACR;QACF,CAAC;QACD,IAAI,CAAC,SAAS,EAAA,IAAM,SAAS,MAAA,KAAW,wBAAwB;YAC9D,MAAM,IAAI,MAAM,MAAM,SAAS,IAAA,CAAK,CAAC;QACvC;QACA,MAAM,WAAW,MAAM,SAAS,IAAA,CAAK;QACrC,IAAI,IAAA,CAAK,KAAA,EAAO;;YACd,KAAA,MAAW,uCAAiB,QAAA,8CAAT,qBAAqB,CAAC,CAAA,CAAG;gBAC1C,IAAA,uPAAA,EAAe,IAAA,CAAK,MAAA,EAAQ,QAAQ,OAAO,MAAM,IAAI;YACvD;QACF;QACA,OAAQ,SAAS,MAAA,EAAQ;YACvB,KAAK;gBACH,WAAO,kPAAA,EAAa,SAAS,KAAK;YACpC,KAAK;gBACH,IAAI,SAAS,SAAA,KAAc,KAAA,GAAW;oBACpC,MAAM,iBACJ,SAAS,SAAA,EACT,IAAI,kPAAA,CAAY,SAAS,YAAY;gBAEzC;gBACA,MAAM,IAAI,MAAM,SAAS,YAAY;YACvC;gBACE,MAAM,IAAI,MAAM,qBAA6C,CAAE,MAA1B,KAAK,SAAA,CAAU,QAAQ,CAAC;QACjE;IACF;IAzf4B;;;;;;;;;;;;;;;;;GAAA,GAkC5B,YACE,OAAA,EACA,OAAA,CAKA;QAxCF,cAAA,IAAA,EAAiB;QACjB,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ,iBAKH,CAAC,CAAA;QACN,cAAA,IAAA,EAAQ,qBAA6B;QA4BnC,IAAI,OAAO,YAAY,WAAW;YAChC,MAAM,IAAI,MACR;QAEJ;QACA,MAAM,gDAAO,UAAW,CAAC;QACzB,IAAI,KAAK,4BAAA,KAAiC,MAAM;YAC9C,IAAA,2PAAA,EAAsB,OAAO;QAC/B;QACA,IAAA,CAAK,MAAA,sDACH,QAAS,MAAA,MAAW,YAChB,8PAAA,EAAsB;YAAE,SAAS;QAAM,CAAC,uDACxC,QAAS,MAAA,MAAW,2DAAQ,QAAS,MAAA,IACnC,QAAQ,MAAA,OACR,iQAAA,EAAyB;YAAE,SAAS;QAAM,CAAC;QACnD,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,KAAA,GAAQ;QACb,wBAAI,sCAAS,IAAA,EAAM;YACjB,IAAA,CAAK,OAAA,CAAQ,QAAQ,IAAI;QAC3B;IACF;AA4bF;AAEA,SAAS,iBAAiB,SAAA,EAAsB,KAAA,EAA4B;IACzE,MAA2B,IAAA,OAAO,kPAAA,EAAa,SAAS;IACzD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4977, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/browser/index.ts"], "sourcesContent": ["/**\n * Tools for accessing Convex in the browser.\n *\n * **If you are using React, use the {@link react} module instead.**\n *\n * ## Usage\n *\n * Create a {@link ConvexHttpClient} to connect to the Convex Cloud.\n *\n * ```typescript\n * import { ConvexHttpClient } from \"convex/browser\";\n * // typically loaded from an environment variable\n * const address = \"https://small-mouse-123.convex.cloud\";\n * const convex = new ConvexHttpClient(address);\n * ```\n *\n * @module\n */\nexport { BaseConvexClient } from \"./sync/client.js\";\nexport type {\n  BaseConvexClientOptions,\n  MutationOptions,\n  SubscribeOptions,\n  ConnectionState,\n  AuthTokenFetcher,\n} from \"./sync/client.js\";\nexport type { ConvexClientOptions } from \"./simple_client.js\";\nexport { ConvexClient } from \"./simple_client.js\";\nexport type {\n  OptimisticUpdate,\n  OptimisticLocalStore,\n} from \"./sync/optimistic_updates.js\";\nexport type { QueryToken } from \"./sync/udf_path_utils.js\";\nexport { ConvexHttpClient } from \"./http_client.js\";\nexport type { QueryJournal } from \"./sync/protocol.js\";\n/** @internal */\nexport type { UserIdentityAttributes } from \"./sync/protocol.js\";\nexport type { FunctionResult } from \"./sync/function_result.js\";\n"], "names": [], "mappings": ";AAkBA,SAAS,wBAAwB;AASjC,SAAS,oBAAoB;AAM7B,SAAS,wBAAwB", "debugId": null}}, {"offset": {"line": 4989, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/use_subscription.ts"], "sourcesContent": ["import { useEffect, useState } from \"react\";\n\n/*\nThis code is taken from https://gist.github.com/bvaughn/e25397f70e8c65b0ae0d7c90b731b189\nbecause correct subscriptions in async React is complex!\n\nIt could probably be replaced with `useSyncExternalStore()`.\n\nThe MIT License (MIT)\nCopyright © 2023 Brian Vaughn\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the “Software”), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\n/**\n * Hook used for safely managing subscriptions in concurrent mode.\n *\n * In order to avoid removing and re-adding subscriptions each time this hook is called,\n * the parameters passed to this hook should be memoized in some way–\n * either by wrapping the entire params object with useMemo()\n * or by wrapping the individual callbacks with useCallback().\n *\n * @internal\n */\nexport function useSubscription<Value>({\n  // (Synchronously) returns the current value of our subscription.\n  getCurrentValue,\n\n  // This function is passed an event handler to attach to the subscription.\n  // It should return an unsubscribe function that removes the handler.\n  subscribe,\n}: {\n  getCurrentValue: () => Value;\n  subscribe: (callback: () => void) => () => void;\n}): Value {\n  // Read the current value from our subscription.\n  // When this value changes, we'll schedule an update with React.\n  // It's important to also store the hook params so that we can check for staleness.\n  // (See the comment in checkForUpdates() below for more info.)\n  const [state, setState] = useState(() => ({\n    getCurrentValue,\n    subscribe,\n    value: getCurrentValue(),\n  }));\n\n  let valueToReturn = state.value;\n\n  // If parameters have changed since our last render, schedule an update with its current value.\n  if (\n    state.getCurrentValue !== getCurrentValue ||\n    state.subscribe !== subscribe\n  ) {\n    // If the subscription has been updated, we'll schedule another update with React.\n    // React will process this update immediately, so the old subscription value won't be committed.\n    // It is still nice to avoid returning a mismatched value though, so let's override the return value.\n    valueToReturn = getCurrentValue();\n\n    setState({\n      getCurrentValue,\n      subscribe,\n      value: valueToReturn,\n    });\n  }\n\n  // It is important not to subscribe while rendering because this can lead to memory leaks.\n  // (Learn more at reactjs.org/docs/strict-mode.html#detecting-unexpected-side-effects)\n  // Instead, we wait until the commit phase to attach our handler.\n  //\n  // We intentionally use a passive effect (useEffect) rather than a synchronous one (useLayoutEffect)\n  // so that we don't stretch the commit phase.\n  // This also has an added benefit when multiple components are subscribed to the same source:\n  // It allows each of the event handlers to safely schedule work without potentially removing an another handler.\n  // (Learn more at https://codesandbox.io/s/k0yvr5970o)\n  useEffect(() => {\n    let didUnsubscribe = false;\n\n    const checkForUpdates = () => {\n      // It's possible that this callback will be invoked even after being unsubscribed,\n      // if it's removed as a result of a subscription event/update.\n      // In this case, React will log a DEV warning about an update from an unmounted component.\n      // We can avoid triggering that warning with this check.\n      if (didUnsubscribe) {\n        return;\n      }\n\n      setState((prevState) => {\n        // Ignore values from stale sources!\n        // Since we subscribe an unsubscribe in a passive effect,\n        // it's possible that this callback will be invoked for a stale (previous) subscription.\n        // This check avoids scheduling an update for that stale subscription.\n        if (\n          prevState.getCurrentValue !== getCurrentValue ||\n          prevState.subscribe !== subscribe\n        ) {\n          return prevState;\n        }\n\n        // Some subscriptions will auto-invoke the handler, even if the value hasn't changed.\n        // If the value hasn't changed, no update is needed.\n        // Return state as-is so React can bail out and avoid an unnecessary render.\n        const value = getCurrentValue();\n        if (prevState.value === value) {\n          return prevState;\n        }\n\n        return { ...prevState, value };\n      });\n    };\n    const unsubscribe = subscribe(checkForUpdates);\n\n    // Because we're subscribing in a passive effect,\n    // it's possible that an update has occurred between render and our effect handler.\n    // Check for this and schedule an update if work has occurred.\n    checkForUpdates();\n\n    return () => {\n      didUnsubscribe = true;\n      unsubscribe();\n    };\n  }, [getCurrentValue, subscribe]);\n\n  // Return the current value for our caller to use while rendering.\n  return valueToReturn;\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,WAAW,gBAAgB;;;AAwC7B,SAAS,qBAOhB;UAPuC,iEAAA;IAErC,eAAA,EAAA,0EAAA;IAAA,qEAAA;IAIA,SAAA,EACF,EAGU,CAV6B;IAerC,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAI,kPAAA;oCAAS,IAAA,CAAO;gBACxC;gBACA;gBACA,OAAO,gBAAgB;YACzB,CAAA,CAAE;;IAEF,IAAI,gBAAgB,MAAM,KAAA;IAG1B,IACE,MAAM,eAAA,KAAoB,mBAC1B,MAAM,SAAA,KAAc,WACpB;QAIA,gBAAgB,gBAAgB;QAEhC,SAAS;YACP;YACA;YACA,OAAO;QACT,CAAC;IACH;IAWA,IAAA,+OAAA;qCAAU,MAAM;YACd,IAAI,iBAAiB;YAErB,MAAM;6DAAkB,MAAM;oBAK5B,IAAI,gBAAgB;wBAClB;oBACF;oBAEA;qEAAS,CAAC,cAAc;4BAKtB,IACE,UAAU,eAAA,KAAoB,mBAC9B,UAAU,SAAA,KAAc,WACxB;gCACA,OAAO;4BACT;4BAKA,MAAM,QAAQ,gBAAgB;4BAC9B,IAAI,UAAU,KAAA,KAAU,OAAO;gCAC7B,OAAO;4BACT;4BAEA,OAAO;gCAAE,GAAG,SAAA;gCAAW;4BAAM;wBAC/B,CAAC;;gBACH;;YACA,MAAM,cAAc,UAAU,eAAe;YAK7C,gBAAgB;YAEhB;6CAAO,MAAM;oBACX,iBAAiB;oBACjB,YAAY;gBACd;;QACF;oCAAG;QAAC;QAAiB,SAAS;KAAC;IAG/B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5061, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/client.ts"], "sourcesContent": ["import { BaseConvexClient } from \"../browser/index.js\";\nimport type { OptimisticUpdate, QueryToken } from \"../browser/index.js\";\nimport React, { useCallback, useContext, useMemo } from \"react\";\nimport { convexToJson, Value } from \"../values/index.js\";\nimport { QueryJournal } from \"../browser/sync/protocol.js\";\nimport {\n  AuthTokenFetcher,\n  BaseConvexClientOptions,\n  ConnectionState,\n} from \"../browser/sync/client.js\";\nimport type { UserIdentityAttributes } from \"../browser/sync/protocol.js\";\nimport { RequestForQueries, useQueries } from \"./use_queries.js\";\nimport { useSubscription } from \"./use_subscription.js\";\nimport { parseArgs } from \"../common/index.js\";\nimport {\n  ArgsAndOptions,\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  getFunctionName,\n  makeFunctionReference,\n} from \"../server/api.js\";\nimport { EmptyObject } from \"../server/registration.js\";\nimport {\n  instantiateDefaultLogger,\n  instantiate<PERSON>oop<PERSON>ogger,\n  Lo<PERSON>,\n} from \"../browser/logging.js\";\nimport { ConvexQueryOptions } from \"../browser/query_options.js\";\n\n// When no arguments are passed, extend subscriptions (for APIs that do this by default)\n// for this amount after the subscription would otherwise be dropped.\nconst DEFAULT_EXTEND_SUBSCRIPTION_FOR = 5_000;\n\nif (typeof React === \"undefined\") {\n  throw new Error(\"Required dependency 'react' not found\");\n}\n\n// TODO Typedoc doesn't generate documentation for the comment below perhaps\n// because it's a callable interface.\n/**\n * An interface to execute a Convex mutation function on the server.\n *\n * @public\n */\nexport interface ReactMutation<Mutation extends FunctionReference<\"mutation\">> {\n  /**\n   * Execute the mutation on the server, returning a `Promise` of its return value.\n   *\n   * @param args - Arguments for the mutation to pass up to the server.\n   * @returns The return value of the server-side function call.\n   */\n  (...args: OptionalRestArgs<Mutation>): Promise<FunctionReturnType<Mutation>>;\n\n  /**\n   * Define an optimistic update to apply as part of this mutation.\n   *\n   * This is a temporary update to the local query results to facilitate a\n   * fast, interactive UI. It enables query results to update before a mutation\n   * executed on the server.\n   *\n   * When the mutation is invoked, the optimistic update will be applied.\n   *\n   * Optimistic updates can also be used to temporarily remove queries from the\n   * client and create loading experiences until a mutation completes and the\n   * new query results are synced.\n   *\n   * The update will be automatically rolled back when the mutation is fully\n   * completed and queries have been updated.\n   *\n   * @param optimisticUpdate - The optimistic update to apply.\n   * @returns A new `ReactMutation` with the update configured.\n   *\n   * @public\n   */\n  withOptimisticUpdate<T extends OptimisticUpdate<FunctionArgs<Mutation>>>(\n    optimisticUpdate: T &\n      (ReturnType<T> extends Promise<any>\n        ? \"Optimistic update handlers must be synchronous\"\n        : {}),\n  ): ReactMutation<Mutation>;\n}\n\n// Exported only for testing.\nexport function createMutation(\n  mutationReference: FunctionReference<\"mutation\">,\n  client: ConvexReactClient,\n  update?: OptimisticUpdate<any>,\n): ReactMutation<any> {\n  function mutation(args?: Record<string, Value>): Promise<unknown> {\n    assertNotAccidentalArgument(args);\n\n    return client.mutation(mutationReference, args, {\n      optimisticUpdate: update,\n    });\n  }\n  mutation.withOptimisticUpdate = function withOptimisticUpdate(\n    optimisticUpdate: OptimisticUpdate<any>,\n  ): ReactMutation<any> {\n    if (update !== undefined) {\n      throw new Error(\n        `Already specified optimistic update for mutation ${getFunctionName(\n          mutationReference,\n        )}`,\n      );\n    }\n    return createMutation(mutationReference, client, optimisticUpdate);\n  };\n  return mutation as ReactMutation<any>;\n}\n\n/**\n * An interface to execute a Convex action on the server.\n *\n * @public\n */\nexport interface ReactAction<Action extends FunctionReference<\"action\">> {\n  /**\n   * Execute the function on the server, returning a `Promise` of its return value.\n   *\n   * @param args - Arguments for the function to pass up to the server.\n   * @returns The return value of the server-side function call.\n   * @public\n   */\n  (...args: OptionalRestArgs<Action>): Promise<FunctionReturnType<Action>>;\n}\n\nfunction createAction(\n  actionReference: FunctionReference<\"action\">,\n  client: ConvexReactClient,\n): ReactAction<any> {\n  return function (args?: Record<string, Value>): Promise<unknown> {\n    return client.action(actionReference, args);\n  } as ReactAction<any>;\n}\n\n/**\n * A watch on the output of a Convex query function.\n *\n * @public\n */\nexport interface Watch<T> {\n  /**\n   * Initiate a watch on the output of a query.\n   *\n   * This will subscribe to this query and call\n   * the callback whenever the query result changes.\n   *\n   * **Important: If the client is already subscribed to this query with the\n   * same arguments this callback will not be invoked until the query result is\n   * updated.** To get the current, local result call\n   * {@link react.Watch.localQueryResult}.\n   *\n   * @param callback - Function that is called whenever the query result changes.\n   * @returns - A function that disposes of the subscription.\n   */\n  onUpdate(callback: () => void): () => void;\n\n  /**\n   * Get the current result of a query.\n   *\n   * This will only return a result if we're already subscribed to the query\n   * and have received a result from the server or the query value has been set\n   * optimistically.\n   *\n   * @returns The result of the query or `undefined` if it isn't known.\n   * @throws An error if the query encountered an error on the server.\n   */\n  localQueryResult(): T | undefined;\n\n  /**\n   * @internal\n   */\n  localQueryLogs(): string[] | undefined;\n\n  /**\n   * Get the current {@link browser.QueryJournal} for this query.\n   *\n   * If we have not yet received a result for this query, this will be `undefined`.\n   */\n  journal(): QueryJournal | undefined;\n}\n\n/**\n * Options for {@link ConvexReactClient.watchQuery}.\n *\n * @public\n */\nexport interface WatchQueryOptions {\n  /**\n   * An (optional) journal produced from a previous execution of this query\n   * function.\n   *\n   * If there is an existing subscription to a query function with the same\n   * name and arguments, this journal will have no effect.\n   */\n  journal?: QueryJournal;\n\n  /**\n   * @internal\n   */\n  componentPath?: string;\n}\n\n/**\n * Options for {@link ConvexReactClient.mutation}.\n *\n * @public\n */\nexport interface MutationOptions<Args extends Record<string, Value>> {\n  /**\n   * An optimistic update to apply along with this mutation.\n   *\n   * An optimistic update locally updates queries while a mutation is pending.\n   * Once the mutation completes, the update will be rolled back.\n   */\n  optimisticUpdate?: OptimisticUpdate<Args>;\n}\n\n/**\n * Options for {@link ConvexReactClient}.\n *\n * @public\n */\nexport interface ConvexReactClientOptions extends BaseConvexClientOptions {}\n\n/**\n * A Convex client for use within React.\n *\n * This loads reactive queries and executes mutations over a WebSocket.\n *\n * @public\n */\nexport class ConvexReactClient {\n  private address: string;\n  private cachedSync?: BaseConvexClient;\n  private listeners: Map<QueryToken, Set<() => void>>;\n  private options: ConvexReactClientOptions;\n  private closed = false;\n  private _logger: Logger;\n\n  private adminAuth?: string;\n  private fakeUserIdentity?: UserIdentityAttributes;\n\n  /**\n   * @param address - The url of your Convex deployment, often provided\n   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.\n   * @param options - See {@link ConvexReactClientOptions} for a full description.\n   */\n  constructor(address: string, options?: ConvexReactClientOptions) {\n    // Validate address immediately since validation by the lazily-instantiated\n    // internal client does not occur synchronously.\n    if (address === undefined) {\n      throw new Error(\n        \"No address provided to ConvexReactClient.\\n\" +\n          \"If trying to deploy to production, make sure to follow all the instructions found at https://docs.convex.dev/production/hosting/\\n\" +\n          \"If running locally, make sure to run `convex dev` and ensure the .env.local file is populated.\",\n      );\n    }\n    if (typeof address !== \"string\") {\n      throw new Error(\n        `ConvexReactClient requires a URL like 'https://happy-otter-123.convex.cloud', received something of type ${typeof address} instead.`,\n      );\n    }\n    if (!address.includes(\"://\")) {\n      throw new Error(\"Provided address was not an absolute URL.\");\n    }\n    this.address = address;\n    this.listeners = new Map();\n    this._logger =\n      options?.logger === false\n        ? instantiateNoopLogger({ verbose: options?.verbose ?? false })\n        : options?.logger !== true && options?.logger\n          ? options.logger\n          : instantiateDefaultLogger({ verbose: options?.verbose ?? false });\n    this.options = { ...options, logger: this._logger };\n  }\n\n  /**\n   * Return the address for this client, useful for creating a new client.\n   *\n   * Not guaranteed to match the address with which this client was constructed:\n   * it may be canonicalized.\n   */\n  get url() {\n    return this.address;\n  }\n\n  /**\n   * Lazily instantiate the `BaseConvexClient` so we don't create the WebSocket\n   * when server-side rendering.\n   *\n   * @internal\n   */\n  get sync() {\n    if (this.closed) {\n      throw new Error(\"ConvexReactClient has already been closed.\");\n    }\n    if (this.cachedSync) {\n      return this.cachedSync;\n    }\n    this.cachedSync = new BaseConvexClient(\n      this.address,\n      (updatedQueries) => this.transition(updatedQueries),\n      this.options,\n    );\n    if (this.adminAuth) {\n      this.cachedSync.setAdminAuth(this.adminAuth, this.fakeUserIdentity);\n    }\n    return this.cachedSync;\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   * `fetchToken` will be called automatically again if a token expires.\n   * `fetchToken` should return `null` if the token cannot be retrieved, for example\n   * when the user's rights were permanently revoked.\n   * @param fetchToken - an async function returning the JWT-encoded OpenID Connect Identity Token\n   * @param onChange - a callback that will be called when the authentication status changes\n   */\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange?: (isAuthenticated: boolean) => void,\n  ) {\n    if (typeof fetchToken === \"string\") {\n      throw new Error(\n        \"Passing a string to ConvexReactClient.setAuth is no longer supported, \" +\n          \"please upgrade to passing in an async function to handle reauthentication.\",\n      );\n    }\n    this.sync.setAuth(\n      fetchToken,\n      onChange ??\n        (() => {\n          // Do nothing\n        }),\n    );\n  }\n\n  /**\n   * Clear the current authentication token if set.\n   */\n  clearAuth() {\n    this.sync.clearAuth();\n  }\n\n  /**\n   * @internal\n   */\n  setAdminAuth(token: string, identity?: UserIdentityAttributes) {\n    this.adminAuth = token;\n    this.fakeUserIdentity = identity;\n    if (this.closed) {\n      throw new Error(\"ConvexReactClient has already been closed.\");\n    }\n    if (this.cachedSync) {\n      this.sync.setAdminAuth(token, identity);\n    }\n  }\n\n  /**\n   * Construct a new {@link Watch} on a Convex query function.\n   *\n   * **Most application code should not call this method directly. Instead use\n   * the {@link useQuery} hook.**\n   *\n   * @param query - A {@link server.FunctionReference} for the public query to run.\n   * @param args - An arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link WatchQueryOptions} options object for this query.\n   *\n   * @returns The {@link Watch} object.\n   */\n  watchQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...argsAndOptions: ArgsAndOptions<Query, WatchQueryOptions>\n  ): Watch<FunctionReturnType<Query>> {\n    const [args, options] = argsAndOptions;\n    const name = getFunctionName(query);\n    return {\n      onUpdate: (callback) => {\n        const { queryToken, unsubscribe } = this.sync.subscribe(\n          name as string,\n          args,\n          options,\n        );\n\n        const currentListeners = this.listeners.get(queryToken);\n        if (currentListeners !== undefined) {\n          currentListeners.add(callback);\n        } else {\n          this.listeners.set(queryToken, new Set([callback]));\n        }\n\n        return () => {\n          if (this.closed) {\n            return;\n          }\n\n          const currentListeners = this.listeners.get(queryToken)!;\n          currentListeners.delete(callback);\n          if (currentListeners.size === 0) {\n            this.listeners.delete(queryToken);\n          }\n          unsubscribe();\n        };\n      },\n\n      localQueryResult: () => {\n        // Use the cached client because we can't have a query result if we don't\n        // even have a client yet!\n        if (this.cachedSync) {\n          return this.cachedSync.localQueryResult(name, args);\n        }\n        return undefined;\n      },\n\n      localQueryLogs: () => {\n        if (this.cachedSync) {\n          return this.cachedSync.localQueryLogs(name, args);\n        }\n        return undefined;\n      },\n\n      journal: () => {\n        if (this.cachedSync) {\n          return this.cachedSync.queryJournal(name, args);\n        }\n        return undefined;\n      },\n    };\n  }\n\n  // Let's try out a queryOptions-style API.\n  // This method is similar to the React Query API `queryClient.prefetchQuery()`.\n  // In the future an ensureQueryData(): Promise<Data> method could exist.\n  /**\n   * Indicates likely future interest in a query subscription.\n   *\n   * The implementation currently immediately subscribes to a query. In the future this method\n   * may prioritize some queries over others, fetch the query result without subscribing, or\n   * do nothing in slow network connections or high load scenarios.\n   *\n   * To use this in a React component, call useQuery() and ignore the return value.\n   *\n   * @param queryOptions - A query (function reference from an api object) and its args, plus\n   * an optional extendSubscriptionFor for how long to subscribe to the query.\n   */\n  prewarmQuery<Query extends FunctionReference<\"query\">>(\n    queryOptions: ConvexQueryOptions<Query> & {\n      extendSubscriptionFor?: number;\n    },\n  ) {\n    const extendSubscriptionFor =\n      queryOptions.extendSubscriptionFor ?? DEFAULT_EXTEND_SUBSCRIPTION_FOR;\n    const watch = this.watchQuery(queryOptions.query, queryOptions.args || {});\n    const unsubscribe = watch.onUpdate(() => {});\n    setTimeout(unsubscribe, extendSubscriptionFor);\n  }\n\n  /**\n   * Execute a mutation function.\n   *\n   * @param mutation - A {@link server.FunctionReference} for the public mutation\n   * to run.\n   * @param args - An arguments object for the mutation. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link MutationOptions} options object for the mutation.\n   * @returns A promise of the mutation's result.\n   */\n  mutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    ...argsAndOptions: ArgsAndOptions<\n      Mutation,\n      MutationOptions<FunctionArgs<Mutation>>\n    >\n  ): Promise<FunctionReturnType<Mutation>> {\n    const [args, options] = argsAndOptions;\n    const name = getFunctionName(mutation);\n    return this.sync.mutation(name, args, options);\n  }\n\n  /**\n   * Execute an action function.\n   *\n   * @param action - A {@link server.FunctionReference} for the public action\n   * to run.\n   * @param args - An arguments object for the action. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the action's result.\n   */\n  action<Action extends FunctionReference<\"action\">>(\n    action: Action,\n    ...args: OptionalRestArgs<Action>\n  ): Promise<FunctionReturnType<Action>> {\n    const name = getFunctionName(action);\n    return this.sync.action(name, ...args);\n  }\n\n  /**\n   * Fetch a query result once.\n   *\n   * **Most application code should subscribe to queries instead, using\n   * the {@link useQuery} hook.**\n   *\n   * @param query - A {@link server.FunctionReference} for the public query\n   * to run.\n   * @param args - An arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the query's result.\n   */\n  query<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): Promise<FunctionReturnType<Query>> {\n    const watch = this.watchQuery(query, ...args);\n    const existingResult = watch.localQueryResult();\n    if (existingResult !== undefined) {\n      return Promise.resolve(existingResult);\n    }\n    return new Promise((resolve, reject) => {\n      const unsubscribe = watch.onUpdate(() => {\n        unsubscribe();\n        try {\n          resolve(watch.localQueryResult());\n        } catch (e) {\n          reject(e);\n        }\n      });\n    });\n  }\n\n  /**\n   * Get the current {@link ConnectionState} between the client and the Convex\n   * backend.\n   *\n   * @returns The {@link ConnectionState} with the Convex backend.\n   */\n  connectionState(): ConnectionState {\n    return this.sync.connectionState();\n  }\n\n  /**\n   * Subscribe to the {@link ConnectionState} between the client and the Convex\n   * backend, calling a callback each time it changes.\n   *\n   * Subscribed callbacks will be called when any part of ConnectionState changes.\n   * ConnectionState may grow in future versions (e.g. to provide a array of\n   * inflight requests) in which case callbacks would be called more frequently.\n   * ConnectionState may also *lose* properties in future versions as we figure\n   * out what information is most useful. As such this API is considered unstable.\n   *\n   * @returns An unsubscribe function to stop listening.\n   */\n  subscribeToConnectionState(\n    cb: (connectionState: ConnectionState) => void,\n  ): () => void {\n    return this.sync.subscribeToConnectionState(cb);\n  }\n\n  /**\n   * Get the logger for this client.\n   *\n   * @returns The {@link Logger} for this client.\n   */\n  get logger(): Logger {\n    return this._logger;\n  }\n\n  /**\n   * Close any network handles associated with this client and stop all subscriptions.\n   *\n   * Call this method when you're done with a {@link ConvexReactClient} to\n   * dispose of its sockets and resources.\n   *\n   * @returns A `Promise` fulfilled when the connection has been completely closed.\n   */\n  async close(): Promise<void> {\n    this.closed = true;\n    // Prevent outstanding React batched updates from invoking listeners.\n    this.listeners = new Map();\n    if (this.cachedSync) {\n      const sync = this.cachedSync;\n      this.cachedSync = undefined;\n      await sync.close();\n    }\n  }\n\n  private transition(updatedQueries: QueryToken[]) {\n    for (const queryToken of updatedQueries) {\n      const callbacks = this.listeners.get(queryToken);\n      if (callbacks) {\n        for (const callback of callbacks) {\n          callback();\n        }\n      }\n    }\n  }\n}\n\nconst ConvexContext = React.createContext<ConvexReactClient>(\n  undefined as unknown as ConvexReactClient, // in the future this will be a mocked client for testing\n);\n\n/**\n * Get the {@link ConvexReactClient} within a React component.\n *\n * This relies on the {@link ConvexProvider} being above in the React component tree.\n *\n * @returns The active {@link ConvexReactClient} object, or `undefined`.\n *\n * @public\n */\nexport function useConvex(): ConvexReactClient {\n  return useContext(ConvexContext);\n}\n\n/**\n * Provides an active Convex {@link ConvexReactClient} to descendants of this component.\n *\n * Wrap your app in this component to use Convex hooks `useQuery`,\n * `useMutation`, and `useConvex`.\n *\n * @param props - an object with a `client` property that refers to a {@link ConvexReactClient}.\n *\n * @public\n */\nexport const ConvexProvider: React.FC<{\n  client: ConvexReactClient;\n  children?: React.ReactNode;\n}> = ({ client, children }) => {\n  return React.createElement(\n    ConvexContext.Provider,\n    { value: client },\n    children,\n  );\n};\n\nexport type OptionalRestArgsOrSkip<FuncRef extends FunctionReference<any>> =\n  FuncRef[\"_args\"] extends EmptyObject\n    ? [args?: EmptyObject | \"skip\"]\n    : [args: FuncRef[\"_args\"] | \"skip\"];\n\n/**\n * Load a reactive query within a React component.\n *\n * This React hook contains internal state that will cause a rerender\n * whenever the query result changes.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param query - a {@link server.FunctionReference} for the public query to run\n * like `api.dir1.dir2.filename.func`.\n * @param args - The arguments to the query function or the string \"skip\" if the\n * query should not be loaded.\n * @returns the result of the query. If the query is loading returns `undefined`.\n *\n * @public\n */\nexport function useQuery<Query extends FunctionReference<\"query\">>(\n  query: Query,\n  ...args: OptionalRestArgsOrSkip<Query>\n): Query[\"_returnType\"] | undefined {\n  const skip = args[0] === \"skip\";\n  const argsObject = args[0] === \"skip\" ? {} : parseArgs(args[0]);\n\n  const queryReference =\n    typeof query === \"string\"\n      ? makeFunctionReference<\"query\", any, any>(query)\n      : query;\n\n  const queryName = getFunctionName(queryReference);\n\n  const queries = useMemo(\n    () =>\n      skip\n        ? ({} as RequestForQueries)\n        : { query: { query: queryReference, args: argsObject } },\n    // Stringify args so args that are semantically the same don't trigger a\n    // rerender. Saves developers from adding `useMemo` on every args usage.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(convexToJson(argsObject)), queryName, skip],\n  );\n\n  const results = useQueries(queries);\n  const result = results[\"query\"];\n  if (result instanceof Error) {\n    throw result;\n  }\n  return result;\n}\n\n/**\n * Construct a new {@link ReactMutation}.\n *\n * Mutation objects can be called like functions to request execution of the\n * corresponding Convex function, or further configured with\n * [optimistic updates](https://docs.convex.dev/using/optimistic-updates).\n *\n * The value returned by this hook is stable across renders, so it can be used\n * by React dependency arrays and memoization logic relying on object identity\n * without causing rerenders.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param mutation - A {@link server.FunctionReference} for the public mutation\n * to run like `api.dir1.dir2.filename.func`.\n * @returns The {@link ReactMutation} object with that name.\n *\n * @public\n */\nexport function useMutation<Mutation extends FunctionReference<\"mutation\">>(\n  mutation: Mutation,\n): ReactMutation<Mutation> {\n  const mutationReference =\n    typeof mutation === \"string\"\n      ? makeFunctionReference<\"mutation\", any, any>(mutation)\n      : mutation;\n\n  const convex = useContext(ConvexContext);\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useMutation` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n  return useMemo(\n    () => createMutation(mutationReference, convex),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [convex, getFunctionName(mutationReference)],\n  );\n}\n\n/**\n * Construct a new {@link ReactAction}.\n *\n * Action objects can be called like functions to request execution of the\n * corresponding Convex function.\n *\n * The value returned by this hook is stable across renders, so it can be used\n * by React dependency arrays and memoization logic relying on object identity\n * without causing rerenders.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param action - A {@link server.FunctionReference} for the public action\n * to run like `api.dir1.dir2.filename.func`.\n * @returns The {@link ReactAction} object with that name.\n *\n * @public\n */\nexport function useAction<Action extends FunctionReference<\"action\">>(\n  action: Action,\n): ReactAction<Action> {\n  const convex = useContext(ConvexContext);\n  const actionReference =\n    typeof action === \"string\"\n      ? makeFunctionReference<\"action\", any, any>(action)\n      : action;\n\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useAction` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n  return useMemo(\n    () => createAction(actionReference, convex),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [convex, getFunctionName(actionReference)],\n  );\n}\n\n/**\n * React hook to get the current {@link ConnectionState} and subscribe to changes.\n *\n * This hook returns the current connection state and automatically rerenders\n * when any part of the connection state changes (e.g., when going online/offline,\n * when requests start/complete, etc.).\n *\n * The shape of ConnectionState may change in the future which may cause this\n * hook to rerender more frequently.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @returns The current {@link ConnectionState} with the Convex backend.\n *\n * @public\n */\nexport function useConvexConnectionState(): ConnectionState {\n  const convex = useContext(ConvexContext);\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useConvexConnectionState` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n\n  const getCurrentValue = useCallback(() => {\n    return convex.connectionState();\n  }, [convex]);\n\n  const subscribe = useCallback(\n    (callback: () => void) => {\n      return convex.subscribeToConnectionState(() => {\n        callback();\n      });\n    },\n    [convex],\n  );\n\n  return useSubscription({ getCurrentValue, subscribe });\n}\n\n// When a function is called with a single argument that looks like a\n// React SyntheticEvent it was likely called as an event handler.\nfunction assertNotAccidentalArgument(value: any) {\n  // these are properties of a React.SyntheticEvent\n  // https://reactjs.org/docs/events.html\n  if (\n    typeof value === \"object\" &&\n    value !== null &&\n    \"bubbles\" in value &&\n    \"persist\" in value &&\n    \"isDefaultPrevented\" in value\n  ) {\n    throw new Error(\n      `Convex function called with SyntheticEvent object. Did you use a Convex function as an event handler directly? Event handlers like onClick receive an event object as their first argument. These SyntheticEvent objects are not valid Convex values. Try wrapping the function like \\`const handler = () => myMutation();\\` and using \\`handler\\` in the event handler.`,\n    );\n  }\n}\n"], "names": ["currentListeners"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,SAAS,wBAAwB;;AAEjC,OAAO,SAAS,aAAa,YAAY,eAAe;AACxD,SAAS,oBAA2B;;AAQpC,SAA4B,kBAAkB;AAC9C,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B;AAUA;;;;;;;;;;;;;;;;;;AASA,MAAM,kCAAkC;AAExC,IAAI,OAAO,6OAAA,KAAU,aAAa;IAChC,MAAM,IAAI,MAAM,uCAAuC;AACzD;AAgDO,SAAS,eACd,iBAAA,EACA,MAAA,EACA,MAAA,EACoB;IACpB,SAAS,SAAS,IAAA,EAAgD;QAChE,4BAA4B,IAAI;QAEhC,OAAO,OAAO,QAAA,CAAS,mBAAmB,MAAM;YAC9C,kBAAkB;QACpB,CAAC;IACH;IACA,SAAS,oBAAA,GAAuB,SAAS,qBACvC,gBAAA,EACoB;QACpB,IAAI,WAAW,KAAA,GAAW;YACxB,MAAM,IAAI,MACR,oDAEC,WAFmD,mPAAA,EAClD;QAGN;QACA,OAAO,eAAe,mBAAmB,QAAQ,gBAAgB;IACnE;IACA,OAAO;AACT;AAkBA,SAAS,aACP,eAAA,EACA,MAAA,EACkB;IAClB,OAAO,SAAU,IAAA,EAAgD;QAC/D,OAAO,OAAO,MAAA,CAAO,iBAAiB,IAAI;IAC5C;AACF;AAmGO,MAAM,kBAAkB;IA2C7B;;;;;GAAA,GAQA,IAAI,MAAM;QACR,OAAO,IAAA,CAAK,OAAA;IACd;IAAA;;;;;GAAA,GAQA,IAAI,OAAO;QACT,IAAI,IAAA,CAAK,MAAA,EAAQ;YACf,MAAM,IAAI,MAAM,4CAA4C;QAC9D;QACA,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,OAAO,IAAA,CAAK,UAAA;QACd;QACA,IAAA,CAAK,UAAA,GAAa,IAAI,gQAAA,CACpB,IAAA,CAAK,OAAA,EACL,CAAC,iBAAmB,IAAA,CAAK,UAAA,CAAW,cAAc,GAClD,IAAA,CAAK,OAAA;QAEP,IAAI,IAAA,CAAK,SAAA,EAAW;YAClB,IAAA,CAAK,UAAA,CAAW,YAAA,CAAa,IAAA,CAAK,SAAA,EAAW,IAAA,CAAK,gBAAgB;QACpE;QACA,OAAO,IAAA,CAAK,UAAA;IACd;IAAA;;;;;;;GAAA,GAUA,QACE,UAAA,EACA,QAAA,EACA;QACA,IAAI,OAAO,eAAe,UAAU;YAClC,MAAM,IAAI,MACR;QAGJ;QACA,IAAA,CAAK,IAAA,CAAK,OAAA,CACR,uDACA,WACG,KAED,CAFO;IAIb;IAAA;;GAAA,GAKA,YAAY;QACV,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU;IACtB;IAAA;;GAAA,GAKA,aAAa,KAAA,EAAe,QAAA,EAAmC;QAC7D,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,gBAAA,GAAmB;QACxB,IAAI,IAAA,CAAK,MAAA,EAAQ;YACf,MAAM,IAAI,MAAM,4CAA4C;QAC9D;QACA,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,OAAO,QAAQ;QACxC;IACF;IAAA;;;;;;;;;;;;GAAA,GAeA,WACE,KAAA,EAEkC;QAFlC,IAAA,IAAA,OAAA,UAAA,QAAA,iBAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,eADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,CAAC,MAAM,OAAO,CAAA,GAAI;QACxB,MAAM,WAAO,mPAAA,EAAgB,KAAK;QAClC,OAAO;YACL,UAAU,CAAC,aAAa;gBACtB,MAAM,EAAE,UAAA,EAAY,WAAA,CAAY,CAAA,GAAI,IAAA,CAAK,IAAA,CAAK,SAAA,CAC5C,MACA,MACA;gBAGF,MAAM,mBAAmB,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,UAAU;gBACtD,IAAI,qBAAqB,KAAA,GAAW;oBAClC,iBAAiB,GAAA,CAAI,QAAQ;gBAC/B,OAAO;oBACL,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,YAAY,aAAA,GAAA,IAAI,IAAI;wBAAC,QAAQ;qBAAC,CAAC;gBACpD;gBAEA,OAAO,MAAM;oBACX,IAAI,IAAA,CAAK,MAAA,EAAQ;wBACf;oBACF;oBAEA,MAAMA,oBAAmB,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,UAAU;oBACtDA,kBAAiB,MAAA,CAAO,QAAQ;oBAChC,IAAIA,kBAAiB,IAAA,KAAS,GAAG;wBAC/B,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,UAAU;oBAClC;oBACA,YAAY;gBACd;YACF;YAEA,kBAAkB,MAAM;gBAGtB,IAAI,IAAA,CAAK,UAAA,EAAY;oBACnB,OAAO,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,MAAM,IAAI;gBACpD;gBACA,OAAO,KAAA;YACT;YAEA,gBAAgB,MAAM;gBACpB,IAAI,IAAA,CAAK,UAAA,EAAY;oBACnB,OAAO,IAAA,CAAK,UAAA,CAAW,cAAA,CAAe,MAAM,IAAI;gBAClD;gBACA,OAAO,KAAA;YACT;YAEA,SAAS,MAAM;gBACb,IAAI,IAAA,CAAK,UAAA,EAAY;oBACnB,OAAO,IAAA,CAAK,UAAA,CAAW,YAAA,CAAa,MAAM,IAAI;gBAChD;gBACA,OAAO,KAAA;YACT;QACF;IACF;IAAA,0CAAA;IAAA,+EAAA;IAAA,wEAAA;IAAA;;;;;;;;;;;GAAA,GAiBA,aACE,YAAA,EAGA;;QACA,MAAM,4EACS,qBAAA,+DAAb,sCAAsC;QACxC,MAAM,QAAQ,IAAA,CAAK,UAAA,CAAW,aAAa,KAAA,EAAO,aAAa,IAAA,IAAQ,CAAC,CAAC;QACzE,MAAM,cAAc,MAAM,QAAA,CAAS,KAAO,CAAD,AAAE;QAC3C,WAAW,aAAa,qBAAqB;IAC/C;IAAA;;;;;;;;;GAAA,GAYA,SACE,QAAA,EAKuC;QALvC,IAAA,IAAA,OAAA,UAAA,QAAA,iBAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,eADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAKH,MAAM,CAAC,MAAM,OAAO,CAAA,GAAI;QACxB,MAAM,WAAO,mPAAA,EAAgB,QAAQ;QACrC,OAAO,IAAA,CAAK,IAAA,CAAK,QAAA,CAAS,MAAM,MAAM,OAAO;IAC/C;IAAA;;;;;;;;GAAA,GAWA,OACE,MAAA,EAEqC;QAFrC,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,KADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,OAAO,uPAAA,EAAgB,MAAM;QACnC,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,MAAM,GAAG,IAAI;IACvC;IAAA;;;;;;;;;;;GAAA,GAcA,MACE,KAAA,EAEoC;QAFpC,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YACG,KADH,OAAA,KAAA,SAAA,CAAA,KACG;;QAEH,MAAM,QAAQ,IAAA,CAAK,UAAA,CAAW,OAAO,GAAG,IAAI;QAC5C,MAAM,iBAAiB,MAAM,gBAAA,CAAiB;QAC9C,IAAI,mBAAmB,KAAA,GAAW;YAChC,OAAO,QAAQ,OAAA,CAAQ,cAAc;QACvC;QACA,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;YACtC,MAAM,cAAc,MAAM,QAAA,CAAS,MAAM;gBACvC,YAAY;gBACZ,IAAI;oBACF,QAAQ,MAAM,gBAAA,CAAiB,CAAC;gBAClC,EAAA,OAAS,GAAG;oBACV,OAAO,CAAC;gBACV;YACF,CAAC;QACH,CAAC;IACH;IAAA;;;;;GAAA,GAQA,kBAAmC;QACjC,OAAO,IAAA,CAAK,IAAA,CAAK,eAAA,CAAgB;IACnC;IAAA;;;;;;;;;;;GAAA,GAcA,2BACE,EAAA,EACY;QACZ,OAAO,IAAA,CAAK,IAAA,CAAK,0BAAA,CAA2B,EAAE;IAChD;IAAA;;;;GAAA,GAOA,IAAI,SAAiB;QACnB,OAAO,IAAA,CAAK,OAAA;IACd;IAAA;;;;;;;GAAA,GAUA,MAAM,QAAuB;QAC3B,IAAA,CAAK,MAAA,GAAS;QAEd,IAAA,CAAK,SAAA,GAAY,aAAA,GAAA,IAAI,IAAI;QACzB,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,MAAM,OAAO,IAAA,CAAK,UAAA;YAClB,IAAA,CAAK,UAAA,GAAa,KAAA;YAClB,MAAM,KAAK,KAAA,CAAM;QACnB;IACF;IAEQ,WAAW,cAAA,EAA8B;QAC/C,KAAA,MAAW,cAAc,eAAgB;YACvC,MAAM,YAAY,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,UAAU;YAC/C,IAAI,WAAW;gBACb,KAAA,MAAW,YAAY,UAAW;oBAChC,SAAS;gBACX;YACF;QACF;IACF;IA5W6B;;;;GAAA,GAgB7B,YAAY,OAAA,EAAiB,OAAA,CAAoC;QAfjE,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ,UAAS;QACjB,cAAA,IAAA,EAAQ;QAER,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAUN,IAAI,YAAY,KAAA,GAAW;YACzB,MAAM,IAAI,MACR;QAIJ;QACA,IAAI,OAAO,YAAY,UAAU;YAC/B,MAAM,IAAI,MACR,4GAA0H,OAAd,OAAO,OAAO,EAAA;QAE9H;QACA,IAAI,CAAC,QAAQ,QAAA,CAAS,KAAK,GAAG;YAC5B,MAAM,IAAI,MAAM,2CAA2C;QAC7D;QACA,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,SAAA,GAAY,aAAA,GAAA,IAAI,IAAI;;QACzB,IAAA,CAAK,OAAA,sDACH,QAAS,MAAA,MAAW,YAChB,8PAAA,EAAsB;YAAE,uFAAkB,OAAA,4CAAT,mBAAoB;QAAM,CAAC,uDAC5D,QAAS,MAAA,MAAW,2DAAQ,QAAS,MAAA,IACnC,QAAQ,MAAA,OACR,iQAAA,EAAyB;YAAE,wFAAkB,OAAA,6CAAT,oBAAoB;QAAM,CAAC;QACvE,IAAA,CAAK,OAAA,GAAU;YAAE,GAAG,OAAA;YAAS,QAAQ,IAAA,CAAK,OAAA;QAAQ;IACpD;AAkUF;AAEA,MAAM,gBAAgB,6OAAA,CAAM,aAAA,CAC1B,KAAA;AAYK,SAAS,YAA+B;IAC7C,WAAO,gPAAA,EAAW,aAAa;AACjC;AAYO,MAAM,iBAGR;QAAC,EAAE,MAAA,EAAQ,QAAA,CAAS,CAAA,KAAM;IAC7B,OAAO,6OAAA,CAAM,aAAA,CACX,cAAc,QAAA,EACd;QAAE,OAAO;IAAO,GAChB;AAEJ;AAuBO,SAAS,SACd,KAAA;IAAA,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;QACG,KADH,OAAA,KAAA,SAAA,CAAA,KACG,EAC+B;;IAClC,MAAM,OAAO,IAAA,CAAK,CAAC,CAAA,KAAM;IACzB,MAAM,aAAa,IAAA,CAAK,CAAC,CAAA,KAAM,SAAS,CAAC,QAAI,+OAAA,EAAU,IAAA,CAAK,CAAC,CAAC;IAE9D,MAAM,iBACJ,OAAO,UAAU,eACb,yPAAA,EAAyC,KAAK,IAC9C;IAEN,MAAM,YAAY,uPAAA,EAAgB,cAAc;IAEhD,MAAM,cAAU,6OAAA;qCACd,IACE,OACK,CAAC,IACF;gBAAE,OAAO;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YAAE;oCAAA,wEAAA;IAAA,wEAAA;IAAA,uDAAA;IAI3D;QAAC,KAAK,SAAA,KAAU,kPAAA,EAAa,UAAU,CAAC;QAAG;QAAW,IAAI;KAAA;IAG5D,MAAM,cAAU,qPAAA,EAAW,OAAO;IAClC,MAAM,SAAS,OAAA,CAAQ,OAAO,CAAA;IAC9B,IAAI,kBAAkB,OAAO;QAC3B,MAAM;IACR;IACA,OAAO;AACT;AAqBO,SAAS,YACd,QAAA,EACyB;IACzB,MAAM,oBACJ,OAAO,aAAa,eAChB,yPAAA,EAA4C,QAAQ,IACpD;IAEN,MAAM,aAAS,gPAAA,EAAW,aAAa;IACvC,IAAI,WAAW,KAAA,GAAW;QACxB,MAAM,IAAI,MACR;IAIJ;IACA,WAAO,6OAAA;+BACL,IAAM,eAAe,mBAAmB,MAAM;8BAAA,uDAAA;IAE9C;QAAC;YAAQ,mPAAA,EAAgB,iBAAiB,CAAC;KAAA;AAE/C;AAoBO,SAAS,UACd,MAAA,EACqB;IACrB,MAAM,aAAS,gPAAA,EAAW,aAAa;IACvC,MAAM,kBACJ,OAAO,WAAW,WACd,6PAAA,EAA0C,MAAM,IAChD;IAEN,IAAI,WAAW,KAAA,GAAW;QACxB,MAAM,IAAI,MACR;IAIJ;IACA,WAAO,6OAAA;6BACL,IAAM,aAAa,iBAAiB,MAAM;4BAAA,uDAAA;IAE1C;QAAC;QAAQ,uPAAA,EAAgB,eAAe,CAAC;KAAA;AAE7C;AAkBO,SAAS,2BAA4C;IAC1D,MAAM,aAAS,gPAAA,EAAW,aAAa;IACvC,IAAI,WAAW,KAAA,GAAW;QACxB,MAAM,IAAI,MACR;IAIJ;IAEA,MAAM,kBAAkB,qPAAA;iEAAY,MAAM;YACxC,OAAO,OAAO,eAAA,CAAgB;QAChC;gEAAG;QAAC,MAAM;KAAC;IAEX,MAAM,gBAAY,iPAAA;2DAChB,CAAC,aAAyB;YACxB,OAAO,OAAO,0BAAA;mEAA2B,MAAM;oBAC7C,SAAS;gBACX,CAAC;;QACH;0DACA;QAAC,MAAM;KAAA;IAGT,WAAO,+PAAA,EAAgB;QAAE;QAAiB;IAAU,CAAC;AACvD;AAIA,SAAS,4BAA4B,KAAA,EAAY;IAG/C,IACE,OAAO,UAAU,YACjB,UAAU,QACV,aAAa,SACb,aAAa,SACb,wBAAwB,OACxB;QACA,MAAM,IAAI,MACR;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 5528, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/queries_observer.ts"], "sourcesContent": ["import { convexToJson, Value } from \"../values/index.js\";\nimport { Watch } from \"./client.js\";\nimport { QueryJournal } from \"../browser/sync/protocol.js\";\nimport { FunctionReference, getFunctionName } from \"../server/api.js\";\n\ntype Identifier = string;\n\ntype QueryInfo = {\n  query: FunctionReference<\"query\">;\n  args: Record<string, Value>;\n  watch: Watch<Value>;\n  unsubscribe: () => void;\n};\n\nexport type CreateWatch = (\n  query: FunctionReference<\"query\">,\n  args: Record<string, Value>,\n  journal?: QueryJournal,\n) => Watch<Value>;\n\n/**\n * A class for observing the results of multiple queries at the same time.\n *\n * Any time the result of a query changes, the listeners are notified.\n */\nexport class QueriesObserver {\n  public createWatch: CreateWatch;\n  private queries: Record<Identifier, QueryInfo>;\n  private listeners: Set<() => void>;\n\n  constructor(createWatch: CreateWatch) {\n    this.createWatch = createWatch;\n    this.queries = {};\n    this.listeners = new Set();\n  }\n\n  setQueries(\n    newQueries: Record<\n      Identifier,\n      { query: FunctionReference<\"query\">; args: Record<string, Value> }\n    >,\n  ) {\n    // Add the new queries before unsubscribing from the old ones so that\n    // the deduping in the `ConvexReactClient` can help if there are duplicates.\n    for (const identifier of Object.keys(newQueries)) {\n      const { query, args } = newQueries[identifier];\n      // Might throw\n      getFunctionName(query);\n\n      if (this.queries[identifier] === undefined) {\n        // No existing query => add it.\n        this.addQuery(identifier, query, args);\n      } else {\n        const existingInfo = this.queries[identifier];\n        if (\n          getFunctionName(query) !== getFunctionName(existingInfo.query) ||\n          JSON.stringify(convexToJson(args)) !==\n            JSON.stringify(convexToJson(existingInfo.args))\n        ) {\n          // Existing query that doesn't match => remove the old and add the new.\n          this.removeQuery(identifier);\n          this.addQuery(identifier, query, args);\n        }\n      }\n    }\n\n    // Prune all the existing queries that we no longer need.\n    for (const identifier of Object.keys(this.queries)) {\n      if (newQueries[identifier] === undefined) {\n        this.removeQuery(identifier);\n      }\n    }\n  }\n\n  subscribe(listener: () => void): () => void {\n    this.listeners.add(listener);\n    return () => {\n      this.listeners.delete(listener);\n    };\n  }\n\n  getLocalResults(\n    queries: Record<\n      Identifier,\n      { query: FunctionReference<\"query\">; args: Record<string, Value> }\n    >,\n  ): Record<Identifier, Value | undefined | Error> {\n    const result: Record<Identifier, Value | Error | undefined> = {};\n    for (const identifier of Object.keys(queries)) {\n      const { query, args } = queries[identifier];\n      // Might throw\n      getFunctionName(query);\n\n      // Note: We're not gonna watch, we could save some allocations\n      // by getting a reference to the client directly instead.\n      const watch = this.createWatch(query, args);\n      let value: Value | undefined | Error;\n      try {\n        value = watch.localQueryResult();\n      } catch (e) {\n        // Only collect instances of `Error` because thats how callers\n        // will distinguish errors from normal results.\n        if (e instanceof Error) {\n          value = e;\n        } else {\n          throw e;\n        }\n      }\n      result[identifier] = value;\n    }\n    return result;\n  }\n\n  setCreateWatch(createWatch: CreateWatch) {\n    this.createWatch = createWatch;\n    // If we have a new watch, we might be using a new Convex client.\n    // Recreate all the watches being careful to preserve the journals.\n    for (const identifier of Object.keys(this.queries)) {\n      const { query, args, watch } = this.queries[identifier];\n      const journal = watch.journal();\n      this.removeQuery(identifier);\n      this.addQuery(identifier, query, args, journal);\n    }\n  }\n\n  destroy() {\n    for (const identifier of Object.keys(this.queries)) {\n      this.removeQuery(identifier);\n    }\n    this.listeners = new Set();\n  }\n\n  private addQuery(\n    identifier: Identifier,\n    query: FunctionReference<\"query\">,\n    args: Record<string, Value>,\n    journal?: QueryJournal,\n  ) {\n    if (this.queries[identifier] !== undefined) {\n      throw new Error(\n        `Tried to add a new query with identifier ${identifier} when it already exists.`,\n      );\n    }\n    const watch = this.createWatch(query, args, journal);\n    const unsubscribe = watch.onUpdate(() => this.notifyListeners());\n    this.queries[identifier] = {\n      query,\n      args,\n      watch,\n      unsubscribe,\n    };\n  }\n\n  private removeQuery(identifier: Identifier) {\n    const info = this.queries[identifier];\n    if (info === undefined) {\n      throw new Error(`No query found with identifier ${identifier}.`);\n    }\n    info.unsubscribe();\n    delete this.queries[identifier];\n  }\n\n  private notifyListeners(): void {\n    for (const listener of this.listeners) {\n      listener();\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,oBAA2B;AAGpC,SAA4B,uBAAuB;;;;;;;;;;;;AAsB5C,MAAM,gBAAgB;IAW3B,WACE,UAAA,EAIA;QAGA,KAAA,MAAW,cAAc,OAAO,IAAA,CAAK,UAAU,EAAG;YAChD,MAAM,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,GAAI,UAAA,CAAW,UAAU,CAAA;YAE7C,IAAA,mPAAA,EAAgB,KAAK;YAErB,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAA,KAAM,KAAA,GAAW;gBAE1C,IAAA,CAAK,QAAA,CAAS,YAAY,OAAO,IAAI;YACvC,OAAO;gBACL,MAAM,eAAe,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAA;gBAC5C,QACE,mPAAA,EAAgB,KAAK,UAAM,mPAAA,EAAgB,aAAa,KAAK,KAC7D,KAAK,SAAA,KAAU,kPAAA,EAAa,IAAI,CAAC,MAC/B,KAAK,SAAA,KAAU,kPAAA,EAAa,aAAa,IAAI,CAAC,GAChD;oBAEA,IAAA,CAAK,WAAA,CAAY,UAAU;oBAC3B,IAAA,CAAK,QAAA,CAAS,YAAY,OAAO,IAAI;gBACvC;YACF;QACF;QAGA,KAAA,MAAW,cAAc,OAAO,IAAA,CAAK,IAAA,CAAK,OAAO,EAAG;YAClD,IAAI,UAAA,CAAW,UAAU,CAAA,KAAM,KAAA,GAAW;gBACxC,IAAA,CAAK,WAAA,CAAY,UAAU;YAC7B;QACF;IACF;IAEA,UAAU,QAAA,EAAkC;QAC1C,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,QAAQ;QAC3B,OAAO,MAAM;YACX,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,QAAQ;QAChC;IACF;IAEA,gBACE,OAAA,EAI+C;QAC/C,MAAM,SAAwD,CAAC;QAC/D,KAAA,MAAW,cAAc,OAAO,IAAA,CAAK,OAAO,EAAG;YAC7C,MAAM,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,GAAI,OAAA,CAAQ,UAAU,CAAA;YAE1C,IAAA,mPAAA,EAAgB,KAAK;YAIrB,MAAM,QAAQ,IAAA,CAAK,WAAA,CAAY,OAAO,IAAI;YAC1C,IAAI;YACJ,IAAI;gBACF,QAAQ,MAAM,gBAAA,CAAiB;YACjC,EAAA,OAAS,GAAG;gBAGV,IAAI,aAAa,OAAO;oBACtB,QAAQ;gBACV,OAAO;oBACL,MAAM;gBACR;YACF;YACA,MAAA,CAAO,UAAU,CAAA,GAAI;QACvB;QACA,OAAO;IACT;IAEA,eAAe,WAAA,EAA0B;QACvC,IAAA,CAAK,WAAA,GAAc;QAGnB,KAAA,MAAW,cAAc,OAAO,IAAA,CAAK,IAAA,CAAK,OAAO,EAAG;YAClD,MAAM,EAAE,KAAA,EAAO,IAAA,EAAM,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAA;YACtD,MAAM,UAAU,MAAM,OAAA,CAAQ;YAC9B,IAAA,CAAK,WAAA,CAAY,UAAU;YAC3B,IAAA,CAAK,QAAA,CAAS,YAAY,OAAO,MAAM,OAAO;QAChD;IACF;IAEA,UAAU;QACR,KAAA,MAAW,cAAc,OAAO,IAAA,CAAK,IAAA,CAAK,OAAO,EAAG;YAClD,IAAA,CAAK,WAAA,CAAY,UAAU;QAC7B;QACA,IAAA,CAAK,SAAA,GAAY,aAAA,GAAA,IAAI,IAAI;IAC3B;IAEQ,SACN,UAAA,EACA,KAAA,EACA,IAAA,EACA,OAAA,EACA;QACA,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAA,KAAM,KAAA,GAAW;YAC1C,MAAM,IAAI,MACR,4CAAsD,OAAV,UAAU,EAAA;QAE1D;QACA,MAAM,QAAQ,IAAA,CAAK,WAAA,CAAY,OAAO,MAAM,OAAO;QACnD,MAAM,cAAc,MAAM,QAAA,CAAS,IAAM,IAAA,CAAK,eAAA,CAAgB,CAAC;QAC/D,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAA,GAAI;YACzB;YACA;YACA;YACA;QACF;IACF;IAEQ,YAAY,UAAA,EAAwB;QAC1C,MAAM,OAAO,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAA;QACpC,IAAI,SAAS,KAAA,GAAW;YACtB,MAAM,IAAI,MAAM,kCAA4C,EAAG,KAAb,UAAU,EAAA;QAC9D;QACA,KAAK,WAAA,CAAY;QACjB,OAAO,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAA;IAChC;IAEQ,kBAAwB;QAC9B,KAAA,MAAW,YAAY,IAAA,CAAK,SAAA,CAAW;YACrC,SAAS;QACX;IACF;IAxIA,YAAY,WAAA,CAA0B;QAJtC,cAAA,IAAA,EAAO;QACP,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,OAAA,GAAU,CAAC;QAChB,IAAA,CAAK,SAAA,GAAY,aAAA,GAAA,IAAI,IAAI;IAC3B;AAqIF", "debugId": null}}, {"offset": {"line": 5647, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/use_queries.ts"], "sourcesContent": ["import { Value } from \"../values/index.js\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useConvex } from \"./client.js\";\nimport { CreateWatch, QueriesObserver } from \"./queries_observer.js\";\nimport { useSubscription } from \"./use_subscription.js\";\nimport { QueryJournal } from \"../browser/index.js\";\nimport { FunctionReference } from \"../server/api.js\";\n\n/**\n * Load a variable number of reactive Convex queries.\n *\n * `useQueries` is similar to {@link useQuery} but it allows\n * loading multiple queries which can be useful for loading a dynamic number\n * of queries without violating the rules of React hooks.\n *\n * This hook accepts an object whose keys are identifiers for each query and the\n * values are objects of `{ query: FunctionReference, args: Record<string, Value> }`. The\n * `query` is a FunctionReference for the Convex query function to load, and the `args` are\n * the arguments to that function.\n *\n * The hook returns an object that maps each identifier to the result of the query,\n * `undefined` if the query is still loading, or an instance of `Error` if the query\n * threw an exception.\n *\n * For example if you loaded a query like:\n * ```typescript\n * const results = useQueries({\n *   messagesInGeneral: {\n *     query: \"listMessages\",\n *     args: { channel: \"#general\" }\n *   }\n * });\n * ```\n * then the result would look like:\n * ```typescript\n * {\n *   messagesInGeneral: [{\n *     channel: \"#general\",\n *     body: \"hello\"\n *     _id: ...,\n *     _creationTime: ...\n *   }]\n * }\n * ```\n *\n * This React hook contains internal state that will cause a rerender\n * whenever any of the query results change.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param queries - An object mapping identifiers to objects of\n * `{query: string, args: Record<string, Value> }` describing which query\n * functions to fetch.\n * @returns An object with the same keys as the input. The values are the result\n * of the query function, `undefined` if it's still loading, or an `Error` if\n * it threw an exception.\n *\n * @public\n */\nexport function useQueries(\n  queries: RequestForQueries,\n): Record<string, any | undefined | Error> {\n  const convex = useConvex();\n  if (convex === undefined) {\n    // Error message includes `useQuery` because this hook is called by `useQuery`\n    // more often than it's called directly.\n    throw new Error(\n      \"Could not find Convex client! `useQuery` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n  const createWatch = useMemo(() => {\n    return (\n      query: FunctionReference<\"query\">,\n      args: Record<string, Value>,\n      journal?: QueryJournal,\n    ) => {\n      return convex.watchQuery(query, args, { journal });\n    };\n  }, [convex]);\n  return useQueriesHelper(queries, createWatch);\n}\n\n/**\n * Internal version of `useQueries` that is exported for testing.\n */\nexport function useQueriesHelper(\n  queries: RequestForQueries,\n  createWatch: CreateWatch,\n): Record<string, any | undefined | Error> {\n  const [observer] = useState(() => new QueriesObserver(createWatch));\n\n  if (observer.createWatch !== createWatch) {\n    observer.setCreateWatch(createWatch);\n  }\n\n  // Unsubscribe from all queries on unmount.\n  useEffect(() => () => observer.destroy(), [observer]);\n\n  const subscription = useMemo(\n    () => ({\n      getCurrentValue: () => {\n        return observer.getLocalResults(queries);\n      },\n      subscribe: (callback: () => void) => {\n        observer.setQueries(queries);\n        return observer.subscribe(callback);\n      },\n    }),\n    [observer, queries],\n  );\n\n  return useSubscription(subscription);\n}\n\n/**\n * An object representing a request to load multiple queries.\n *\n * The keys of this object are identifiers and the values are objects containing\n * the query function and the arguments to pass to it.\n *\n * This is used as an argument to {@link useQueries}.\n * @public\n */\nexport type RequestForQueries = Record<\n  string,\n  {\n    query: FunctionReference<\"query\">;\n    args: Record<string, Value>;\n  }\n>;\n"], "names": [], "mappings": ";;;;;;AACA,SAAS,WAAW,SAAS,gBAAgB;AAC7C,SAAS,iBAAiB;AAC1B,SAAsB,uBAAuB;AAC7C,SAAS,uBAAuB;;;;;;AAuDzB,SAAS,WACd,OAAA,EACyC;IACzC,MAAM,aAAS,+OAAA,CAAU;IACzB,IAAI,WAAW,KAAA,GAAW;QAGxB,MAAM,IAAI,MACR;IAIJ;IACA,MAAM,kBAAc,6OAAA;2CAAQ,MAAM;YAChC;mDAAO,CACL,OACA,MACA,YACG;oBACH,OAAO,OAAO,UAAA,CAAW,OAAO,MAAM;wBAAE;oBAAQ,CAAC;gBACnD;;QACF;0CAAG;QAAC,MAAM;KAAC;IACX,OAAO,iBAAiB,SAAS,WAAW;AAC9C;AAKO,SAAS,iBACd,OAAA,EACA,WAAA,EACyC;IACzC,MAAM,CAAC,QAAQ,CAAA,OAAI,8OAAA;qCAAS,IAAM,IAAI,+PAAA,CAAgB,WAAW,CAAC;;IAElE,IAAI,SAAS,WAAA,KAAgB,aAAa;QACxC,SAAS,cAAA,CAAe,WAAW;IACrC;IAGA,IAAA,+OAAA;sCAAU;8CAAM,IAAM,SAAS,OAAA,CAAQ;;qCAAG;QAAC,QAAQ;KAAC;IAEpD,MAAM,mBAAe,6OAAA;kDACnB,IAAA,CAAO;gBACL,eAAA;8DAAiB,MAAM;wBACrB,OAAO,SAAS,eAAA,CAAgB,OAAO;oBACzC;;gBACA,SAAA;8DAAW,CAAC,aAAyB;wBACnC,SAAS,UAAA,CAAW,OAAO;wBAC3B,OAAO,SAAS,SAAA,CAAU,QAAQ;oBACpC;;YACF,CAAA;iDACA;QAAC;QAAU,OAAO;KAAA;IAGpB,WAAO,+PAAA,EAAgB,YAAY;AACrC", "debugId": null}}, {"offset": {"line": 5720, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/use_paginated_query.ts"], "sourcesContent": ["import { useMemo, useState } from \"react\";\n\nimport { OptimisticLocalStore } from \"../browser/index.js\";\nimport {\n  FunctionReturnType,\n  PaginationOptions,\n  paginationOptsValidator,\n  PaginationResult,\n} from \"../server/index.js\";\nimport { ConvexError, convexTo<PERSON>son, Infer, Value } from \"../values/index.js\";\nimport { useQueries } from \"./use_queries.js\";\nimport {\n  FunctionArgs,\n  FunctionReference,\n  getFunctionName,\n} from \"../server/api.js\";\nimport { BetterOmit, Expand } from \"../type_utils.js\";\nimport { useConvex } from \"./client.js\";\nimport { compareValues } from \"../values/compare.js\";\n\n/**\n * A {@link server.FunctionReference} that is usable with {@link usePaginatedQuery}.\n *\n * This function reference must:\n * - Refer to a public query\n * - Have an argument named \"paginationOpts\" of type {@link server.PaginationOptions}\n * - Have a return type of {@link server.PaginationResult}.\n *\n * @public\n */\nexport type PaginatedQueryReference = FunctionReference<\n  \"query\",\n  \"public\",\n  { paginationOpts: PaginationOptions },\n  PaginationResult<any>\n>;\n\n// Incrementing integer for each page queried in the usePaginatedQuery hook.\ntype QueryPageKey = number;\n\ntype UsePaginatedQueryState = {\n  query: FunctionReference<\"query\">;\n  args: Record<string, Value>;\n  id: number;\n  nextPageKey: QueryPageKey;\n  pageKeys: QueryPageKey[];\n  queries: Record<\n    QueryPageKey,\n    {\n      query: FunctionReference<\"query\">;\n      // Use the validator type as a test that it matches the args\n      // we generate.\n      args: { paginationOpts: Infer<typeof paginationOptsValidator> };\n    }\n  >;\n  ongoingSplits: Record<QueryPageKey, [QueryPageKey, QueryPageKey]>;\n  skip: boolean;\n};\n\nconst splitQuery =\n  (key: QueryPageKey, splitCursor: string, continueCursor: string) =>\n  (prevState: UsePaginatedQueryState) => {\n    const queries = { ...prevState.queries };\n    const splitKey1 = prevState.nextPageKey;\n    const splitKey2 = prevState.nextPageKey + 1;\n    const nextPageKey = prevState.nextPageKey + 2;\n    queries[splitKey1] = {\n      query: prevState.query,\n      args: {\n        ...prevState.args,\n        paginationOpts: {\n          ...prevState.queries[key].args.paginationOpts,\n          endCursor: splitCursor,\n        },\n      },\n    };\n    queries[splitKey2] = {\n      query: prevState.query,\n      args: {\n        ...prevState.args,\n        paginationOpts: {\n          ...prevState.queries[key].args.paginationOpts,\n          cursor: splitCursor,\n          endCursor: continueCursor,\n        },\n      },\n    };\n    const ongoingSplits = { ...prevState.ongoingSplits };\n    ongoingSplits[key] = [splitKey1, splitKey2];\n    return {\n      ...prevState,\n      nextPageKey,\n      queries,\n      ongoingSplits,\n    };\n  };\n\nconst completeSplitQuery =\n  (key: QueryPageKey) => (prevState: UsePaginatedQueryState) => {\n    const completedSplit = prevState.ongoingSplits[key];\n    if (completedSplit === undefined) {\n      return prevState;\n    }\n    const queries = { ...prevState.queries };\n    delete queries[key];\n    const ongoingSplits = { ...prevState.ongoingSplits };\n    delete ongoingSplits[key];\n    let pageKeys = prevState.pageKeys.slice();\n    const pageIndex = prevState.pageKeys.findIndex((v) => v === key);\n    if (pageIndex >= 0) {\n      pageKeys = [\n        ...prevState.pageKeys.slice(0, pageIndex),\n        ...completedSplit,\n        ...prevState.pageKeys.slice(pageIndex + 1),\n      ];\n    }\n    return {\n      ...prevState,\n      queries,\n      pageKeys,\n      ongoingSplits,\n    };\n  };\n\n/**\n * Load data reactively from a paginated query to a create a growing list.\n *\n * This can be used to power \"infinite scroll\" UIs.\n *\n * This hook must be used with public query references that match\n * {@link PaginatedQueryReference}.\n *\n * `usePaginatedQuery` concatenates all the pages of results into a single list\n * and manages the continuation cursors when requesting more items.\n *\n * Example usage:\n * ```typescript\n * const { results, status, isLoading, loadMore } = usePaginatedQuery(\n *   api.messages.list,\n *   { channel: \"#general\" },\n *   { initialNumItems: 5 }\n * );\n * ```\n *\n * If the query reference or arguments change, the pagination state will be reset\n * to the first page. Similarly, if any of the pages result in an InvalidCursor\n * error or an error associated with too much data, the pagination state will also\n * reset to the first page.\n *\n * To learn more about pagination, see [Paginated Queries](https://docs.convex.dev/database/pagination).\n *\n * @param query - A FunctionReference to the public query function to run.\n * @param args - The arguments object for the query function, excluding\n * the `paginationOpts` property. That property is injected by this hook.\n * @param options - An object specifying the `initialNumItems` to be loaded in\n * the first page.\n * @returns A {@link UsePaginatedQueryResult} that includes the currently loaded\n * items, the status of the pagination, and a `loadMore` function.\n *\n * @public\n */\nexport function usePaginatedQuery<Query extends PaginatedQueryReference>(\n  query: Query,\n  args: PaginatedQueryArgs<Query> | \"skip\",\n  options: { initialNumItems: number },\n): UsePaginatedQueryReturnType<Query> {\n  if (\n    typeof options?.initialNumItems !== \"number\" ||\n    options.initialNumItems < 0\n  ) {\n    throw new Error(\n      `\\`options.initialNumItems\\` must be a positive number. Received \\`${options?.initialNumItems}\\`.`,\n    );\n  }\n  const skip = args === \"skip\";\n  const argsObject = skip ? {} : args;\n  const queryName = getFunctionName(query);\n  const createInitialState = useMemo(() => {\n    return () => {\n      const id = nextPaginationId();\n      return {\n        query,\n        args: argsObject as Record<string, Value>,\n        id,\n        nextPageKey: 1,\n        pageKeys: skip ? [] : [0],\n        queries: skip\n          ? ({} as UsePaginatedQueryState[\"queries\"])\n          : {\n              0: {\n                query,\n                args: {\n                  ...argsObject,\n                  paginationOpts: {\n                    numItems: options.initialNumItems,\n                    cursor: null,\n                    id,\n                  },\n                },\n              },\n            },\n        ongoingSplits: {},\n        skip,\n      };\n    };\n    // ESLint doesn't like that we're stringifying the args. We do this because\n    // we want to avoid rerendering if the args are a different\n    // object that serializes to the same result.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    JSON.stringify(convexToJson(argsObject as Value)),\n    queryName,\n    options.initialNumItems,\n    skip,\n  ]);\n\n  const [state, setState] =\n    useState<UsePaginatedQueryState>(createInitialState);\n\n  // `currState` is the state that we'll render based on.\n  let currState = state;\n  if (\n    getFunctionName(query) !== getFunctionName(state.query) ||\n    JSON.stringify(convexToJson(argsObject as Value)) !==\n      JSON.stringify(convexToJson(state.args)) ||\n    skip !== state.skip\n  ) {\n    currState = createInitialState();\n    setState(currState);\n  }\n  const convexClient = useConvex();\n  const logger = convexClient.logger;\n\n  const resultsObject = useQueries(currState.queries);\n\n  const [results, maybeLastResult]: [\n    Value[],\n    undefined | PaginationResult<Value>,\n  ] = useMemo(() => {\n    let currResult = undefined;\n\n    const allItems = [];\n    for (const pageKey of currState.pageKeys) {\n      currResult = resultsObject[pageKey];\n      if (currResult === undefined) {\n        break;\n      }\n\n      if (currResult instanceof Error) {\n        if (\n          currResult.message.includes(\"InvalidCursor\") ||\n          (currResult instanceof ConvexError &&\n            typeof currResult.data === \"object\" &&\n            currResult.data?.isConvexSystemError === true &&\n            currResult.data?.paginationError === \"InvalidCursor\")\n        ) {\n          // - InvalidCursor: If the cursor is invalid, probably the paginated\n          // database query was data-dependent and changed underneath us. The\n          // cursor in the params or journal no longer matches the current\n          // database query.\n\n          // In all cases, we want to restart pagination to throw away all our\n          // existing cursors.\n          logger.warn(\n            \"usePaginatedQuery hit error, resetting pagination state: \" +\n              currResult.message,\n          );\n          setState(createInitialState);\n          return [[], undefined];\n        } else {\n          throw currResult;\n        }\n      }\n      const ongoingSplit = currState.ongoingSplits[pageKey];\n      if (ongoingSplit !== undefined) {\n        if (\n          resultsObject[ongoingSplit[0]] !== undefined &&\n          resultsObject[ongoingSplit[1]] !== undefined\n        ) {\n          // Both pages of the split have results now. Swap them in.\n          setState(completeSplitQuery(pageKey));\n        }\n      } else if (\n        currResult.splitCursor &&\n        (currResult.pageStatus === \"SplitRecommended\" ||\n          currResult.pageStatus === \"SplitRequired\" ||\n          currResult.page.length > options.initialNumItems * 2)\n      ) {\n        // If a single page has more than double the expected number of items,\n        // or if the server requests a split, split the page into two.\n        setState(\n          splitQuery(\n            pageKey,\n            currResult.splitCursor,\n            currResult.continueCursor,\n          ),\n        );\n      }\n      if (currResult.pageStatus === \"SplitRequired\") {\n        // If pageStatus is 'SplitRequired', it means the server was not able to\n        // fetch the full page. So we stop results before the incomplete\n        // page and return 'LoadingMore' while the page is splitting.\n        return [allItems, undefined];\n      }\n      allItems.push(...currResult.page);\n    }\n    return [allItems, currResult];\n  }, [\n    resultsObject,\n    currState.pageKeys,\n    currState.ongoingSplits,\n    options.initialNumItems,\n    createInitialState,\n    logger,\n  ]);\n\n  const statusObject = useMemo(() => {\n    if (maybeLastResult === undefined) {\n      if (currState.nextPageKey === 1) {\n        return {\n          status: \"LoadingFirstPage\",\n          isLoading: true,\n          loadMore: (_numItems: number) => {\n            // Intentional noop.\n          },\n        } as const;\n      } else {\n        return {\n          status: \"LoadingMore\",\n          isLoading: true,\n          loadMore: (_numItems: number) => {\n            // Intentional noop.\n          },\n        } as const;\n      }\n    }\n    if (maybeLastResult.isDone) {\n      return {\n        status: \"Exhausted\",\n        isLoading: false,\n        loadMore: (_numItems: number) => {\n          // Intentional noop.\n        },\n      } as const;\n    }\n    const continueCursor = maybeLastResult.continueCursor;\n    let alreadyLoadingMore = false;\n    return {\n      status: \"CanLoadMore\",\n      isLoading: false,\n      loadMore: (numItems: number) => {\n        if (!alreadyLoadingMore) {\n          alreadyLoadingMore = true;\n          setState((prevState) => {\n            const pageKeys = [...prevState.pageKeys, prevState.nextPageKey];\n            const queries = { ...prevState.queries };\n            queries[prevState.nextPageKey] = {\n              query: prevState.query,\n              args: {\n                ...prevState.args,\n                paginationOpts: {\n                  numItems,\n                  cursor: continueCursor,\n                  id: prevState.id,\n                },\n              },\n            };\n            return {\n              ...prevState,\n              nextPageKey: prevState.nextPageKey + 1,\n              pageKeys,\n              queries,\n            };\n          });\n        }\n      },\n    } as const;\n  }, [maybeLastResult, currState.nextPageKey]);\n\n  return {\n    results,\n    ...statusObject,\n  };\n}\n\nlet paginationId = 0;\n/**\n * Generate a new, unique ID for a pagination session.\n *\n * Every usage of {@link usePaginatedQuery} puts a unique ID into the\n * query function arguments as a \"cache-buster\". This serves two purposes:\n *\n * 1. All calls to {@link usePaginatedQuery} have independent query\n * journals.\n *\n * Every time we start a new pagination session, we'll load the first page of\n * results and receive a fresh journal. Without the ID, we might instead reuse\n * a query subscription already present in our client. This isn't desirable\n * because the existing query function result may have grown or shrunk from the\n * requested `initialNumItems`.\n *\n * 2. We can restart the pagination session on some types of errors.\n *\n * Sometimes we want to restart pagination from the beginning if we hit an error.\n * Similar to (1), we'd like to ensure that this new session actually requests\n * its first page from the server and doesn't reuse a query result already\n * present in the client that may have hit the error.\n *\n * @returns The pagination ID.\n */\nfunction nextPaginationId(): number {\n  paginationId++;\n  return paginationId;\n}\n\n/**\n * Reset pagination id for tests only, so tests know what it is.\n */\nexport function resetPaginationId() {\n  paginationId = 0;\n}\n\n/**\n * The result of calling the {@link usePaginatedQuery} hook.\n *\n * This includes:\n * - `results` - An array of the currently loaded results.\n * - `isLoading` - Whether the hook is currently loading results.\n * - `status` - The status of the pagination. The possible statuses are:\n *   - \"LoadingFirstPage\": The hook is loading the first page of results.\n *   - \"CanLoadMore\": This query may have more items to fetch. Call `loadMore` to\n *   fetch another page.\n *   - \"LoadingMore\": We're currently loading another page of results.\n *   - \"Exhausted\": We've paginated to the end of the list.\n * - `loadMore(n)` A callback to fetch more results. This will only fetch more\n * results if the status is \"CanLoadMore\".\n *\n * @public\n */\nexport type UsePaginatedQueryResult<Item> = {\n  results: Item[];\n  loadMore: (numItems: number) => void;\n} & (\n  | {\n      status: \"LoadingFirstPage\";\n      isLoading: true;\n    }\n  | {\n      status: \"CanLoadMore\";\n      isLoading: false;\n    }\n  | {\n      status: \"LoadingMore\";\n      isLoading: true;\n    }\n  | {\n      status: \"Exhausted\";\n      isLoading: false;\n    }\n);\n\n/**\n * The possible pagination statuses in {@link UsePaginatedQueryResult}.\n *\n * This is a union of string literal types.\n * @public\n */\nexport type PaginationStatus = UsePaginatedQueryResult<any>[\"status\"];\n\n/**\n * Given a {@link PaginatedQueryReference}, get the type of the arguments\n * object for the query, excluding the `paginationOpts` argument.\n *\n * @public\n */\nexport type PaginatedQueryArgs<Query extends PaginatedQueryReference> = Expand<\n  BetterOmit<FunctionArgs<Query>, \"paginationOpts\">\n>;\n\n/**\n * Given a {@link PaginatedQueryReference}, get the type of the item being\n * paginated over.\n * @public\n */\nexport type PaginatedQueryItem<Query extends PaginatedQueryReference> =\n  FunctionReturnType<Query>[\"page\"][number];\n\n/**\n * The return type of {@link usePaginatedQuery}.\n *\n * @public\n */\nexport type UsePaginatedQueryReturnType<Query extends PaginatedQueryReference> =\n  UsePaginatedQueryResult<PaginatedQueryItem<Query>>;\n\n/**\n * Optimistically update the values in a paginated list.\n *\n * This optimistic update is designed to be used to update data loaded with\n * {@link usePaginatedQuery}. It updates the list by applying\n * `updateValue` to each element of the list across all of the loaded pages.\n *\n * This will only apply to queries with a matching names and arguments.\n *\n * Example usage:\n * ```ts\n * const myMutation = useMutation(api.myModule.myMutation)\n * .withOptimisticUpdate((localStore, mutationArg) => {\n *\n *   // Optimistically update the document with ID `mutationArg`\n *   // to have an additional property.\n *\n *   optimisticallyUpdateValueInPaginatedQuery(\n *     localStore,\n *     api.myModule.paginatedQuery\n *     {},\n *     currentValue => {\n *       if (mutationArg === currentValue._id) {\n *         return {\n *           ...currentValue,\n *           \"newProperty\": \"newValue\",\n *         };\n *       }\n *       return currentValue;\n *     }\n *   );\n *\n * });\n * ```\n *\n * @param localStore - An {@link OptimisticLocalStore} to update.\n * @param query - A {@link FunctionReference} for the paginated query to update.\n * @param args - The arguments object to the query function, excluding the\n * `paginationOpts` property.\n * @param updateValue - A function to produce the new values.\n *\n * @public\n */\nexport function optimisticallyUpdateValueInPaginatedQuery<\n  Query extends PaginatedQueryReference,\n>(\n  localStore: OptimisticLocalStore,\n  query: Query,\n  args: PaginatedQueryArgs<Query>,\n  updateValue: (\n    currentValue: PaginatedQueryItem<Query>,\n  ) => PaginatedQueryItem<Query>,\n): void {\n  const expectedArgs = JSON.stringify(convexToJson(args as Value));\n\n  for (const queryResult of localStore.getAllQueries(query)) {\n    if (queryResult.value !== undefined) {\n      const { paginationOpts: _, ...innerArgs } = queryResult.args as {\n        paginationOpts: PaginationOptions;\n      };\n      if (JSON.stringify(convexToJson(innerArgs as Value)) === expectedArgs) {\n        const value = queryResult.value;\n        if (\n          typeof value === \"object\" &&\n          value !== null &&\n          Array.isArray(value.page)\n        ) {\n          localStore.setQuery(query, queryResult.args, {\n            ...value,\n            page: value.page.map(updateValue),\n          });\n        }\n      }\n    }\n  }\n}\n\n/**\n * Updates a paginated query to insert an element at the top of the list.\n *\n * This is regardless of the sort order, so if the list is in descending order,\n * the inserted element will be treated as the \"biggest\" element, but if it's\n * ascending, it'll be treated as the \"smallest\".\n *\n * Example:\n * ```ts\n * const createTask = useMutation(api.tasks.create)\n *   .withOptimisticUpdate((localStore, mutationArgs) => {\n *   insertAtTop({\n *     paginatedQuery: api.tasks.list,\n *     argsToMatch: { listId: mutationArgs.listId },\n *     localQueryStore: localStore,\n *     item: { _id: crypto.randomUUID() as Id<\"tasks\">, title: mutationArgs.title, completed: false },\n *   });\n * });\n * ```\n *\n * @param options.paginatedQuery - A function reference to the paginated query.\n * @param options.argsToMatch - Optional arguments that must be in each relevant paginated query.\n * This is useful if you use the same query function with different arguments to load\n * different lists.\n * @param options.localQueryStore\n * @param options.item The item to insert.\n * @returns\n */\nexport function insertAtTop<Query extends PaginatedQueryReference>(options: {\n  paginatedQuery: Query;\n  argsToMatch?: Partial<PaginatedQueryArgs<Query>>;\n  localQueryStore: OptimisticLocalStore;\n  item: PaginatedQueryItem<Query>;\n}) {\n  const { paginatedQuery, argsToMatch, localQueryStore, item } = options;\n  const queries = localQueryStore.getAllQueries(paginatedQuery);\n  const queriesThatMatch = queries.filter((q) => {\n    if (argsToMatch === undefined) {\n      return true;\n    }\n    return Object.keys(argsToMatch).every(\n      // @ts-expect-error -- This should be safe since both should be plain objects\n      (k) => compareValues(argsToMatch[k], q.args[k]) === 0,\n    );\n  });\n  const firstPage = queriesThatMatch.find(\n    (q) => q.args.paginationOpts.cursor === null,\n  );\n  if (firstPage === undefined || firstPage.value === undefined) {\n    // first page is not loaded, so don't update it until it loads\n    return;\n  }\n  localQueryStore.setQuery(paginatedQuery, firstPage.args, {\n    ...firstPage.value,\n    page: [item, ...firstPage.value.page],\n  });\n}\n\n/**\n * Updates a paginated query to insert an element at the bottom of the list.\n *\n * This is regardless of the sort order, so if the list is in descending order,\n * the inserted element will be treated as the \"smallest\" element, but if it's\n * ascending, it'll be treated as the \"biggest\".\n *\n * This only has an effect if the last page is loaded, since otherwise it would result\n * in the element being inserted at the end of whatever is loaded (which is the middle of the list)\n * and then popping out once the optimistic update is over.\n *\n * @param options.paginatedQuery - A function reference to the paginated query.\n * @param options.argsToMatch - Optional arguments that must be in each relevant paginated query.\n * This is useful if you use the same query function with different arguments to load\n * different lists.\n * @param options.localQueryStore\n * @param options.element The element to insert.\n * @returns\n */\nexport function insertAtBottomIfLoaded<\n  Query extends PaginatedQueryReference,\n>(options: {\n  paginatedQuery: Query;\n  argsToMatch?: Partial<PaginatedQueryArgs<Query>>;\n  localQueryStore: OptimisticLocalStore;\n  item: PaginatedQueryItem<Query>;\n}) {\n  const { paginatedQuery, localQueryStore, item, argsToMatch } = options;\n  const queries = localQueryStore.getAllQueries(paginatedQuery);\n  const queriesThatMatch = queries.filter((q) => {\n    if (argsToMatch === undefined) {\n      return true;\n    }\n    return Object.keys(argsToMatch).every(\n      // @ts-expect-error -- This should be safe since both should be plain objects\n      (k) => compareValues(argsToMatch[k], q.args[k]) === 0,\n    );\n  });\n  const lastPage = queriesThatMatch.find(\n    (q) => q.value !== undefined && q.value.isDone,\n  );\n  if (lastPage === undefined) {\n    // last page is not loaded, so don't update it since the item would immediately pop out\n    // when the server updates\n    return;\n  }\n  localQueryStore.setQuery(paginatedQuery, lastPage.args, {\n    ...lastPage.value!,\n    page: [...lastPage.value!.page, item],\n  });\n}\n\ntype LocalQueryResult<Query extends FunctionReference<\"query\">> = {\n  args: FunctionArgs<Query>;\n  value: undefined | FunctionReturnType<Query>;\n};\n\ntype LoadedResult<Query extends FunctionReference<\"query\">> = {\n  args: FunctionArgs<Query>;\n  value: FunctionReturnType<Query>;\n};\n\n/**\n * This is a helper function for inserting an item at a specific position in a paginated query.\n *\n * You must provide the sortOrder and a function for deriving the sort key (an array of values) from an item in the list.\n *\n * This will only work if the server query uses the same sort order and sort key as the optimistic update.\n *\n * Example:\n * ```ts\n * const createTask = useMutation(api.tasks.create)\n *   .withOptimisticUpdate((localStore, mutationArgs) => {\n *   insertAtPosition({\n *     paginatedQuery: api.tasks.listByPriority,\n *     argsToMatch: { listId: mutationArgs.listId },\n *     sortOrder: \"asc\",\n *     sortKeyFromItem: (item) => [item.priority, item._creationTime],\n *     localQueryStore: localStore,\n *     item: {\n *       _id: crypto.randomUUID() as Id<\"tasks\">,\n *       _creationTime: Date.now(),\n *       title: mutationArgs.title,\n *       completed: false,\n *       priority: mutationArgs.priority,\n *     },\n *   });\n * });\n * ```\n * @param options.paginatedQuery - A function reference to the paginated query.\n * @param options.argsToMatch - Optional arguments that must be in each relevant paginated query.\n * This is useful if you use the same query function with different arguments to load\n * different lists.\n * @param options.sortOrder - The sort order of the paginated query (\"asc\" or \"desc\").\n * @param options.sortKeyFromItem - A function for deriving the sort key (an array of values) from an element in the list.\n * Including a tie-breaker field like `_creationTime` is recommended.\n * @param options.localQueryStore\n * @param options.item - The item to insert.\n * @returns\n */\nexport function insertAtPosition<\n  Query extends PaginatedQueryReference,\n>(options: {\n  paginatedQuery: Query;\n  argsToMatch?: Partial<PaginatedQueryArgs<Query>>;\n  sortOrder: \"asc\" | \"desc\";\n  sortKeyFromItem: (element: PaginatedQueryItem<Query>) => Value | Value[];\n  localQueryStore: OptimisticLocalStore;\n  item: PaginatedQueryItem<Query>;\n}) {\n  const {\n    paginatedQuery,\n    sortOrder,\n    sortKeyFromItem,\n    localQueryStore,\n    item,\n    argsToMatch,\n  } = options;\n\n  const queries: LocalQueryResult<Query>[] =\n    localQueryStore.getAllQueries(paginatedQuery);\n  // Group into sets of pages for the same usePaginatedQuery. Grouping is by all\n  // args except paginationOpts, but including paginationOpts.id.\n  const queryGroups: Record<string, LocalQueryResult<Query>[]> = {};\n  for (const query of queries) {\n    if (\n      argsToMatch !== undefined &&\n      !Object.keys(argsToMatch).every(\n        (k) =>\n          // @ts-ignore why is this not working?\n          argsToMatch[k] === query.args[k],\n      )\n    ) {\n      continue;\n    }\n    const key = JSON.stringify(\n      Object.fromEntries(\n        Object.entries(query.args).map(([k, v]) => [\n          k,\n          k === \"paginationOpts\" ? (v as any).id : v,\n        ]),\n      ),\n    );\n    queryGroups[key] ??= [];\n    queryGroups[key].push(query);\n  }\n  for (const pageQueries of Object.values(queryGroups)) {\n    insertAtPositionInPages({\n      pageQueries,\n      paginatedQuery,\n      sortOrder,\n      sortKeyFromItem,\n      localQueryStore,\n      item,\n    });\n  }\n}\n\nfunction insertAtPositionInPages<\n  Query extends PaginatedQueryReference,\n>(options: {\n  pageQueries: LocalQueryResult<Query>[];\n  paginatedQuery: Query;\n  sortOrder: \"asc\" | \"desc\";\n  sortKeyFromItem: (element: PaginatedQueryItem<Query>) => Value | Value[];\n  localQueryStore: OptimisticLocalStore;\n  item: PaginatedQueryItem<Query>;\n}) {\n  const {\n    pageQueries,\n    sortOrder,\n    sortKeyFromItem,\n    localQueryStore,\n    item,\n    paginatedQuery,\n  } = options;\n  const insertedKey = sortKeyFromItem(item);\n  const loadedPages: LoadedResult<Query>[] = pageQueries.filter(\n    (q): q is LoadedResult<Query> =>\n      q.value !== undefined && q.value.page.length > 0,\n  );\n  const sortedPages = loadedPages.sort((a, b) => {\n    const aKey = sortKeyFromItem(a.value.page[0]);\n    const bKey = sortKeyFromItem(b.value.page[0]);\n    if (sortOrder === \"asc\") {\n      return compareValues(aKey, bKey);\n    } else {\n      return compareValues(bKey, aKey);\n    }\n  });\n\n  // check if the inserted element is before the first page\n  const firstLoadedPage = sortedPages[0];\n  if (firstLoadedPage === undefined) {\n    // no pages, so don't update until they load\n    return;\n  }\n  const firstPageKey = sortKeyFromItem(firstLoadedPage.value.page[0]);\n  const isBeforeFirstPage =\n    sortOrder === \"asc\"\n      ? compareValues(insertedKey, firstPageKey) <= 0\n      : compareValues(insertedKey, firstPageKey) >= 0;\n  if (isBeforeFirstPage) {\n    if (firstLoadedPage.args.paginationOpts.cursor === null) {\n      localQueryStore.setQuery(paginatedQuery, firstLoadedPage.args, {\n        ...firstLoadedPage.value,\n        page: [item, ...firstLoadedPage.value.page],\n      });\n    } else {\n      // if the very first page is not loaded\n      return;\n    }\n    return;\n  }\n\n  const lastLoadedPage = sortedPages[sortedPages.length - 1];\n  if (lastLoadedPage === undefined) {\n    // no pages, so don't update until they load\n    return;\n  }\n  const lastPageKey = sortKeyFromItem(\n    lastLoadedPage.value.page[lastLoadedPage.value.page.length - 1],\n  );\n  const isAfterLastPage =\n    sortOrder === \"asc\"\n      ? compareValues(insertedKey, lastPageKey) >= 0\n      : compareValues(insertedKey, lastPageKey) <= 0;\n  if (isAfterLastPage) {\n    // Only update if the last page is done loading, otherwise it will pop out\n    // when the server updates the query\n    if (lastLoadedPage.value.isDone) {\n      localQueryStore.setQuery(paginatedQuery, lastLoadedPage.args, {\n        ...lastLoadedPage.value,\n        page: [...lastLoadedPage.value.page, item],\n      });\n    }\n    return;\n  }\n\n  // if sorted in ascending order, find the first page that starts with a key greater than the inserted element,\n  // and update the page before it\n  // if sorted in descending order, find the first page that starts with a key less than the inserted element,\n  // and update the page before it\n\n  const successorPageIndex = sortedPages.findIndex((p) =>\n    sortOrder === \"asc\"\n      ? compareValues(sortKeyFromItem(p.value.page[0]), insertedKey) > 0\n      : compareValues(sortKeyFromItem(p.value.page[0]), insertedKey) < 0,\n  );\n  const pageToUpdate =\n    successorPageIndex === -1\n      ? sortedPages[sortedPages.length - 1]\n      : sortedPages[successorPageIndex - 1];\n  if (pageToUpdate === undefined) {\n    // no pages, so don't update until they load\n    return;\n  }\n  // If ascending, find the first element that is greater than or equal to the inserted element\n  // If descending, find the first element that is less than or equal to the inserted element\n  const indexWithinPage = pageToUpdate.value.page.findIndex((e) =>\n    sortOrder === \"asc\"\n      ? compareValues(sortKeyFromItem(e), insertedKey) >= 0\n      : compareValues(sortKeyFromItem(e), insertedKey) <= 0,\n  );\n  const newPage =\n    indexWithinPage === -1\n      ? [...pageToUpdate.value.page, item]\n      : [\n          ...pageToUpdate.value.page.slice(0, indexWithinPage),\n          item,\n          ...pageToUpdate.value.page.slice(indexWithinPage),\n        ];\n  localQueryStore.setQuery(paginatedQuery, pageToUpdate.args, {\n    ...pageToUpdate.value,\n    page: newPage,\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,SAAS,gBAAgB;AASlC,SAAS,aAAa,oBAAkC;;;AACxD,SAAS,kBAAkB;AAC3B;AAMA,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;;;;;;;;AAyC9B,MAAM,aACJ,CAAC,KAAmB,aAAqB,iBACzC,CAAC,cAAsC;QACrC,MAAM,UAAU;YAAE,GAAG,UAAU,OAAA;QAAQ;QACvC,MAAM,YAAY,UAAU,WAAA;QAC5B,MAAM,YAAY,UAAU,WAAA,GAAc;QAC1C,MAAM,cAAc,UAAU,WAAA,GAAc;QAC5C,OAAA,CAAQ,SAAS,CAAA,GAAI;YACnB,OAAO,UAAU,KAAA;YACjB,MAAM;gBACJ,GAAG,UAAU,IAAA;gBACb,gBAAgB;oBACd,GAAG,UAAU,OAAA,CAAQ,GAAG,CAAA,CAAE,IAAA,CAAK,cAAA;oBAC/B,WAAW;gBACb;YACF;QACF;QACA,OAAA,CAAQ,SAAS,CAAA,GAAI;YACnB,OAAO,UAAU,KAAA;YACjB,MAAM;gBACJ,GAAG,UAAU,IAAA;gBACb,gBAAgB;oBACd,GAAG,UAAU,OAAA,CAAQ,GAAG,CAAA,CAAE,IAAA,CAAK,cAAA;oBAC/B,QAAQ;oBACR,WAAW;gBACb;YACF;QACF;QACA,MAAM,gBAAgB;YAAE,GAAG,UAAU,aAAA;QAAc;QACnD,aAAA,CAAc,GAAG,CAAA,GAAI;YAAC;YAAW,SAAS;SAAA;QAC1C,OAAO;YACL,GAAG,SAAA;YACH;YACA;YACA;QACF;IACF;AAEF,MAAM,qBACJ,CAAC,MAAsB,CAAC,cAAsC;QAC5D,MAAM,iBAAiB,UAAU,aAAA,CAAc,GAAG,CAAA;QAClD,IAAI,mBAAmB,KAAA,GAAW;YAChC,OAAO;QACT;QACA,MAAM,UAAU;YAAE,GAAG,UAAU,OAAA;QAAQ;QACvC,OAAO,OAAA,CAAQ,GAAG,CAAA;QAClB,MAAM,gBAAgB;YAAE,GAAG,UAAU,aAAA;QAAc;QACnD,OAAO,aAAA,CAAc,GAAG,CAAA;QACxB,IAAI,WAAW,UAAU,QAAA,CAAS,KAAA,CAAM;QACxC,MAAM,YAAY,UAAU,QAAA,CAAS,SAAA,CAAU,CAAC,IAAM,MAAM,GAAG;QAC/D,IAAI,aAAa,GAAG;YAClB,WAAW;mBACN,UAAU,QAAA,CAAS,KAAA,CAAM,GAAG,SAAS;mBACrC;mBACA,UAAU,QAAA,CAAS,KAAA,CAAM,YAAY,CAAC;aAC3C;QACF;QACA,OAAO;YACL,GAAG,SAAA;YACH;YACA;YACA;QACF;IACF;AAuCK,SAAS,kBACd,KAAA,EACA,IAAA,EACA,OAAA,EACoC;IACpC,IACE,0DAAO,QAAS,eAAA,MAAoB,YACpC,QAAQ,eAAA,GAAkB,GAC1B;QACA,MAAM,IAAI,MACR,kEAA6F,yDAAxB,QAAS,eAAe,EAAA;IAEjG;IACA,MAAM,OAAO,SAAS;IACtB,MAAM,aAAa,OAAO,CAAC,IAAI;IAC/B,MAAM,gBAAY,mPAAA,EAAgB,KAAK;IACvC,MAAM,yBAAqB,6OAAA;yDAAQ,MAAM;YACvC;iEAAO,MAAM;oBACX,MAAM,KAAK,iBAAiB;oBAC5B,OAAO;wBACL;wBACA,MAAM;wBACN;wBACA,aAAa;wBACb,UAAU,OAAO,CAAC,CAAA,GAAI;4BAAC,CAAC;yBAAA;wBACxB,SAAS,OACJ,CAAC,IACF;4BACE,GAAG;gCACD;gCACA,MAAM;oCACJ,GAAG,UAAA;oCACH,gBAAgB;wCACd,UAAU,QAAQ,eAAA;wCAClB,QAAQ;wCACR;oCACF;gCACF;4BACF;wBACF;wBACJ,eAAe,CAAC;wBAChB;oBACF;gBACF;;QAKF;wDAAG;QAAA,uDAAA;QAED,KAAK,SAAA,CAAU,sPAAA,EAAa,UAAmB,CAAC;QAChD;QACA,QAAQ,eAAA;QACR;KACD;IAED,MAAM,CAAC,OAAO,QAAQ,CAAA,OACpB,8OAAA,EAAiC,kBAAkB;IAGrD,IAAI,YAAY;IAChB,QACE,mPAAA,EAAgB,KAAK,MAAM,uPAAA,EAAgB,MAAM,KAAK,KACtD,KAAK,SAAA,KAAU,kPAAA,EAAa,UAAmB,CAAC,MAC9C,KAAK,SAAA,KAAU,kPAAA,EAAa,MAAM,IAAI,CAAC,KACzC,SAAS,MAAM,IAAA,EACf;QACA,YAAY,mBAAmB;QAC/B,SAAS,SAAS;IACpB;IACA,MAAM,mBAAe,+OAAA,CAAU;IAC/B,MAAM,SAAS,aAAa,MAAA;IAE5B,MAAM,oBAAgB,qPAAA,EAAW,UAAU,OAAO;IAElD,MAAM,CAAC,SAAS,eAAe,CAAA,OAG3B,6OAAA;qCAAQ,MAAM;YAChB,IAAI,aAAa,KAAA;YAEjB,MAAM,WAAW,CAAC,CAAA;YAClB,KAAA,MAAW,WAAW,UAAU,QAAA,CAAU;gBACxC,aAAa,aAAA,CAAc,OAAO,CAAA;gBAClC,IAAI,eAAe,KAAA,GAAW;oBAC5B;gBACF;gBAEA,IAAI,sBAAsB,OAAO;;oBAC/B,IACE,WAAW,OAAA,CAAQ,QAAA,CAAS,eAAe,KAC1C,sBAAsB,kPAAA,IACrB,OAAO,WAAW,IAAA,KAAS,YAC3B,gCAAW,IAAA,sEAAM,mBAAA,MAAwB,yCAC9B,IAAA,sDAAX,kBAAiB,eAAA,MAAoB,iBACvC;wBAQA,OAAO,IAAA,CACL,8DACE,WAAW,OAAA;wBAEf,SAAS,kBAAkB;wBAC3B,OAAO;4BAAC,CAAC,CAAA;4BAAG,KAAA,CAAS;yBAAA;oBACvB,OAAO;wBACL,MAAM;oBACR;gBACF;gBACA,MAAM,eAAe,UAAU,aAAA,CAAc,OAAO,CAAA;gBACpD,IAAI,iBAAiB,KAAA,GAAW;oBAC9B,IACE,aAAA,CAAc,YAAA,CAAa,CAAC,CAAC,CAAA,KAAM,KAAA,KACnC,aAAA,CAAc,YAAA,CAAa,CAAC,CAAC,CAAA,KAAM,KAAA,GACnC;wBAEA,SAAS,mBAAmB,OAAO,CAAC;oBACtC;gBACF,OAAA,IACE,WAAW,WAAA,IAAA,CACV,WAAW,UAAA,KAAe,sBACzB,WAAW,UAAA,KAAe,mBAC1B,WAAW,IAAA,CAAK,MAAA,GAAS,QAAQ,eAAA,GAAkB,CAAA,GACrD;oBAGA,SACE,WACE,SACA,WAAW,WAAA,EACX,WAAW,cAAA;gBAGjB;gBACA,IAAI,WAAW,UAAA,KAAe,iBAAiB;oBAI7C,OAAO;wBAAC;wBAAU,KAAA,CAAS;qBAAA;gBAC7B;gBACA,SAAS,IAAA,CAAK,GAAG,WAAW,IAAI;YAClC;YACA,OAAO;gBAAC;gBAAU,UAAU;aAAA;QAC9B;oCAAG;QACD;QACA,UAAU,QAAA;QACV,UAAU,aAAA;QACV,QAAQ,eAAA;QACR;QACA;KACD;IAED,MAAM,mBAAe,6OAAA;mDAAQ,MAAM;YACjC,IAAI,oBAAoB,KAAA,GAAW;gBACjC,IAAI,UAAU,WAAA,KAAgB,GAAG;oBAC/B,OAAO;wBACL,QAAQ;wBACR,WAAW;wBACX,QAAA;uEAAU,CAAC,aAEX,CAFiC;;oBAGnC;gBACF,OAAO;oBACL,OAAO;wBACL,QAAQ;wBACR,WAAW;wBACX,QAAA;uEAAU,CAAC,aAEX,CAFiC;;oBAGnC;gBACF;YACF;YACA,IAAI,gBAAgB,MAAA,EAAQ;gBAC1B,OAAO;oBACL,QAAQ;oBACR,WAAW;oBACX,QAAA;mEAAU,CAAC,aAEX,CAFiC;;gBAGnC;YACF;YACA,MAAM,iBAAiB,gBAAgB,cAAA;YACvC,IAAI,qBAAqB;YACzB,OAAO;gBACL,QAAQ;gBACR,WAAW;gBACX,QAAA;+DAAU,CAAC,aAAqB;wBAC9B,IAAI,CAAC,oBAAoB;4BACvB,qBAAqB;4BACrB;2EAAS,CAAC,cAAc;oCACtB,MAAM,WAAW,CAAC;2CAAG,UAAU,QAAA;wCAAU,UAAU,WAAW;qCAAA;oCAC9D,MAAM,UAAU;wCAAE,GAAG,UAAU,OAAA;oCAAQ;oCACvC,OAAA,CAAQ,UAAU,WAAW,CAAA,GAAI;wCAC/B,OAAO,UAAU,KAAA;wCACjB,MAAM;4CACJ,GAAG,UAAU,IAAA;4CACb,gBAAgB;gDACd;gDACA,QAAQ;gDACR,IAAI,UAAU,EAAA;4CAChB;wCACF;oCACF;oCACA,OAAO;wCACL,GAAG,SAAA;wCACH,aAAa,UAAU,WAAA,GAAc;wCACrC;wCACA;oCACF;gCACF,CAAC;;wBACH;oBACF;;YACF;QACF;kDAAG;QAAC;QAAiB,UAAU,WAAW;KAAC;IAE3C,OAAO;QACL;QACA,GAAG,YAAA;IACL;AACF;AAEA,IAAI,eAAe;AAyBnB,SAAS,mBAA2B;IAClC;IACA,OAAO;AACT;AAKO,SAAS,oBAAoB;IAClC,eAAe;AACjB;AAsHO,SAAS,0CAGd,UAAA,EACA,KAAA,EACA,IAAA,EACA,WAAA,EAGM;IACN,MAAM,eAAe,KAAK,SAAA,KAAU,kPAAA,EAAa,IAAa,CAAC;IAE/D,KAAA,MAAW,eAAe,WAAW,aAAA,CAAc,KAAK,EAAG;QACzD,IAAI,YAAY,KAAA,KAAU,KAAA,GAAW;YACnC,MAAM,EAAE,gBAAgB,CAAA,EAAG,GAAG,UAAU,CAAA,GAAI,YAAY,IAAA;YAGxD,IAAI,KAAK,SAAA,KAAU,kPAAA,EAAa,SAAkB,CAAC,MAAM,cAAc;gBACrE,MAAM,QAAQ,YAAY,KAAA;gBAC1B,IACE,OAAO,UAAU,YACjB,UAAU,QACV,MAAM,OAAA,CAAQ,MAAM,IAAI,GACxB;oBACA,WAAW,QAAA,CAAS,OAAO,YAAY,IAAA,EAAM;wBAC3C,GAAG,KAAA;wBACH,MAAM,MAAM,IAAA,CAAK,GAAA,CAAI,WAAW;oBAClC,CAAC;gBACH;YACF;QACF;IACF;AACF;AA8BO,SAAS,YAAmD,OAAA,EAKhE;IACD,MAAM,EAAE,cAAA,EAAgB,WAAA,EAAa,eAAA,EAAiB,IAAA,CAAK,CAAA,GAAI;IAC/D,MAAM,UAAU,gBAAgB,aAAA,CAAc,cAAc;IAC5D,MAAM,mBAAmB,QAAQ,MAAA,CAAO,CAAC,MAAM;QAC7C,IAAI,gBAAgB,KAAA,GAAW;YAC7B,OAAO;QACT;QACA,OAAO,OAAO,IAAA,CAAK,WAAW,EAAE,KAAA,CAAA,6EAAA;QAE9B,CAAC,QAAM,qPAAA,EAAc,WAAA,CAAY,CAAC,CAAA,EAAG,EAAE,IAAA,CAAK,CAAC,CAAC,MAAM;IAExD,CAAC;IACD,MAAM,YAAY,iBAAiB,IAAA,CACjC,CAAC,IAAM,EAAE,IAAA,CAAK,cAAA,CAAe,MAAA,KAAW;IAE1C,IAAI,cAAc,KAAA,KAAa,UAAU,KAAA,KAAU,KAAA,GAAW;QAE5D;IACF;IACA,gBAAgB,QAAA,CAAS,gBAAgB,UAAU,IAAA,EAAM;QACvD,GAAG,UAAU,KAAA;QACb,MAAM;YAAC,MAAM;eAAG,UAAU,KAAA,CAAM,IAAI;SAAA;IACtC,CAAC;AACH;AAqBO,SAAS,uBAEd,OAAA,EAKC;IACD,MAAM,EAAE,cAAA,EAAgB,eAAA,EAAiB,IAAA,EAAM,WAAA,CAAY,CAAA,GAAI;IAC/D,MAAM,UAAU,gBAAgB,aAAA,CAAc,cAAc;IAC5D,MAAM,mBAAmB,QAAQ,MAAA,CAAO,CAAC,MAAM;QAC7C,IAAI,gBAAgB,KAAA,GAAW;YAC7B,OAAO;QACT;QACA,OAAO,OAAO,IAAA,CAAK,WAAW,EAAE,KAAA,CAAA,6EAAA;QAE9B,CAAC,QAAM,qPAAA,EAAc,WAAA,CAAY,CAAC,CAAA,EAAG,EAAE,IAAA,CAAK,CAAC,CAAC,MAAM;IAExD,CAAC;IACD,MAAM,WAAW,iBAAiB,IAAA,CAChC,CAAC,IAAM,EAAE,KAAA,KAAU,KAAA,KAAa,EAAE,KAAA,CAAM,MAAA;IAE1C,IAAI,aAAa,KAAA,GAAW;QAG1B;IACF;IACA,gBAAgB,QAAA,CAAS,gBAAgB,SAAS,IAAA,EAAM;QACtD,GAAG,SAAS,KAAA;QACZ,MAAM,CAAC;eAAG,SAAS,KAAA,CAAO,IAAA;YAAM,IAAI;SAAA;IACtC,CAAC;AACH;AAkDO,SAAS,iBAEd,OAAA,EAOC;IACD,MAAM,EACJ,cAAA,EACA,SAAA,EACA,eAAA,EACA,eAAA,EACA,IAAA,EACA,WAAA,EACF,GAAI;IAEJ,MAAM,UACJ,gBAAgB,aAAA,CAAc,cAAc;IAG9C,MAAM,cAAyD,CAAC;IAChE,KAAA,MAAW,SAAS,QAAS;QAC3B,IACE,gBAAgB,KAAA,KAChB,CAAC,OAAO,IAAA,CAAK,WAAW,EAAE,KAAA,CACxB,CAAC,IAAA,sCAAA;YAEC,WAAA,CAAY,CAAC,CAAA,KAAM,MAAM,IAAA,CAAK,CAAC,CAAA,GAEnC;YACA;QACF;QACA,MAAM,MAAM,KAAK,SAAA,CACf,OAAO,WAAA,CACL,OAAO,OAAA,CAAQ,MAAM,IAAI,EAAE,GAAA,CAAI;gBAAC,CAAC,GAAG,CAAC,CAAA;mBAAM;gBACzC;gBACA,MAAM,mBAAoB,EAAU,EAAA,GAAK;aAC1C;;YAGL;QAAA,CAAA,mBAAA,WAAA,CAAA,IAAA,cAAA,8BAAA,mBAAA,WAAA,CAAA,IAAA,GAAqB,CAAC,CAAA;QACtB,WAAA,CAAY,GAAG,CAAA,CAAE,IAAA,CAAK,KAAK;IAC7B;IACA,KAAA,MAAW,eAAe,OAAO,MAAA,CAAO,WAAW,EAAG;QACpD,wBAAwB;YACtB;YACA;YACA;YACA;YACA;YACA;QACF,CAAC;IACH;AACF;AAEA,SAAS,wBAEP,OAAA,EAOC;IACD,MAAM,EACJ,WAAA,EACA,SAAA,EACA,eAAA,EACA,eAAA,EACA,IAAA,EACA,cAAA,EACF,GAAI;IACJ,MAAM,cAAc,gBAAgB,IAAI;IACxC,MAAM,cAAqC,YAAY,MAAA,CACrD,CAAC,IACC,EAAE,KAAA,KAAU,KAAA,KAAa,EAAE,KAAA,CAAM,IAAA,CAAK,MAAA,GAAS;IAEnD,MAAM,cAAc,YAAY,IAAA,CAAK,CAAC,GAAG,MAAM;QAC7C,MAAM,OAAO,gBAAgB,EAAE,KAAA,CAAM,IAAA,CAAK,CAAC,CAAC;QAC5C,MAAM,OAAO,gBAAgB,EAAE,KAAA,CAAM,IAAA,CAAK,CAAC,CAAC;QAC5C,IAAI,cAAc,OAAO;YACvB,WAAO,qPAAA,EAAc,MAAM,IAAI;QACjC,OAAO;YACL,WAAO,qPAAA,EAAc,MAAM,IAAI;QACjC;IACF,CAAC;IAGD,MAAM,kBAAkB,WAAA,CAAY,CAAC,CAAA;IACrC,IAAI,oBAAoB,KAAA,GAAW;QAEjC;IACF;IACA,MAAM,eAAe,gBAAgB,gBAAgB,KAAA,CAAM,IAAA,CAAK,CAAC,CAAC;IAClE,MAAM,oBACJ,cAAc,YACV,qPAAA,EAAc,aAAa,YAAY,KAAK,QAC5C,qPAAA,EAAc,aAAa,YAAY,KAAK;IAClD,IAAI,mBAAmB;QACrB,IAAI,gBAAgB,IAAA,CAAK,cAAA,CAAe,MAAA,KAAW,MAAM;YACvD,gBAAgB,QAAA,CAAS,gBAAgB,gBAAgB,IAAA,EAAM;gBAC7D,GAAG,gBAAgB,KAAA;gBACnB,MAAM;oBAAC,MAAM;uBAAG,gBAAgB,KAAA,CAAM,IAAI;iBAAA;YAC5C,CAAC;QACH,OAAO;YAEL;QACF;QACA;IACF;IAEA,MAAM,iBAAiB,WAAA,CAAY,YAAY,MAAA,GAAS,CAAC,CAAA;IACzD,IAAI,mBAAmB,KAAA,GAAW;QAEhC;IACF;IACA,MAAM,cAAc,gBAClB,eAAe,KAAA,CAAM,IAAA,CAAK,eAAe,KAAA,CAAM,IAAA,CAAK,MAAA,GAAS,CAAC,CAAA;IAEhE,MAAM,kBACJ,cAAc,YACV,qPAAA,EAAc,aAAa,WAAW,KAAK,QAC3C,qPAAA,EAAc,aAAa,WAAW,KAAK;IACjD,IAAI,iBAAiB;QAGnB,IAAI,eAAe,KAAA,CAAM,MAAA,EAAQ;YAC/B,gBAAgB,QAAA,CAAS,gBAAgB,eAAe,IAAA,EAAM;gBAC5D,GAAG,eAAe,KAAA;gBAClB,MAAM,CAAC;uBAAG,eAAe,KAAA,CAAM,IAAA;oBAAM,IAAI;iBAAA;YAC3C,CAAC;QACH;QACA;IACF;IAOA,MAAM,qBAAqB,YAAY,SAAA,CAAU,CAAC,IAChD,cAAc,YACV,qPAAA,EAAc,gBAAgB,EAAE,KAAA,CAAM,IAAA,CAAK,CAAC,CAAC,GAAG,WAAW,IAAI,QAC/D,qPAAA,EAAc,gBAAgB,EAAE,KAAA,CAAM,IAAA,CAAK,CAAC,CAAC,GAAG,WAAW,IAAI;IAErE,MAAM,eACJ,uBAAuB,CAAA,IACnB,WAAA,CAAY,YAAY,MAAA,GAAS,CAAC,CAAA,GAClC,WAAA,CAAY,qBAAqB,CAAC,CAAA;IACxC,IAAI,iBAAiB,KAAA,GAAW;QAE9B;IACF;IAGA,MAAM,kBAAkB,aAAa,KAAA,CAAM,IAAA,CAAK,SAAA,CAAU,CAAC,IACzD,cAAc,QACV,yPAAA,EAAc,gBAAgB,CAAC,GAAG,WAAW,KAAK,QAClD,qPAAA,EAAc,gBAAgB,CAAC,GAAG,WAAW,KAAK;IAExD,MAAM,UACJ,oBAAoB,CAAA,IAChB,CAAC;WAAG,aAAa,KAAA,CAAM,IAAA;QAAM,IAAI;KAAA,GACjC;WACK,aAAa,KAAA,CAAM,IAAA,CAAK,KAAA,CAAM,GAAG,eAAe;QACnD;WACG,aAAa,KAAA,CAAM,IAAA,CAAK,KAAA,CAAM,eAAe;KAClD;IACN,gBAAgB,QAAA,CAAS,gBAAgB,aAAa,IAAA,EAAM;QAC1D,GAAG,aAAa,KAAA;QAChB,MAAM;IACR,CAAC;AACH", "debugId": null}}, {"offset": {"line": 6181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/ConvexAuthState.tsx"], "sourcesContent": ["import React, {\n  createContext,\n  ReactNode,\n  useContext,\n  useEffect,\n  useState,\n} from \"react\";\nimport { AuthTokenFetcher } from \"../browser/sync/client.js\";\nimport { ConvexProvider } from \"./client.js\";\n\n// Until we can import from our own entry points (requires TypeScript 4.7),\n// just describe the interface enough to help users pass the right type.\ntype IConvexReactClient = {\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ): void;\n  clearAuth(): void;\n};\n\n/**\n * Type representing the state of an auth integration with Convex.\n *\n * @public\n */\nexport type ConvexAuthState = {\n  isLoading: boolean;\n  isAuthenticated: boolean;\n};\n\nconst ConvexAuthContext = createContext<ConvexAuthState>(undefined as any);\n\n/**\n * Get the {@link ConvexAuthState} within a React component.\n *\n * This relies on a Convex auth integration provider being above in the React\n * component tree.\n *\n * @returns The current {@link ConvexAuthState}.\n *\n * @public\n */\nexport function useConvexAuth(): {\n  isLoading: boolean;\n  isAuthenticated: boolean;\n} {\n  const authContext = useContext(ConvexAuthContext);\n  if (authContext === undefined) {\n    throw new Error(\n      \"Could not find `ConvexProviderWithAuth` (or `ConvexProviderWithClerk` \" +\n        \"or `ConvexProviderWithAuth0`) \" +\n        \"as an ancestor component. This component may be missing, or you \" +\n        \"might have two instances of the `convex/react` module loaded in your \" +\n        \"project.\",\n    );\n  }\n  return authContext;\n}\n\n/**\n * A replacement for {@link ConvexProvider} which additionally provides\n * {@link ConvexAuthState} to descendants of this component.\n *\n * Use this to integrate any auth provider with Convex. The `useAuth` prop\n * should be a React hook that returns the provider's authentication state\n * and a function to fetch a JWT access token.\n *\n * If the `useAuth` prop function updates causing a rerender then auth state\n * will transition to loading and the `fetchAccessToken()` function called again.\n *\n * See [Custom Auth Integration](https://docs.convex.dev/auth/advanced/custom-auth) for more information.\n *\n * @public\n */\nexport function ConvexProviderWithAuth({\n  children,\n  client,\n  useAuth,\n}: {\n  children?: ReactNode;\n  client: IConvexReactClient;\n  useAuth: () => {\n    isLoading: boolean;\n    isAuthenticated: boolean;\n    fetchAccessToken: (args: {\n      forceRefreshToken: boolean;\n    }) => Promise<string | null>;\n  };\n}) {\n  const {\n    isLoading: authProviderLoading,\n    isAuthenticated: authProviderAuthenticated,\n    fetchAccessToken,\n  } = useAuth();\n  const [isConvexAuthenticated, setIsConvexAuthenticated] = useState<\n    boolean | null\n  >(null);\n\n  // If the useAuth went back to the authProviderLoading state (which is unusual but possible)\n  // reset the Convex auth state to null so that we can correctly\n  // transition the state from \"loading\" to \"authenticated\"\n  // without going through \"unauthenticated\".\n  if (authProviderLoading && isConvexAuthenticated !== null) {\n    setIsConvexAuthenticated(null);\n  }\n\n  // If the useAuth goes to not authenticated then isConvexAuthenticated should reflect that.\n  if (\n    !authProviderLoading &&\n    !authProviderAuthenticated &&\n    isConvexAuthenticated !== false\n  ) {\n    setIsConvexAuthenticated(false);\n  }\n\n  return (\n    <ConvexAuthContext.Provider\n      value={{\n        isLoading: isConvexAuthenticated === null,\n        isAuthenticated:\n          authProviderAuthenticated && (isConvexAuthenticated ?? false),\n      }}\n    >\n      <ConvexAuthStateFirstEffect\n        authProviderAuthenticated={authProviderAuthenticated}\n        fetchAccessToken={fetchAccessToken}\n        authProviderLoading={authProviderLoading}\n        client={client}\n        setIsConvexAuthenticated={setIsConvexAuthenticated}\n      />\n      <ConvexProvider client={client as any}>{children}</ConvexProvider>\n      <ConvexAuthStateLastEffect\n        authProviderAuthenticated={authProviderAuthenticated}\n        fetchAccessToken={fetchAccessToken}\n        authProviderLoading={authProviderLoading}\n        client={client}\n        setIsConvexAuthenticated={setIsConvexAuthenticated}\n      />\n    </ConvexAuthContext.Provider>\n  );\n}\n\n// First child ensures we `setAuth` before\n// other child components subscribe to queries via `useEffect`.\nfunction ConvexAuthStateFirstEffect({\n  authProviderAuthenticated,\n  fetchAccessToken,\n  authProviderLoading,\n  client,\n  setIsConvexAuthenticated,\n}: {\n  authProviderAuthenticated: boolean;\n  fetchAccessToken: (args: {\n    forceRefreshToken: boolean;\n  }) => Promise<string | null>;\n  authProviderLoading: boolean;\n  client: IConvexReactClient;\n  setIsConvexAuthenticated: React.Dispatch<\n    React.SetStateAction<boolean | null>\n  >;\n}) {\n  useEffect(() => {\n    let isThisEffectRelevant = true;\n    if (authProviderAuthenticated) {\n      client.setAuth(fetchAccessToken, (backendReportsIsAuthenticated) => {\n        if (isThisEffectRelevant) {\n          setIsConvexAuthenticated(() => backendReportsIsAuthenticated);\n        }\n      });\n      return () => {\n        isThisEffectRelevant = false;\n\n        // If unmounting or something changed before we finished fetching the token\n        // we shouldn't transition to a loaded state.\n        setIsConvexAuthenticated((isConvexAuthenticated) =>\n          isConvexAuthenticated ? false : null,\n        );\n      };\n    }\n  }, [\n    authProviderAuthenticated,\n    fetchAccessToken,\n    authProviderLoading,\n    client,\n    setIsConvexAuthenticated,\n  ]);\n  return null;\n}\n\n// Last child ensures we `clearAuth` last,\n// so that queries from unmounted sibling components\n// unsubscribe first and don't rerun without auth on the server\nfunction ConvexAuthStateLastEffect({\n  authProviderAuthenticated,\n  fetchAccessToken,\n  authProviderLoading,\n  client,\n  setIsConvexAuthenticated,\n}: {\n  authProviderAuthenticated: boolean;\n  fetchAccessToken: (args: {\n    forceRefreshToken: boolean;\n  }) => Promise<string | null>;\n  authProviderLoading: boolean;\n  client: IConvexReactClient;\n  setIsConvexAuthenticated: React.Dispatch<\n    React.SetStateAction<boolean | null>\n  >;\n}) {\n  useEffect(() => {\n    // If rendered with authProviderAuthenticated=true then clear that auth on in cleanup.\n    if (authProviderAuthenticated) {\n      return () => {\n        client.clearAuth();\n        // Set state back to loading in case this is a transition from one\n        // fetchToken function to another which signals a new auth context,\n        // e.g. a new orgId from Clerk. Auth context changes like this\n        // return isAuthenticated: true from useAuth() but if\n        // useAuth reports isAuthenticated: false on the next render\n        // then this null value will be overridden to false.\n        setIsConvexAuthenticated(() => null);\n      };\n    }\n  }, [\n    authProviderAuthenticated,\n    fetchAccessToken,\n    authProviderLoading,\n    client,\n    setIsConvexAuthenticated,\n  ]);\n  return null;\n}\n"], "names": [], "mappings": ";;;;;;AAAA,OAAO;AAQP,SAAS,sBAAsB;;;;AAsB/B,MAAM,wBAAoB,mPAAA,EAA+B,KAAA,CAAgB;AAYlE,SAAS,gBAGd;IACA,MAAM,kBAAc,gPAAA,EAAW,iBAAiB;IAChD,IAAI,gBAAgB,KAAA,GAAW;QAC7B,MAAM,IAAI,MACR;IAMJ;IACA,OAAO;AACT;AAiBO,SAAS,4BAIhB;UAHE,QAAA,EACA,MAAA,EACA,OAAA,EACF,EAUG,CAdoC;IAerC,MAAM,EACJ,WAAW,mBAAA,EACX,iBAAiB,yBAAA,EACjB,gBAAA,EACF,GAAI,QAAQ;IACZ,MAAM,CAAC,uBAAuB,wBAAwB,CAAA,OAAI,8OAAA,EAExD,IAAI;IAMN,IAAI,uBAAuB,0BAA0B,MAAM;QACzD,yBAAyB,IAAI;IAC/B;IAGA,IACE,CAAC,uBACD,CAAC,6BACD,0BAA0B,OAC1B;QACA,yBAAyB,KAAK;IAChC;IAEA,OACE,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,kBAAkB,QAAA,EAAlB;QACC,OAAO;YACL,WAAW,0BAA0B;YACrC,iBACE,6BAAA,sEAA8B,wBAAyB,KAAA;QAC3D;IAAA,GAEA,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,4BAAA;QACC;QACA;QACA;QACA;QACA;IAAA,IAEF,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,oPAAA,EAAA;QAAe;IAAA,GAAwB,QAAS,GACjD,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,2BAAA;QACC;QACA;QACA;QACA;QACA;IAAA;AAIR;AAIA,SAAS,gCAMT;UALE,yBAAA,EACA,gBAAA,EACA,mBAAA,EACA,MAAA,EACA,wBAAA,EACF,EAUG,CAhBiC;IAiBlC,IAAA,+OAAA;gDAAU,MAAM;YACd,IAAI,uBAAuB;YAC3B,IAAI,2BAA2B;gBAC7B,OAAO,OAAA,CAAQ;4DAAkB,CAAC,kCAAkC;wBAClE,IAAI,sBAAsB;4BACxB;wEAAyB,IAAM,6BAA6B;;wBAC9D;oBACF,CAAC;;gBACD;4DAAO,MAAM;wBACX,uBAAuB;wBAIvB;oEAAyB,CAAC,wBACxB,wBAAwB,QAAQ;;oBAEpC;;YACF;QACF;+CAAG;QACD;QACA;QACA;QACA;QACA;KACD;IACD,OAAO;AACT;AAKA,SAAS,+BAMT;UALE,yBAAA,EACA,gBAAA,EACA,mBAAA,EACA,MAAA,EACA,wBAAA,EACF,EAUG,CAhBgC;IAiBjC,IAAA,+OAAA;+CAAU,MAAM;YAEd,IAAI,2BAA2B;gBAC7B;2DAAO,MAAM;wBACX,OAAO,SAAA,CAAU;wBAOjB;mEAAyB,IAAM,IAAI;;oBACrC;;YACF;QACF;8CAAG;QACD;QACA;QACA;QACA;QACA;KACD;IACD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 6293, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/auth_helpers.tsx"], "sourcesContent": ["import React from \"react\";\nimport { ReactNode } from \"react\";\nimport { useConvexAuth } from \"./ConvexAuthState.js\";\n\n/**\n * Renders children if the client is authenticated.\n *\n * @public\n */\nexport function Authenticated({ children }: { children: ReactNode }) {\n  const { isLoading, isAuthenticated } = useConvexAuth();\n  if (isLoading || !isAuthenticated) {\n    return null;\n  }\n  return <>{children}</>;\n}\n\n/**\n * Renders children if the client is using authentication but is not authenticated.\n *\n * @public\n */\nexport function Unauthenticated({ children }: { children: ReactNode }) {\n  const { isLoading, isAuthenticated } = useConvexAuth();\n  if (isLoading || isAuthenticated) {\n    return null;\n  }\n  return <>{children}</>;\n}\n\n/**\n * Renders children if the client isn't using authentication or is in the process\n * of authenticating.\n *\n * @public\n */\nexport function AuthLoading({ children }: { children: ReactNode }) {\n  const { isLoading } = useConvexAuth();\n  if (!isLoading) {\n    return null;\n  }\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,OAAO,WAAW;AAElB,SAAS,qBAAqB;;;;AAOvB,SAAS,mBAAyB;UAAT,QAAA,CAAS,CAAA,EAA4B,CAAvC;IAC5B,MAAM,EAAE,SAAA,EAAW,eAAA,CAAgB,CAAA,OAAI,4PAAA,CAAc;IACrD,IAAI,aAAa,CAAC,iBAAiB;QACjC,OAAO;IACT;IACA,OAAO,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAA,6OAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAOO,SAAS,qBAA2B;UAAT,QAAA,CAAS,CAAA,EAA4B,CAAvC;IAC9B,MAAM,EAAE,SAAA,EAAW,eAAA,CAAgB,CAAA,OAAI,4PAAA,CAAc;IACrD,IAAI,aAAa,iBAAiB;QAChC,OAAO;IACT;IACA,OAAO,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAA,6OAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAQO,SAAS,iBAAuB;UAAT,QAAA,CAAS,CAAA,EAA4B,CAAvC;IAC1B,MAAM,EAAE,SAAA,CAAU,CAAA,OAAI,4PAAA,CAAc;IACpC,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IACA,OAAO,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAA,6OAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB", "debugId": null}}, {"offset": {"line": 6334, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/hydration.tsx"], "sourcesContent": ["import { useMemo } from \"react\";\nimport { useQuery } from \"../react/client.js\";\nimport { FunctionReference, makeFunctionReference } from \"../server/api.js\";\nimport { jsonToConvex } from \"../values/index.js\";\n\n/**\n * The preloaded query payload, which should be passed to a client component\n * and passed to {@link usePreloadedQuery}.\n *\n * @public\n */\nexport type Preloaded<Query extends FunctionReference<\"query\">> = {\n  __type: Query;\n  _name: string;\n  _argsJSON: string;\n  _valueJSON: string;\n};\n\n/**\n * Load a reactive query within a React component using a `Preloaded` payload\n * from a Server Component returned by {@link nextjs.preloadQuery}.\n *\n * This React hook contains internal state that will cause a rerender\n * whenever the query result changes.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param preloadedQuery - The `Preloaded` query payload from a Server Component.\n * @returns the result of the query. Initially returns the result fetched\n * by the Server Component. Subsequently returns the result fetched by the client.\n *\n * @public\n */\nexport function usePreloadedQuery<Query extends FunctionReference<\"query\">>(\n  preloadedQuery: Preloaded<Query>,\n): Query[\"_returnType\"] {\n  const args = useMemo(\n    () => jsonToConvex(preloadedQuery._argsJSON),\n    [preloadedQuery._argsJSON],\n  ) as Query[\"_args\"];\n  const preloadedResult = useMemo(\n    () => jsonToConvex(preloadedQuery._valueJSON),\n    [preloadedQuery._valueJSON],\n  );\n  const result = useQuery(\n    makeFunctionReference(preloadedQuery._name) as Query,\n    args,\n  );\n  return result === undefined ? preloadedResult : result;\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,eAAe;AACxB,SAAS,gBAAgB;AACzB,SAA4B,6BAA6B;;AACzD,SAAS,oBAAoB;;;;;;AA8BtB,SAAS,kBACd,cAAA,EACsB;IACtB,MAAM,WAAO,6OAAA;2CACX,IAAM,sPAAA,EAAa,eAAe,SAAS;0CAC3C;QAAC,eAAe,SAAS;KAAA;IAE3B,MAAM,sBAAkB,6OAAA;sDACtB,QAAM,kPAAA,EAAa,eAAe,UAAU;qDAC5C;QAAC,eAAe,UAAU;KAAA;IAE5B,MAAM,aAAS,8OAAA,MACb,yPAAA,EAAsB,eAAe,KAAK,GAC1C;IAEF,OAAO,WAAW,KAAA,IAAY,kBAAkB;AAClD", "debugId": null}}, {"offset": {"line": 6366, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react/index.ts"], "sourcesContent": ["/**\n * Tools to integrate Convex into React applications.\n *\n * This module contains:\n * 1. {@link ConvexReactClient}, a client for using Convex in React.\n * 2. {@link ConvexProvider}, a component that stores this client in React context.\n * 3. {@link Authenticated}, {@link Unauthenticated} and {@link AuthLoading} helper auth components.\n * 4. Hooks {@link useQuery}, {@link useMutation}, {@link useAction} and more for accessing this\n *    client from your React components.\n *\n * ## Usage\n *\n * ### Creating the client\n *\n * ```typescript\n * import { ConvexReactClient } from \"convex/react\";\n *\n * // typically loaded from an environment variable\n * const address = \"https://small-mouse-123.convex.cloud\"\n * const convex = new ConvexReactClient(address);\n * ```\n *\n * ### Storing the client in React Context\n *\n * ```typescript\n * import { ConvexProvider } from \"convex/react\";\n *\n * <ConvexProvider client={convex}>\n *   <App />\n * </ConvexProvider>\n * ```\n *\n * ### Using the auth helpers\n *\n * ```typescript\n * import { Authenticated, Unauthenticated, AuthLoading } from \"convex/react\";\n *\n * <Authenticated>\n *   Logged in\n * </Authenticated>\n * <Unauthenticated>\n *   Logged out\n * </Unauthenticated>\n * <AuthLoading>\n *   Still loading\n * </AuthLoading>\n * ```\n *\n * ### Using React hooks\n *\n * ```typescript\n * import { useQuery, useMutation } from \"convex/react\";\n * import { api } from \"../convex/_generated/api\";\n *\n * function App() {\n *   const counter = useQuery(api.getCounter.default);\n *   const increment = useMutation(api.incrementCounter.default);\n *   // Your component here!\n * }\n * ```\n * @module\n */\nexport * from \"./use_paginated_query.js\";\nexport { useQueries, type RequestForQueries } from \"./use_queries.js\";\nexport type { AuthTokenFetcher } from \"../browser/sync/client.js\";\nexport * from \"./auth_helpers.js\";\nexport * from \"./ConvexAuthState.js\";\nexport * from \"./hydration.js\";\n/* @internal */\nexport { useSubscription } from \"./use_subscription.js\";\nexport {\n  type ReactMutation,\n  type ReactAction,\n  type Watch,\n  type WatchQueryOptions,\n  type MutationOptions,\n  type ConvexReactClientOptions,\n  type OptionalRestArgsOrSkip,\n  ConvexReactClient,\n  useConvex,\n  ConvexProvider,\n  useQuery,\n  useMutation,\n  useAction,\n  useConvexConnectionState,\n} from \"./client.js\";\n"], "names": [], "mappings": ";AA8DA,cAAc;AACd,SAAS,kBAA0C;AAEnD,cAAc;AACd,cAAc;AACd,cAAc;AAEd,SAAS,uBAAuB;AAChC", "debugId": null}}, {"offset": {"line": 6386, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react-clerk/ConvexProviderWithClerk.tsx"], "sourcesContent": ["import React from \"react\";\n\nimport { ReactNode, useCallback, useMemo } from \"react\";\nimport { AuthTokenFetcher } from \"../browser/sync/client.js\";\nimport { ConvexProviderWithAuth } from \"../react/ConvexAuthState.js\";\n\n// Until we can import from our own entry points (requires TypeScript 4.7),\n// just describe the interface enough to help users pass the right type.\ntype IConvexReactClient = {\n  setAuth(fetchToken: AuthTokenFetcher): void;\n  clearAuth(): void;\n};\n\n// https://clerk.com/docs/reference/clerk-react/useauth\ntype UseAuth = () => {\n  isLoaded: boolean;\n  isSignedIn: boolean | undefined;\n  getToken: (options: {\n    template?: \"convex\";\n    skipCache?: boolean;\n  }) => Promise<string | null>;\n  // We don't use these properties but they should trigger a new token fetch.\n  orgId: string | undefined | null;\n  orgRole: string | undefined | null;\n};\n\n/**\n * A wrapper React component which provides a {@link react.ConvexReactClient}\n * authenticated with Clerk.\n *\n * It must be wrapped by a configured `Clerk<PERSON><PERSON><PERSON>`, from\n * `@clerk/clerk-react`, `@clerk/clerk-expo`, `@clerk/nextjs` or\n * another React-based Clerk client library and have the corresponding\n * `useAuth` hook passed in.\n *\n * See [Convex Clerk](https://docs.convex.dev/auth/clerk) on how to set up\n * Convex with Clerk.\n *\n * @public\n */\nexport function ConvexProviderWithClerk({\n  children,\n  client,\n  useAuth,\n}: {\n  children: ReactNode;\n  client: IConvexReactClient;\n  useAuth: UseAuth; // useAuth from Clerk\n}) {\n  const useAuthFromClerk = useUseAuthFromClerk(useAuth);\n  return (\n    <ConvexProviderWithAuth client={client} useAuth={useAuthFromClerk}>\n      {children}\n    </ConvexProviderWithAuth>\n  );\n}\n\nfunction useUseAuthFromClerk(useAuth: UseAuth) {\n  return useMemo(\n    () =>\n      function useAuthFromClerk() {\n        const { isLoaded, isSignedIn, getToken, orgId, orgRole } = useAuth();\n        const fetchAccessToken = useCallback(\n          async ({ forceRefreshToken }: { forceRefreshToken: boolean }) => {\n            try {\n              return getToken({\n                template: \"convex\",\n                skipCache: forceRefreshToken,\n              });\n            } catch {\n              return null;\n            }\n          },\n          // Build a new fetchAccessToken to trigger setAuth() whenever these change.\n          // Anything else from the JWT Clerk wants to be reactive goes here too.\n          // Clerk's Expo useAuth hook is not memoized so we don't include getToken.\n          // eslint-disable-next-line react-hooks/exhaustive-deps\n          [orgId, orgRole],\n        );\n        return useMemo(\n          () => ({\n            isLoading: !isLoaded,\n            isAuthenticated: isSignedIn ?? false,\n            fetchAccessToken,\n          }),\n          [isLoaded, isSignedIn, fetchAccessToken],\n        );\n      },\n    [useAuth],\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,WAAW;AAIlB,SAAS,8BAA8B;;;;;AAoChC,SAAS,6BAIhB;UAHE,QAAA,EACA,MAAA,EACA,OAAA,EACF,EAIG,CARqC;IAStC,MAAM,mBAAmB,oBAAoB,OAAO;IACpD,OACE,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,qQAAA,EAAA;QAAuB;QAAgB,SAAS;IAAA,GAC9C,QACH;AAEJ;AAEA,SAAS,oBAAoB,OAAA,EAAkB;IAC7C,WAAO,6OAAA;uCACL,IACE,SAAS,mBAAmB;gBAC1B,MAAM,EAAE,QAAA,EAAU,UAAA,EAAY,QAAA,EAAU,KAAA,EAAO,OAAA,CAAQ,CAAA,GAAI,QAAQ;gBACnE,MAAM,uBAAmB,iPAAA;kGACvB;4BAAO,EAAE,iBAAA,CAAkB,CAAA,KAAsC;wBAC/D,IAAI;4BACF,OAAO,SAAS;gCACd,UAAU;gCACV,WAAW;4BACb,CAAC;wBACH,EAAA,UAAQ;4BACN,OAAO;wBACT;oBACF;iGAAA,2EAAA;gBAAA,uEAAA;gBAAA,0EAAA;gBAAA,uDAAA;gBAKA;oBAAC;oBAAO,OAAO;iBAAA;gBAEjB,WAAO,6OAAA;4EACL,IAAA,CAAO;4BACL,WAAW,CAAC;4BACZ,gEAAiB,aAAc;4BAC/B;wBACF,CAAA;2EACA;oBAAC;oBAAU;oBAAY,gBAAgB;iBAAA;YAE3C;sCACF;QAAC,OAAO;KAAA;AAEZ", "debugId": null}}, {"offset": {"line": 6448, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2%2B55f3e2d4ca346cd1/node_modules/convex/src/react-clerk/index.ts"], "sourcesContent": ["/**\n * React login component for use with Clerk.\n *\n * @module\n */\nexport { ConvexProviderWithClerk } from \"./ConvexProviderWithClerk.js\";\n"], "names": [], "mappings": ";AAKA,SAAS,+BAA+B", "debugId": null}}]}