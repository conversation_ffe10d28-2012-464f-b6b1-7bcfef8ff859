(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/0bf16_@clerk_nextjs_dist_esm_app-router_96ad20d7._.js",
  "static/chunks/0bf16_@clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_a199cae1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/0bf16_@clerk_nextjs_dist_esm_app-router_554a0307._.js",
  "static/chunks/0bf16_@clerk_nextjs_dist_esm_app-router_keyless-actions_d7a57148.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-client] (ecmascript)");
    });
});
}),
]);