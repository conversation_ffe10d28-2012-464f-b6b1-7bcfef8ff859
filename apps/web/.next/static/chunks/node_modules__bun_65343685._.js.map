{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/%40clerk%2Bnextjs%406.31.6%2B04be5137c725fb89/node_modules/%40clerk/nextjs/src/utils/invalidateNextRouterCache.ts"], "sourcesContent": ["// interface FetchDataOutput {\n//   dataHref: string;\n//   json: Record<string, any> | null;\n//   response: Response;\n//   text: string;\n//   cacheKey: string;\n// }\n//\n// interface NextDataCache {\n//   [asPath: string]: Promise<FetchDataOutput>;\n// }\n\n/**\n * A placeholder for the actual next types.\n * The types above are not exported from the next package currently,\n * we only include them here for documentation purposes.\n * see: https://github.com/vercel/next.js/blob/018208fb15c9b969e173684668cea89588f4c536/packages/next/src/shared/lib/router/router.ts#L655\n */\ntype NextDataCache = any;\n\n/**\n * Only for /pages router\n *\n * Next currently prefetches the page of every visible Link on the page.\n * For every prefetch request, the middleware runs and the response is cached in\n * window.next.router.sdc or window.next.router.sdc\n *\n * Imagine a scenario with a /protected page requiring the user to be signed in using middleware.\n * If we don't invalidate the cache, we end up in the following redirect flow:\n * home -> /protected -> middleware redirects to /sign-in -> perform sign-in\n *            -> try to navigate to /protected but the cached 307 response is used\n *                   -> redirect to /sign-in instead -> withRedirectToHome -> home\n * When the auth state changes and the middleware runs again, the client-side router\n * does not automatically invalidate the cache so the browser follows the cached response\n *\n * This helper invalidates both known caches help prevent the scenario described above.\n */\nexport const invalidateNextRouterCache = () => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  const invalidate = (cache: NextDataCache) => {\n    // deleting the keys without nuking the cache by reassigning the variable to an empty object,\n    // in case next holds a reference to it\n    Object.keys(cache).forEach(key => {\n      delete cache[key];\n    });\n  };\n\n  try {\n    invalidate((window as any).next.router.sdc);\n    invalidate((window as any).next.router.sbc);\n  } catch {\n    return;\n  }\n};\n"], "names": [], "mappings": ";;;;;AAqCO,MAAM,4BAA4B,MAAM;IAC7C,IAAI,OAAO,WAAW,aAAa;QACjC;IACF;IAEA,MAAM,aAAa,CAAC,UAAyB;QAG3C,OAAO,IAAA,CAAK,KAAK,EAAE,OAAA,CAAQ,CAAA,QAAO;YAChC,OAAO,KAAA,CAAM,GAAG,CAAA;QAClB,CAAC;IACH;IAEA,IAAI;QACF,WAAY,OAAe,IAAA,CAAK,MAAA,CAAO,GAAG;QAC1C,WAAY,OAAe,IAAA,CAAK,MAAA,CAAO,GAAG;IAC5C,EAAA,UAAQ;QACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/%40clerk%2Bnextjs%406.31.6%2B04be5137c725fb89/node_modules/%40clerk/nextjs/src/pages/ClerkProvider.tsx"], "sourcesContent": ["import { Clerk<PERSON>rovider as ReactClerkProvider } from '@clerk/clerk-react';\n// Override Clerk React error thrower to show that errors come from @clerk/nextjs\nimport { setClerkJsLoadingErrorPackageName, setErrorThrowerOptions } from '@clerk/clerk-react/internal';\nimport { useRouter } from 'next/router';\nimport React from 'react';\n\nimport { useSafeLayoutEffect } from '../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider } from '../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../types';\nimport { ClerkJSScript } from '../utils/clerk-js-script';\nimport { invalidateNextRouterCache } from '../utils/invalidateNextRouterCache';\nimport { mergeNextClerkPropsWithEnv } from '../utils/mergeNextClerkPropsWithEnv';\nimport { removeBasePath } from '../utils/removeBasePath';\nimport { RouterTelemetry } from '../utils/router-telemetry';\n\nsetErrorThrowerOptions({ packageName: PACKAGE_NAME });\nsetClerkJsLoadingErrorPackageName(PACKAGE_NAME);\n\nexport function ClerkProvider({ children, ...props }: NextClerkProviderProps): JSX.Element {\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true } = props;\n  const { push, replace } = useRouter();\n  ReactClerkProvider.displayName = 'ReactClerkProvider';\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = invalidateNextRouterCache;\n  }, []);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onAfterSetActive = () => {\n      // Re-run the middleware every time there auth state changes.\n      // This enables complete control from a centralised place (NextJS middleware),\n      // as we will invoke it every time the client-side auth state changes, eg: signing-out, switching orgs, etc.\\\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        void push(window.location.href);\n      }\n    };\n  }, []);\n\n  const navigate = (to: string) => push(removeBasePath(to));\n  const replaceNavigate = (to: string) => replace(removeBasePath(to));\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    routerPush: navigate,\n    routerReplace: replaceNavigate,\n  });\n  // ClerkProvider automatically injects __clerk_ssr_state\n  // getAuth returns a user-facing authServerSideProps that hides __clerk_ssr_state\n  // @ts-expect-error initialState is hidden from the types as it's a private prop\n  const initialState = props.authServerSideProps?.__clerk_ssr_state || props.__clerk_ssr_state;\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider\n        {...mergedProps}\n        initialState={initialState}\n      >\n        <RouterTelemetry />\n        <ClerkJSScript router='pages' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,iBAAiB,0BAA0B;AAEpD,SAAS,mCAAmC,8BAA8B;;;AAC1E,SAAS,iBAAiB;AAC1B,OAAO,WAAW;AAElB,SAAS,2BAA2B;AACpC,SAAS,gCAAgC;AAEzC,SAAS,qBAAqB;AAC9B,SAAS,iCAAiC;AAC1C,SAAS,kCAAkC;AAC3C,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;;;;;;;;;;;;;IAEhC,8SAAA,EAAuB;IAAE,aAAa;AAAa,CAAC;IACpD,yRAAA,EAAkC,eAAY;AAEvC,SAAS,mBAAmC,EAAwC;UAA3D,QAAA,EAAU,GAAG,MAAM,CAAA,GAArB;IAlB9B,IAAA;IAmBE,MAAM,EAAE,+CAA+C,IAAA,CAAK,CAAA,GAAI;IAChE,MAAM,EAAE,IAAA,EAAM,OAAA,CAAQ,CAAA,OAAI,mNAAA,CAAU;IACpC,yRAAA,CAAmB,WAAA,GAAc;IAEjC,IAAA,kTAAA;6CAAoB,MAAM;YACxB,OAAO,6BAAA,GAAgC,wSAAA;QACzC;4CAAG,CAAC,CAAC;IAEL,IAAA,kTAAA;6CAAoB,MAAM;YACxB,OAAO,4BAAA;qDAA+B,MAAM;oBAI1C,IAAI,8CAA8C;wBAChD,KAAK,KAAK,OAAO,QAAA,CAAS,IAAI;oBAChC;gBACF;;QACF;4CAAG,CAAC,CAAC;IAEL,MAAM,WAAW,CAAC,KAAe,SAAK,kRAAA,EAAe,EAAE,CAAC;IACxD,MAAM,kBAAkB,CAAC,KAAe,YAAQ,kRAAA,EAAe,EAAE,CAAC;IAClE,MAAM,kBAAc,0SAAA,EAA2B;QAC7C,GAAG,KAAA;QACH,YAAY;QACZ,eAAe;IACjB,CAAC;IAID,MAAM,eAAA,CAAA,CAAe,KAAA,MAAM,mBAAA,KAAN,OAAA,KAAA,IAAA,GAA2B,iBAAA,KAAqB,MAAM,iBAAA;IAE3E,OACE,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,6SAAA,EAAA;QAAyB,SAAS;IAAA,GACjC,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,yRAAA,EAAA;QACE,GAAG,WAAA;QACJ;IAAA,GAEA,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,wRAAA,EAAA,IAAgB,GACjB,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,wRAAA,EAAA;QAAc,QAAO;IAAA,CAAQ,GAC7B;AAIT", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/%40clerk%2Bnextjs%406.31.6%2B04be5137c725fb89/node_modules/%40clerk/nextjs/src/client-boundary/ClerkProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nimport { ClientClerkProvider } from '../app-router/client/ClerkProvider';\nimport { ClerkProvider as PageClerkProvider } from '../pages/ClerkProvider';\nimport { type NextClerkProviderProps } from '../types';\n\n/**\n * This is a compatibility layer to support a single ClerkProvider component in both the app and pages routers.\n */\nexport function ClerkProvider(props: NextClerkProviderProps) {\n  const router = useRouter();\n\n  const Provider = router ? PageClerkProvider : ClientClerkProvider;\n\n  return <Provider {...props} />;\n}\n"], "names": [], "mappings": ";;;;AAEA,SAAS,iBAAiB;AAC1B,OAAO,WAAW;AAElB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB,yBAAyB;;;;;;;AAM5C,SAAS,cAAc,KAAA,EAA+B;IAC3D,MAAM,aAAS,6NAAA,CAAU;IAEzB,MAAM,WAAW,SAAS,gRAAA,GAAoB,wSAAA;IAE9C,OAAO,aAAA,GAAA,6OAAA,CAAA,aAAA,CAAC,UAAA;QAAU,GAAG,KAAA;IAAA,CAAO;AAC9B", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/%40clerk%2Bnextjs%406.31.6%2B04be5137c725fb89/node_modules/%40clerk/nextjs/src/index.ts"], "sourcesContent": ["/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  AuthenticateWithRedirect<PERSON><PERSON>back,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  ClerkF<PERSON>,\n  <PERSON>Loaded,\n  ClerkLoading,\n  RedirectToCreateOrganization,\n  RedirectToOrganizationProfile,\n  RedirectToSignIn,\n  RedirectToSignUp,\n  RedirectToTasks,\n  RedirectToUserProfile,\n} from './client-boundary/controlComponents';\n\n/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  APIKeys,\n  CreateOrganization,\n  GoogleOneTap,\n  OrganizationList,\n  OrganizationProfile,\n  OrganizationSwitcher,\n  PricingTable,\n  SignIn,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUp,\n  SignUpButton,\n  TaskChooseOrganization,\n  UserButton,\n  UserProfile,\n  Waitlist,\n} from './client-boundary/uiComponents';\n\n/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  useAuth,\n  useClerk,\n  useEmailLink,\n  useOrganization,\n  useOrganizationList,\n  useReverification,\n  useSession,\n  useSessionList,\n  useSignIn,\n  useSignUp,\n  useUser,\n} from './client-boundary/hooks';\n\n/**\n * Conditionally export components that exhibit different behavior\n * when used in /app vs /pages.\n * We defined the runtime and the type values explicitly,\n * because TS will not recognize the subpath import unless the HOST\n * application sets moduleResolution to 'NodeNext'.\n */\n// @ts-ignore\nimport * as ComponentsModule from '#components';\n\nimport type { ServerComponentsServerModuleTypes } from './components.server';\n\nexport const ClerkProvider = ComponentsModule.ClerkProvider as ServerComponentsServerModuleTypes['ClerkProvider'];\nexport const SignedIn = ComponentsModule.SignedIn as ServerComponentsServerModuleTypes['SignedIn'];\nexport const SignedOut = ComponentsModule.SignedOut as ServerComponentsServerModuleTypes['SignedOut'];\nexport const Protect = ComponentsModule.Protect as ServerComponentsServerModuleTypes['Protect'];\n"], "names": [], "mappings": ";;;;;;;;;;AAIA;;;AAkBA;;;AAwBA;AAsBA,YAAY,sBAAsB;;;;;;AAI3B,MAAM,gBAAgB,iBAAiB,4QAAA;AACvC,MAAM,WAAW,iBAAiB,+PAAA;AAClC,MAAM,YAAY,iBAAiB,gQAAA;AACnC,MAAM,UAAU,iBAAiB,8PAAA", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/%40radix-ui%2Breact-compose-refs%401.1.2%2B57b8f439e5339a04/node_modules/%40radix-ui/react-compose-refs/src/compose-refs.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "names": [], "mappings": ";;;;;;;AAAA,YAAY,WAAW;;AAQvB,SAAS,OAAU,GAAA,EAAqB,KAAA,EAAU;IAChD,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,IAAI,KAAK;IAClB,OAAA,IAAW,QAAQ,QAAQ,QAAQ,KAAA,GAAW;QAC5C,IAAI,OAAA,GAAU;IAChB;AACF;AAMA,SAAS;IAAA,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAkB,KAAlB,QAAA,SAAA,CAAA,KAAkB,EAA8C;;IACvE,OAAO,CAAC,SAAS;QACf,IAAI,aAAa;QACjB,MAAM,WAAW,KAAK,GAAA,CAAI,CAAC,QAAQ;YACjC,MAAM,UAAU,OAAO,KAAK,IAAI;YAChC,IAAI,CAAC,cAAc,OAAO,WAAW,YAAY;gBAC/C,aAAa;YACf;YACA,OAAO;QACT,CAAC;QAMD,IAAI,YAAY;YACd,OAAO,MAAM;gBACX,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;oBACxC,MAAM,UAAU,QAAA,CAAS,CAAC,CAAA;oBAC1B,IAAI,OAAO,WAAW,YAAY;wBAChC,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAA,CAAK,CAAC,CAAA,EAAG,IAAI;oBACtB;gBACF;YACF;QACF;IACF;AACF;AAMA,SAAS;IAAA,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAsB,KAAtB,QAAA,SAAA,CAAA,KAAsB,EAA8C;;IAE3E,OAAa,iPAAA,CAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/%40radix-ui%2Breact-slot%401.2.3%2B57b8f439e5339a04/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "Slot", "props", "Slottable"], "mappings": ";;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,mBAAmB;AAmCpB,SAkEG,YAAAA,WAlEH;;;;AAAA,uBAAA;AAzB0B,SAAS,WAAW,SAAA,EAAmB;IACvE,MAAM,YAAY,aAAA,GAAA,gBAAgB,SAAS;IAC3C,MAAMC,QAAa,gPAAA,CAAmC,CAAC,OAAO,iBAAiB;QAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,gBAAsB,8OAAA,CAAS,OAAA,CAAQ,QAAQ;QACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;QAEhD,IAAI,WAAW;YAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;YAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;gBAC/C,IAAI,UAAU,WAAW;oBAGvB,IAAU,8OAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,OAAa,8OAAA,CAAS,IAAA,CAAK,IAAI;oBACzE,OAAa,oPAAA,CAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;gBACN,OAAO;oBACL,OAAO;gBACT;YACF,CAAC;YAED,OACE,aAAA,GAAA,IAAA,kPAAA,EAAC,WAAA;gBAAW,GAAG,SAAA;gBAAW,KAAK;gBAC5B,UAAM,oPAAA,CAAe,UAAU,IACtB,kPAAA,CAAa,YAAY,KAAA,GAAW,WAAW,IACrD;YAAA,CACN;QAEJ;QAEA,OACE,aAAA,GAAA,IAAA,kPAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B;QAAA,CACH;IAEJ,CAAC;IAEDA,MAAK,WAAA,GAAc,GAAY,OAAT,SAAS,EAAA;IAC/B,OAAOA;AACT;AAEA,IAAM,OAAO,aAAA,GAAA,WAAW,MAAM;AAAA,uBAAA;AAUH,SAAS,gBAAgB,SAAA,EAAmB;IACrE,MAAM,YAAkB,gPAAA,CAAgC,CAAC,OAAO,iBAAiB;QAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QAEnC,IAAU,oPAAA,CAAe,QAAQ,GAAG;YAClC,MAAM,cAAc,cAAc,QAAQ;YAC1C,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;YAE9D,IAAI,SAAS,IAAA,KAAe,8OAAA,EAAU;gBACpCA,OAAM,GAAA,GAAM,mBAAe,sSAAA,EAAY,cAAc,WAAW,IAAI;YACtE;YACA,OAAa,kPAAA,CAAa,UAAUA,MAAK;QAC3C;QAEA,OAAa,8OAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,IAAU,8OAAA,CAAS,IAAA,CAAK,IAAI,IAAI;IAC1E,CAAC;IAED,UAAU,WAAA,GAAc,GAAY,OAAT,SAAS,EAAA;IACpC,OAAO;AACT;AAMA,IAAM,uBAAuB,OAAO,iBAAiB;AAAA,uBAAA;AAUnB,SAAS,gBAAgB,SAAA,EAAmB;IAC5E,MAAMC,aAAgC;YAAC,EAAE,QAAA,CAAS,CAAA,KAAM;QACtD,OAAO,aAAA,GAAA,IAAA,kPAAA,EAAAH,uPAAAA,EAAA;YAAG;QAAA,CAAS;IACrB;IACAG,WAAU,WAAA,GAAc,GAAY,OAAT,SAAS,EAAA;IACpCA,WAAU,SAAA,GAAY;IACtB,OAAOA;AACT;AAEA,IAAM,YAAY,aAAA,GAAA,gBAAgB,WAAW;AAM7C,SAAS,YACP,KAAA,EAC+D;IAC/D,OACQ,oPAAA,CAAe,KAAK,KAC1B,OAAO,MAAM,IAAA,KAAS,cACtB,eAAe,MAAM,IAAA,IACrB,MAAM,IAAA,CAAK,SAAA,KAAc;AAE7B;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;;wBAAI,SAAoB;;oBAChD,MAAM,SAAS,eAAe,GAAG,IAAI;oBACrC,cAAc,GAAG,IAAI;oBACrB,OAAO;gBACT;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;0CASzC;IAPT,IAAI,oDAAgB,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,sEAApD,iCAAuD,GAAA;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,qDAAgB,wBAAA,CAAyB,SAAS,KAAK,yGAAG,GAAA;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/clsx%402.1.1/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/class-variance-authority%400.7.1/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,AAAC,GAAQ,OAAN,SAAU,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,gMAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/%40radix-ui%2Breact-primitive%402.1.3%2B1913163318ab61df/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,kBAAkB;AA4ChB;;;;;AA1CX,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,WAAO,+QAAA,EAAW,aAAiB,CAAE,MAAN,IAAI;IACzC,MAAM,OAAa,gPAAA,CAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,UAAU,OAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;YAChC,MAAA,CAAe,OAAO,GAAA,CAAI,UAAU,CAAC,CAAA,GAAI;QAC5C;QAEA,OAAO,aAAA,GAAA,IAAA,kPAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,aAAiB,OAAJ,IAAI;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,CAAS,sPAAA,CAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/%40radix-ui%2Breact-label%402.1.7%2B1913163318ab61df/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,QAAc,gPAAA,CAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,IAAA,kPAAA,EAAC,wRAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;gBAKtB;YAHA,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;aAEvD,qBAAA,MAAM,WAAA,cAAN,yCAAA,wBAAA,OAAoB,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "debugId": null}}]}