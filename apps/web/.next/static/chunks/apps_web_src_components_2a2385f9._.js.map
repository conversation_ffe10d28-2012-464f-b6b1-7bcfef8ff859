{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/header.tsx"], "sourcesContent": ["'use client';\nimport Link from 'next/link';\n\nexport default function Header() {\n  return (\n    <header className=\"border-b\">\n      <div className=\"mx-auto flex max-w-5xl items-center justify-between px-4 py-2\">\n        <nav className=\"flex items-center gap-6\">\n          <Link className=\"font-semibold text-foreground\" href=\"/dashboard\">\n            Dashboard\n          </Link>\n          <Link className=\"text-foreground\" href=\"/fireflies\">\n            Fireflies\n          </Link>\n        </nav>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGe,SAAS;IACtB,qBACE,kQAAC;QAAO,WAAU;kBAChB,cAAA,kQAAC;YAAI,WAAU;sBACb,cAAA,kQAAC;gBAAI,WAAU;;kCACb,kQAAC,+OAAI;wBAAC,WAAU;wBAAgC,MAAK;kCAAa;;;;;;kCAGlE,kQAAC,+OAAI;wBAAC,WAAU;wBAAkB,MAAK;kCAAa;;;;;;;;;;;;;;;;;;;;;;AAO9D;KAfwB", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/theme-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\nimport type * as React from 'react';\n\nexport function ThemeProvider({\n  children,\n  ...props\n}: React.ComponentProps<typeof NextThemesProvider>) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKO,SAAS,cAAc,KAGoB;QAHpB,EAC5B,QAAQ,EACR,GAAG,OAC6C,GAHpB;IAI5B,qBAAO,kQAAC,kPAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KALgB", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/sonner.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from 'next-themes';\nimport { Toaster as Sonner, type ToasterProps } from 'sonner';\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = 'system' } = useTheme();\n\n  return (\n    <Sonner\n      className=\"toaster group\"\n      style={\n        {\n          '--normal-bg': 'var(--popover)',\n          '--normal-text': 'var(--popover-foreground)',\n          '--normal-border': 'var(--border)',\n        } as React.CSSProperties\n      }\n      theme={theme as ToasterProps['theme']}\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU;QAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,IAAA,6OAAQ;IAErC,qBACE,kQAAC,4NAAM;QACL,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAEF,OAAO;QACN,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,6OAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@clerk/nextjs';\nimport { ConvexReactClient } from 'convex/react';\nimport { ConvexProviderWithClerk } from 'convex/react-clerk';\nimport type React from 'react';\nimport { ThemeProvider } from './theme-provider';\nimport { Toaster } from './ui/sonner';\n\nconst convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;\nif (!convexUrl) {\n  throw new Error('Missing NEXT_PUBLIC_CONVEX_URL');\n}\nconst convex = new ConvexReactClient(convexUrl);\n\nexport default function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <ThemeProvider\n      attribute=\"class\"\n      defaultTheme=\"system\"\n      disableTransitionOnChange\n      enableSystem\n    >\n      <ConvexProviderWithClerk client={convex} useAuth={useAuth}>\n        {children}\n      </ConvexProviderWithClerk>\n      <Toaster richColors />\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AASkB;;AAPlB;AACA;AAAA;AACA;AAAA;AAEA;AACA;AAPA;;;;;;;AASA,MAAM;AACN;;AAGA,MAAM,SAAS,IAAI,uPAAiB,CAAC;AAEtB,SAAS,UAAU,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;IAChC,qBACE,kQAAC,0KAAa;QACZ,WAAU;QACV,cAAa;QACb,yBAAyB;QACzB,YAAY;;0BAEZ,kQAAC,uRAAuB;gBAAC,QAAQ;gBAAQ,SAAS,kVAAO;0BACtD;;;;;;0BAEH,kQAAC,+JAAO;gBAAC,UAAU;;;;;;;;;;;;AAGzB;KAdwB", "debugId": null}}]}