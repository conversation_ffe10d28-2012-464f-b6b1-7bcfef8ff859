// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
}


// Validate ../../src/app/dashboard/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ../../src/app/fireflies/page.tsx
{
  const handler = {} as typeof import("../../src/app/fireflies/page.js")
  handler satisfies AppPageConfig<"/fireflies">
}

// Validate ../../src/app/page.tsx
{
  const handler = {} as typeof import("../../src/app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../src/app/sign-in/[[...sign-in]]/page.tsx
{
  const handler = {} as typeof import("../../src/app/sign-in/[[...sign-in]]/page.js")
  handler satisfies AppPageConfig<"/sign-in/[[...sign-in]]">
}

// Validate ../../src/app/sign-up/[[...sign-up]]/page.tsx
{
  const handler = {} as typeof import("../../src/app/sign-up/[[...sign-up]]/page.js")
  handler satisfies AppPageConfig<"/sign-up/[[...sign-up]]">
}

// Validate ../../src/app/team/page.tsx
{
  const handler = {} as typeof import("../../src/app/team/page.js")
  handler satisfies AppPageConfig<"/team">
}

// Validate ../../src/app/api/fireflies/search/route.ts
{
  const handler = {} as typeof import("../../src/app/api/fireflies/search/route.js")
  handler satisfies RouteHandlerConfig<"/api/fireflies/search">
}

// Validate ../../src/app/api/fireflies/team/route.ts
{
  const handler = {} as typeof import("../../src/app/api/fireflies/team/route.js")
  handler satisfies RouteHandlerConfig<"/api/fireflies/team">
}

// Validate ../../src/app/api/fireflies/transcript/route.ts
{
  const handler = {} as typeof import("../../src/app/api/fireflies/transcript/route.js")
  handler satisfies RouteHandlerConfig<"/api/fireflies/transcript">
}





// Validate ../../src/app/layout.tsx
{
  const handler = {} as typeof import("../../src/app/layout.js")
  handler satisfies LayoutConfig<"/">
}
