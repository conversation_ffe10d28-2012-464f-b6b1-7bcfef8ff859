{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/header.tsx"], "sourcesContent": ["'use client';\nimport Link from 'next/link';\n\nexport default function Header() {\n  return (\n    <header className=\"border-b\">\n      <div className=\"mx-auto flex max-w-5xl items-center justify-between px-4 py-2\">\n        <nav className=\"flex items-center gap-6\">\n          <Link className=\"font-semibold text-foreground\" href=\"/dashboard\">\n            Dashboard\n          </Link>\n          <Link className=\"text-foreground\" href=\"/fireflies\">\n            Fireflies\n          </Link>\n        </nav>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGe,SAAS;IACtB,qBACE,mTAAC;QAAO,WAAU;kBAChB,cAAA,mTAAC;YAAI,WAAU;sBACb,cAAA,mTAAC;gBAAI,WAAU;;kCACb,mTAAC,4OAAI;wBAAC,WAAU;wBAAgC,MAAK;kCAAa;;;;;;kCAGlE,mTAAC,4OAAI;wBAAC,WAAU;wBAAkB,MAAK;kCAAa;;;;;;;;;;;;;;;;;;;;;;AAO9D", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/theme-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\nimport type * as React from 'react';\n\nexport function ThemeProvider({\n  children,\n  ...props\n}: React.ComponentProps<typeof NextThemesProvider>) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKO,SAAS,cAAc,EAC5B,QAAQ,EACR,GAAG,OAC6C;IAChD,qBAAO,mTAAC,+OAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/sonner.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from 'next-themes';\nimport { Toaster as Sonner, type ToasterProps } from 'sonner';\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = 'system' } = useTheme();\n\n  return (\n    <Sonner\n      className=\"toaster group\"\n      style={\n        {\n          '--normal-bg': 'var(--popover)',\n          '--normal-text': 'var(--popover-foreground)',\n          '--normal-border': 'var(--border)',\n        } as React.CSSProperties\n      }\n      theme={theme as ToasterProps['theme']}\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,IAAA,0OAAQ;IAErC,qBACE,mTAAC,yNAAM;QACL,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAEF,OAAO;QACN,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@clerk/nextjs';\nimport { ConvexReactClient } from 'convex/react';\nimport { ConvexProviderWithClerk } from 'convex/react-clerk';\nimport type React from 'react';\nimport { ThemeProvider } from './theme-provider';\nimport { Toaster } from './ui/sonner';\n\nconst convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;\nif (!convexUrl) {\n  throw new Error('Missing NEXT_PUBLIC_CONVEX_URL');\n}\nconst convex = new ConvexReactClient(convexUrl);\n\nexport default function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <ThemeProvider\n      attribute=\"class\"\n      defaultTheme=\"system\"\n      disableTransitionOnChange\n      enableSystem\n    >\n      <ConvexProviderWithClerk client={convex} useAuth={useAuth}>\n        {children}\n      </ConvexProviderWithClerk>\n      <Toaster richColors />\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AAEA;AACA;AAPA;;;;;;;AASA,MAAM;AACN;;AAGA,MAAM,SAAS,IAAI,oPAAiB,CAAC;AAEtB,SAAS,UAAU,EAAE,QAAQ,EAAiC;IAC3E,qBACE,mTAAC,uKAAa;QACZ,WAAU;QACV,cAAa;QACb,yBAAyB;QACzB,YAAY;;0BAEZ,mTAAC,oRAAuB;gBAAC,QAAQ;gBAAQ,SAAS,+UAAO;0BACtD;;;;;;0BAEH,mTAAC,4JAAO;gBAAC,UAAU;;;;;;;;;;;;AAGzB", "debugId": null}}]}