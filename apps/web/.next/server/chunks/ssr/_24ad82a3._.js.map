{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/packages/backend/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;;;AAED;AAAA;;AAUO,MAAM,MAAM,mNAAM;AAClB,MAAM,WAAW,mNAAM", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,mOAAO,EAAC,IAAA,6LAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot as SlotPrimitive } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex shrink-0 items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? SlotPrimitive.Slot : 'button';\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ variant, size, className }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,iPAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,sQAAa,CAAC,IAAI,GAAG;IAE5C,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/card.tsx"], "sourcesContent": ["import type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn(\n        'flex flex-col gap-6 rounded-xl border bg-card py-6 text-card-foreground shadow-sm',\n        className\n      )}\n      data-slot=\"card\"\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      data-slot=\"card-header\"\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('font-semibold leading-none', className)}\n      data-slot=\"card-title\"\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('text-muted-foreground text-sm', className)}\n      data-slot=\"card-description\"\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      data-slot=\"card-action\"\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('px-6', className)}\n      data-slot=\"card-content\"\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      data-slot=\"card-footer\"\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EACX,qFACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EACX,8JACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,8BAA8B;QAC5C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EACX,kEACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,QAAQ;QACtB,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,2CAA2C;QACzD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/input.tsx"], "sourcesContent": ["import type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\n  return (\n    <input\n      className={cn(\n        'flex h-9 w-full min-w-0 rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-xs outline-none transition-[color,box-shadow] selection:bg-primary selection:text-primary-foreground file:inline-flex file:h-7 file:border-0 file:bg-transparent file:font-medium file:text-foreground file:text-sm placeholder:text-muted-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-input/30',\n        'focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50',\n        'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',\n        className\n      )}\n      data-slot=\"input\"\n      type={type}\n      {...props}\n    />\n  );\n}\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EACX,mcACA,iFACA,0GACA;QAEF,aAAU;QACV,MAAM;QACL,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/label.tsx"], "sourcesContent": ["import * as LabelPrimitive from '@radix-ui/react-label';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      className={cn(\n        'flex select-none items-center gap-2 font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50',\n        className\n      )}\n      data-slot=\"label\"\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAAA;AAGA;;;;AAEA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,mTAAC,wQAAmB;QAClB,WAAW,IAAA,wIAAE,EACX,uNACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\nimport { api } from '@Fireflies-Sales/backend/convex/_generated/api';\nimport { useQuery } from 'convex/react';\nimport { useMemo, useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\n\ntype Analysis = ReturnType<\n  typeof useQuery<typeof api.analyses.list>\n> extends infer T\n  ? T extends { page: infer P }\n    ? P extends Array<infer A>\n      ? A\n      : never\n    : never\n  : never;\n\nconst ALL_TOPICS: readonly string[] = [\n  'biggest twitter spaces account in the world',\n  'endorsement from elon musk',\n  'investment',\n  'media',\n  'exposure',\n  'brand awarness',\n  'community building',\n  'connections',\n  'OTC',\n  'post-tge setup',\n  'biggest KOLs in the world',\n  'tge',\n  'tge launches',\n  'founder brand building',\n  'trust',\n  'dealflow partnership',\n  'collaboration with our existing projects',\n  '600+ projects in our captable',\n  'listing help',\n  'mm (market making) help',\n  'VC connections',\n  'KOL connections',\n];\n\nconst brandButton =\n  'bg-[var(--brand-button)] text-[var(--brand-button-text)] hover:opacity-90';\nconst PERCENT = 100;\nconst RECENT_LIMIT = 50;\n\nexport default function DashboardPage() {\n  const [from, setFrom] = useState<string>(defaultFromISO());\n  const [to, setTo] = useState<string>(defaultToISO());\n  const [topicFilter, setTopicFilter] = useState<string>('');\n  const [minCount, setMinCount] = useState<number>(0);\n  const [sortKey, setSortKey] = useState<\n    'mentions' | 'coverage' | 'transcripts'\n  >('mentions');\n\n  const analyses = useQuery(api.analyses.list, {\n    fromISO: from || undefined,\n    toISO: to || undefined,\n    limit: 1000,\n  });\n\n  const filtered = useMemo(() => {\n    if (!analyses) {\n      return [] as Analysis[];\n    }\n    const selectedTopics = getSelectedTopics(topicFilter);\n    return analyses.page.filter((a) => {\n      const res = (\n        a as unknown as { results: Array<{ topic: string; count: number }> }\n      ).results;\n      const filteredRes = selectedTopics.length\n        ? res.filter((r) => selectedTopics.includes(r.topic))\n        : res;\n      const total = filteredRes.reduce((acc, r) => acc + (r.count || 0), 0);\n      return total >= minCount;\n    });\n  }, [analyses, topicFilter, minCount]);\n\n  const stats = useMemo(\n    () => buildStats(filtered, topicFilter),\n    [filtered, topicFilter]\n  );\n\n  function safeCsv(s: string) {\n    const v = s.replaceAll('\"', '\"\"');\n    return `\"${v}\"`;\n  }\n  function dateIso(ms?: number) {\n    if (!ms) {\n      return '';\n    }\n    const d = new Date(ms);\n    return d.toISOString().slice(0, 10);\n  }\n  function exportCsv() {\n    const rows: string[] = [];\n    rows.push(['Transcript Title', 'User', 'Date', 'Top Topics'].join(','));\n    for (const a of filtered) {\n      const aa = a as unknown as {\n        transcriptTitle?: string;\n        userName?: string;\n        userEmail?: string;\n        userId: string;\n        dateMs?: number;\n        createdAt?: number;\n        results: Array<{ topic: string; count: number }>;\n      };\n      const title = safeCsv(aa.transcriptTitle || 'Untitled');\n      const user = safeCsv(aa.userName || aa.userEmail || aa.userId);\n      const date = safeCsv(dateIso(aa.dateMs || aa.createdAt));\n      const topics = safeCsv(renderTopTopicsInline(aa.results, topicFilter));\n      rows.push([title, user, date, topics].join(','));\n    }\n    const blob = new Blob([rows.join('\\n')], {\n      type: 'text/csv;charset=utf-8;',\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'sales-insights.csv';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  }\n  return (\n    <main className=\"mx-auto max-w-5xl px-4 py-6\">\n      <h1 className=\"mb-2 font-semibold text-2xl text-foreground\">\n        Sales Insights Dashboard\n      </h1>\n      <p className=\"mb-6 text-muted-foreground\">\n        Track how much your sales team spoke about the right topics and drill\n        into details with filters and sorting.\n      </p>\n\n      <Card className=\"mb-6\">\n        <CardHeader>\n          <CardTitle>Filters</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <form className=\"grid grid-cols-1 gap-4 md:grid-cols-4\">\n            <div className=\"grid gap-1\">\n              <Label htmlFor=\"from\">From</Label>\n              <Input\n                id=\"from\"\n                onChange={(e) => setFrom(dateFromInput(e.target.value))}\n                type=\"date\"\n                value={dateInput(from)}\n              />\n            </div>\n            <div className=\"grid gap-1\">\n              <Label htmlFor=\"to\">To</Label>\n              <Input\n                id=\"to\"\n                onChange={(e) => setTo(dateFromInput(e.target.value))}\n                type=\"date\"\n                value={dateInput(to)}\n              />\n            </div>\n            <div className=\"grid gap-1\">\n              <Label htmlFor=\"topics\">Topics (comma-separated)</Label>\n              <Input\n                id=\"topics\"\n                onChange={(e) => setTopicFilter(e.target.value)}\n                placeholder=\"e.g. investment, media\"\n                value={topicFilter}\n              />\n            </div>\n            <div className=\"grid gap-1\">\n              <Label htmlFor=\"min\">Min Mentions</Label>\n              <Input\n                id=\"min\"\n                min={0}\n                onChange={(e) =>\n                  setMinCount(Number.parseInt(e.target.value || '0', 10))\n                }\n                type=\"number\"\n                value={minCount}\n              />\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n\n      <section className=\"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3\">\n        <KPI title=\"Transcripts\" value={stats.totalTranscripts.toString()} />\n        <KPI title=\"Total Mentions\" value={stats.totalMentions.toString()} />\n        <KPI\n          title=\"Avg Coverage\"\n          value={`${(stats.avgCoverage * PERCENT).toFixed(0)}%`}\n        />\n      </section>\n\n      <div className=\"mb-6 flex items-center gap-3\">\n        <span className=\"text-muted-foreground\">Sort by:</span>\n        <Button\n          className={brandButton}\n          onClick={() => setSortKey('mentions')}\n          type=\"button\"\n        >\n          Mentions\n        </Button>\n        <Button\n          className={brandButton}\n          onClick={() => setSortKey('coverage')}\n          type=\"button\"\n        >\n          Coverage\n        </Button>\n        <Button\n          className={brandButton}\n          onClick={() => setSortKey('transcripts')}\n          type=\"button\"\n        >\n          Transcripts\n        </Button>\n        <div className=\"grow\" />\n        <Button className={brandButton} onClick={exportCsv} type=\"button\">\n          Export CSV\n        </Button>\n      </div>\n\n      <section className=\"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Top Topics</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ul className=\"grid gap-2\">\n              {stats.topTopics.map((t) => (\n                <li className=\"flex items-center justify-between\" key={t.topic}>\n                  <span>{t.topic}</span>\n                  <span className=\"text-muted-foreground text-sm\">\n                    {t.count}\n                  </span>\n                </li>\n              ))}\n            </ul>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>By Sales Rep</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ul className=\"grid gap-2\">\n              {stats.byRep(sortKey).map((r) => (\n                <li\n                  className=\"flex items-center justify-between\"\n                  key={r.userId}\n                >\n                  <div>\n                    <div className=\"font-medium\">\n                      {r.userName || r.userEmail || r.userId}\n                    </div>\n                    <div className=\"text-muted-foreground text-sm\">\n                      {r.transcripts} transcripts · {r.mentions} mentions\n                    </div>\n                  </div>\n                  <div className=\"text-sm\">\n                    Coverage {(r.coverage * PERCENT).toFixed(0)}%\n                  </div>\n                </li>\n              ))}\n            </ul>\n          </CardContent>\n        </Card>\n      </section>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Transcripts</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ul className=\"grid gap-3\">\n            {filtered.slice(0, RECENT_LIMIT).map((a) => (\n              <li className=\"rounded-md border p-3\" key={a._id}>\n                <div className=\"flex flex-wrap items-baseline justify-between gap-2\">\n                  <div className=\"font-medium\">\n                    {a.transcriptTitle || 'Untitled'} ·{' '}\n                    <span className=\"text-muted-foreground text-sm\">\n                      {a.userName || a.userEmail}\n                    </span>\n                  </div>\n                  {a.transcriptUrl ? (\n                    <a\n                      className=\"text-sm underline\"\n                      href={a.transcriptUrl}\n                      rel=\"noopener\"\n                      target=\"_blank\"\n                    >\n                      Open in Fireflies\n                    </a>\n                  ) : null}\n                </div>\n                <div className=\"mt-2 text-muted-foreground text-sm\">\n                  Topics:{' '}\n                  {renderTopTopicsInline(\n                    (\n                      a as unknown as {\n                        results: Array<{ topic: string; count: number }>;\n                      }\n                    ).results,\n                    topicFilter\n                  )}\n                </div>\n              </li>\n            ))}\n          </ul>\n        </CardContent>\n      </Card>\n    </main>\n  );\n}\n\nfunction defaultFromISO() {\n  const d = new Date();\n  const DAYS_BACK = 7;\n  d.setUTCDate(d.getUTCDate() - DAYS_BACK);\n  d.setUTCHours(0, 0, 0, 0);\n  return d.toISOString();\n}\nfunction defaultToISO() {\n  const d = new Date();\n  const END_HOUR = 23;\n  const END_MIN = 59;\n  const END_SEC = 59;\n  const END_MS = 999;\n  d.setUTCHours(END_HOUR, END_MIN, END_SEC, END_MS);\n  return d.toISOString();\n}\nfunction dateInput(iso: string) {\n  return iso.slice(0, 10);\n}\nfunction dateFromInput(input: string) {\n  if (!input) {\n    return '';\n  }\n  const [y, m, d] = input.split('-').map(Number);\n  const dt = new Date(Date.UTC(y, (m || 1) - 1, d || 1, 0, 0, 0, 0));\n  return dt.toISOString();\n}\n\nfunction getSelectedTopics(text: string): string[] {\n  const tokens = text\n    .split(',')\n    .map((x) => x.trim())\n    .filter(Boolean);\n  const canon = new Set(ALL_TOPICS.map((t) => t.toLowerCase()));\n  return tokens.filter((t) => canon.has(t.toLowerCase()));\n}\n\nfunction buildStats(\n  list: Array<{\n    results: Array<{ topic: string; count: number }>;\n    userId: string;\n    userEmail?: string;\n    userName?: string;\n  }>,\n  topicFilter: string\n) {\n  const selectedTopics = getSelectedTopics(topicFilter);\n  const topics = selectedTopics.length ? selectedTopics : ALL_TOPICS;\n  const totalTranscripts = list.length;\n  let totalMentions = 0;\n  const topicToCount = new Map<string, number>();\n  const repMap = new Map<\n    string,\n    {\n      userId: string;\n      userEmail?: string;\n      userName?: string;\n      transcripts: number;\n      mentions: number;\n      coverage: number;\n    }\n  >();\n\n  for (const a of list) {\n    const res = (a.results as Array<{ topic: string; count: number }>) || [];\n    const filtered = res.filter((r) => topics.includes(r.topic));\n    const mentions = filtered.reduce((acc, r) => acc + (r.count || 0), 0);\n    totalMentions += mentions;\n\n    for (const r of filtered) {\n      topicToCount.set(\n        r.topic,\n        (topicToCount.get(r.topic) || 0) + (r.count || 0)\n      );\n    }\n\n    const repKey = a.userId as string;\n    const prev = repMap.get(repKey) || {\n      userId: a.userId,\n      userEmail: a.userEmail,\n      userName: a.userName,\n      transcripts: 0,\n      mentions: 0,\n      coverage: 0,\n    };\n    prev.transcripts += 1;\n    prev.mentions += mentions;\n    const coveredTopics = filtered.filter((r) => (r.count || 0) > 0).length;\n    const coverage = topics.length > 0 ? coveredTopics / topics.length : 0;\n    prev.coverage =\n      (prev.coverage * (prev.transcripts - 1) + coverage) / prev.transcripts;\n    repMap.set(repKey, prev);\n  }\n\n  const TOP_N = 20;\n  const topTopics = Array.from(topicToCount.entries())\n    .map(([topic, count]) => ({ topic, count }))\n    .sort((a, b) => b.count - a.count)\n    .slice(0, TOP_N);\n\n  return {\n    totalTranscripts,\n    totalMentions,\n    avgCoverage:\n      totalTranscripts > 0\n        ? Array.from(repMap.values()).reduce((acc, r) => acc + r.coverage, 0) /\n            repMap.size || 0\n        : 0,\n    topTopics,\n    byRep: (key: 'mentions' | 'coverage' | 'transcripts') =>\n      Array.from(repMap.values()).sort((a, b) => {\n        if (key === 'mentions') {\n          return b.mentions - a.mentions;\n        }\n        if (key === 'coverage') {\n          return b.coverage - a.coverage;\n        }\n        return b.transcripts - a.transcripts;\n      }),\n  };\n}\n\nfunction renderTopTopicsInline(\n  results: Array<{ topic: string; count: number }>,\n  topicFilter: string\n) {\n  const selected = new Set(getSelectedTopics(topicFilter));\n  const TOP_INLINE = 5;\n  const items = results\n    .filter((r) => (selected.size ? selected.has(r.topic) : true))\n    .filter((r) => (r.count || 0) > 0)\n    .sort((a, b) => b.count - a.count)\n    .slice(0, TOP_INLINE)\n    .map((r) => `${r.topic} (${r.count})`);\n  return items.length ? items.join(', ') : '—';\n}\n\nfunction KPI({ title, value }: { title: string; value: string }) {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>{title}</CardTitle>\n      </CardHeader>\n      <CardContent>\n        <div className=\"font-semibold text-3xl text-foreground\">{value}</div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AAmBA,MAAM,aAAgC;IACpC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,cACJ;AACF,MAAM,UAAU;AAChB,MAAM,eAAe;AAEN,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,sRAAQ,EAAS;IACzC,MAAM,CAAC,IAAI,MAAM,GAAG,IAAA,sRAAQ,EAAS;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,sRAAQ,EAAS;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,sRAAQ,EAAS;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,sRAAQ,EAEpC;IAEF,MAAM,WAAW,IAAA,2OAAQ,EAAC,yJAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;QAC3C,SAAS,QAAQ;QACjB,OAAO,MAAM;QACb,OAAO;IACT;IAEA,MAAM,WAAW,IAAA,qRAAO,EAAC;QACvB,IAAI,CAAC,UAAU;YACb,OAAO,EAAE;QACX;QACA,MAAM,iBAAiB,kBAAkB;QACzC,OAAO,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3B,MAAM,MAAM,AACV,EACA,OAAO;YACT,MAAM,cAAc,eAAe,MAAM,GACrC,IAAI,MAAM,CAAC,CAAC,IAAM,eAAe,QAAQ,CAAC,EAAE,KAAK,KACjD;YACJ,MAAM,QAAQ,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG;YACnE,OAAO,SAAS;QAClB;IACF,GAAG;QAAC;QAAU;QAAa;KAAS;IAEpC,MAAM,QAAQ,IAAA,qRAAO,EACnB,IAAM,WAAW,UAAU,cAC3B;QAAC;QAAU;KAAY;IAGzB,SAAS,QAAQ,CAAS;QACxB,MAAM,IAAI,EAAE,UAAU,CAAC,KAAK;QAC5B,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACjB;IACA,SAAS,QAAQ,EAAW;QAC1B,IAAI,CAAC,IAAI;YACP,OAAO;QACT;QACA,MAAM,IAAI,IAAI,KAAK;QACnB,OAAO,EAAE,WAAW,GAAG,KAAK,CAAC,GAAG;IAClC;IACA,SAAS;QACP,MAAM,OAAiB,EAAE;QACzB,KAAK,IAAI,CAAC;YAAC;YAAoB;YAAQ;YAAQ;SAAa,CAAC,IAAI,CAAC;QAClE,KAAK,MAAM,KAAK,SAAU;YACxB,MAAM,KAAK;YASX,MAAM,QAAQ,QAAQ,GAAG,eAAe,IAAI;YAC5C,MAAM,OAAO,QAAQ,GAAG,QAAQ,IAAI,GAAG,SAAS,IAAI,GAAG,MAAM;YAC7D,MAAM,OAAO,QAAQ,QAAQ,GAAG,MAAM,IAAI,GAAG,SAAS;YACtD,MAAM,SAAS,QAAQ,sBAAsB,GAAG,OAAO,EAAE;YACzD,KAAK,IAAI,CAAC;gBAAC;gBAAO;gBAAM;gBAAM;aAAO,CAAC,IAAI,CAAC;QAC7C;QACA,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,IAAI,CAAC;SAAM,EAAE;YACvC,MAAM;QACR;QACA,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IACA,qBACE,mTAAC;QAAK,WAAU;;0BACd,mTAAC;gBAAG,WAAU;0BAA8C;;;;;;0BAG5D,mTAAC;gBAAE,WAAU;0BAA6B;;;;;;0BAK1C,mTAAC,uJAAI;gBAAC,WAAU;;kCACd,mTAAC,6JAAU;kCACT,cAAA,mTAAC,4JAAS;sCAAC;;;;;;;;;;;kCAEb,mTAAC,8JAAW;kCACV,cAAA,mTAAC;4BAAK,WAAU;;8CACd,mTAAC;oCAAI,WAAU;;sDACb,mTAAC,yJAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,mTAAC,yJAAK;4CACJ,IAAG;4CACH,UAAU,CAAC,IAAM,QAAQ,cAAc,EAAE,MAAM,CAAC,KAAK;4CACrD,MAAK;4CACL,OAAO,UAAU;;;;;;;;;;;;8CAGrB,mTAAC;oCAAI,WAAU;;sDACb,mTAAC,yJAAK;4CAAC,SAAQ;sDAAK;;;;;;sDACpB,mTAAC,yJAAK;4CACJ,IAAG;4CACH,UAAU,CAAC,IAAM,MAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CACnD,MAAK;4CACL,OAAO,UAAU;;;;;;;;;;;;8CAGrB,mTAAC;oCAAI,WAAU;;sDACb,mTAAC,yJAAK;4CAAC,SAAQ;sDAAS;;;;;;sDACxB,mTAAC,yJAAK;4CACJ,IAAG;4CACH,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,aAAY;4CACZ,OAAO;;;;;;;;;;;;8CAGX,mTAAC;oCAAI,WAAU;;sDACb,mTAAC,yJAAK;4CAAC,SAAQ;sDAAM;;;;;;sDACrB,mTAAC,yJAAK;4CACJ,IAAG;4CACH,KAAK;4CACL,UAAU,CAAC,IACT,YAAY,OAAO,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,KAAK;4CAErD,MAAK;4CACL,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjB,mTAAC;gBAAQ,WAAU;;kCACjB,mTAAC;wBAAI,OAAM;wBAAc,OAAO,MAAM,gBAAgB,CAAC,QAAQ;;;;;;kCAC/D,mTAAC;wBAAI,OAAM;wBAAiB,OAAO,MAAM,aAAa,CAAC,QAAQ;;;;;;kCAC/D,mTAAC;wBACC,OAAM;wBACN,OAAO,GAAG,CAAC,MAAM,WAAW,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;0BAIzD,mTAAC;gBAAI,WAAU;;kCACb,mTAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACxC,mTAAC,2JAAM;wBACL,WAAW;wBACX,SAAS,IAAM,WAAW;wBAC1B,MAAK;kCACN;;;;;;kCAGD,mTAAC,2JAAM;wBACL,WAAW;wBACX,SAAS,IAAM,WAAW;wBAC1B,MAAK;kCACN;;;;;;kCAGD,mTAAC,2JAAM;wBACL,WAAW;wBACX,SAAS,IAAM,WAAW;wBAC1B,MAAK;kCACN;;;;;;kCAGD,mTAAC;wBAAI,WAAU;;;;;;kCACf,mTAAC,2JAAM;wBAAC,WAAW;wBAAa,SAAS;wBAAW,MAAK;kCAAS;;;;;;;;;;;;0BAKpE,mTAAC;gBAAQ,WAAU;;kCACjB,mTAAC,uJAAI;;0CACH,mTAAC,6JAAU;0CACT,cAAA,mTAAC,4JAAS;8CAAC;;;;;;;;;;;0CAEb,mTAAC,8JAAW;0CACV,cAAA,mTAAC;oCAAG,WAAU;8CACX,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,kBACpB,mTAAC;4CAAG,WAAU;;8DACZ,mTAAC;8DAAM,EAAE,KAAK;;;;;;8DACd,mTAAC;oDAAK,WAAU;8DACb,EAAE,KAAK;;;;;;;2CAH2C,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;kCAWtE,mTAAC,uJAAI;;0CACH,mTAAC,6JAAU;0CACT,cAAA,mTAAC,4JAAS;8CAAC;;;;;;;;;;;0CAEb,mTAAC,8JAAW;0CACV,cAAA,mTAAC;oCAAG,WAAU;8CACX,MAAM,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,kBACzB,mTAAC;4CACC,WAAU;;8DAGV,mTAAC;;sEACC,mTAAC;4DAAI,WAAU;sEACZ,EAAE,QAAQ,IAAI,EAAE,SAAS,IAAI,EAAE,MAAM;;;;;;sEAExC,mTAAC;4DAAI,WAAU;;gEACZ,EAAE,WAAW;gEAAC;gEAAgB,EAAE,QAAQ;gEAAC;;;;;;;;;;;;;8DAG9C,mTAAC;oDAAI,WAAU;;wDAAU;wDACb,CAAC,EAAE,QAAQ,GAAG,OAAO,EAAE,OAAO,CAAC;wDAAG;;;;;;;;2CAXzC,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoBzB,mTAAC,uJAAI;;kCACH,mTAAC,6JAAU;kCACT,cAAA,mTAAC,4JAAS;sCAAC;;;;;;;;;;;kCAEb,mTAAC,8JAAW;kCACV,cAAA,mTAAC;4BAAG,WAAU;sCACX,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC,kBACpC,mTAAC;oCAAG,WAAU;;sDACZ,mTAAC;4CAAI,WAAU;;8DACb,mTAAC;oDAAI,WAAU;;wDACZ,EAAE,eAAe,IAAI;wDAAW;wDAAG;sEACpC,mTAAC;4DAAK,WAAU;sEACb,EAAE,QAAQ,IAAI,EAAE,SAAS;;;;;;;;;;;;gDAG7B,EAAE,aAAa,iBACd,mTAAC;oDACC,WAAU;oDACV,MAAM,EAAE,aAAa;oDACrB,KAAI;oDACJ,QAAO;8DACR;;;;;2DAGC;;;;;;;sDAEN,mTAAC;4CAAI,WAAU;;gDAAqC;gDAC1C;gDACP,sBACC,AACE,EAGA,OAAO,EACT;;;;;;;;mCA3BqC,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqC9D;AAEA,SAAS;IACP,MAAM,IAAI,IAAI;IACd,MAAM,YAAY;IAClB,EAAE,UAAU,CAAC,EAAE,UAAU,KAAK;IAC9B,EAAE,WAAW,CAAC,GAAG,GAAG,GAAG;IACvB,OAAO,EAAE,WAAW;AACtB;AACA,SAAS;IACP,MAAM,IAAI,IAAI;IACd,MAAM,WAAW;IACjB,MAAM,UAAU;IAChB,MAAM,UAAU;IAChB,MAAM,SAAS;IACf,EAAE,WAAW,CAAC,UAAU,SAAS,SAAS;IAC1C,OAAO,EAAE,WAAW;AACtB;AACA,SAAS,UAAU,GAAW;IAC5B,OAAO,IAAI,KAAK,CAAC,GAAG;AACtB;AACA,SAAS,cAAc,KAAa;IAClC,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,MAAM,CAAC,GAAG,GAAG,EAAE,GAAG,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC;IACvC,MAAM,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG;IAC/D,OAAO,GAAG,WAAW;AACvB;AAEA,SAAS,kBAAkB,IAAY;IACrC,MAAM,SAAS,KACZ,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IACjB,MAAM,CAAC;IACV,MAAM,QAAQ,IAAI,IAAI,WAAW,GAAG,CAAC,CAAC,IAAM,EAAE,WAAW;IACzD,OAAO,OAAO,MAAM,CAAC,CAAC,IAAM,MAAM,GAAG,CAAC,EAAE,WAAW;AACrD;AAEA,SAAS,WACP,IAKE,EACF,WAAmB;IAEnB,MAAM,iBAAiB,kBAAkB;IACzC,MAAM,SAAS,eAAe,MAAM,GAAG,iBAAiB;IACxD,MAAM,mBAAmB,KAAK,MAAM;IACpC,IAAI,gBAAgB;IACpB,MAAM,eAAe,IAAI;IACzB,MAAM,SAAS,IAAI;IAYnB,KAAK,MAAM,KAAK,KAAM;QACpB,MAAM,MAAM,AAAC,EAAE,OAAO,IAAgD,EAAE;QACxE,MAAM,WAAW,IAAI,MAAM,CAAC,CAAC,IAAM,OAAO,QAAQ,CAAC,EAAE,KAAK;QAC1D,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG;QACnE,iBAAiB;QAEjB,KAAK,MAAM,KAAK,SAAU;YACxB,aAAa,GAAG,CACd,EAAE,KAAK,EACP,CAAC,aAAa,GAAG,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;QAEpD;QAEA,MAAM,SAAS,EAAE,MAAM;QACvB,MAAM,OAAO,OAAO,GAAG,CAAC,WAAW;YACjC,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,SAAS;YACtB,UAAU,EAAE,QAAQ;YACpB,aAAa;YACb,UAAU;YACV,UAAU;QACZ;QACA,KAAK,WAAW,IAAI;QACpB,KAAK,QAAQ,IAAI;QACjB,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM;QACvE,MAAM,WAAW,OAAO,MAAM,GAAG,IAAI,gBAAgB,OAAO,MAAM,GAAG;QACrE,KAAK,QAAQ,GACX,CAAC,KAAK,QAAQ,GAAG,CAAC,KAAK,WAAW,GAAG,CAAC,IAAI,QAAQ,IAAI,KAAK,WAAW;QACxE,OAAO,GAAG,CAAC,QAAQ;IACrB;IAEA,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,IAAI,CAAC,aAAa,OAAO,IAC9C,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,GAAK,CAAC;YAAE;YAAO;QAAM,CAAC,GACzC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;IAEZ,OAAO;QACL;QACA;QACA,aACE,mBAAmB,IACf,MAAM,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE,KAC/D,OAAO,IAAI,IAAI,IACjB;QACN;QACA,OAAO,CAAC,MACN,MAAM,IAAI,CAAC,OAAO,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG;gBACnC,IAAI,QAAQ,YAAY;oBACtB,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;gBAChC;gBACA,IAAI,QAAQ,YAAY;oBACtB,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;gBAChC;gBACA,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW;YACtC;IACJ;AACF;AAEA,SAAS,sBACP,OAAgD,EAChD,WAAmB;IAEnB,MAAM,WAAW,IAAI,IAAI,kBAAkB;IAC3C,MAAM,aAAa;IACnB,MAAM,QAAQ,QACX,MAAM,CAAC,CAAC,IAAO,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,EAAE,KAAK,IAAI,MACvD,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,GAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,YACT,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;IACvC,OAAO,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ;AAC3C;AAEA,SAAS,IAAI,EAAE,KAAK,EAAE,KAAK,EAAoC;IAC7D,qBACE,mTAAC,uJAAI;;0BACH,mTAAC,6JAAU;0BACT,cAAA,mTAAC,4JAAS;8BAAE;;;;;;;;;;;0BAEd,mTAAC,8JAAW;0BACV,cAAA,mTAAC;oBAAI,WAAU;8BAA0C;;;;;;;;;;;;;;;;;AAIjE", "debugId": null}}]}