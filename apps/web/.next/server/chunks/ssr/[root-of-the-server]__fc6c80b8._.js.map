{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/packages/backend/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;;;AAED;AAAA;;AAUO,MAAM,MAAM,mNAAM;AAClB,MAAM,WAAW,mNAAM", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,mOAAO,EAAC,IAAA,6LAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot as SlotPrimitive } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex shrink-0 items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? SlotPrimitive.Slot : 'button';\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ variant, size, className }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,iPAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,sQAAa,CAAC,IAAI,GAAG;IAE5C,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/card.tsx"], "sourcesContent": ["import type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn(\n        'flex flex-col gap-6 rounded-xl border bg-card py-6 text-card-foreground shadow-sm',\n        className\n      )}\n      data-slot=\"card\"\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      data-slot=\"card-header\"\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('font-semibold leading-none', className)}\n      data-slot=\"card-title\"\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('text-muted-foreground text-sm', className)}\n      data-slot=\"card-description\"\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      data-slot=\"card-action\"\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('px-6', className)}\n      data-slot=\"card-content\"\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      data-slot=\"card-footer\"\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EACX,qFACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EACX,8JACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,8BAA8B;QAC5C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EACX,kEACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,QAAQ;QACtB,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EAAC,2CAA2C;QACzD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/input.tsx"], "sourcesContent": ["import type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\n  return (\n    <input\n      className={cn(\n        'flex h-9 w-full min-w-0 rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-xs outline-none transition-[color,box-shadow] selection:bg-primary selection:text-primary-foreground file:inline-flex file:h-7 file:border-0 file:bg-transparent file:font-medium file:text-foreground file:text-sm placeholder:text-muted-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-input/30',\n        'focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50',\n        'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',\n        className\n      )}\n      data-slot=\"input\"\n      type={type}\n      {...props}\n    />\n  );\n}\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,mTAAC;QACC,WAAW,IAAA,wIAAE,EACX,mcACA,iFACA,0GACA;QAEF,aAAU;QACV,MAAM;QACL,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/components/ui/label.tsx"], "sourcesContent": ["import * as LabelPrimitive from '@radix-ui/react-label';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      className={cn(\n        'flex select-none items-center gap-2 font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50',\n        className\n      )}\n      data-slot=\"label\"\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAAA;AAGA;;;;AAEA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,mTAAC,wQAAmB;QAClB,WAAW,IAAA,wIAAE,EACX,uNACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/apps/web/src/app/fireflies/page.tsx"], "sourcesContent": ["'use client';\n\nimport { api } from '@Fireflies-Sales/backend/convex/_generated/api';\nimport { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs';\nimport { useMutation } from 'convex/react';\nimport { useState } from 'react';\nimport { z } from 'zod';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\n\ntype MatchItem = {\n  index: number;\n  speaker: string | null;\n  text: string;\n  start_time: string;\n  end_time: string;\n  start_seconds: number | null;\n  end_seconds: number | null;\n  hits?: number;\n  reason?: string;\n  confidence?: number;\n};\n\nconst responseSchema = z.object({\n  transcript_id: z.string(),\n  transcript_title: z.string().nullable().optional(),\n  transcript_url: z.string().nullable().optional(),\n  speaker: z.string().nullable(),\n  total_sentences: z.number(),\n  word: z.string().nullable(),\n  literal_word: z\n    .object({\n      count: z.number(),\n      matches: z.array(\n        z.object({\n          index: z.number(),\n          speaker: z.string().nullable(),\n          text: z.string(),\n          start_time: z.string(),\n          end_time: z.string(),\n          start_seconds: z.number().nullable(),\n          end_seconds: z.number().nullable(),\n          hits: z.number(),\n        })\n      ),\n    })\n    .nullable()\n    .optional(),\n  concept: z.string().nullable(),\n  concept_result: z\n    .object({\n      count: z.number(),\n      matches: z.array(\n        z.object({\n          index: z.number(),\n          speaker: z.string().nullable(),\n          text: z.string(),\n          start_time: z.string(),\n          end_time: z.string(),\n          start_seconds: z.number().nullable(),\n          end_seconds: z.number().nullable(),\n          reason: z.string().optional(),\n          confidence: z.number().optional(),\n        })\n      ),\n      samples: z.array(z.string()),\n    })\n    .nullable()\n    .optional(),\n});\n\nconst PRESET_CONCEPTS = [\n  'biggest twitter spaces account in the world',\n  'endorsement from elon musk',\n  'investment',\n  'media',\n  'exposure',\n  'brand awareness',\n  'community building',\n  'connections',\n  'OTC',\n  'post-tge setup',\n  'biggest KOLs in the world',\n  'tge',\n  'tge launches',\n  'founder brand building',\n  'trust',\n  // additional topics from PRD addendum\n  'dealflow partnership',\n  'collaboration with our existing projects',\n  '600+ projects in our captable',\n  'listing help',\n  'mm (market making) help',\n  'VC connections',\n  'KOL connections',\n] as const;\n\n// UI constants\nconst DEFAULT_TOPK = 25;\nconst MAX_PRESET_CONCEPT_CHIPS = 8;\nconst MIN_TOPK = 1;\nconst MAX_TOPK = 100;\nconst DECIMAL_RADIX = 10;\nconst PERCENT = 100;\n\nexport default function FirefliesPage() {\n  const [source, setSource] = useState('');\n  const [word, setWord] = useState('');\n  const [concept, setConcept] = useState('');\n  const [speaker, setSpeaker] = useState('');\n  const [topK, setTopK] = useState(DEFAULT_TOPK);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [data, setData] = useState<z.infer<typeof responseSchema> | null>(null);\n  const [meta, setMeta] = useState<{\n    id: string;\n    title: string | null;\n    transcript_url: string | null;\n    total_sentences: number;\n  } | null>(null);\n  const logSearch = useMutation(api.searches.log);\n\n  const buildRequestBody = (): Record<string, unknown> => {\n    const body: Record<string, unknown> = { source, topK };\n    if (word.trim()) {\n      body.word = word.trim();\n    }\n    if (concept.trim()) {\n      body.concept = concept.trim();\n    }\n    if (speaker.trim()) {\n      body.speaker = speaker.trim();\n    }\n    return body;\n  };\n\n  const onFetchTranscript = async () => {\n    setError(null);\n    setMeta(null);\n    try {\n      const resp = await fetch('/api/fireflies/transcript', {\n        method: 'POST',\n        headers: { 'content-type': 'application/json' },\n        body: JSON.stringify({ source }),\n      });\n      const json = await resp.json();\n      if (resp.ok) {\n        setMeta(json);\n      } else {\n        setError(json?.error ?? 'Request failed');\n      }\n    } catch (err) {\n      const msg = err instanceof Error ? err.message : String(err);\n      setError(msg);\n    }\n  };\n\n  const handleResponse = (\n    json: unknown,\n    ok: boolean\n  ): z.infer<typeof responseSchema> | string => {\n    if (!ok) {\n      const err = json as { error?: string };\n      return err?.error ?? 'Request failed';\n    }\n    const parsed = responseSchema.safeParse(json);\n    if (!parsed.success) {\n      return 'Unexpected response format';\n    }\n    return parsed.data;\n  };\n\n  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(null);\n    setData(null);\n    try {\n      const body = buildRequestBody();\n\n      const resp = await fetch('/api/fireflies/search', {\n        method: 'POST',\n        headers: { 'content-type': 'application/json' },\n        body: JSON.stringify(body),\n      });\n      const json = await resp.json();\n      const result = handleResponse(json, resp.ok);\n      if (typeof result === 'string') {\n        setError(result);\n      } else {\n        setData(result);\n        // Log to Convex (authenticated)\n        try {\n          await logSearch({\n            source,\n            word: word.trim() || undefined,\n            concept: concept.trim() || undefined,\n            speaker: speaker.trim() || undefined,\n          });\n        } catch {\n          // ignore log errors\n        }\n      }\n    } catch (err) {\n      const msg = err instanceof Error ? err.message : String(err);\n      setError(msg);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <main className=\"min-h-[calc(100svh-3rem)] w-full bg-background px-4 py-8\">\n      <div className=\"mx-auto max-w-3xl\">\n        <div className=\"mb-6 flex items-center justify-between\">\n          <h1 className=\"font-semibold text-2xl text-foreground\">\n            Fireflies Search\n          </h1>\n          <UserButton />\n        </div>\n        <p className=\"mb-8 text-muted-foreground\">\n          Enter a Fireflies transcript ID or view URL. Search for a literal word\n          and/or a broader concept. Results include timestamps and reasoning for\n          concept matches.\n        </p>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Search Parameters</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <SignedIn>\n              <form className=\"grid gap-6\" onSubmit={onSubmit}>\n                <div className=\"grid gap-2\">\n                  <Label htmlFor=\"source\">Transcript Source</Label>\n                  <Input\n                    autoComplete=\"off\"\n                    id=\"source\"\n                    name=\"source\"\n                    onChange={(e) => setSource(e.target.value)}\n                    placeholder=\"abc123DEF or https://app.fireflies.ai/view/org::abc123DEF\"\n                    required\n                    value={source}\n                  />\n                  <div className=\"flex items-center gap-3 pt-2\">\n                    <Button\n                      disabled={!source.trim()}\n                      onClick={onFetchTranscript}\n                      type=\"button\"\n                    >\n                      Fetch Transcript Info\n                    </Button>\n                    <Button disabled={loading || !source.trim()} type=\"submit\">\n                      {loading ? 'Searching…' : 'Search'}\n                    </Button>\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-1 gap-4 md:grid-cols-2\">\n                  <div className=\"grid gap-2\">\n                    <Label htmlFor=\"word\">Exact Word (optional)</Label>\n                    <Input\n                      autoComplete=\"off\"\n                      id=\"word\"\n                      name=\"word\"\n                      onChange={(e) => setWord(e.target.value)}\n                      placeholder=\"e.g. pricing\"\n                      value={word}\n                    />\n                  </div>\n                  <div className=\"grid gap-2\">\n                    <Label htmlFor=\"speaker\">Speaker (optional)</Label>\n                    <Input\n                      autoComplete=\"off\"\n                      id=\"speaker\"\n                      name=\"speaker\"\n                      onChange={(e) => setSpeaker(e.target.value)}\n                      placeholder=\"Exact speaker name\"\n                      value={speaker}\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid gap-2\">\n                  <Label htmlFor=\"concept\">Concept (optional)</Label>\n                  <Input\n                    autoComplete=\"off\"\n                    id=\"concept\"\n                    name=\"concept\"\n                    onChange={(e) => setConcept(e.target.value)}\n                    placeholder=\"e.g. expressions of apology or regret about a delay\"\n                    value={concept}\n                  />\n                  <div className=\"flex flex-wrap gap-2 pt-2\">\n                    {PRESET_CONCEPTS.slice(0, MAX_PRESET_CONCEPT_CHIPS).map(\n                      (c) => (\n                        <Button\n                          aria-label={`Use concept: ${c}`}\n                          className=\"h-8 rounded-full px-3 text-xs\"\n                          key={c}\n                          onClick={() => setConcept(c)}\n                          type=\"button\"\n                        >\n                          {c}\n                        </Button>\n                      )\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"grid gap-2\">\n                  <Label htmlFor=\"topK\">Max Concept Matches</Label>\n                  <Input\n                    id=\"topK\"\n                    max={MAX_TOPK}\n                    min={MIN_TOPK}\n                    name=\"topK\"\n                    onChange={(e) =>\n                      setTopK(\n                        Number.parseInt(e.target.value, DECIMAL_RADIX) ||\n                          DEFAULT_TOPK\n                      )\n                    }\n                    type=\"number\"\n                    value={topK}\n                  />\n                </div>\n\n                <div className=\"flex items-center gap-4\">\n                  {data?.transcript_url ? (\n                    <a\n                      className=\"underline\"\n                      href={data.transcript_url}\n                      rel=\"noopener\"\n                      target=\"_blank\"\n                    >\n                      View on Fireflies\n                    </a>\n                  ) : null}\n                </div>\n              </form>\n            </SignedIn>\n            <SignedOut>\n              <div className=\"flex items-center gap-4\">\n                <SignInButton mode=\"modal\">\n                  <Button type=\"button\">Sign in to search</Button>\n                </SignInButton>\n              </div>\n            </SignedOut>\n          </CardContent>\n        </Card>\n\n        {error ? (\n          <p className=\"mt-6 text-red-600\" role=\"alert\">\n            {error}\n          </p>\n        ) : null}\n\n        {meta ? (\n          <Card className=\"mt-8\">\n            <CardHeader>\n              <CardTitle>Transcript</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid gap-1\">\n                <div className=\"font-medium\">{meta.title || 'Untitled'}</div>\n                <div className=\"text-muted-foreground text-sm\">\n                  ID: {meta.id} · Sentences: {meta.total_sentences}\n                </div>\n                {meta.transcript_url ? (\n                  <a\n                    className=\"text-sm underline\"\n                    href={meta.transcript_url}\n                    rel=\"noopener\"\n                    target=\"_blank\"\n                  >\n                    Open in Fireflies\n                  </a>\n                ) : null}\n              </div>\n            </CardContent>\n          </Card>\n        ) : null}\n\n        {data ? (\n          <div className=\"mt-8 grid gap-8\">\n            {data.word && data.literal_word ? (\n              <section aria-labelledby=\"word-results\" className=\"grid gap-4\">\n                <h2\n                  className=\"font-semibold text-foreground text-xl\"\n                  id=\"word-results\"\n                >\n                  Word Matches: “{data.word}” ({data.literal_word.count} hits)\n                </h2>\n                <ul className=\"grid gap-2\">\n                  {data.literal_word.matches.map((m: MatchItem) => (\n                    <li className=\"rounded-md border p-3\" key={`w-${m.index}`}>\n                      <div className=\"flex flex-wrap items-baseline justify-between gap-2\">\n                        <div className=\"font-mono text-sm\">#{m.index}</div>\n                        <div className=\"text-muted-foreground text-sm\">\n                          {m.start_time} – {m.end_time}\n                        </div>\n                      </div>\n                      <p className=\"mt-2\">{m.text}</p>\n                      {m.speaker ? (\n                        <p className=\"mt-1 text-muted-foreground text-sm\">\n                          Speaker: {m.speaker}\n                        </p>\n                      ) : null}\n                    </li>\n                  ))}\n                </ul>\n              </section>\n            ) : null}\n\n            {data.concept && data.concept_result ? (\n              <section aria-labelledby=\"concept-results\" className=\"grid gap-4\">\n                <h2\n                  className=\"font-semibold text-foreground text-xl\"\n                  id=\"concept-results\"\n                >\n                  Concept Matches: “{data.concept}” ({data.concept_result.count}\n                  )\n                </h2>\n                <ul className=\"grid gap-2\">\n                  {data.concept_result.matches.map((m: MatchItem) => (\n                    <li className=\"rounded-md border p-3\" key={`c-${m.index}`}>\n                      <div className=\"flex flex-wrap items-baseline justify-between gap-2\">\n                        <div className=\"font-mono text-sm\">#{m.index}</div>\n                        <div className=\"text-muted-foreground text-sm\">\n                          {m.start_time} – {m.end_time}\n                        </div>\n                      </div>\n                      <p className=\"mt-2\">{m.text}</p>\n                      <div className=\"mt-1 text-muted-foreground text-sm\">\n                        {m.speaker ? (\n                          <span>Speaker: {m.speaker} · </span>\n                        ) : null}\n                        {typeof m.confidence === 'number' ? (\n                          <span>\n                            Confidence: {(m.confidence * PERCENT).toFixed(0)}%\n                          </span>\n                        ) : null}\n                      </div>\n                      {m.reason ? (\n                        <p className=\"mt-2 text-muted-foreground text-sm\">\n                          Reason: {m.reason}\n                        </p>\n                      ) : null}\n                    </li>\n                  ))}\n                </ul>\n              </section>\n            ) : null}\n          </div>\n        ) : null}\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAyBA,MAAM,iBAAiB,iOAAC,CAAC,MAAM,CAAC;IAC9B,eAAe,iOAAC,CAAC,MAAM;IACvB,kBAAkB,iOAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,gBAAgB,iOAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,SAAS,iOAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,iBAAiB,iOAAC,CAAC,MAAM;IACzB,MAAM,iOAAC,CAAC,MAAM,GAAG,QAAQ;IACzB,cAAc,iOAAC,CACZ,MAAM,CAAC;QACN,OAAO,iOAAC,CAAC,MAAM;QACf,SAAS,iOAAC,CAAC,KAAK,CACd,iOAAC,CAAC,MAAM,CAAC;YACP,OAAO,iOAAC,CAAC,MAAM;YACf,SAAS,iOAAC,CAAC,MAAM,GAAG,QAAQ;YAC5B,MAAM,iOAAC,CAAC,MAAM;YACd,YAAY,iOAAC,CAAC,MAAM;YACpB,UAAU,iOAAC,CAAC,MAAM;YAClB,eAAe,iOAAC,CAAC,MAAM,GAAG,QAAQ;YAClC,aAAa,iOAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,MAAM,iOAAC,CAAC,MAAM;QAChB;IAEJ,GACC,QAAQ,GACR,QAAQ;IACX,SAAS,iOAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,gBAAgB,iOAAC,CACd,MAAM,CAAC;QACN,OAAO,iOAAC,CAAC,MAAM;QACf,SAAS,iOAAC,CAAC,KAAK,CACd,iOAAC,CAAC,MAAM,CAAC;YACP,OAAO,iOAAC,CAAC,MAAM;YACf,SAAS,iOAAC,CAAC,MAAM,GAAG,QAAQ;YAC5B,MAAM,iOAAC,CAAC,MAAM;YACd,YAAY,iOAAC,CAAC,MAAM;YACpB,UAAU,iOAAC,CAAC,MAAM;YAClB,eAAe,iOAAC,CAAC,MAAM,GAAG,QAAQ;YAClC,aAAa,iOAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,QAAQ,iOAAC,CAAC,MAAM,GAAG,QAAQ;YAC3B,YAAY,iOAAC,CAAC,MAAM,GAAG,QAAQ;QACjC;QAEF,SAAS,iOAAC,CAAC,KAAK,CAAC,iOAAC,CAAC,MAAM;IAC3B,GACC,QAAQ,GACR,QAAQ;AACb;AAEA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,sCAAsC;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,eAAe;AACf,MAAM,eAAe;AACrB,MAAM,2BAA2B;AACjC,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,gBAAgB;AACtB,MAAM,UAAU;AAED,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,sRAAQ,EAAC;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,sRAAQ,EAAC;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,sRAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,sRAAQ,EAAC;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,sRAAQ,EAAC;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,sRAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,sRAAQ,EAAgB;IAClD,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,sRAAQ,EAAwC;IACxE,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,sRAAQ,EAKtB;IACV,MAAM,YAAY,IAAA,8OAAW,EAAC,yJAAG,CAAC,QAAQ,CAAC,GAAG;IAE9C,MAAM,mBAAmB;QACvB,MAAM,OAAgC;YAAE;YAAQ;QAAK;QACrD,IAAI,KAAK,IAAI,IAAI;YACf,KAAK,IAAI,GAAG,KAAK,IAAI;QACvB;QACA,IAAI,QAAQ,IAAI,IAAI;YAClB,KAAK,OAAO,GAAG,QAAQ,IAAI;QAC7B;QACA,IAAI,QAAQ,IAAI,IAAI;YAClB,KAAK,OAAO,GAAG,QAAQ,IAAI;QAC7B;QACA,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,SAAS;QACT,QAAQ;QACR,IAAI;YACF,MAAM,OAAO,MAAM,MAAM,6BAA6B;gBACpD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YACA,MAAM,OAAO,MAAM,KAAK,IAAI;YAC5B,IAAI,KAAK,EAAE,EAAE;gBACX,QAAQ;YACV,OAAO;gBACL,SAAS,MAAM,SAAS;YAC1B;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,MAAM,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YACxD,SAAS;QACX;IACF;IAEA,MAAM,iBAAiB,CACrB,MACA;QAEA,IAAI,CAAC,IAAI;YACP,MAAM,MAAM;YACZ,OAAO,KAAK,SAAS;QACvB;QACA,MAAM,SAAS,eAAe,SAAS,CAAC;QACxC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,OAAO;QACT;QACA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,WAAW,OAAO;QACtB,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,QAAQ;QACR,IAAI;YACF,MAAM,OAAO;YAEb,MAAM,OAAO,MAAM,MAAM,yBAAyB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,KAAK,IAAI;YAC5B,MAAM,SAAS,eAAe,MAAM,KAAK,EAAE;YAC3C,IAAI,OAAO,WAAW,UAAU;gBAC9B,SAAS;YACX,OAAO;gBACL,QAAQ;gBACR,gCAAgC;gBAChC,IAAI;oBACF,MAAM,UAAU;wBACd;wBACA,MAAM,KAAK,IAAI,MAAM;wBACrB,SAAS,QAAQ,IAAI,MAAM;wBAC3B,SAAS,QAAQ,IAAI,MAAM;oBAC7B;gBACF,EAAE,OAAM;gBACN,oBAAoB;gBACtB;YACF;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,MAAM,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YACxD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,mTAAC;QAAK,WAAU;kBACd,cAAA,mTAAC;YAAI,WAAU;;8BACb,mTAAC;oBAAI,WAAU;;sCACb,mTAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,mTAAC,+QAAU;;;;;;;;;;;8BAEb,mTAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAM1C,mTAAC,uJAAI;;sCACH,mTAAC,6JAAU;sCACT,cAAA,mTAAC,4JAAS;0CAAC;;;;;;;;;;;sCAEb,mTAAC,8JAAW;;8CACV,mTAAC,uQAAQ;8CACP,cAAA,mTAAC;wCAAK,WAAU;wCAAa,UAAU;;0DACrC,mTAAC;gDAAI,WAAU;;kEACb,mTAAC,yJAAK;wDAAC,SAAQ;kEAAS;;;;;;kEACxB,mTAAC,yJAAK;wDACJ,cAAa;wDACb,IAAG;wDACH,MAAK;wDACL,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wDACzC,aAAY;wDACZ,QAAQ;wDACR,OAAO;;;;;;kEAET,mTAAC;wDAAI,WAAU;;0EACb,mTAAC,2JAAM;gEACL,UAAU,CAAC,OAAO,IAAI;gEACtB,SAAS;gEACT,MAAK;0EACN;;;;;;0EAGD,mTAAC,2JAAM;gEAAC,UAAU,WAAW,CAAC,OAAO,IAAI;gEAAI,MAAK;0EAC/C,UAAU,eAAe;;;;;;;;;;;;;;;;;;0DAKhC,mTAAC;gDAAI,WAAU;;kEACb,mTAAC;wDAAI,WAAU;;0EACb,mTAAC,yJAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,mTAAC,yJAAK;gEACJ,cAAa;gEACb,IAAG;gEACH,MAAK;gEACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACvC,aAAY;gEACZ,OAAO;;;;;;;;;;;;kEAGX,mTAAC;wDAAI,WAAU;;0EACb,mTAAC,yJAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,mTAAC,yJAAK;gEACJ,cAAa;gEACb,IAAG;gEACH,MAAK;gEACL,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC1C,aAAY;gEACZ,OAAO;;;;;;;;;;;;;;;;;;0DAKb,mTAAC;gDAAI,WAAU;;kEACb,mTAAC,yJAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,mTAAC,yJAAK;wDACJ,cAAa;wDACb,IAAG;wDACH,MAAK;wDACL,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC1C,aAAY;wDACZ,OAAO;;;;;;kEAET,mTAAC;wDAAI,WAAU;kEACZ,gBAAgB,KAAK,CAAC,GAAG,0BAA0B,GAAG,CACrD,CAAC,kBACC,mTAAC,2JAAM;gEACL,cAAY,CAAC,aAAa,EAAE,GAAG;gEAC/B,WAAU;gEAEV,SAAS,IAAM,WAAW;gEAC1B,MAAK;0EAEJ;+DAJI;;;;;;;;;;;;;;;;0DAWf,mTAAC;gDAAI,WAAU;;kEACb,mTAAC,yJAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,mTAAC,yJAAK;wDACJ,IAAG;wDACH,KAAK;wDACL,KAAK;wDACL,MAAK;wDACL,UAAU,CAAC,IACT,QACE,OAAO,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,kBAC9B;wDAGN,MAAK;wDACL,OAAO;;;;;;;;;;;;0DAIX,mTAAC;gDAAI,WAAU;0DACZ,MAAM,+BACL,mTAAC;oDACC,WAAU;oDACV,MAAM,KAAK,cAAc;oDACzB,KAAI;oDACJ,QAAO;8DACR;;;;;2DAGC;;;;;;;;;;;;;;;;;8CAIV,mTAAC,wQAAS;8CACR,cAAA,mTAAC;wCAAI,WAAU;kDACb,cAAA,mTAAC,qRAAY;4CAAC,MAAK;sDACjB,cAAA,mTAAC,2JAAM;gDAAC,MAAK;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAO/B,sBACC,mTAAC;oBAAE,WAAU;oBAAoB,MAAK;8BACnC;;;;;2BAED;gBAEH,qBACC,mTAAC,uJAAI;oBAAC,WAAU;;sCACd,mTAAC,6JAAU;sCACT,cAAA,mTAAC,4JAAS;0CAAC;;;;;;;;;;;sCAEb,mTAAC,8JAAW;sCACV,cAAA,mTAAC;gCAAI,WAAU;;kDACb,mTAAC;wCAAI,WAAU;kDAAe,KAAK,KAAK,IAAI;;;;;;kDAC5C,mTAAC;wCAAI,WAAU;;4CAAgC;4CACxC,KAAK,EAAE;4CAAC;4CAAe,KAAK,eAAe;;;;;;;oCAEjD,KAAK,cAAc,iBAClB,mTAAC;wCACC,WAAU;wCACV,MAAM,KAAK,cAAc;wCACzB,KAAI;wCACJ,QAAO;kDACR;;;;;+CAGC;;;;;;;;;;;;;;;;;2BAIR;gBAEH,qBACC,mTAAC;oBAAI,WAAU;;wBACZ,KAAK,IAAI,IAAI,KAAK,YAAY,iBAC7B,mTAAC;4BAAQ,mBAAgB;4BAAe,WAAU;;8CAChD,mTAAC;oCACC,WAAU;oCACV,IAAG;;wCACJ;wCACiB,KAAK,IAAI;wCAAC;wCAAI,KAAK,YAAY,CAAC,KAAK;wCAAC;;;;;;;8CAExD,mTAAC;oCAAG,WAAU;8CACX,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,kBAC9B,mTAAC;4CAAG,WAAU;;8DACZ,mTAAC;oDAAI,WAAU;;sEACb,mTAAC;4DAAI,WAAU;;gEAAoB;gEAAE,EAAE,KAAK;;;;;;;sEAC5C,mTAAC;4DAAI,WAAU;;gEACZ,EAAE,UAAU;gEAAC;gEAAI,EAAE,QAAQ;;;;;;;;;;;;;8DAGhC,mTAAC;oDAAE,WAAU;8DAAQ,EAAE,IAAI;;;;;;gDAC1B,EAAE,OAAO,iBACR,mTAAC;oDAAE,WAAU;;wDAAqC;wDACtC,EAAE,OAAO;;;;;;2DAEnB;;2CAZqC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE;;;;;;;;;;;;;;;mCAiB7D;wBAEH,KAAK,OAAO,IAAI,KAAK,cAAc,iBAClC,mTAAC;4BAAQ,mBAAgB;4BAAkB,WAAU;;8CACnD,mTAAC;oCACC,WAAU;oCACV,IAAG;;wCACJ;wCACoB,KAAK,OAAO;wCAAC;wCAAI,KAAK,cAAc,CAAC,KAAK;wCAAC;;;;;;;8CAGhE,mTAAC;oCAAG,WAAU;8CACX,KAAK,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,kBAChC,mTAAC;4CAAG,WAAU;;8DACZ,mTAAC;oDAAI,WAAU;;sEACb,mTAAC;4DAAI,WAAU;;gEAAoB;gEAAE,EAAE,KAAK;;;;;;;sEAC5C,mTAAC;4DAAI,WAAU;;gEACZ,EAAE,UAAU;gEAAC;gEAAI,EAAE,QAAQ;;;;;;;;;;;;;8DAGhC,mTAAC;oDAAE,WAAU;8DAAQ,EAAE,IAAI;;;;;;8DAC3B,mTAAC;oDAAI,WAAU;;wDACZ,EAAE,OAAO,iBACR,mTAAC;;gEAAK;gEAAU,EAAE,OAAO;gEAAC;;;;;;mEACxB;wDACH,OAAO,EAAE,UAAU,KAAK,yBACvB,mTAAC;;gEAAK;gEACS,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,OAAO,CAAC;gEAAG;;;;;;mEAEjD;;;;;;;gDAEL,EAAE,MAAM,iBACP,mTAAC;oDAAE,WAAU;;wDAAqC;wDACvC,EAAE,MAAM;;;;;;2DAEjB;;2CAtBqC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE;;;;;;;;;;;;;;;mCA2B7D;;;;;;2BAEJ;;;;;;;;;;;;AAIZ", "debugId": null}}]}