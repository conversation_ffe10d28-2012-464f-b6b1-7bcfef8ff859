{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/values/base64.ts"], "sourcesContent": ["/*\nhttps://github.com/beatgammit/base64-js/blob/88957c9943c7e2a0f03cdf73e71d579e433627d3/index.js\nCopyright (c) 2014 Jameson Little\nThe MIT License (MIT)\n*/\n\n// Vendored because this library has no ESM build, and some environments\n// (SvelteKit) are happiest when all dependencies are ESM.\n\nvar lookup: string[] = [];\nvar revLookup: number[] = [];\nvar Arr = Uint8Array;\n\nvar code = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup[\"-\".charCodeAt(0)] = 62;\nrevLookup[\"_\".charCodeAt(0)] = 63;\n\nfunction getLens(b64: string) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error(\"Invalid string. Length must be a multiple of 4\");\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf(\"=\");\n  if (validLen === -1) validLen = len;\n\n  var placeHoldersLen = validLen === len ? 0 : 4 - (validLen % 4);\n\n  return [validLen, placeHoldersLen];\n}\n\n// base64 is 4/3 + up to two characters of the original data\n/** @public */\nexport function byteLength(b64: string): number {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return ((validLen + placeHoldersLen) * 3) / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(_b64: string, validLen: number, placeHoldersLen: number) {\n  return ((validLen + placeHoldersLen) * 3) / 4 - placeHoldersLen;\n}\n\n/** @public */\nexport function toByteArray(b64: string): Uint8Array {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n\n  var curByte = 0;\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n\n  var i;\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = (tmp >> 16) & 0xff;\n    arr[curByte++] = (tmp >> 8) & 0xff;\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4);\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2);\n    arr[curByte++] = (tmp >> 8) & 0xff;\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num: number) {\n  return (\n    lookup[(num >> 18) & 0x3f] +\n    lookup[(num >> 12) & 0x3f] +\n    lookup[(num >> 6) & 0x3f] +\n    lookup[num & 0x3f]\n  );\n}\n\nfunction encodeChunk(uint8: Uint8Array, start: number, end: number) {\n  var tmp;\n  var output = [];\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xff0000) +\n      ((uint8[i + 1] << 8) & 0xff00) +\n      (uint8[i + 2] & 0xff);\n    output.push(tripletToBase64(tmp));\n  }\n  return output.join(\"\");\n}\n\n/** @public */\nexport function fromByteArray(uint8: Uint8Array): string {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(\n      encodeChunk(\n        uint8,\n        i,\n        i + maxChunkLength > len2 ? len2 : i + maxChunkLength,\n      ),\n    );\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[(tmp << 4) & 0x3f] + \"==\");\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(\n      lookup[tmp >> 10] +\n        lookup[(tmp >> 4) & 0x3f] +\n        lookup[(tmp << 2) & 0x3f] +\n        \"=\",\n    );\n  }\n\n  return parts.join(\"\");\n}\n\nexport function fromByteArrayUrlSafeNoPadding(uint8: Uint8Array): string {\n  return fromByteArray(uint8)\n    .replace(/\\+/g, \"-\")\n    .replace(/\\//g, \"_\")\n    .replace(/=/g, \"\");\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AASA,IAAI,SAAmB,CAAC,CAAA;AACxB,IAAI,YAAsB,CAAC,CAAA;AAC3B,IAAI,MAAM;AAEV,IAAI,OAAO;AACX,IAAA,IAAS,IAAI,GAAG,MAAM,KAAK,MAAA,EAAQ,IAAI,KAAK,EAAE,EAAG;IAC/C,MAAA,CAAO,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;IAClB,SAAA,CAAU,KAAK,UAAA,CAAW,CAAC,CAAC,CAAA,GAAI;AAClC;AAIA,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,GAAI;AAC/B,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,GAAI;AAE/B,SAAS,QAAQ,GAAA,EAAa;IAC5B,IAAI,MAAM,IAAI,MAAA;IAEd,IAAI,MAAM,IAAI,GAAG;QACf,MAAM,IAAI,MAAM,gDAAgD;IAClE;IAIA,IAAI,WAAW,IAAI,OAAA,CAAQ,GAAG;IAC9B,IAAI,aAAa,CAAA,EAAI,CAAA,WAAW;IAEhC,IAAI,kBAAkB,aAAa,MAAM,IAAI,IAAK,WAAW;IAE7D,OAAO;QAAC;QAAU,eAAe;KAAA;AACnC;AAIO,SAAS,WAAW,GAAA,EAAqB;IAC9C,IAAI,OAAO,QAAQ,GAAG;IACtB,IAAI,WAAW,IAAA,CAAK,CAAC,CAAA;IACrB,IAAI,kBAAkB,IAAA,CAAK,CAAC,CAAA;IAC5B,OAAA,CAAS,WAAW,eAAA,IAAmB,IAAK,IAAI;AAClD;AAEA,SAAS,YAAY,IAAA,EAAc,QAAA,EAAkB,eAAA,EAAyB;IAC5E,OAAA,CAAS,WAAW,eAAA,IAAmB,IAAK,IAAI;AAClD;AAGO,SAAS,YAAY,GAAA,EAAyB;IACnD,IAAI;IACJ,IAAI,OAAO,QAAQ,GAAG;IACtB,IAAI,WAAW,IAAA,CAAK,CAAC,CAAA;IACrB,IAAI,kBAAkB,IAAA,CAAK,CAAC,CAAA;IAE5B,IAAI,MAAM,IAAI,IAAI,YAAY,KAAK,UAAU,eAAe,CAAC;IAE7D,IAAI,UAAU;IAGd,IAAI,MAAM,kBAAkB,IAAI,WAAW,IAAI;IAE/C,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QAC3B,MACG,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,IAAK,KAChC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK,KACpC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK,IACrC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA;QACjC,GAAA,CAAI,SAAS,CAAA,GAAK,OAAO,KAAM;QAC/B,GAAA,CAAI,SAAS,CAAA,GAAK,OAAO,IAAK;QAC9B,GAAA,CAAI,SAAS,CAAA,GAAI,MAAM;IACzB;IAEA,IAAI,oBAAoB,GAAG;QACzB,MACG,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,IAAK,IAChC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK;QACvC,GAAA,CAAI,SAAS,CAAA,GAAI,MAAM;IACzB;IAEA,IAAI,oBAAoB,GAAG;QACzB,MACG,SAAA,CAAU,IAAI,UAAA,CAAW,CAAC,CAAC,CAAA,IAAK,KAChC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK,IACpC,SAAA,CAAU,IAAI,UAAA,CAAW,IAAI,CAAC,CAAC,CAAA,IAAK;QACvC,GAAA,CAAI,SAAS,CAAA,GAAK,OAAO,IAAK;QAC9B,GAAA,CAAI,SAAS,CAAA,GAAI,MAAM;IACzB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,GAAA,EAAa;IACpC,OACE,MAAA,CAAQ,OAAO,KAAM,EAAI,CAAA,GACzB,MAAA,CAAQ,OAAO,KAAM,EAAI,CAAA,GACzB,MAAA,CAAQ,OAAO,IAAK,EAAI,CAAA,GACxB,MAAA,CAAO,MAAM,EAAI,CAAA;AAErB;AAEA,SAAS,YAAY,KAAA,EAAmB,KAAA,EAAe,GAAA,EAAa;IAClE,IAAI;IACJ,IAAI,SAAS,CAAC,CAAA;IACd,IAAA,IAAS,IAAI,OAAO,IAAI,KAAK,KAAK,EAAG;QACnC,MAAA,CACI,KAAA,CAAM,CAAC,CAAA,IAAK,KAAM,QAAA,IAAA,CAClB,KAAA,CAAM,IAAI,CAAC,CAAA,IAAK,IAAK,KAAA,IAAA,CACtB,KAAA,CAAM,IAAI,CAAC,CAAA,GAAI,GAAA;QAClB,OAAO,IAAA,CAAK,gBAAgB,GAAG,CAAC;IAClC;IACA,OAAO,OAAO,IAAA,CAAK,EAAE;AACvB;AAGO,SAAS,cAAc,KAAA,EAA2B;IACvD,IAAI;IACJ,IAAI,MAAM,MAAM,MAAA;IAChB,IAAI,aAAa,MAAM;IACvB,IAAI,QAAQ,CAAC,CAAA;IACb,IAAI,iBAAiB;IAGrB,IAAA,IAAS,IAAI,GAAG,OAAO,MAAM,YAAY,IAAI,MAAM,KAAK,eAAgB;QACtE,MAAM,IAAA,CACJ,YACE,OACA,GACA,IAAI,iBAAiB,OAAO,OAAO,IAAI;IAG7C;IAGA,IAAI,eAAe,GAAG;QACpB,MAAM,KAAA,CAAM,MAAM,CAAC,CAAA;QACnB,MAAM,IAAA,CAAK,MAAA,CAAO,OAAO,CAAC,CAAA,GAAI,MAAA,CAAQ,OAAO,IAAK,EAAI,CAAA,GAAI,IAAI;IAChE,OAAA,IAAW,eAAe,GAAG;QAC3B,MAAA,CAAO,KAAA,CAAM,MAAM,CAAC,CAAA,IAAK,CAAA,IAAK,KAAA,CAAM,MAAM,CAAC,CAAA;QAC3C,MAAM,IAAA,CACJ,MAAA,CAAO,OAAO,EAAE,CAAA,GACd,MAAA,CAAQ,OAAO,IAAK,EAAI,CAAA,GACxB,MAAA,CAAQ,OAAO,IAAK,EAAI,CAAA,GACxB;IAEN;IAEA,OAAO,MAAM,IAAA,CAAK,EAAE;AACtB;AAEO,SAAS,8BAA8B,KAAA,EAA2B;IACvE,OAAO,cAAc,KAAK,EACvB,OAAA,CAAQ,OAAO,GAAG,EAClB,OAAA,CAAQ,OAAO,GAAG,EAClB,OAAA,CAAQ,MAAM,EAAE;AACrB", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/common/index.ts"], "sourcesContent": ["import type { Value } from \"../values/value.js\";\n\n/**\n * Validate that the arguments to a Convex function are an object, defaulting\n * `undefined` to `{}`.\n */\nexport function parseArgs(\n  args: Record<string, Value> | undefined,\n): Record<string, Value> {\n  if (args === undefined) {\n    return {};\n  }\n  if (!isSimpleObject(args)) {\n    throw new Error(\n      `The arguments to a Convex function must be an object. Received: ${\n        args as any\n      }`,\n    );\n  }\n  return args;\n}\n\nexport function validateDeploymentUrl(deploymentUrl: string) {\n  // Don't use things like `new URL(deploymentUrl).hostname` since these aren't\n  // supported by React Native's JS environment\n  if (typeof deploymentUrl === \"undefined\") {\n    throw new Error(\n      `Client created with undefined deployment address. If you used an environment variable, check that it's set.`,\n    );\n  }\n  if (typeof deploymentUrl !== \"string\") {\n    throw new Error(\n      `Invalid deployment address: found ${deploymentUrl as any}\".`,\n    );\n  }\n  if (\n    !(deploymentUrl.startsWith(\"http:\") || deploymentUrl.startsWith(\"https:\"))\n  ) {\n    throw new Error(\n      `Invalid deployment address: Must start with \"https://\" or \"http://\". Found \"${deploymentUrl}\".`,\n    );\n  }\n\n  // Most clients should connect to \".convex.cloud\". But we also support localhost and\n  // custom custom. We validate the deployment url is a valid url, which is the most\n  // common failure pattern.\n  try {\n    new URL(deploymentUrl);\n  } catch {\n    throw new Error(\n      `Invalid deployment address: \"${deploymentUrl}\" is not a valid URL. If you believe this URL is correct, use the \\`skipConvexDeploymentUrlCheck\\` option to bypass this.`,\n    );\n  }\n\n  // If a user uses .convex.site, this is very likely incorrect.\n  if (deploymentUrl.endsWith(\".convex.site\")) {\n    throw new Error(\n      `Invalid deployment address: \"${deploymentUrl}\" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \\`skipConvexDeploymentUrlCheck\\` option to bypass this.`,\n    );\n  }\n}\n\n/**\n * Check whether a value is a plain old JavaScript object.\n */\nexport function isSimpleObject(value: unknown) {\n  const isObject = typeof value === \"object\";\n  const prototype = Object.getPrototypeOf(value);\n  const isSimple =\n    prototype === null ||\n    prototype === Object.prototype ||\n    // Objects generated from other contexts (e.g. across Node.js `vm` modules) will not satisfy the previous\n    // conditions but are still simple objects.\n    prototype?.constructor?.name === \"Object\";\n  return isObject && isSimple;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAMO,SAAS,UACd,IAAA,EACuB;IACvB,IAAI,SAAS,KAAA,GAAW;QACtB,OAAO,CAAC;IACV;IACA,IAAI,CAAC,eAAe,IAAI,GAAG;QACzB,MAAM,IAAI,MACR,CAAA,gEAAA,EACE,IACF,EAAA;IAEJ;IACA,OAAO;AACT;AAEO,SAAS,sBAAsB,aAAA,EAAuB;IAG3D,IAAI,OAAO,kBAAkB,aAAa;QACxC,MAAM,IAAI,MACR,CAAA,2GAAA,CAAA;IAEJ;IACA,IAAI,OAAO,kBAAkB,UAAU;QACrC,MAAM,IAAI,MACR,CAAA,kCAAA,EAAqC,aAAoB,CAAA,EAAA,CAAA;IAE7D;IACA,IACE,CAAA,CAAE,cAAc,UAAA,CAAW,OAAO,KAAK,cAAc,UAAA,CAAW,QAAQ,CAAA,GACxE;QACA,MAAM,IAAI,MACR,CAAA,4EAAA,EAA+E,aAAa,CAAA,EAAA,CAAA;IAEhG;IAKA,IAAI;QACF,IAAI,IAAI,aAAa;IACvB,EAAA,OAAQ;QACN,MAAM,IAAI,MACR,CAAA,6BAAA,EAAgC,aAAa,CAAA,yHAAA,CAAA;IAEjD;IAGA,IAAI,cAAc,QAAA,CAAS,cAAc,GAAG;QAC1C,MAAM,IAAI,MACR,CAAA,6BAAA,EAAgC,aAAa,CAAA,sNAAA,CAAA;IAEjD;AACF;AAKO,SAAS,eAAe,KAAA,EAAgB;IAC7C,MAAM,WAAW,OAAO,UAAU;IAClC,MAAM,YAAY,OAAO,cAAA,CAAe,KAAK;IAC7C,MAAM,WACJ,cAAc,QACd,cAAc,OAAO,SAAA,IAAA,yGAAA;IAAA,2CAAA;IAGrB,WAAW,aAAa,SAAS;IACnC,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/values/value.ts"], "sourcesContent": ["/**\n * Utilities for working with values stored in Convex.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n * @module\n */\nimport * as Base64 from \"./base64.js\";\nimport { isSimpleObject } from \"../common/index.js\";\n\nconst LITTLE_ENDIAN = true;\n// This code is used by code that may not have bigint literals.\nconst MIN_INT64 = BigInt(\"-9223372036854775808\");\nconst MAX_INT64 = BigInt(\"9223372036854775807\");\nconst ZERO = BigInt(\"0\");\nconst EIGHT = BigInt(\"8\");\nconst TWOFIFTYSIX = BigInt(\"256\");\n\n/**\n * The type of JavaScript values serializable to JSON.\n *\n * @public\n */\nexport type JSONValue =\n  | null\n  | boolean\n  | number\n  | string\n  | JSONValue[]\n  | { [key: string]: JSONValue };\n\n/**\n * An identifier for a document in Convex.\n *\n * Convex documents are uniquely identified by their `Id`, which is accessible\n * on the `_id` field. To learn more, see [Document IDs](https://docs.convex.dev/database/document-ids).\n *\n * Documents can be loaded using `db.get(id)` in query and mutation functions.\n *\n * IDs are base 32 encoded strings which are URL safe.\n *\n * IDs are just strings at runtime, but this type can be used to distinguish them from other\n * strings at compile time.\n *\n * If you're using code generation, use the `Id` type generated for your data model in\n * `convex/_generated/dataModel.d.ts`.\n *\n * @typeParam TableName - A string literal type of the table name (like \"users\").\n *\n * @public\n */\nexport type Id<TableName extends string> = string & { __tableName: TableName };\n\n/**\n * A value supported by Convex.\n *\n * Values can be:\n * - stored inside of documents.\n * - used as arguments and return types to queries and mutation functions.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n *\n * @public\n */\nexport type Value =\n  | null\n  | bigint\n  | number\n  | boolean\n  | string\n  | ArrayBuffer\n  | Value[]\n  | { [key: string]: undefined | Value };\n\n/**\n * The types of {@link Value} that can be used to represent numbers.\n *\n * @public\n */\nexport type NumericValue = bigint | number;\n\nfunction isSpecial(n: number) {\n  return Number.isNaN(n) || !Number.isFinite(n) || Object.is(n, -0);\n}\n\nexport function slowBigIntToBase64(value: bigint): string {\n  // the conversion is easy if we pretend it's unsigned\n  if (value < ZERO) {\n    value -= MIN_INT64 + MIN_INT64;\n  }\n  let hex = value.toString(16);\n  if (hex.length % 2 === 1) hex = \"0\" + hex;\n\n  const bytes = new Uint8Array(new ArrayBuffer(8));\n  let i = 0;\n  for (const hexByte of hex.match(/.{2}/g)!.reverse()) {\n    bytes.set([parseInt(hexByte, 16)], i++);\n    value >>= EIGHT;\n  }\n  return Base64.fromByteArray(bytes);\n}\n\nexport function slowBase64ToBigInt(encoded: string): bigint {\n  const integerBytes = Base64.toByteArray(encoded);\n  if (integerBytes.byteLength !== 8) {\n    throw new Error(\n      `Received ${integerBytes.byteLength} bytes, expected 8 for $integer`,\n    );\n  }\n  let value = ZERO;\n  let power = ZERO;\n  for (const byte of integerBytes) {\n    value += BigInt(byte) * TWOFIFTYSIX ** power;\n    power++;\n  }\n  if (value > MAX_INT64) {\n    value += MIN_INT64 + MIN_INT64;\n  }\n  return value;\n}\n\nexport function modernBigIntToBase64(value: bigint): string {\n  if (value < MIN_INT64 || MAX_INT64 < value) {\n    throw new Error(\n      `BigInt ${value} does not fit into a 64-bit signed integer.`,\n    );\n  }\n  const buffer = new ArrayBuffer(8);\n  new DataView(buffer).setBigInt64(0, value, true);\n  return Base64.fromByteArray(new Uint8Array(buffer));\n}\n\nexport function modernBase64ToBigInt(encoded: string): bigint {\n  const integerBytes = Base64.toByteArray(encoded);\n  if (integerBytes.byteLength !== 8) {\n    throw new Error(\n      `Received ${integerBytes.byteLength} bytes, expected 8 for $integer`,\n    );\n  }\n  const intBytesView = new DataView(integerBytes.buffer);\n  return intBytesView.getBigInt64(0, true);\n}\n\n// Fall back to a slower version on Safari 14 which lacks these APIs.\nexport const bigIntToBase64 = (DataView.prototype as any).setBigInt64\n  ? modernBigIntToBase64\n  : slowBigIntToBase64;\nexport const base64ToBigInt = (DataView.prototype as any).getBigInt64\n  ? modernBase64ToBigInt\n  : slowBase64ToBigInt;\n\nconst MAX_IDENTIFIER_LEN = 1024;\n\nfunction validateObjectField(k: string) {\n  if (k.length > MAX_IDENTIFIER_LEN) {\n    throw new Error(\n      `Field name ${k} exceeds maximum field name length ${MAX_IDENTIFIER_LEN}.`,\n    );\n  }\n  if (k.startsWith(\"$\")) {\n    throw new Error(`Field name ${k} starts with a '$', which is reserved.`);\n  }\n  for (let i = 0; i < k.length; i += 1) {\n    const charCode = k.charCodeAt(i);\n    // Non-control ASCII characters\n    if (charCode < 32 || charCode >= 127) {\n      throw new Error(\n        `Field name ${k} has invalid character '${k[i]}': Field names can only contain non-control ASCII characters`,\n      );\n    }\n  }\n}\n\n/**\n * Parse a Convex value from its JSON representation.\n *\n * This function will deserialize serialized Int64s to `BigInt`s, Bytes to `ArrayBuffer`s etc.\n *\n * To learn more about Convex values, see [Types](https://docs.convex.dev/using/types).\n *\n * @param value - The JSON representation of a Convex value previously created with {@link convexToJson}.\n * @returns The JavaScript representation of the Convex value.\n *\n * @public\n */\nexport function jsonToConvex(value: JSONValue): Value {\n  if (value === null) {\n    return value;\n  }\n  if (typeof value === \"boolean\") {\n    return value;\n  }\n  if (typeof value === \"number\") {\n    return value;\n  }\n  if (typeof value === \"string\") {\n    return value;\n  }\n  if (Array.isArray(value)) {\n    return value.map((value) => jsonToConvex(value));\n  }\n  if (typeof value !== \"object\") {\n    throw new Error(`Unexpected type of ${value as any}`);\n  }\n  const entries = Object.entries(value);\n  if (entries.length === 1) {\n    const key = entries[0][0];\n    if (key === \"$bytes\") {\n      if (typeof value.$bytes !== \"string\") {\n        throw new Error(`Malformed $bytes field on ${value as any}`);\n      }\n      return Base64.toByteArray(value.$bytes).buffer;\n    }\n    if (key === \"$integer\") {\n      if (typeof value.$integer !== \"string\") {\n        throw new Error(`Malformed $integer field on ${value as any}`);\n      }\n      return base64ToBigInt(value.$integer);\n    }\n    if (key === \"$float\") {\n      if (typeof value.$float !== \"string\") {\n        throw new Error(`Malformed $float field on ${value as any}`);\n      }\n      const floatBytes = Base64.toByteArray(value.$float);\n      if (floatBytes.byteLength !== 8) {\n        throw new Error(\n          `Received ${floatBytes.byteLength} bytes, expected 8 for $float`,\n        );\n      }\n      const floatBytesView = new DataView(floatBytes.buffer);\n      const float = floatBytesView.getFloat64(0, LITTLE_ENDIAN);\n      if (!isSpecial(float)) {\n        throw new Error(`Float ${float} should be encoded as a number`);\n      }\n      return float;\n    }\n    if (key === \"$set\") {\n      throw new Error(\n        `Received a Set which is no longer supported as a Convex type.`,\n      );\n    }\n    if (key === \"$map\") {\n      throw new Error(\n        `Received a Map which is no longer supported as a Convex type.`,\n      );\n    }\n  }\n  const out: { [key: string]: Value } = {};\n  for (const [k, v] of Object.entries(value)) {\n    validateObjectField(k);\n    out[k] = jsonToConvex(v);\n  }\n  return out;\n}\n\nexport function stringifyValueForError(value: any) {\n  return JSON.stringify(value, (_key, value) => {\n    if (value === undefined) {\n      // By default `JSON.stringify` converts undefined, functions, symbols,\n      // Infinity, and NaN to null which produces a confusing error message.\n      // We deal with `undefined` specifically because it's the most common.\n      // Ideally we'd use a pretty-printing library that prints `undefined`\n      // (no quotes), but it might not be worth the bundle size cost.\n      return \"undefined\";\n    }\n    if (typeof value === \"bigint\") {\n      // `JSON.stringify` throws on bigints by default.\n      return `${value.toString()}n`;\n    }\n    return value;\n  });\n}\n\nfunction convexToJsonInternal(\n  value: Value,\n  originalValue: Value,\n  context: string,\n  includeTopLevelUndefined: boolean,\n): JSONValue {\n  if (value === undefined) {\n    const contextText =\n      context &&\n      ` (present at path ${context} in original object ${stringifyValueForError(\n        originalValue,\n      )})`;\n    throw new Error(\n      `undefined is not a valid Convex value${contextText}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`,\n    );\n  }\n  if (value === null) {\n    return value;\n  }\n  if (typeof value === \"bigint\") {\n    if (value < MIN_INT64 || MAX_INT64 < value) {\n      throw new Error(\n        `BigInt ${value} does not fit into a 64-bit signed integer.`,\n      );\n    }\n    return { $integer: bigIntToBase64(value) };\n  }\n  if (typeof value === \"number\") {\n    if (isSpecial(value)) {\n      const buffer = new ArrayBuffer(8);\n      new DataView(buffer).setFloat64(0, value, LITTLE_ENDIAN);\n      return { $float: Base64.fromByteArray(new Uint8Array(buffer)) };\n    } else {\n      return value;\n    }\n  }\n  if (typeof value === \"boolean\") {\n    return value;\n  }\n  if (typeof value === \"string\") {\n    return value;\n  }\n  if (value instanceof ArrayBuffer) {\n    return { $bytes: Base64.fromByteArray(new Uint8Array(value)) };\n  }\n  if (Array.isArray(value)) {\n    return value.map((value, i) =>\n      convexToJsonInternal(value, originalValue, context + `[${i}]`, false),\n    );\n  }\n  if (value instanceof Set) {\n    throw new Error(\n      errorMessageForUnsupportedType(context, \"Set\", [...value], originalValue),\n    );\n  }\n  if (value instanceof Map) {\n    throw new Error(\n      errorMessageForUnsupportedType(context, \"Map\", [...value], originalValue),\n    );\n  }\n\n  if (!isSimpleObject(value)) {\n    const theType = value?.constructor?.name;\n    const typeName = theType ? `${theType} ` : \"\";\n    throw new Error(\n      errorMessageForUnsupportedType(context, typeName, value, originalValue),\n    );\n  }\n\n  const out: { [key: string]: JSONValue } = {};\n  const entries = Object.entries(value);\n  entries.sort(([k1, _v1], [k2, _v2]) => (k1 === k2 ? 0 : k1 < k2 ? -1 : 1));\n  for (const [k, v] of entries) {\n    if (v !== undefined) {\n      validateObjectField(k);\n      out[k] = convexToJsonInternal(v, originalValue, context + `.${k}`, false);\n    } else if (includeTopLevelUndefined) {\n      validateObjectField(k);\n      out[k] = convexOrUndefinedToJsonInternal(\n        v,\n        originalValue,\n        context + `.${k}`,\n      );\n    }\n  }\n  return out;\n}\n\nfunction errorMessageForUnsupportedType(\n  context: string,\n  typeName: string,\n  value: any,\n  originalValue: any,\n) {\n  if (context) {\n    return `${typeName}${stringifyValueForError(\n      value,\n    )} is not a supported Convex type (present at path ${context} in original object ${stringifyValueForError(\n      originalValue,\n    )}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`;\n  } else {\n    return `${typeName}${stringifyValueForError(\n      value,\n    )} is not a supported Convex type.`;\n  }\n}\n\n// convexOrUndefinedToJsonInternal wrapper exists so we can pipe through the\n// `originalValue` and `context` through for better error messaging.\nfunction convexOrUndefinedToJsonInternal(\n  value: Value | undefined,\n  originalValue: Value | undefined,\n  context: string,\n): JSONValue {\n  if (value === undefined) {\n    return { $undefined: null };\n  } else {\n    if (originalValue === undefined) {\n      // This should not happen.\n      throw new Error(\n        `Programming error. Current value is ${stringifyValueForError(\n          value,\n        )} but original value is undefined`,\n      );\n    }\n    return convexToJsonInternal(value, originalValue, context, false);\n  }\n}\n\n/**\n * Convert a Convex value to its JSON representation.\n *\n * Use {@link jsonToConvex} to recreate the original value.\n *\n * To learn more about Convex values, see [Types](https://docs.convex.dev/using/types).\n *\n * @param value - A Convex value to convert into JSON.\n * @returns The JSON representation of `value`.\n *\n * @public\n */\nexport function convexToJson(value: Value): JSONValue {\n  return convexToJsonInternal(value, value, \"\", false);\n}\n\n// Convert a Convex value or `undefined` into its JSON representation.\n// `undefined` is used in filters to represent a missing object field.\nexport function convexOrUndefinedToJson(value: Value | undefined): JSONValue {\n  return convexOrUndefinedToJsonInternal(value, value, \"\");\n}\n\n/**\n * Similar to convexToJson but also serializes top level undefined fields\n * using convexOrUndefinedToJson().\n *\n * @param value - A Convex value to convert into JSON.\n * @returns The JSON representation of `value`.\n */\nexport function patchValueToJson(value: Value): JSONValue {\n  return convexToJsonInternal(value, value, \"\", true);\n}\n"], "names": ["value"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAOA,YAAY,YAAY;AACxB,SAAS,sBAAsB;;;;AAE/B,MAAM,gBAAgB;AAEtB,MAAM,YAAY,OAAO,sBAAsB;AAC/C,MAAM,YAAY,OAAO,qBAAqB;AAC9C,MAAM,OAAO,OAAO,GAAG;AACvB,MAAM,QAAQ,OAAO,GAAG;AACxB,MAAM,cAAc,OAAO,KAAK;AAkEhC,SAAS,UAAU,CAAA,EAAW;IAC5B,OAAO,OAAO,KAAA,CAAM,CAAC,KAAK,CAAC,OAAO,QAAA,CAAS,CAAC,KAAK,OAAO,EAAA,CAAG,GAAG,CAAA,CAAE;AAClE;AAEO,SAAS,mBAAmB,KAAA,EAAuB;IAExD,IAAI,QAAQ,MAAM;QAChB,SAAS,YAAY;IACvB;IACA,IAAI,MAAM,MAAM,QAAA,CAAS,EAAE;IAC3B,IAAI,IAAI,MAAA,GAAS,MAAM,EAAG,CAAA,MAAM,MAAM;IAEtC,MAAM,QAAQ,IAAI,WAAW,IAAI,YAAY,CAAC,CAAC;IAC/C,IAAI,IAAI;IACR,KAAA,MAAW,WAAW,IAAI,KAAA,CAAM,OAAO,EAAG,OAAA,CAAQ,EAAG;QACnD,MAAM,GAAA,CAAI;YAAC,SAAS,SAAS,EAAE,CAAC;SAAA,EAAG,GAAG;QACtC,UAAU;IACZ;IACA,OAAO,OAAO,sNAAA,CAAc,KAAK;AACnC;AAEO,SAAS,mBAAmB,OAAA,EAAyB;IAC1D,MAAM,eAAe,OAAO,oNAAA,CAAY,OAAO;IAC/C,IAAI,aAAa,UAAA,KAAe,GAAG;QACjC,MAAM,IAAI,MACR,CAAA,SAAA,EAAY,aAAa,UAAU,CAAA,+BAAA,CAAA;IAEvC;IACA,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,KAAA,MAAW,QAAQ,aAAc;QAC/B,SAAS,OAAO,IAAI,IAAI,eAAe;QACvC;IACF;IACA,IAAI,QAAQ,WAAW;QACrB,SAAS,YAAY;IACvB;IACA,OAAO;AACT;AAEO,SAAS,qBAAqB,KAAA,EAAuB;IAC1D,IAAI,QAAQ,aAAa,YAAY,OAAO;QAC1C,MAAM,IAAI,MACR,CAAA,OAAA,EAAU,KAAK,CAAA,2CAAA,CAAA;IAEnB;IACA,MAAM,SAAS,IAAI,YAAY,CAAC;IAChC,IAAI,SAAS,MAAM,EAAE,WAAA,CAAY,GAAG,OAAO,IAAI;IAC/C,OAAO,OAAO,sNAAA,CAAc,IAAI,WAAW,MAAM,CAAC;AACpD;AAEO,SAAS,qBAAqB,OAAA,EAAyB;IAC5D,MAAM,eAAe,OAAO,oNAAA,CAAY,OAAO;IAC/C,IAAI,aAAa,UAAA,KAAe,GAAG;QACjC,MAAM,IAAI,MACR,CAAA,SAAA,EAAY,aAAa,UAAU,CAAA,+BAAA,CAAA;IAEvC;IACA,MAAM,eAAe,IAAI,SAAS,aAAa,MAAM;IACrD,OAAO,aAAa,WAAA,CAAY,GAAG,IAAI;AACzC;AAGO,MAAM,iBAAkB,SAAS,SAAA,CAAkB,WAAA,GACtD,uBACA;AACG,MAAM,iBAAkB,SAAS,SAAA,CAAkB,WAAA,GACtD,uBACA;AAEJ,MAAM,qBAAqB;AAE3B,SAAS,oBAAoB,CAAA,EAAW;IACtC,IAAI,EAAE,MAAA,GAAS,oBAAoB;QACjC,MAAM,IAAI,MACR,CAAA,WAAA,EAAc,CAAC,CAAA,mCAAA,EAAsC,kBAAkB,CAAA,CAAA,CAAA;IAE3E;IACA,IAAI,EAAE,UAAA,CAAW,GAAG,GAAG;QACrB,MAAM,IAAI,MAAM,CAAA,WAAA,EAAc,CAAC,CAAA,sCAAA,CAAwC;IACzE;IACA,IAAA,IAAS,IAAI,GAAG,IAAI,EAAE,MAAA,EAAQ,KAAK,EAAG;QACpC,MAAM,WAAW,EAAE,UAAA,CAAW,CAAC;QAE/B,IAAI,WAAW,MAAM,YAAY,KAAK;YACpC,MAAM,IAAI,MACR,CAAA,WAAA,EAAc,CAAC,CAAA,wBAAA,EAA2B,CAAA,CAAE,CAAC,CAAC,CAAA,4DAAA,CAAA;QAElD;IACF;AACF;AAcO,SAAS,aAAa,KAAA,EAAyB;IACpD,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,OAAO,UAAU,WAAW;QAC9B,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACxB,OAAO,MAAM,GAAA,CAAI,CAACA,SAAU,aAAaA,MAAK,CAAC;IACjD;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,MAAM,IAAI,MAAM,CAAA,mBAAA,EAAsB,KAAY,EAAE;IACtD;IACA,MAAM,UAAU,OAAO,OAAA,CAAQ,KAAK;IACpC,IAAI,QAAQ,MAAA,KAAW,GAAG;QACxB,MAAM,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA;QACxB,IAAI,QAAQ,UAAU;YACpB,IAAI,OAAO,MAAM,MAAA,KAAW,UAAU;gBACpC,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,KAAY,EAAE;YAC7D;YACA,OAAO,OAAO,oNAAA,CAAY,MAAM,MAAM,EAAE,MAAA;QAC1C;QACA,IAAI,QAAQ,YAAY;YACtB,IAAI,OAAO,MAAM,QAAA,KAAa,UAAU;gBACtC,MAAM,IAAI,MAAM,CAAA,4BAAA,EAA+B,KAAY,EAAE;YAC/D;YACA,OAAO,eAAe,MAAM,QAAQ;QACtC;QACA,IAAI,QAAQ,UAAU;YACpB,IAAI,OAAO,MAAM,MAAA,KAAW,UAAU;gBACpC,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,KAAY,EAAE;YAC7D;YACA,MAAM,aAAa,OAAO,oNAAA,CAAY,MAAM,MAAM;YAClD,IAAI,WAAW,UAAA,KAAe,GAAG;gBAC/B,MAAM,IAAI,MACR,CAAA,SAAA,EAAY,WAAW,UAAU,CAAA,6BAAA,CAAA;YAErC;YACA,MAAM,iBAAiB,IAAI,SAAS,WAAW,MAAM;YACrD,MAAM,QAAQ,eAAe,UAAA,CAAW,GAAG,aAAa;YACxD,IAAI,CAAC,UAAU,KAAK,GAAG;gBACrB,MAAM,IAAI,MAAM,CAAA,MAAA,EAAS,KAAK,CAAA,8BAAA,CAAgC;YAChE;YACA,OAAO;QACT;QACA,IAAI,QAAQ,QAAQ;YAClB,MAAM,IAAI,MACR,CAAA,6DAAA,CAAA;QAEJ;QACA,IAAI,QAAQ,QAAQ;YAClB,MAAM,IAAI,MACR,CAAA,6DAAA,CAAA;QAEJ;IACF;IACA,MAAM,MAAgC,CAAC;IACvC,KAAA,MAAW,CAAC,GAAG,CAAC,CAAA,IAAK,OAAO,OAAA,CAAQ,KAAK,EAAG;QAC1C,oBAAoB,CAAC;QACrB,GAAA,CAAI,CAAC,CAAA,GAAI,aAAa,CAAC;IACzB;IACA,OAAO;AACT;AAEO,SAAS,uBAAuB,KAAA,EAAY;IACjD,OAAO,KAAK,SAAA,CAAU,OAAO,CAAC,MAAMA,WAAU;QAC5C,IAAIA,WAAU,KAAA,GAAW;YAMvB,OAAO;QACT;QACA,IAAI,OAAOA,WAAU,UAAU;YAE7B,OAAO,GAAGA,OAAM,QAAA,CAAS,CAAC,CAAA,CAAA,CAAA;QAC5B;QACA,OAAOA;IACT,CAAC;AACH;AAEA,SAAS,qBACP,KAAA,EACA,aAAA,EACA,OAAA,EACA,wBAAA,EACW;IACX,IAAI,UAAU,KAAA,GAAW;QACvB,MAAM,cACJ,WACA,CAAA,kBAAA,EAAqB,OAAO,CAAA,oBAAA,EAAuB,uBACjD,eACD,CAAA,CAAA;QACH,MAAM,IAAI,MACR,CAAA,qCAAA,EAAwC,WAAW,CAAA,mFAAA,CAAA;IAEvD;IACA,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,QAAQ,aAAa,YAAY,OAAO;YAC1C,MAAM,IAAI,MACR,CAAA,OAAA,EAAU,KAAK,CAAA,2CAAA,CAAA;QAEnB;QACA,OAAO;YAAE,UAAU,eAAe,KAAK;QAAE;IAC3C;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,UAAU,KAAK,GAAG;YACpB,MAAM,SAAS,IAAI,YAAY,CAAC;YAChC,IAAI,SAAS,MAAM,EAAE,UAAA,CAAW,GAAG,OAAO,aAAa;YACvD,OAAO;gBAAE,QAAQ,OAAO,sNAAA,CAAc,IAAI,WAAW,MAAM,CAAC;YAAE;QAChE,OAAO;YACL,OAAO;QACT;IACF;IACA,IAAI,OAAO,UAAU,WAAW;QAC9B,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,IAAI,iBAAiB,aAAa;QAChC,OAAO;YAAE,QAAQ,OAAO,sNAAA,CAAc,IAAI,WAAW,KAAK,CAAC;QAAE;IAC/D;IACA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACxB,OAAO,MAAM,GAAA,CAAI,CAACA,QAAO,IACvB,qBAAqBA,QAAO,eAAe,UAAU,CAAA,CAAA,EAAI,CAAC,CAAA,CAAA,CAAA,EAAK,KAAK;IAExE;IACA,IAAI,iBAAiB,KAAK;QACxB,MAAM,IAAI,MACR,+BAA+B,SAAS,OAAO,CAAC;eAAG,KAAK;SAAA,EAAG,aAAa;IAE5E;IACA,IAAI,iBAAiB,KAAK;QACxB,MAAM,IAAI,MACR,+BAA+B,SAAS,OAAO,CAAC;eAAG,KAAK;SAAA,EAAG,aAAa;IAE5E;IAEA,IAAI,KAAC,6NAAA,EAAe,KAAK,GAAG;QAC1B,MAAM,UAAU,OAAO,aAAa;QACpC,MAAM,WAAW,UAAU,GAAG,OAAO,CAAA,CAAA,CAAA,GAAM;QAC3C,MAAM,IAAI,MACR,+BAA+B,SAAS,UAAU,OAAO,aAAa;IAE1E;IAEA,MAAM,MAAoC,CAAC;IAC3C,MAAM,UAAU,OAAO,OAAA,CAAQ,KAAK;IACpC,QAAQ,IAAA,CAAK,CAAC,CAAC,IAAI,GAAG,CAAA,EAAG,CAAC,IAAI,GAAG,CAAA,GAAO,OAAO,KAAK,IAAI,KAAK,KAAK,CAAA,IAAK,CAAE;IACzE,KAAA,MAAW,CAAC,GAAG,CAAC,CAAA,IAAK,QAAS;QAC5B,IAAI,MAAM,KAAA,GAAW;YACnB,oBAAoB,CAAC;YACrB,GAAA,CAAI,CAAC,CAAA,GAAI,qBAAqB,GAAG,eAAe,UAAU,CAAA,CAAA,EAAI,CAAC,EAAA,EAAI,KAAK;QAC1E,OAAA,IAAW,0BAA0B;YACnC,oBAAoB,CAAC;YACrB,GAAA,CAAI,CAAC,CAAA,GAAI,gCACP,GACA,eACA,UAAU,CAAA,CAAA,EAAI,CAAC,EAAA;QAEnB;IACF;IACA,OAAO;AACT;AAEA,SAAS,+BACP,OAAA,EACA,QAAA,EACA,KAAA,EACA,aAAA,EACA;IACA,IAAI,SAAS;QACX,OAAO,GAAG,QAAQ,GAAG,uBACnB,OACD,iDAAA,EAAoD,OAAO,CAAA,oBAAA,EAAuB,uBACjF,eACD,oFAAA,CAAA;IACH,OAAO;QACL,OAAO,GAAG,QAAQ,GAAG,uBACnB,OACD,gCAAA,CAAA;IACH;AACF;AAIA,SAAS,gCACP,KAAA,EACA,aAAA,EACA,OAAA,EACW;IACX,IAAI,UAAU,KAAA,GAAW;QACvB,OAAO;YAAE,YAAY;QAAK;IAC5B,OAAO;QACL,IAAI,kBAAkB,KAAA,GAAW;YAE/B,MAAM,IAAI,MACR,CAAA,oCAAA,EAAuC,uBACrC,OACD,gCAAA,CAAA;QAEL;QACA,OAAO,qBAAqB,OAAO,eAAe,SAAS,KAAK;IAClE;AACF;AAcO,SAAS,aAAa,KAAA,EAAyB;IACpD,OAAO,qBAAqB,OAAO,OAAO,IAAI,KAAK;AACrD;AAIO,SAAS,wBAAwB,KAAA,EAAqC;IAC3E,OAAO,gCAAgC,OAAO,OAAO,EAAE;AACzD;AASO,SAAS,iBAAiB,KAAA,EAAyB;IACxD,OAAO,qBAAqB,OAAO,OAAO,IAAI,IAAI;AACpD", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/values/validators.ts"], "sourcesContent": ["import { GenericId } from \"./index.js\";\nimport { GenericValidator } from \"./validator.js\";\nimport { JSONValue, convexToJson } from \"./value.js\";\n\ntype TableNameFromType<T> =\n  T extends GenericId<infer TableName> ? TableName : string;\n\n/**\n * Avoid using `instanceof BaseValidator`; this is inheritence for code reuse\n * not type heirarchy.\n */\nabstract class BaseValidator<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = never,\n> {\n  /**\n   * Only for TypeScript, the TS type of the JS values validated\n   * by this validator.\n   */\n  readonly type!: Type;\n  /**\n   * Only for TypeScript, if this an Object validator, then\n   * this is the TS type of its property names.\n   */\n  readonly fieldPaths!: FieldPaths;\n\n  /**\n   * Whether this is an optional Object property value validator.\n   */\n  readonly isOptional: IsOptional;\n\n  /**\n   * Always `\"true\"`.\n   */\n  readonly isConvexValidator: true;\n\n  constructor({ isOptional }: { isOptional: IsOptional }) {\n    this.isOptional = isOptional;\n    this.isConvexValidator = true;\n  }\n  /** @deprecated - use isOptional instead */\n  get optional(): boolean {\n    return this.isOptional === \"optional\" ? true : false;\n  }\n  /** @internal */\n  abstract get json(): ValidatorJSON;\n  /** @internal */\n  abstract asOptional(): Validator<Type | undefined, \"optional\", FieldPaths>;\n}\n\n/**\n * The type of the `v.id(tableName)` validator.\n */\nexport class VId<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The name of the table that the validated IDs must belong to.\n   */\n  readonly tableName: TableNameFromType<Type>;\n\n  /**\n   * The kind of validator, `\"id\"`.\n   */\n  readonly kind = \"id\" as const;\n\n  /**\n   * Usually you'd use `v.id(tableName)` instead.\n   */\n  constructor({\n    isOptional,\n    tableName,\n  }: {\n    isOptional: IsOptional;\n    tableName: TableNameFromType<Type>;\n  }) {\n    super({ isOptional });\n    if (typeof tableName !== \"string\") {\n      throw new Error(\"v.id(tableName) requires a string\");\n    }\n    this.tableName = tableName;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: \"id\", tableName: this.tableName };\n  }\n  /** @internal */\n  asOptional() {\n    return new VId<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n      tableName: this.tableName,\n    });\n  }\n}\n\n/**\n * The type of the `v.float64()` validator.\n */\nexport class VFloat64<\n  Type = number,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"float64\"`.\n   */\n  readonly kind = \"float64\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    // Server expects the old name `number` string instead of `float64`.\n    return { type: \"number\" };\n  }\n  /** @internal */\n  asOptional() {\n    return new VFloat64<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.int64()` validator.\n */\nexport class VInt64<\n  Type = bigint,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"int64\"`.\n   */\n  readonly kind = \"int64\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    // Server expects the old name `bigint`.\n    return { type: \"bigint\" };\n  }\n  /** @internal */\n  asOptional() {\n    return new VInt64<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.boolean()` validator.\n */\nexport class VBoolean<\n  Type = boolean,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"boolean\"`.\n   */\n  readonly kind = \"boolean\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VBoolean<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.bytes()` validator.\n */\nexport class VBytes<\n  Type = ArrayBuffer,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"bytes\"`.\n   */\n  readonly kind = \"bytes\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VBytes<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.string()` validator.\n */\nexport class VString<\n  Type = string,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"string\"`.\n   */\n  readonly kind = \"string\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VString<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.null()` validator.\n */\nexport class VNull<\n  Type = null,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"null\"`.\n   */\n  readonly kind = \"null\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VNull<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.any()` validator.\n */\nexport class VAny<\n  Type = any,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The kind of validator, `\"any\"`.\n   */\n  readonly kind = \"any\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VAny<Type | undefined, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.object()` validator.\n */\nexport class VObject<\n  Type,\n  Fields extends Record<string, GenericValidator>,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = {\n    [Property in keyof Fields]:\n      | JoinFieldPaths<Property & string, Fields[Property][\"fieldPaths\"]>\n      | Property;\n  }[keyof Fields] &\n    string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * An object with the validator for each property.\n   */\n  readonly fields: Fields;\n\n  /**\n   * The kind of validator, `\"object\"`.\n   */\n  readonly kind = \"object\" as const;\n\n  /**\n   * Usually you'd use `v.object({ ... })` instead.\n   */\n  constructor({\n    isOptional,\n    fields,\n  }: {\n    isOptional: IsOptional;\n    fields: Fields;\n  }) {\n    super({ isOptional });\n    globalThis.Object.values(fields).forEach((v) => {\n      if (!v.isConvexValidator) {\n        throw new Error(\"v.object() entries must be valiators\");\n      }\n    });\n    this.fields = fields;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: globalThis.Object.fromEntries(\n        globalThis.Object.entries(this.fields).map(([k, v]) => [\n          k,\n          {\n            fieldType: v.json,\n            optional: v.isOptional === \"optional\" ? true : false,\n          },\n        ]),\n      ),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VObject<Type | undefined, Fields, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n      fields: this.fields,\n    });\n  }\n}\n\n/**\n * The type of the `v.literal()` validator.\n */\nexport class VLiteral<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The value that the validated values must be equal to.\n   */\n  readonly value: Type;\n\n  /**\n   * The kind of validator, `\"literal\"`.\n   */\n  readonly kind = \"literal\" as const;\n\n  /**\n   * Usually you'd use `v.literal(value)` instead.\n   */\n  constructor({ isOptional, value }: { isOptional: IsOptional; value: Type }) {\n    super({ isOptional });\n    if (\n      typeof value !== \"string\" &&\n      typeof value !== \"boolean\" &&\n      typeof value !== \"number\" &&\n      typeof value !== \"bigint\"\n    ) {\n      throw new Error(\"v.literal(value) must be a string, number, or boolean\");\n    }\n    this.value = value;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: convexToJson(this.value as string | boolean | number | bigint),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VLiteral<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n      value: this.value,\n    });\n  }\n}\n\n/**\n * The type of the `v.array()` validator.\n */\nexport class VArray<\n  Type,\n  Element extends Validator<any, \"required\", any>,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The validator for the elements of the array.\n   */\n  readonly element: Element;\n\n  /**\n   * The kind of validator, `\"array\"`.\n   */\n  readonly kind = \"array\" as const;\n\n  /**\n   * Usually you'd use `v.array(element)` instead.\n   */\n  constructor({\n    isOptional,\n    element,\n  }: {\n    isOptional: IsOptional;\n    element: Element;\n  }) {\n    super({ isOptional });\n    this.element = element;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: this.element.json,\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VArray<Type | undefined, Element, \"optional\">({\n      isOptional: \"optional\",\n      element: this.element,\n    });\n  }\n}\n\n/**\n * The type of the `v.record()` validator.\n */\nexport class VRecord<\n  Type,\n  Key extends Validator<string, \"required\", any>,\n  Value extends Validator<any, \"required\", any>,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The validator for the keys of the record.\n   */\n  readonly key: Key;\n\n  /**\n   * The validator for the values of the record.\n   */\n  readonly value: Value;\n\n  /**\n   * The kind of validator, `\"record\"`.\n   */\n  readonly kind = \"record\" as const;\n\n  /**\n   * Usually you'd use `v.record(key, value)` instead.\n   */\n  constructor({\n    isOptional,\n    key,\n    value,\n  }: {\n    isOptional: IsOptional;\n    key: Key;\n    value: Value;\n  }) {\n    super({ isOptional });\n    if ((key.isOptional as OptionalProperty) === \"optional\") {\n      throw new Error(\"Record validator cannot have optional keys\");\n    }\n    if ((value.isOptional as OptionalProperty) === \"optional\") {\n      throw new Error(\"Record validator cannot have optional values\");\n    }\n    if (!key.isConvexValidator || !value.isConvexValidator) {\n      throw new Error(\"Key and value of v.record() but be validators\");\n    }\n    this.key = key;\n    this.value = value;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      // This cast is needed because TypeScript thinks the key type is too wide\n      keys: this.key.json as RecordKeyValidatorJSON,\n      values: {\n        fieldType: this.value.json,\n        optional: false,\n      },\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VRecord<Type | undefined, Key, Value, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n      key: this.key,\n      value: this.value,\n    });\n  }\n}\n\n/**\n * The type of the `v.union()` validator.\n */\nexport class VUnion<\n  Type,\n  T extends Validator<any, \"required\", any>[],\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = T[number][\"fieldPaths\"],\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The array of validators, one of which must match the value.\n   */\n  readonly members: T;\n\n  /**\n   * The kind of validator, `\"union\"`.\n   */\n  readonly kind = \"union\" as const;\n\n  /**\n   * Usually you'd use `v.union(...members)` instead.\n   */\n  constructor({ isOptional, members }: { isOptional: IsOptional; members: T }) {\n    super({ isOptional });\n    members.forEach((member) => {\n      if (!member.isConvexValidator) {\n        throw new Error(\"All members of v.union() must be validators\");\n      }\n    });\n    this.members = members;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: this.members.map((v) => v.json),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VUnion<Type | undefined, T, \"optional\">({\n      isOptional: \"optional\",\n      members: this.members,\n    });\n  }\n}\n\n// prettier-ignore\nexport type VOptional<T extends Validator<any, OptionalProperty, any>> =\n  T extends VId<infer Type, OptionalProperty> ? VId<Type | undefined, \"optional\">\n  : T extends VString<infer Type, OptionalProperty>\n    ? VString<Type | undefined, \"optional\">\n  : T extends VFloat64<infer Type, OptionalProperty>\n    ? VFloat64<Type | undefined, \"optional\">\n  : T extends VInt64<infer Type, OptionalProperty>\n    ? VInt64<Type | undefined, \"optional\">\n  : T extends VBoolean<infer Type, OptionalProperty>\n    ? VBoolean<Type | undefined, \"optional\">\n  : T extends VNull<infer Type, OptionalProperty>\n    ? VNull<Type | undefined, \"optional\">\n  : T extends VAny<infer Type, OptionalProperty>\n    ? VAny<Type | undefined, \"optional\">\n  : T extends VLiteral<infer Type, OptionalProperty>\n    ? VLiteral<Type | undefined, \"optional\">\n  : T extends VBytes<infer Type, OptionalProperty>\n    ? VBytes<Type | undefined, \"optional\">\n  : T extends VObject< infer Type, infer Fields, OptionalProperty, infer FieldPaths>\n    ? VObject<Type | undefined, Fields, \"optional\", FieldPaths>\n  : T extends VArray<infer Type, infer Element, OptionalProperty>\n    ? VArray<Type | undefined, Element, \"optional\">\n  : T extends VRecord< infer Type, infer Key, infer Value, OptionalProperty, infer FieldPaths>\n    ? VRecord<Type | undefined, Key, Value, \"optional\", FieldPaths>\n  : T extends VUnion<infer Type, infer Members, OptionalProperty, infer FieldPaths>\n    ? VUnion<Type | undefined, Members, \"optional\", FieldPaths>\n  : never\n\n/**\n * Type representing whether a property in an object is optional or required.\n *\n * @public\n */\nexport type OptionalProperty = \"optional\" | \"required\";\n\n/**\n * A validator for a Convex value.\n *\n * This should be constructed using the validator builder, {@link v}.\n *\n * A validator encapsulates:\n * - The TypeScript type of this value.\n * - Whether this field should be optional if it's included in an object.\n * - The TypeScript type for the set of index field paths that can be used to\n * build indexes on this value.\n * - A JSON representation of the validator.\n *\n * Specific types of validators contain additional information: for example\n * an `ArrayValidator` contains an `element` property with the validator\n * used to validate each element of the list. Use the shared 'kind' property\n * to identity the type of validator.\n *\n * More validators can be added in future releases so an exhaustive\n * switch statement on validator `kind` should be expected to break\n * in future releases of Convex.\n *\n * @public\n */\nexport type Validator<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = never,\n> =\n  | VId<Type, IsOptional>\n  | VString<Type, IsOptional>\n  | VFloat64<Type, IsOptional>\n  | VInt64<Type, IsOptional>\n  | VBoolean<Type, IsOptional>\n  | VNull<Type, IsOptional>\n  | VAny<Type, IsOptional>\n  | VLiteral<Type, IsOptional>\n  | VBytes<Type, IsOptional>\n  | VObject<\n      Type,\n      Record<string, Validator<any, OptionalProperty, any>>,\n      IsOptional,\n      FieldPaths\n    >\n  | VArray<Type, Validator<any, \"required\", any>, IsOptional>\n  | VRecord<\n      Type,\n      Validator<string, \"required\", any>,\n      Validator<any, \"required\", any>,\n      IsOptional,\n      FieldPaths\n    >\n  | VUnion<Type, Validator<any, \"required\", any>[], IsOptional, FieldPaths>;\n\n/**\n * Join together two index field paths.\n *\n * This is used within the validator builder, {@link v}.\n * @public\n */\nexport type JoinFieldPaths<\n  Start extends string,\n  End extends string,\n> = `${Start}.${End}`;\n\nexport type ObjectFieldType = { fieldType: ValidatorJSON; optional: boolean };\n\nexport type ValidatorJSON =\n  | { type: \"null\" }\n  | { type: \"number\" }\n  | { type: \"bigint\" }\n  | { type: \"boolean\" }\n  | { type: \"string\" }\n  | { type: \"bytes\" }\n  | { type: \"any\" }\n  | { type: \"literal\"; value: JSONValue }\n  | { type: \"id\"; tableName: string }\n  | { type: \"array\"; value: ValidatorJSON }\n  | {\n      type: \"record\";\n      keys: RecordKeyValidatorJSON;\n      values: RecordValueValidatorJSON;\n    }\n  | { type: \"object\"; value: Record<string, ObjectFieldType> }\n  | { type: \"union\"; value: ValidatorJSON[] };\n\nexport type RecordKeyValidatorJSON =\n  | { type: \"string\" }\n  | { type: \"id\"; tableName: string }\n  | { type: \"union\"; value: RecordKeyValidatorJSON[] };\n\nexport type RecordValueValidatorJSON = ObjectFieldType & { optional: false };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAoB,oBAAoB;;;;;;;;;;;AASxC,MAAe,cAIb;IAsBA,YAAY,EAAE,UAAA,CAAW,CAAA,CAA+B;QAjBxD;;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS;QAGP,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,iBAAA,GAAoB;IAC3B;IAAA,yCAAA,GAEA,IAAI,WAAoB;QACtB,OAAO,IAAA,CAAK,UAAA,KAAe,aAAa,OAAO;IACjD;AAKF;AAKO,MAAM,YAGH,cAAgC;IAAA;;GAAA,GAcxC,YAAY,EACV,UAAA,EACA,SAAA,EACF,CAGG;QACD,KAAA,CAAM;YAAE;QAAW,CAAC;QAjBtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAad,IAAI,OAAO,cAAc,UAAU;YACjC,MAAM,IAAI,MAAM,mCAAmC;QACrD;QACA,IAAA,CAAK,SAAA,GAAY;IACnB;IAAA,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM;YAAM,WAAW,IAAA,CAAK,SAAA;QAAU;IACjD;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,IAAkC;YAC3C,YAAY;YACZ,WAAW,IAAA,CAAK,SAAA;QAClB,CAAC;IACH;AACF;AAKO,MAAM,iBAGH,cAAgC;IAHnC,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;IAAA,cAAA,GAGhB,IAAI,OAAsB;QAExB,OAAO;YAAE,MAAM;QAAS;IAC1B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,SAAuC;YAChD,YAAY;QACd,CAAC;IACH;AACF;AAKO,MAAM,eAGH,cAAgC;IAHnC,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;IAAA,cAAA,GAGhB,IAAI,OAAsB;QAExB,OAAO;YAAE,MAAM;QAAS;IAC1B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,OAAqC;YAAE,YAAY;QAAW,CAAC;IAC5E;AACF;AAKO,MAAM,iBAGH,cAAgC;IAHnC,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;IAAA,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM,IAAA,CAAK,IAAA;QAAK;IAC3B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,SAAuC;YAChD,YAAY;QACd,CAAC;IACH;AACF;AAKO,MAAM,eAGH,cAAgC;IAHnC,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;IAAA,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM,IAAA,CAAK,IAAA;QAAK;IAC3B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,OAAqC;YAAE,YAAY;QAAW,CAAC;IAC5E;AACF;AAKO,MAAM,gBAGH,cAAgC;IAHnC,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;IAAA,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM,IAAA,CAAK,IAAA;QAAK;IAC3B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,QAAsC;YAC/C,YAAY;QACd,CAAC;IACH;AACF;AAKO,MAAM,cAGH,cAAgC;IAHnC,aAAA;QAAA,KAAA,IAAA;QAOL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;IAAA,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YAAE,MAAM,IAAA,CAAK,IAAA;QAAK;IAC3B;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,MAAoC;YAAE,YAAY;QAAW,CAAC;IAC3E;AACF;AAKO,MAAM,aAIH,cAA4C;IAJ/C,aAAA;QAAA,KAAA,IAAA;QAQL;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;IAAA;IAAA,cAAA,GAGhB,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;QACb;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,KAA+C;YACxD,YAAY;QACd,CAAC;IACH;AACF;AAKO,MAAM,gBAUH,cAA4C;IAAA;;GAAA,GAcpD,YAAY,EACV,UAAA,EACA,MAAA,EACF,CAGG;QACD,KAAA,CAAM;YAAE;QAAW,CAAC;QAjBtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAad,WAAW,MAAA,CAAO,MAAA,CAAO,MAAM,EAAE,OAAA,CAAQ,CAAC,MAAM;YAC9C,IAAI,CAAC,EAAE,iBAAA,EAAmB;gBACxB,MAAM,IAAI,MAAM,sCAAsC;YACxD;QACF,CAAC;QACD,IAAA,CAAK,MAAA,GAAS;IAChB;IAAA,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YACX,OAAO,WAAW,MAAA,CAAO,WAAA,CACvB,WAAW,MAAA,CAAO,OAAA,CAAQ,IAAA,CAAK,MAAM,EAAE,GAAA,CAAI,CAAC,CAAC,GAAG,CAAC,CAAA,GAAM;oBACrD;oBACA;wBACE,WAAW,EAAE,IAAA;wBACb,UAAU,EAAE,UAAA,KAAe,aAAa,OAAO;oBACjD;iBACD;QAEL;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,QAA0D;YACnE,YAAY;YACZ,QAAQ,IAAA,CAAK,MAAA;QACf,CAAC;IACH;AACF;AAKO,MAAM,iBAGH,cAAgC;IAAA;;GAAA,GAcxC,YAAY,EAAE,UAAA,EAAY,KAAA,CAAM,CAAA,CAA4C;QAC1E,KAAA,CAAM;YAAE;QAAW,CAAC;QAXtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAOd,IACE,OAAO,UAAU,YACjB,OAAO,UAAU,aACjB,OAAO,UAAU,YACjB,OAAO,UAAU,UACjB;YACA,MAAM,IAAI,MAAM,uDAAuD;QACzE;QACA,IAAA,CAAK,KAAA,GAAQ;IACf;IAAA,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YACX,WAAO,2NAAA,EAAa,IAAA,CAAK,KAA2C;QACtE;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,SAAuC;YAChD,YAAY;YACZ,OAAO,IAAA,CAAK,KAAA;QACd,CAAC;IACH;AACF;AAKO,MAAM,eAIH,cAAgC;IAAA;;GAAA,GAcxC,YAAY,EACV,UAAA,EACA,OAAA,EACF,CAGG;QACD,KAAA,CAAM;YAAE;QAAW,CAAC;QAjBtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAad,IAAA,CAAK,OAAA,GAAU;IACjB;IAAA,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YACX,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA;QACtB;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,OAA8C;YACvD,YAAY;YACZ,SAAS,IAAA,CAAK,OAAA;QAChB,CAAC;IACH;AACF;AAKO,MAAM,gBAMH,cAA4C;IAAA;;GAAA,GAmBpD,YAAY,EACV,UAAA,EACA,GAAA,EACA,KAAA,EACF,CAIG;QACD,KAAA,CAAM;YAAE;QAAW,CAAC;QAxBtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAed,IAAK,IAAI,UAAA,KAAoC,YAAY;YACvD,MAAM,IAAI,MAAM,4CAA4C;QAC9D;QACA,IAAK,MAAM,UAAA,KAAoC,YAAY;YACzD,MAAM,IAAI,MAAM,8CAA8C;QAChE;QACA,IAAI,CAAC,IAAI,iBAAA,IAAqB,CAAC,MAAM,iBAAA,EAAmB;YACtD,MAAM,IAAI,MAAM,+CAA+C;QACjE;QACA,IAAA,CAAK,GAAA,GAAM;QACX,IAAA,CAAK,KAAA,GAAQ;IACf;IAAA,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YAAA,yEAAA;YAEX,MAAM,IAAA,CAAK,GAAA,CAAI,IAAA;YACf,QAAQ;gBACN,WAAW,IAAA,CAAK,KAAA,CAAM,IAAA;gBACtB,UAAU;YACZ;QACF;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,QAA8D;YACvE,YAAY;YACZ,KAAK,IAAA,CAAK,GAAA;YACV,OAAO,IAAA,CAAK,KAAA;QACd,CAAC;IACH;AACF;AAKO,MAAM,eAKH,cAA4C;IAAA;;GAAA,GAcpD,YAAY,EAAE,UAAA,EAAY,OAAA,CAAQ,CAAA,CAA2C;QAC3E,KAAA,CAAM;YAAE;QAAW,CAAC;QAXtB;;KAAA,GAAA,cAAA,IAAA,EAAS;QAKT;;KAAA,GAAA,cAAA,IAAA,EAAS,QAAO;QAOd,QAAQ,OAAA,CAAQ,CAAC,WAAW;YAC1B,IAAI,CAAC,OAAO,iBAAA,EAAmB;gBAC7B,MAAM,IAAI,MAAM,6CAA6C;YAC/D;QACF,CAAC;QACD,IAAA,CAAK,OAAA,GAAU;IACjB;IAAA,cAAA,GAEA,IAAI,OAAsB;QACxB,OAAO;YACL,MAAM,IAAA,CAAK,IAAA;YACX,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,IAAM,EAAE,IAAI;QACvC;IACF;IAAA,cAAA,GAEA,aAAa;QACX,OAAO,IAAI,OAAwC;YACjD,YAAY;YACZ,SAAS,IAAA,CAAK,OAAA;QAChB,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 845, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/values/validator.ts"], "sourcesContent": ["import { Expand } from \"../type_utils.js\";\nimport { GenericId } from \"./index.js\";\nimport {\n  OptionalProperty,\n  VAny,\n  VArray,\n  VBoolean,\n  VBytes,\n  VFloat64,\n  VId,\n  VInt64,\n  VLiteral,\n  VNull,\n  VObject,\n  VOptional,\n  VRecord,\n  VString,\n  VUnion,\n  Validator,\n} from \"./validators.js\";\n\n/**\n * The type that all validators must extend.\n *\n * @public\n */\nexport type GenericValidator = Validator<any, any, any>;\n\nexport function isValidator(v: any): v is GenericValidator {\n  return !!v.isConvexValidator;\n}\n\n/**\n * Coerce an object with validators as properties to a validator.\n * If a validator is passed, return it.\n *\n * @public\n */\nexport function asObjectValidator<\n  V extends Validator<any, any, any> | PropertyValidators,\n>(\n  obj: V,\n): V extends Validator<any, any, any>\n  ? V\n  : V extends PropertyValidators\n    ? Validator<ObjectType<V>>\n    : never {\n  if (isValidator(obj)) {\n    return obj as any;\n  } else {\n    return v.object(obj as PropertyValidators) as any;\n  }\n}\n\n/**\n * Coerce an object with validators as properties to a validator.\n * If a validator is passed, return it.\n *\n * @public\n */\nexport type AsObjectValidator<\n  V extends Validator<any, any, any> | PropertyValidators,\n> =\n  V extends Validator<any, any, any>\n    ? V\n    : V extends PropertyValidators\n      ? Validator<ObjectType<V>>\n      : never;\n\n/**\n * The validator builder.\n *\n * This builder allows you to build validators for Convex values.\n *\n * Validators can be used in [schema definitions](https://docs.convex.dev/database/schemas)\n * and as input validators for Convex functions.\n *\n * @public\n */\nexport const v = {\n  /**\n   * Validates that the value corresponds to an ID of a document in given table.\n   * @param tableName The name of the table.\n   */\n  id: <TableName extends string>(tableName: TableName) => {\n    return new VId<GenericId<TableName>>({\n      isOptional: \"required\",\n      tableName,\n    });\n  },\n\n  /**\n   * Validates that the value is of type Null.\n   */\n  null: () => {\n    return new VNull({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Float64 (Number in JS).\n   *\n   * Alias for `v.float64()`\n   */\n  number: () => {\n    return new VFloat64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Float64 (Number in JS).\n   */\n  float64: () => {\n    return new VFloat64({ isOptional: \"required\" });\n  },\n\n  /**\n   * @deprecated Use `v.int64()` instead\n   */\n  bigint: () => {\n    return new VInt64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Int64 (BigInt in JS).\n   */\n  int64: () => {\n    return new VInt64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of type Boolean.\n   */\n  boolean: () => {\n    return new VBoolean({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of type String.\n   */\n  string: () => {\n    return new VString({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Bytes (constructed in JS via `ArrayBuffer`).\n   */\n  bytes: () => {\n    return new VBytes({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is equal to the given literal value.\n   * @param literal The literal value to compare against.\n   */\n  literal: <T extends string | number | bigint | boolean>(literal: T) => {\n    return new VLiteral<T>({ isOptional: \"required\", value: literal });\n  },\n\n  /**\n   * Validates that the value is an Array of the given element type.\n   * @param element The validator for the elements of the array.\n   */\n  array: <T extends Validator<any, \"required\", any>>(element: T) => {\n    return new VArray<T[\"type\"][], T>({ isOptional: \"required\", element });\n  },\n\n  /**\n   * Validates that the value is an Object with the given properties.\n   * @param fields An object specifying the validator for each property.\n   */\n  object: <T extends PropertyValidators>(fields: T) => {\n    return new VObject<ObjectType<T>, T>({ isOptional: \"required\", fields });\n  },\n\n  /**\n   * Validates that the value is a Record with keys and values that match the given types.\n   * @param keys The validator for the keys of the record. This cannot contain string literals.\n   * @param values The validator for the values of the record.\n   */\n  record: <\n    Key extends Validator<string, \"required\", any>,\n    Value extends Validator<any, \"required\", any>,\n  >(\n    keys: Key,\n    values: Value,\n  ) => {\n    return new VRecord<Record<Infer<Key>, Value[\"type\"]>, Key, Value>({\n      isOptional: \"required\",\n      key: keys,\n      value: values,\n    });\n  },\n\n  /**\n   * Validates that the value matches one of the given validators.\n   * @param members The validators to match against.\n   */\n  union: <T extends Validator<any, \"required\", any>[]>(...members: T) => {\n    return new VUnion<T[number][\"type\"], T>({\n      isOptional: \"required\",\n      members,\n    });\n  },\n\n  /**\n   * Does not validate the value.\n   */\n  any: () => {\n    return new VAny({ isOptional: \"required\" });\n  },\n\n  /**\n   * Allows not specifying a value for a property in an Object.\n   * @param value The property value validator to make optional.\n   *\n   * ```typescript\n   * const objectWithOptionalFields = v.object({\n   *   requiredField: v.string(),\n   *   optionalField: v.optional(v.string()),\n   * });\n   * ```\n   */\n  optional: <T extends GenericValidator>(value: T) => {\n    return value.asOptional() as VOptional<T>;\n  },\n};\n\n/**\n * Validators for each property of an object.\n *\n * This is represented as an object mapping the property name to its\n * {@link Validator}.\n *\n * @public\n */\nexport type PropertyValidators = Record<\n  string,\n  Validator<any, OptionalProperty, any>\n>;\n\n/**\n * Compute the type of an object from {@link PropertyValidators}.\n *\n * @public\n */\nexport type ObjectType<Fields extends PropertyValidators> = Expand<\n  // Map each key to the corresponding property validator's type making\n  // the optional ones optional.\n  {\n    // This `Exclude<..., undefined>` does nothing unless\n    // the tsconfig.json option `\"exactOptionalPropertyTypes\": true,`\n    // is used. When it is it results in a more accurate type.\n    // When it is not the `Exclude` removes `undefined` but it is\n    // added again by the optional property.\n    [Property in OptionalKeys<Fields>]?: Exclude<\n      Infer<Fields[Property]>,\n      undefined\n    >;\n  } & {\n    [Property in RequiredKeys<Fields>]: Infer<Fields[Property]>;\n  }\n>;\n\ntype OptionalKeys<PropertyValidators extends Record<string, GenericValidator>> =\n  {\n    [Property in keyof PropertyValidators]: PropertyValidators[Property][\"isOptional\"] extends \"optional\"\n      ? Property\n      : never;\n  }[keyof PropertyValidators];\n\ntype RequiredKeys<PropertyValidators extends Record<string, GenericValidator>> =\n  Exclude<keyof PropertyValidators, OptionalKeys<PropertyValidators>>;\n\n/**\n * Extract a TypeScript type from a validator.\n *\n * Example usage:\n * ```ts\n * const objectSchema = v.object({\n *   property: v.string(),\n * });\n * type MyObject = Infer<typeof objectSchema>; // { property: string }\n * ```\n * @typeParam V - The type of a {@link Validator} constructed with {@link v}.\n *\n * @public\n */\nexport type Infer<T extends Validator<any, OptionalProperty, any>> = T[\"type\"];\n"], "names": ["v"], "mappings": ";;;;;;;;AAEA;;;AA0BO,SAAS,YAAYA,EAAAA,EAA+B;IACzD,OAAO,CAAC,CAACA,GAAE,iBAAA;AACb;AAQO,SAAS,kBAGd,GAAA,EAKU;IACV,IAAI,YAAY,GAAG,GAAG;QACpB,OAAO;IACT,OAAO;QACL,OAAO,EAAE,MAAA,CAAO,GAAyB;IAC3C;AACF;AA2BO,MAAM,IAAI;IAAA;;;GAAA,GAKf,IAAI,CAA2B,cAAyB;QACtD,OAAO,IAAI,uNAAA,CAA0B;YACnC,YAAY;YACZ;QACF,CAAC;IACH;IAAA;;GAAA,GAKA,MAAM,MAAM;QACV,OAAO,IAAI,yNAAA,CAAM;YAAE,YAAY;QAAW,CAAC;IAC7C;IAAA;;;;GAAA,GAOA,QAAQ,MAAM;QACZ,OAAO,IAAI,4NAAA,CAAS;YAAE,YAAY;QAAW,CAAC;IAChD;IAAA;;GAAA,GAKA,SAAS,MAAM;QACb,OAAO,IAAI,4NAAA,CAAS;YAAE,YAAY;QAAW,CAAC;IAChD;IAAA;;GAAA,GAKA,QAAQ,MAAM;QACZ,OAAO,IAAI,0NAAA,CAAO;YAAE,YAAY;QAAW,CAAC;IAC9C;IAAA;;GAAA,GAKA,OAAO,MAAM;QACX,OAAO,IAAI,0NAAA,CAAO;YAAE,YAAY;QAAW,CAAC;IAC9C;IAAA;;GAAA,GAKA,SAAS,MAAM;QACb,OAAO,IAAI,4NAAA,CAAS;YAAE,YAAY;QAAW,CAAC;IAChD;IAAA;;GAAA,GAKA,QAAQ,MAAM;QACZ,OAAO,IAAI,2NAAA,CAAQ;YAAE,YAAY;QAAW,CAAC;IAC/C;IAAA;;GAAA,GAKA,OAAO,MAAM;QACX,OAAO,IAAI,0NAAA,CAAO;YAAE,YAAY;QAAW,CAAC;IAC9C;IAAA;;;GAAA,GAMA,SAAS,CAA+C,YAAe;QACrE,OAAO,IAAI,4NAAA,CAAY;YAAE,YAAY;YAAY,OAAO;QAAQ,CAAC;IACnE;IAAA;;;GAAA,GAMA,OAAO,CAA4C,YAAe;QAChE,OAAO,IAAI,0NAAA,CAAuB;YAAE,YAAY;YAAY;QAAQ,CAAC;IACvE;IAAA;;;GAAA,GAMA,QAAQ,CAA+B,WAAc;QACnD,OAAO,IAAI,2NAAA,CAA0B;YAAE,YAAY;YAAY;QAAO,CAAC;IACzE;IAAA;;;;GAAA,GAOA,QAAQ,CAIN,MACA,WACG;QACH,OAAO,IAAI,2NAAA,CAAuD;YAChE,YAAY;YACZ,KAAK;YACL,OAAO;QACT,CAAC;IACH;IAAA;;;GAAA,GAMA,OAAO,CAAA,GAAiD,YAAe;QACrE,OAAO,IAAI,0NAAA,CAA6B;YACtC,YAAY;YACZ;QACF,CAAC;IACH;IAAA;;GAAA,GAKA,KAAK,MAAM;QACT,OAAO,IAAI,wNAAA,CAAK;YAAE,YAAY;QAAW,CAAC;IAC5C;IAAA;;;;;;;;;;GAAA,GAaA,UAAU,CAA6B,UAAa;QAClD,OAAO,MAAM,UAAA,CAAW;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/values/errors.ts"], "sourcesContent": ["import { Value, stringifyValueForError } from \"./value.js\";\n\nconst IDENTIFYING_FIELD = Symbol.for(\"ConvexError\");\n\nexport class ConvexError<TData extends Value> extends Error {\n  name = \"ConvexError\";\n  data: TData;\n  [IDENTIFYING_FIELD] = true;\n\n  constructor(data: TData) {\n    super(typeof data === \"string\" ? data : stringifyValueForError(data));\n    this.data = data;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAgB,8BAA8B;;;;;;;;;;AAA9C,IAAA,IAAA;;AAEA,MAAM,oBAAoB,OAAO,GAAA,CAAI,aAAa;AAE3C,MAAM,oBAAA,CAAyC,KAAA,OAGnD,KAAA,mBAHmD,EAAA,EAAM;IAK1D,YAAY,IAAA,CAAa;QACvB,KAAA,CAAM,OAAO,SAAS,WAAW,WAAO,qOAAA,EAAuB,IAAI,CAAC;QALtE,cAAA,IAAA,EAAA,QAAO;QACP,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAC,IAAqB;QAIpB,IAAA,CAAK,IAAA,GAAO;IACd;AACF", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/values/compare_utf8.ts"], "sourcesContent": ["/**\n * Taken from https://github.com/rocicorp/compare-utf8/blob/main/LICENSE\n * (Apache Version 2.0, January 2004)\n */\n\n/**\n * This is copied here instead of added as a dependency to avoid bundling issues.\n */\n\n/**\n * Compares two JavaScript strings as if they were UTF-8 encoded byte arrays.\n * @param {string} a\n * @param {string} b\n * @returns {number}\n */\nexport function compareUTF8(a: string, b: string): number {\n  const aLength = a.length;\n  const bLength = b.length;\n  const length = Math.min(aLength, bLength);\n  for (let i = 0; i < length; ) {\n    const aCodePoint = a.codePointAt(i)!;\n    const bCodePoint = b.codePointAt(i)!;\n    if (aCodePoint !== bCodePoint) {\n      // Code points below 0x80 are represented the same way in UTF-8 as in\n      // UTF-16.\n      if (aCodePoint < 0x80 && bCodePoint < 0x80) {\n        return aCodePoint - bCodePoint;\n      }\n\n      // get the UTF-8 bytes for the code points\n      const aLength = utf8Bytes(aCodePoint, aBytes);\n      const bLength = utf8Bytes(bCodePoint, bBytes);\n      return compareArrays(aBytes, aLength, bBytes, bLength);\n    }\n\n    i += utf16LengthForCodePoint(aCodePoint);\n  }\n\n  return aLength - bLength;\n}\n\n/**\n * @param {number[]} a\n * @param {number} aLength\n * @param {number[]} b\n * @param {number} bLength\n * @returns {number}\n */\nfunction compareArrays(\n  a: number[],\n  aLength: number,\n  b: number[],\n  bLength: number,\n) {\n  const length = Math.min(aLength, bLength);\n  for (let i = 0; i < length; i++) {\n    const aValue = a[i];\n    const bValue = b[i];\n    if (aValue !== bValue) {\n      return aValue - bValue;\n    }\n  }\n  return aLength - bLength;\n}\n\n/**\n * @param {number} aCodePoint\n * @returns {number}\n */\nexport function utf16LengthForCodePoint(aCodePoint: number) {\n  return aCodePoint > 0xffff ? 2 : 1;\n}\n\n// 2 preallocated arrays for utf8Bytes.\nconst arr = () => Array.from({ length: 4 }, () => 0);\nconst aBytes = arr();\nconst bBytes = arr();\n\n/**\n * @param {number} codePoint\n * @param {number[]} bytes\n * @returns {number}\n */\nfunction utf8Bytes(codePoint: number, bytes: number[]) {\n  if (codePoint < 0x80) {\n    bytes[0] = codePoint;\n    return 1;\n  }\n\n  let count;\n  let offset;\n\n  if (codePoint <= 0x07ff) {\n    count = 1;\n    offset = 0xc0;\n  } else if (codePoint <= 0xffff) {\n    count = 2;\n    offset = 0xe0;\n  } else if (codePoint <= 0x10ffff) {\n    count = 3;\n    offset = 0xf0;\n  } else {\n    throw new Error(\"Invalid code point\");\n  }\n\n  bytes[0] = (codePoint >> (6 * count)) + offset;\n  let i = 1;\n  for (; count > 0; count--) {\n    const temp = codePoint >> (6 * (count - 1));\n    bytes[i++] = 0x80 | (temp & 0x3f);\n  }\n  return i;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function greaterThan(a: string, b: string) {\n  return compareUTF8(a, b) > 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function greaterThanEq(a: string, b: string) {\n  return compareUTF8(a, b) >= 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function lessThan(a: string, b: string) {\n  return compareUTF8(a, b) < 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function lessThanEq(a: string, b: string) {\n  return compareUTF8(a, b) <= 0;\n}\n"], "names": ["a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;AAeO,SAAS,YAAY,CAAA,EAAW,CAAA,EAAmB;IACxD,MAAM,UAAU,EAAE,MAAA;IAClB,MAAM,UAAU,EAAE,MAAA;IAClB,MAAM,SAAS,KAAK,GAAA,CAAI,SAAS,OAAO;IACxC,IAAA,IAAS,IAAI,GAAG,IAAI,QAAU;QAC5B,MAAM,aAAa,EAAE,WAAA,CAAY,CAAC;QAClC,MAAM,aAAa,EAAE,WAAA,CAAY,CAAC;QAClC,IAAI,eAAe,YAAY;YAG7B,IAAI,aAAa,OAAQ,aAAa,KAAM;gBAC1C,OAAO,aAAa;YACtB;YAGA,MAAMA,WAAU,UAAU,YAAY,MAAM;YAC5C,MAAMC,WAAU,UAAU,YAAY,MAAM;YAC5C,OAAO,cAAc,QAAQD,UAAS,QAAQC,QAAO;QACvD;QAEA,KAAK,wBAAwB,UAAU;IACzC;IAEA,OAAO,UAAU;AACnB;AASA,SAAS,cACP,CAAA,EACA,OAAA,EACA,CAAA,EACA,OAAA,EACA;IACA,MAAM,SAAS,KAAK,GAAA,CAAI,SAAS,OAAO;IACxC,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,SAAS,CAAA,CAAE,CAAC,CAAA;QAClB,MAAM,SAAS,CAAA,CAAE,CAAC,CAAA;QAClB,IAAI,WAAW,QAAQ;YACrB,OAAO,SAAS;QAClB;IACF;IACA,OAAO,UAAU;AACnB;AAMO,SAAS,wBAAwB,UAAA,EAAoB;IAC1D,OAAO,aAAa,QAAS,IAAI;AACnC;AAGA,MAAM,MAAM,IAAM,MAAM,IAAA,CAAK;QAAE,QAAQ;IAAE,GAAG,IAAM,CAAC;AACnD,MAAM,SAAS,IAAI;AACnB,MAAM,SAAS,IAAI;AAOnB,SAAS,UAAU,SAAA,EAAmB,KAAA,EAAiB;IACrD,IAAI,YAAY,KAAM;QACpB,KAAA,CAAM,CAAC,CAAA,GAAI;QACX,OAAO;IACT;IAEA,IAAI;IACJ,IAAI;IAEJ,IAAI,aAAa,MAAQ;QACvB,QAAQ;QACR,SAAS;IACX,OAAA,IAAW,aAAa,OAAQ;QAC9B,QAAQ;QACR,SAAS;IACX,OAAA,IAAW,aAAa,SAAU;QAChC,QAAQ;QACR,SAAS;IACX,OAAO;QACL,MAAM,IAAI,MAAM,oBAAoB;IACtC;IAEA,KAAA,CAAM,CAAC,CAAA,GAAA,CAAK,aAAc,IAAI,KAAA,IAAU;IACxC,IAAI,IAAI;IACR,MAAO,QAAQ,GAAG,QAAS;QACzB,MAAM,OAAO,aAAc,IAAA,CAAK,QAAQ,CAAA;QACxC,KAAA,CAAM,GAAG,CAAA,GAAI,MAAQ,OAAO;IAC9B;IACA,OAAO;AACT;AAOO,SAAS,YAAY,CAAA,EAAW,CAAA,EAAW;IAChD,OAAO,YAAY,GAAG,CAAC,IAAI;AAC7B;AAOO,SAAS,cAAc,CAAA,EAAW,CAAA,EAAW;IAClD,OAAO,YAAY,GAAG,CAAC,KAAK;AAC9B;AAOO,SAAS,SAAS,CAAA,EAAW,CAAA,EAAW;IAC7C,OAAO,YAAY,GAAG,CAAC,IAAI;AAC7B;AAOO,SAAS,WAAW,CAAA,EAAW,CAAA,EAAW;IAC/C,OAAO,YAAY,GAAG,CAAC,KAAK;AAC9B", "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/values/compare.ts"], "sourcesContent": ["import { Value } from \"./value.js\";\nimport { compareUTF8 } from \"./compare_utf8.js\";\n\nexport function compareValues(k1: Value | undefined, k2: Value | undefined) {\n  return compareAsTuples(makeComparable(k1), makeComparable(k2));\n}\n\nfunction compareAsTuples<T>(a: [number, T], b: [number, T]): number {\n  if (a[0] === b[0]) {\n    return compareSameTypeValues(a[1], b[1]);\n  } else if (a[0] < b[0]) {\n    return -1;\n  }\n  return 1;\n}\n\nfunction compareSameTypeValues<T>(v1: T, v2: T): number {\n  if (v1 === undefined || v1 === null) {\n    return 0;\n  }\n  if (typeof v1 === \"number\") {\n    if (typeof v2 !== \"number\") {\n      throw new Error(`Unexpected type ${v2 as any}`);\n    }\n    return compareNumbers(v1, v2);\n  }\n  if (typeof v1 === \"string\") {\n    if (typeof v2 !== \"string\") {\n      throw new Error(`Unexpected type ${v2 as any}`);\n    }\n    return compareUTF8(v1, v2);\n  }\n  if (\n    typeof v1 === \"bigint\" ||\n    typeof v1 === \"boolean\" ||\n    typeof v1 === \"string\"\n  ) {\n    return v1 < v2 ? -1 : v1 === v2 ? 0 : 1;\n  }\n  if (!Array.isArray(v1) || !Array.isArray(v2)) {\n    throw new Error(`Unexpected type ${v1 as any}`);\n  }\n  for (let i = 0; i < v1.length && i < v2.length; i++) {\n    const cmp = compareAsTuples(v1[i], v2[i]);\n    if (cmp !== 0) {\n      return cmp;\n    }\n  }\n  if (v1.length < v2.length) {\n    return -1;\n  }\n  if (v1.length > v2.length) {\n    return 1;\n  }\n  return 0;\n}\n\nfunction compareNumbers(v1: number, v2: number): number {\n  // Handle NaN values\n  if (isNaN(v1) || isNaN(v2)) {\n    // Create DataViews for bit-level comparison\n    const buffer1 = new ArrayBuffer(8);\n    const buffer2 = new ArrayBuffer(8);\n    new DataView(buffer1).setFloat64(0, v1, /* little-endian */ true);\n    new DataView(buffer2).setFloat64(0, v2, /* little-endian */ true);\n\n    // Read as BigInt to compare bits\n    const v1Bits = BigInt(\n      new DataView(buffer1).getBigInt64(0, /* little-endian */ true),\n    );\n    const v2Bits = BigInt(\n      new DataView(buffer2).getBigInt64(0, /* little-endian */ true),\n    );\n\n    // The sign bit is the most significant bit (bit 63)\n    const v1Sign = (v1Bits & 0x8000000000000000n) !== 0n;\n    const v2Sign = (v2Bits & 0x8000000000000000n) !== 0n;\n\n    // If one value is NaN and the other isn't, use sign bits first\n    if (isNaN(v1) !== isNaN(v2)) {\n      // If v1 is NaN, compare based on sign bits\n      if (isNaN(v1)) {\n        return v1Sign ? -1 : 1;\n      }\n      // If v2 is NaN, compare based on sign bits\n      return v2Sign ? 1 : -1;\n    }\n\n    // If both are NaN, compare their binary representations\n    if (v1Sign !== v2Sign) {\n      return v1Sign ? -1 : 1; // true means negative\n    }\n    return v1Bits < v2Bits ? -1 : v1Bits === v2Bits ? 0 : 1;\n  }\n\n  if (Object.is(v1, v2)) {\n    return 0;\n  }\n\n  if (Object.is(v1, -0)) {\n    return Object.is(v2, 0) ? -1 : -Math.sign(v2);\n  }\n  if (Object.is(v2, -0)) {\n    return Object.is(v1, 0) ? 1 : Math.sign(v1);\n  }\n\n  // Handle regular number comparison\n  return v1 < v2 ? -1 : 1;\n}\n\n// Returns an array which can be compared to other arrays as if they were tuples.\n// For example, [1, null] < [2, 1n] means null sorts before all bigints\n// And [3, 5] < [3, 6] means floats sort as expected\n// And [7, [[5, \"a\"]]] < [7, [[5, \"a\"], [5, \"b\"]]] means arrays sort as expected\nfunction makeComparable(v: Value | undefined): [number, any] {\n  if (v === undefined) {\n    return [0, undefined];\n  }\n  if (v === null) {\n    return [1, null];\n  }\n  if (typeof v === \"bigint\") {\n    return [2, v];\n  }\n  if (typeof v === \"number\") {\n    return [3, v];\n  }\n  if (typeof v === \"boolean\") {\n    return [4, v];\n  }\n  if (typeof v === \"string\") {\n    return [5, v];\n  }\n  if (v instanceof ArrayBuffer) {\n    return [6, Array.from(new Uint8Array(v)).map(makeComparable)];\n  }\n  if (Array.isArray(v)) {\n    return [7, v.map(makeComparable)];\n  }\n  // Otherwise, it's an POJO.\n  const keys = Object.keys(v).sort();\n  const pojo: Value[] = keys.map((k) => [k, v[k]!]);\n  return [8, pojo.map(makeComparable)];\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,mBAAmB;;;AAErB,SAAS,cAAc,EAAA,EAAuB,EAAA,EAAuB;IAC1E,OAAO,gBAAgB,eAAe,EAAE,GAAG,eAAe,EAAE,CAAC;AAC/D;AAEA,SAAS,gBAAmB,CAAA,EAAgB,CAAA,EAAwB;IAClE,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,CAAA,CAAE,CAAC,CAAA,EAAG;QACjB,OAAO,sBAAsB,CAAA,CAAE,CAAC,CAAA,EAAG,CAAA,CAAE,CAAC,CAAC;IACzC,OAAA,IAAW,CAAA,CAAE,CAAC,CAAA,GAAI,CAAA,CAAE,CAAC,CAAA,EAAG;QACtB,OAAO,CAAA;IACT;IACA,OAAO;AACT;AAEA,SAAS,sBAAyB,EAAA,EAAO,EAAA,EAAe;IACtD,IAAI,OAAO,KAAA,KAAa,OAAO,MAAM;QACnC,OAAO;IACT;IACA,IAAI,OAAO,OAAO,UAAU;QAC1B,IAAI,OAAO,OAAO,UAAU;YAC1B,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,EAAS,EAAE;QAChD;QACA,OAAO,eAAe,IAAI,EAAE;IAC9B;IACA,IAAI,OAAO,OAAO,UAAU;QAC1B,IAAI,OAAO,OAAO,UAAU;YAC1B,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,EAAS,EAAE;QAChD;QACA,WAAO,iOAAA,EAAY,IAAI,EAAE;IAC3B;IACA,IACE,OAAO,OAAO,YACd,OAAO,OAAO,aACd,OAAO,OAAO,UACd;QACA,OAAO,KAAK,KAAK,CAAA,IAAK,OAAO,KAAK,IAAI;IACxC;IACA,IAAI,CAAC,MAAM,OAAA,CAAQ,EAAE,KAAK,CAAC,MAAM,OAAA,CAAQ,EAAE,GAAG;QAC5C,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,EAAS,EAAE;IAChD;IACA,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,MAAA,IAAU,IAAI,GAAG,MAAA,EAAQ,IAAK;QACnD,MAAM,MAAM,gBAAgB,EAAA,CAAG,CAAC,CAAA,EAAG,EAAA,CAAG,CAAC,CAAC;QACxC,IAAI,QAAQ,GAAG;YACb,OAAO;QACT;IACF;IACA,IAAI,GAAG,MAAA,GAAS,GAAG,MAAA,EAAQ;QACzB,OAAO,CAAA;IACT;IACA,IAAI,GAAG,MAAA,GAAS,GAAG,MAAA,EAAQ;QACzB,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,eAAe,EAAA,EAAY,EAAA,EAAoB;IAEtD,IAAI,MAAM,EAAE,KAAK,MAAM,EAAE,GAAG;QAE1B,MAAM,UAAU,IAAI,YAAY,CAAC;QACjC,MAAM,UAAU,IAAI,YAAY,CAAC;QACjC,IAAI,SAAS,OAAO,EAAE,UAAA,CAAW,GAAG,IAAA,iBAAA,GAAwB;QAC5D,IAAI,SAAS,OAAO,EAAE,UAAA,CAAW,GAAG,IAAA,iBAAA,GAAwB;QAG5D,MAAM,SAAS,OACb,IAAI,SAAS,OAAO,EAAE,WAAA,CAAY,GAAA,iBAAA,GAAuB;QAE3D,MAAM,SAAS,OACb,IAAI,SAAS,OAAO,EAAE,WAAA,CAAY,GAAA,iBAAA,GAAuB;QAI3D,MAAM,SAAA,CAAU,SAAS,mBAAA,MAAyB,EAAA;QAClD,MAAM,SAAA,CAAU,SAAS,mBAAA,MAAyB,EAAA;QAGlD,IAAI,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG;YAE3B,IAAI,MAAM,EAAE,GAAG;gBACb,OAAO,SAAS,CAAA,IAAK;YACvB;YAEA,OAAO,SAAS,IAAI,CAAA;QACtB;QAGA,IAAI,WAAW,QAAQ;YACrB,OAAO,SAAS,CAAA,IAAK;QACvB;QACA,OAAO,SAAS,SAAS,CAAA,IAAK,WAAW,SAAS,IAAI;IACxD;IAEA,IAAI,OAAO,EAAA,CAAG,IAAI,EAAE,GAAG;QACrB,OAAO;IACT;IAEA,IAAI,OAAO,EAAA,CAAG,IAAI,CAAA,CAAE,GAAG;QACrB,OAAO,OAAO,EAAA,CAAG,IAAI,CAAC,IAAI,CAAA,IAAK,CAAC,KAAK,IAAA,CAAK,EAAE;IAC9C;IACA,IAAI,OAAO,EAAA,CAAG,IAAI,CAAA,CAAE,GAAG;QACrB,OAAO,OAAO,EAAA,CAAG,IAAI,CAAC,IAAI,IAAI,KAAK,IAAA,CAAK,EAAE;IAC5C;IAGA,OAAO,KAAK,KAAK,CAAA,IAAK;AACxB;AAMA,SAAS,eAAe,CAAA,EAAqC;IAC3D,IAAI,MAAM,KAAA,GAAW;QACnB,OAAO;YAAC;YAAG,KAAA,CAAS;SAAA;IACtB;IACA,IAAI,MAAM,MAAM;QACd,OAAO;YAAC;YAAG,IAAI;SAAA;IACjB;IACA,IAAI,OAAO,MAAM,UAAU;QACzB,OAAO;YAAC;YAAG,CAAC;SAAA;IACd;IACA,IAAI,OAAO,MAAM,UAAU;QACzB,OAAO;YAAC;YAAG,CAAC;SAAA;IACd;IACA,IAAI,OAAO,MAAM,WAAW;QAC1B,OAAO;YAAC;YAAG,CAAC;SAAA;IACd;IACA,IAAI,OAAO,MAAM,UAAU;QACzB,OAAO;YAAC;YAAG,CAAC;SAAA;IACd;IACA,IAAI,aAAa,aAAa;QAC5B,OAAO;YAAC;YAAG,MAAM,IAAA,CAAK,IAAI,WAAW,CAAC,CAAC,EAAE,GAAA,CAAI,cAAc,CAAC;SAAA;IAC9D;IACA,IAAI,MAAM,OAAA,CAAQ,CAAC,GAAG;QACpB,OAAO;YAAC;YAAG,EAAE,GAAA,CAAI,cAAc,CAAC;SAAA;IAClC;IAEA,MAAM,OAAO,OAAO,IAAA,CAAK,CAAC,EAAE,IAAA,CAAK;IACjC,MAAM,OAAgB,KAAK,GAAA,CAAI,CAAC,IAAM;YAAC;YAAG,CAAA,CAAE,CAAC,CAAE;SAAC;IAChD,OAAO;QAAC;QAAG,KAAK,GAAA,CAAI,cAAc,CAAC;KAAA;AACrC", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/values/index.ts"], "sourcesContent": ["/**\n * Utilities for working with values stored in Convex.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n * @module\n */\n\nexport { convexToJson, jsonToConvex } from \"./value.js\";\nexport type {\n  Id as GenericId,\n  JSONValue,\n  Value,\n  NumericValue,\n} from \"./value.js\";\nexport { v, asObjectValidator } from \"./validator.js\";\nexport type {\n  AsObjectValidator,\n  GenericValidator,\n  ObjectType,\n  PropertyValidators,\n} from \"./validator.js\";\nexport type {\n  ValidatorJSON,\n  RecordKeyValidatorJSON,\n  RecordValueValidatorJSON,\n  ObjectFieldType,\n  Validator,\n  OptionalProperty,\n  VId,\n  VFloat64,\n  VInt64,\n  VBoolean,\n  VBytes,\n  VString,\n  VNull,\n  VAny,\n  VObject,\n  VLiteral,\n  VArray,\n  VRecord,\n  VUnion,\n  VOptional,\n} from \"./validators.js\";\nimport * as Base64 from \"./base64.js\";\nexport { Base64 };\nexport type { Infer } from \"./validator.js\";\nexport * from \"./errors.js\";\nexport { compareValues } from \"./compare.js\";\n"], "names": [], "mappings": ";AAQA,SAAS,cAAc,oBAAoB;AAO3C,SAAS,GAAG,yBAAyB;AA6BrC,YAAY,YAAY;AAGxB,cAAc;AACd,SAAS,qBAAqB", "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/index.ts"], "sourcesContent": ["export const version = \"1.26.2\";\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,UAAU", "debugId": null}}, {"offset": {"line": 1308, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/syscall.ts"], "sourcesContent": ["import { ConvexError } from \"../../values/errors.js\";\nimport { jsonToConvex } from \"../../values/value.js\";\n\ndeclare const Convex: {\n  syscall: (op: string, jsonArgs: string) => string;\n  asyncSyscall: (op: string, jsonArgs: string) => Promise<string>;\n  jsSyscall: (op: string, args: Record<string, any>) => any;\n};\n/**\n * Perform a syscall, taking in a JSON-encodable object as an argument, serializing with\n * JSON.stringify, calling into Rust, and then parsing the response as a JSON-encodable\n * value. If one of your arguments is a Convex value, you must call `convexToJson` on it\n * before passing it to this function, and if the return value has a Convex value, you're\n * also responsible for calling `jsonToConvex`: This layer only deals in JSON.\n */\n\nexport function performSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.syscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  const resultStr = Convex.syscall(op, JSON.stringify(arg));\n  return JSON.parse(resultStr);\n}\n\nexport async function performAsyncSyscall(\n  op: string,\n  arg: Record<string, any>,\n): Promise<any> {\n  if (typeof Convex === \"undefined\" || Convex.asyncSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  let resultStr;\n  try {\n    resultStr = await Convex.asyncSyscall(op, JSON.stringify(arg));\n  } catch (e: any) {\n    // Rethrow the exception to attach stack trace starting from here.\n    // If the error came from JS it will include its own stack trace in the message.\n    // If it came from Rust it won't.\n\n    // This only happens if we're propagating ConvexErrors\n    if (e.data !== undefined) {\n      const rethrown = new ConvexError(e.message);\n      rethrown.data = jsonToConvex(e.data);\n      throw rethrown;\n    }\n    throw new Error(e.message);\n  }\n  return JSON.parse(resultStr);\n}\n\n/**\n * Call into a \"JS\" syscall. Like `performSyscall`, this calls a dynamically linked\n * function set up in the Convex function execution. Unlike `performSyscall`, the\n * arguments do not need to be JSON-encodable and neither does the return value.\n *\n * @param op\n * @param arg\n * @returns\n */\nexport function performJsSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.jsSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  return Convex.jsSyscall(op, arg);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;;;;AAetB,SAAS,eAAe,EAAA,EAAY,GAAA,EAA+B;IACxE,IAAI,OAAO,WAAW,eAAe,OAAO,OAAA,KAAY,KAAA,GAAW;QACjE,MAAM,IAAI,MACR;IAGJ;IACA,MAAM,YAAY,OAAO,OAAA,CAAQ,IAAI,KAAK,SAAA,CAAU,GAAG,CAAC;IACxD,OAAO,KAAK,KAAA,CAAM,SAAS;AAC7B;AAEA,eAAsB,oBACpB,EAAA,EACA,GAAA,EACc;IACd,IAAI,OAAO,WAAW,eAAe,OAAO,YAAA,KAAiB,KAAA,GAAW;QACtE,MAAM,IAAI,MACR;IAGJ;IACA,IAAI;IACJ,IAAI;QACF,YAAY,MAAM,OAAO,YAAA,CAAa,IAAI,KAAK,SAAA,CAAU,GAAG,CAAC;IAC/D,EAAA,OAAS,GAAQ;QAMf,IAAI,EAAE,IAAA,KAAS,KAAA,GAAW;YACxB,MAAM,WAAW,IAAI,2NAAA,CAAY,EAAE,OAAO;YAC1C,SAAS,IAAA,GAAO,+NAAA,EAAa,EAAE,IAAI;YACnC,MAAM;QACR;QACA,MAAM,IAAI,MAAM,EAAE,OAAO;IAC3B;IACA,OAAO,KAAK,KAAA,CAAM,SAAS;AAC7B;AAWO,SAAS,iBAAiB,EAAA,EAAY,GAAA,EAA+B;IAC1E,IAAI,OAAO,WAAW,eAAe,OAAO,SAAA,KAAc,KAAA,GAAW;QACnE,MAAM,IAAI,MACR;IAGJ;IACA,OAAO,OAAO,SAAA,CAAU,IAAI,GAAG;AACjC", "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/functionName.ts"], "sourcesContent": ["/**\n * A symbol for accessing the name of a {@link FunctionReference} at runtime.\n */\nexport const functionName = Symbol.for(\"functionName\");\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,eAAe,OAAO,GAAA,CAAI,cAAc", "debugId": null}}, {"offset": {"line": 1365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/components/paths.ts"], "sourcesContent": ["import { functionName } from \"../functionName.js\";\n\nexport const toReferencePath = Symbol.for(\"toReferencePath\");\n\n// Multiple instances of the same Symbol.for() are equal at runtime but not\n// at type-time, so `[toReferencePath]` properties aren't used in types.\n// Use this function to set the property invisibly.\nexport function setReferencePath<T>(obj: T, value: string) {\n  (obj as any)[toReferencePath] = value;\n}\n\nexport function extractReferencePath(reference: any): string | null {\n  return reference[toReferencePath] ?? null;\n}\n\nexport function isFunctionHandle(s: string): boolean {\n  return s.startsWith(\"function://\");\n}\n\nexport function getFunctionAddress(functionReference: any) {\n  // The `run*` syscalls expect either a UDF path at \"name\" or a serialized\n  // reference at \"reference\". Dispatch on `functionReference` to coerce\n  // it to one or the other.\n  let functionAddress;\n\n  // Legacy path for passing in UDF paths directly as function references.\n  if (typeof functionReference === \"string\") {\n    if (isFunctionHandle(functionReference)) {\n      functionAddress = { functionHandle: functionReference };\n    } else {\n      functionAddress = { name: functionReference };\n    }\n  }\n  // Path for passing in a `FunctionReference`, either from `api` or directly\n  // created from a UDF path with `makeFunctionReference`.\n  else if (functionReference[functionName]) {\n    functionAddress = { name: functionReference[functionName] };\n  }\n  // Reference to a component's function derived from `app` or `component`.\n  else {\n    const referencePath = extractReferencePath(functionReference);\n    if (!referencePath) {\n      throw new Error(`${functionReference} is not a functionReference`);\n    }\n    functionAddress = { reference: referencePath };\n  }\n  return functionAddress;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,SAAS,oBAAoB;;;AAEtB,MAAM,kBAAkB,OAAO,GAAA,CAAI,iBAAiB;AAKpD,SAAS,iBAAoB,GAAA,EAAQ,KAAA,EAAe;IACxD,GAAA,CAAY,eAAe,CAAA,GAAI;AAClC;AAEO,SAAS,qBAAqB,SAAA,EAA+B;IAClE,OAAO,SAAA,CAAU,eAAe,CAAA,IAAK;AACvC;AAEO,SAAS,iBAAiB,CAAA,EAAoB;IACnD,OAAO,EAAE,UAAA,CAAW,aAAa;AACnC;AAEO,SAAS,mBAAmB,iBAAA,EAAwB;IAIzD,IAAI;IAGJ,IAAI,OAAO,sBAAsB,UAAU;QACzC,IAAI,iBAAiB,iBAAiB,GAAG;YACvC,kBAAkB;gBAAE,gBAAgB;YAAkB;QACxD,OAAO;YACL,kBAAkB;gBAAE,MAAM;YAAkB;QAC9C;IACF,OAAA,IAGS,iBAAA,CAAkB,kOAAY,CAAA,EAAG;QACxC,kBAAkB;YAAE,MAAM,iBAAA,CAAkB,kOAAY,CAAA;QAAE;IAC5D,OAEK;QACH,MAAM,gBAAgB,qBAAqB,iBAAiB;QAC5D,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM,GAAG,iBAAiB,CAAA,2BAAA,CAA6B;QACnE;QACA,kBAAkB;YAAE,WAAW;QAAc;IAC/C;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/actions_impl.ts"], "sourcesContent": ["import { convexTo<PERSON>son, jsonToConvex, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { FunctionReference } from \"../../server/api.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nfunction syscallArgs(\n  requestId: string,\n  functionReference: any,\n  args?: Record<string, Value>,\n) {\n  const address = getFunctionAddress(functionReference);\n  return {\n    ...address,\n    args: convexToJson(parseArgs(args)),\n    version,\n    requestId,\n  };\n}\n\nexport function setupActionCalls(requestId: string) {\n  return {\n    runQuery: async (\n      query: FunctionReference<\"query\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/query\",\n        syscallArgs(requestId, query, args),\n      );\n      return jsonToConvex(result);\n    },\n    runMutation: async (\n      mutation: FunctionReference<\"mutation\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/mutation\",\n        syscallArgs(requestId, mutation, args),\n      );\n      return jsonToConvex(result);\n    },\n    runAction: async (\n      action: FunctionReference<\"action\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/action\",\n        syscallArgs(requestId, action, args),\n      );\n      return jsonToConvex(result);\n    },\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,cAAc,oBAA2B;;AAClD,SAAS,eAAe;AACxB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB;AAE1B,SAAS,0BAA0B;;;;;;;AAEnC,SAAS,YACP,SAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,MAAM,cAAU,+OAAA,EAAmB,iBAAiB;IACpD,OAAO;QACL,GAAG,OAAA;QACH,UAAM,2NAAA,EAAa,4NAAA,EAAU,IAAI,CAAC;iBAClC,4MAAA;QACA;IACF;AACF;AAEO,SAAS,iBAAiB,SAAA,EAAmB;IAClD,OAAO;QACL,UAAU,OACR,OACA,SACiB;YACjB,MAAM,SAAS,UAAM,4OAAA,EACnB,qBACA,YAAY,WAAW,OAAO,IAAI;YAEpC,WAAO,2NAAA,EAAa,MAAM;QAC5B;QACA,aAAa,OACX,UACA,SACiB;YACjB,MAAM,SAAS,UAAM,4OAAA,EACnB,wBACA,YAAY,WAAW,UAAU,IAAI;YAEvC,WAAO,2NAAA,EAAa,MAAM;QAC5B;QACA,WAAW,OACT,QACA,SACiB;YACjB,MAAM,SAAS,UAAM,4OAAA,EACnB,sBACA,YAAY,WAAW,QAAQ,IAAI;YAErC,WAAO,2NAAA,EAAa,MAAM;QAC5B;IACF;AACF", "debugId": null}}, {"offset": {"line": 1466, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/vector_search.ts"], "sourcesContent": ["import { Id, Value } from \"../values/value.js\";\nimport {\n  DocumentByInfo,\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n  NamedTableInfo,\n  NamedVectorIndex,\n  TableNamesInDataModel,\n  VectorIndexNames,\n} from \"./data_model.js\";\n\n/**\n * An object with parameters for performing a vector search against a vector index.\n * @public\n */\nexport interface VectorSearchQuery<\n  TableInfo extends GenericTableInfo,\n  IndexName extends VectorIndexNames<TableInfo>,\n> {\n  /**\n   * The query vector.\n   *\n   * This must have the same length as the `dimensions` of the index.\n   * This vector search will return the IDs of the documents most similar to\n   * this vector.\n   */\n  vector: number[];\n  /**\n   * The number of results to return. If specified, must be between 1 and 256\n   * inclusive.\n   *\n   * @default 10\n   */\n  limit?: number;\n  /**\n   * Optional filter expression made up of `q.or` and `q.eq` operating\n   * over the filter fields of the index.\n   *\n   * e.g. `filter: q => q.or(q.eq(\"genre\", \"comedy\"), q.eq(\"genre\", \"drama\"))`\n   *\n   * @param q\n   * @returns\n   */\n  filter?: (\n    q: VectorFilterBuilder<\n      DocumentByInfo<TableInfo>,\n      NamedVectorIndex<TableInfo, IndexName>\n    >,\n  ) => FilterExpression<boolean>;\n}\n\nexport type VectorSearch<\n  DataModel extends GenericDataModel,\n  TableName extends TableNamesInDataModel<DataModel>,\n  IndexName extends VectorIndexNames<NamedTableInfo<DataModel, TableName>>,\n> = (\n  tableName: TableName,\n  indexName: IndexName,\n  query: VectorSearchQuery<NamedTableInfo<DataModel, TableName>, IndexName>,\n) => Promise<Array<{ _id: Id<TableName>; _score: number }>>;\n\n/**\n * Expressions are evaluated to produce a {@link values.Value} in the course of executing a query.\n *\n * To construct an expression, use the {@link VectorFilterBuilder} provided within\n * {@link VectorSearchQuery}.\n *\n * @typeParam T - The type that this expression evaluates to.\n * @public\n */\nexport abstract class FilterExpression<T extends Value | undefined> {\n  // Property for nominal type support.\n  private _isExpression: undefined;\n\n  // Property to distinguish expressions by the type they resolve to.\n  private _value!: T;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n\n/**\n * An interface for defining filters for vector searches.\n *\n * This has a similar interface to {@link FilterBuilder}, which is used in\n * database queries, but supports only the methods that can be efficiently\n * done in a vector search.\n *\n * @public\n */\nexport interface VectorFilterBuilder<\n  Document extends GenericDocument,\n  VectorIndexConfig extends GenericVectorIndexConfig,\n> {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  /**\n   * Is the field at `fieldName` equal to `value`\n   *\n   * @public\n   * */\n  eq<FieldName extends VectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<Document, FieldName>,\n  ): FilterExpression<boolean>;\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * `exprs[0] || exprs[1] || ... || exprs[n]`\n   *\n   * @public\n   */\n  or(...exprs: Array<FilterExpression<boolean>>): FilterExpression<boolean>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAyEO,MAAe,iBAA8C;IAAA;;GAAA,GAUlE,aAAc;QARd,qCAAA;QAAA,cAAA,IAAA,EAAQ;QAGR,mEAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/validate.ts"], "sourcesContent": ["export function validateArg(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (arg === undefined) {\n    throw new TypeError(\n      `Must provide arg ${idx} \\`${argName}\\` to \\`${method}\\``,\n    );\n  }\n}\n\nexport function validateArgIsInteger(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (!Number.isInteger(arg)) {\n    throw new TypeError(\n      `Arg ${idx} \\`${argName}\\` to \\`${method}\\` must be an integer`,\n    );\n  }\n}\n\nexport function validateArgIsNonNegativeInteger(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (!Number.isInteger(arg) || arg < 0) {\n    throw new TypeError(\n      `Arg ${idx} \\`${argName}\\` to \\`${method}\\` must be a non-negative integer`,\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAO,SAAS,YACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,QAAQ,KAAA,GAAW;QACrB,MAAM,IAAI,UACR,CAAA,iBAAA,EAAoB,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,EAAA,CAAA;IAEzD;AACF;AAEO,SAAS,qBACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,CAAC,OAAO,SAAA,CAAU,GAAG,GAAG;QAC1B,MAAM,IAAI,UACR,CAAA,IAAA,EAAO,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,qBAAA,CAAA;IAE5C;AACF;AAEO,SAAS,gCACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,CAAC,OAAO,SAAA,CAAU,GAAG,KAAK,MAAM,GAAG;QACrC,MAAM,IAAI,UACR,CAAA,IAAA,EAAO,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,iCAAA,CAAA;IAE5C;AACF", "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/vector_search_impl.ts"], "sourcesContent": ["import { J<PERSON>NValue } from \"../../values/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { version } from \"../../index.js\";\nimport {\n  FilterExpression,\n  VectorFilterBuilder,\n  VectorSearch,\n  VectorSearchQuery,\n} from \"../vector_search.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n} from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { Value, convexOrUndefinedToJson } from \"../../values/value.js\";\n\nexport function setupActionVectorSearch(\n  requestId: string,\n): VectorSearch<GenericDataModel, string, string> {\n  return async (\n    tableName: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) => {\n    validateArg(tableName, 1, \"vectorSearch\", \"tableName\");\n    validateArg(indexName, 2, \"vectorSearch\", \"indexName\");\n    validateArg(query, 3, \"vectorSearch\", \"query\");\n    if (\n      !query.vector ||\n      !Array.isArray(query.vector) ||\n      query.vector.length === 0\n    ) {\n      throw Error(\"`vector` must be a non-empty Array in vectorSearch\");\n    }\n\n    return await new VectorQueryImpl(\n      requestId,\n      tableName + \".\" + indexName,\n      query,\n    ).collect();\n  };\n}\n\nexport class VectorQueryImpl {\n  private requestId: string;\n  private state:\n    | { type: \"preparing\"; query: SerializedVectorQuery }\n    | { type: \"consumed\" };\n\n  constructor(\n    requestId: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) {\n    this.requestId = requestId;\n    const filters = query.filter\n      ? serializeExpression(query.filter(filterBuilderImpl))\n      : null;\n\n    this.state = {\n      type: \"preparing\",\n      query: {\n        indexName,\n        limit: query.limit,\n        vector: query.vector,\n        expressions: filters,\n      },\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    if (this.state.type === \"consumed\") {\n      throw new Error(\"This query is closed and can't emit any more values.\");\n    }\n    const query = this.state.query;\n    this.state = { type: \"consumed\" };\n\n    const { results } = await performAsyncSyscall(\"1.0/actions/vectorSearch\", {\n      requestId: this.requestId,\n      version,\n      query,\n    });\n    return results;\n  }\n}\n\ntype SerializedVectorQuery = {\n  indexName: string;\n  limit?: number;\n  vector: Array<number>;\n  expressions: JSONValue;\n};\n\ntype ExpressionOrValue<T extends Value | undefined> = FilterExpression<T> | T;\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends FilterExpression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: VectorFilterBuilder<\n  GenericDocument,\n  GenericVectorIndexConfig\n> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<FieldName extends GenericVectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): FilterExpression<boolean> {\n    if (typeof fieldName !== \"string\") {\n      throw new Error(\"The first argument to `q.eq` must be a field name.\");\n    }\n    return new ExpressionImpl({\n      $eq: [\n        serializeExpression(new ExpressionImpl({ $field: fieldName })),\n        serializeExpression(value),\n      ],\n    });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): FilterExpression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA,SAAS,2BAA2B;AACpC,SAAS,eAAe;AACxB;AAaA,SAAS,mBAAmB;AAC5B,SAAgB,+BAA+B;;;;;;;;;;;;;;;AAExC,SAAS,wBACd,SAAA,EACgD;IAChD,OAAO,OACL,WACA,WACA,UACG;QACH,IAAA,qOAAA,EAAY,WAAW,GAAG,gBAAgB,WAAW;QACrD,IAAA,qOAAA,EAAY,WAAW,GAAG,gBAAgB,WAAW;QACrD,IAAA,qOAAA,EAAY,OAAO,GAAG,gBAAgB,OAAO;QAC7C,IACE,CAAC,MAAM,MAAA,IACP,CAAC,MAAM,OAAA,CAAQ,MAAM,MAAM,KAC3B,MAAM,MAAA,CAAO,MAAA,KAAW,GACxB;YACA,MAAM,MAAM,oDAAoD;QAClE;QAEA,OAAO,MAAM,IAAI,gBACf,WACA,YAAY,MAAM,WAClB,OACA,OAAA,CAAQ;IACZ;AACF;AAEO,MAAM,gBAAgB;IAM3B,YACE,SAAA,EACA,SAAA,EACA,KAAA,CACA;QATF,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QASN,IAAA,CAAK,SAAA,GAAY;QACjB,MAAM,UAAU,MAAM,MAAA,GAClB,oBAAoB,MAAM,MAAA,CAAO,iBAAiB,CAAC,IACnD;QAEJ,IAAA,CAAK,KAAA,GAAQ;YACX,MAAM;YACN,OAAO;gBACL;gBACA,OAAO,MAAM,KAAA;gBACb,QAAQ,MAAM,MAAA;gBACd,aAAa;YACf;QACF;IACF;IAEA,MAAM,UAA+B;QACnC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClC,MAAM,IAAI,MAAM,sDAAsD;QACxE;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAW;QAEhC,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,MAAM,gPAAA,EAAoB,4BAA4B;YACxE,WAAW,IAAA,CAAK,SAAA;qBAChB,4MAAA;YACA;QACF,CAAC;QACD,OAAO;IACT;AACF;AAaO,MAAM,uBAAuB,uOAAA,CAAsB;IAExD,YAAY,KAAA,CAAkB;QAC5B,KAAA,CAAM;QAFR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,YAAuB;QACrB,OAAO,IAAA,CAAK,KAAA;IACd;AACF;AAEO,SAAS,oBACd,IAAA,EACW;IACX,IAAI,gBAAgB,gBAAgB;QAClC,OAAO,KAAK,SAAA,CAAU;IACxB,OAAO;QAGL,OAAO;YAAE,cAAU,sOAAA,EAAwB,IAAyB;QAAE;IACxE;AACF;AAEO,MAAM,oBAGT;IAAA,8EAAA;IAGF,IACE,SAAA,EACA,KAAA,EAC2B;QAC3B,IAAI,OAAO,cAAc,UAAU;YACjC,MAAM,IAAI,MAAM,oDAAoD;QACtE;QACA,OAAO,IAAI,eAAe;YACxB,KAAK;gBACH,oBAAoB,IAAI,eAAe;oBAAE,QAAQ;gBAAU,CAAC,CAAC;gBAC7D,oBAAoB,KAAK;aAC3B;QACF,CAAC;IACH;IAAA,8EAAA;IAIA,IAAA,GAAM,KAAA,EAAqE;QACzE,OAAO,IAAI,eAAe;YAAE,KAAK,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACnE;AACF", "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/authentication_impl.ts"], "sourcesContent": ["import { Auth } from \"../authentication.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\n\nexport function setupAuth(requestId: string): Auth {\n  return {\n    getUserIdentity: async () => {\n      return await performAsyncSyscall(\"1.0/getUserIdentity\", {\n        requestId,\n      });\n    },\n  };\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,2BAA2B;;;AAE7B,SAAS,UAAU,SAAA,EAAyB;IACjD,OAAO;QACL,iBAAiB,YAAY;YAC3B,OAAO,UAAM,4OAAA,EAAoB,uBAAuB;gBACtD;YACF,CAAC;QACH;IACF;AACF", "debugId": null}}, {"offset": {"line": 1660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/filter_builder.ts"], "sourcesContent": ["import { Value, NumericValue } from \"../values/index.js\";\nimport {\n  DocumentByInfo,\n  FieldPaths,\n  FieldTypeFromFieldPath,\n  GenericTableInfo,\n} from \"./data_model.js\";\n\n/**\n * Expressions are evaluated to produce a {@link values.Value} in the course of executing a query.\n *\n * To construct an expression, use the {@link FilterBuilder} provided within\n * {@link OrderedQuery.filter}.\n *\n * @typeParam T - The type that this expression evaluates to.\n * @public\n */\nexport abstract class Expression<T extends Value | undefined> {\n  // Property for nominal type support.\n  private _isExpression: undefined;\n\n  // Property to distinguish expressions by the type they resolve to.\n  private _value!: T;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n/**\n * An {@link Expression} or a constant {@link values.Value}\n *\n * @public\n */\nexport type ExpressionOrValue<T extends Value | undefined> = Expression<T> | T;\n\n/**\n * An interface for defining filters in queries.\n *\n * `FilterBuilder` has various methods that produce {@link Expression}s.\n * These expressions can be nested together along with constants to express\n * a filter predicate.\n *\n * `FilterBuilder` is used within {@link OrderedQuery.filter} to create query\n * filters.\n *\n * Here are the available methods:\n *\n * |                               |                                               |\n * |-------------------------------|-----------------------------------------------|\n * | **Comparisons**               | Error when `l` and `r` are not the same type. |\n * | [`eq(l, r)`](#eq)             | `l === r`                                     |\n * | [`neq(l, r)`](#neq)           | `l !== r`                                     |\n * | [`lt(l, r)`](#lt)             | `l < r`                                       |\n * | [`lte(l, r)`](#lte)           | `l <= r`                                      |\n * | [`gt(l, r)`](#gt)             | `l > r`                                       |\n * | [`gte(l, r)`](#gte)           | `l >= r`                                      |\n * |                               |                                               |\n * | **Arithmetic**                | Error when `l` and `r` are not the same type. |\n * | [`add(l, r)`](#add)           | `l + r`                                       |\n * | [`sub(l, r)`](#sub)           | `l - r`                                       |\n * | [`mul(l, r)`](#mul)           | `l * r`                                       |\n * | [`div(l, r)`](#div)           | `l / r`                                       |\n * | [`mod(l, r)`](#mod)           | `l % r`                                       |\n * | [`neg(x)`](#neg)              | `-x`                                          |\n * |                               |                                               |\n * | **Logic**                     | Error if any param is not a `bool`.           |\n * | [`not(x)`](#not)              | `!x`                                          |\n * | [`and(a, b, ..., z)`](#and)   | `a && b && ... && z`                          |\n * | [`or(a, b, ..., z)`](#or)     | <code>a &#124;&#124; b &#124;&#124; ... &#124;&#124; z</code> |\n * |                               |                                               |\n * | **Other**                     |                                               |\n * | [`field(fieldPath)`](#field)  | Evaluates to the field at `fieldPath`.        |\n * @public\n */\nexport interface FilterBuilder<TableInfo extends GenericTableInfo> {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  /**\n   * `l === r`\n   *\n   * @public\n   * */\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l !== r`\n   *\n   * @public\n   * */\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l < r`\n   *\n   * @public\n   */\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l <= r`\n   *\n   * @public\n   */\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l > r`\n   *\n   * @public\n   */\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l >= r`\n   *\n   * @public\n   */\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  /**\n   * `l + r`\n   *\n   * @public\n   */\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l - r`\n   *\n   * @public\n   */\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l * r`\n   *\n   * @public\n   */\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l / r`\n   *\n   * @public\n   */\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l % r`\n   *\n   * @public\n   */\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `-x`\n   *\n   * @public\n   */\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T>;\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * `exprs[0] && exprs[1] && ... && exprs[n]`\n   *\n   * @public\n   */\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean>;\n\n  /**\n   * `exprs[0] || exprs[1] || ... || exprs[n]`\n   *\n   * @public\n   */\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean>;\n\n  /**\n   * `!x`\n   *\n   * @public\n   */\n  not(x: ExpressionOrValue<boolean>): Expression<boolean>;\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * Evaluates to the field at the given `fieldPath`.\n   *\n   * For example, in {@link OrderedQuery.filter} this can be used to examine the values being filtered.\n   *\n   * #### Example\n   *\n   * On this object:\n   * ```\n   * {\n   *   \"user\": {\n   *     \"isActive\": true\n   *   }\n   * }\n   * ```\n   *\n   * `field(\"user.isActive\")` evaluates to `true`.\n   *\n   * @public\n   */\n  field<FieldPath extends FieldPaths<TableInfo>>(\n    fieldPath: FieldPath,\n  ): Expression<FieldTypeFromFieldPath<DocumentByInfo<TableInfo>, FieldPath>>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAiBO,MAAe,WAAwC;IAAA;;GAAA,GAU5D,aAAc;QARd,qCAAA;QAAA,cAAA,IAAA,EAAQ;QAGR,mEAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "debugId": null}}, {"offset": {"line": 1687, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/filter_builder_impl.ts"], "sourcesContent": ["import { JSONValue, Value, NumericValue } from \"../../values/index.js\";\nimport { convexOrUndefinedTo<PERSON><PERSON> } from \"../../values/value.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"../filter_builder.js\";\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends Expression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: FilterBuilder<GenericTableInfo> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $eq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $neq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $add: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $sub: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mul: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $div: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mod: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T> {\n    return new ExpressionImpl({ $neg: serializeExpression(x) });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $and: exprs.map(serializeExpression) });\n  },\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n\n  not(x: ExpressionOrValue<boolean>): Expression<boolean> {\n    return new ExpressionImpl({ $not: serializeExpression(x) });\n  },\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n  field(fieldPath: string): Expression<any> {\n    return new ExpressionImpl({ $field: fieldPath });\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;AACA,SAAS,+BAA+B;AAExC;;;;;;;;;;;;AAQO,MAAM,uBAAuB,kOAAA,CAAgB;IAElD,YAAY,KAAA,CAAkB;QAC5B,KAAA,CAAM;QAFR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,YAAuB;QACrB,OAAO,IAAA,CAAK,KAAA;IACd;AACF;AAEO,SAAS,oBACd,IAAA,EACW;IACX,IAAI,gBAAgB,gBAAgB;QAClC,OAAO,KAAK,SAAA,CAAU;IACxB,OAAO;QAGL,OAAO;YAAE,cAAU,sOAAA,EAAwB,IAAyB;QAAE;IACxE;AACF;AAEO,MAAM,oBAAqD;IAAA,8EAAA;IAGhE,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAAA,8EAAA;IAIA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KAA4B,CAAA,EAAwC;QAClE,OAAO,IAAI,eAAe;YAAE,MAAM,oBAAoB,CAAC;QAAE,CAAC;IAC5D;IAAA,8EAAA;IAIA,KAAA,GAAO,KAAA,EAA+D;QACpE,OAAO,IAAI,eAAe;YAAE,MAAM,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACpE;IAEA,IAAA,GAAM,KAAA,EAA+D;QACnE,OAAO,IAAI,eAAe;YAAE,KAAK,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACnE;IAEA,KAAI,CAAA,EAAoD;QACtD,OAAO,IAAI,eAAe;YAAE,MAAM,oBAAoB,CAAC;QAAE,CAAC;IAC5D;IAAA,8EAAA;IAGA,OAAM,SAAA,EAAoC;QACxC,OAAO,IAAI,eAAe;YAAE,QAAQ;QAAU,CAAC;IACjD;AACF", "debugId": null}}, {"offset": {"line": 1850, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/index_range_builder.ts"], "sourcesContent": ["import {\n  GenericIndexFields,\n  GenericDocument,\n  FieldTypeFromFieldPath,\n} from \"./data_model.js\";\n\n/**\n * A type that adds 1 to a number literal type (up to 14).\n *\n * This is necessary to step through the fields in an index.\n */\ntype PlusOne<N extends number> = [\n  1,\n  2,\n  3,\n  4,\n  5,\n  6,\n  7,\n  8,\n  9,\n  10,\n  11,\n  12,\n  13,\n  14,\n  15,\n][N];\n\n/**\n * Builder to define an index range to query.\n *\n * An index range is a description of which documents Convex should consider\n * when running the query.\n *\n * An index range is always a chained list of:\n * 1. 0 or more equality expressions defined with `.eq`.\n * 2. [Optionally] A lower bound expression defined with `.gt` or `.gte`.\n * 3. [Optionally] An upper bound expression defined with `.lt` or `.lte`.\n *\n * **You must step through fields in index order.**\n *\n * Each equality expression must compare a different index field, starting from\n * the beginning and in order. The upper and lower bounds must follow the\n * equality expressions and compare the next field.\n *\n * For example, if there is an index of messages on\n * `[\"projectId\", \"priority\"]`, a range searching for \"messages in 'myProjectId'\n * with priority at least 100\" would look like:\n * ```ts\n * q.eq(\"projectId\", myProjectId)\n *  .gte(\"priority\", 100)\n * ```\n *\n * **The performance of your query is based on the specificity of the range.**\n *\n * This class is designed to only allow you to specify ranges that Convex can\n * efficiently use your index to find. For all other filtering use\n * {@link OrderedQuery.filter}.\n *\n * To learn about indexes, see [Indexes](https://docs.convex.dev/using/indexes).\n * @public\n */\nexport interface IndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFields extends GenericIndexFields,\n  FieldNum extends number = 0,\n> extends LowerBoundIndexRangeBuilder<Document, IndexFields[FieldNum]> {\n  /**\n   * Restrict this range to documents where `doc[fieldName] === value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  eq(\n    fieldName: IndexFields[FieldNum],\n    value: FieldTypeFromFieldPath<Document, IndexFields[FieldNum]>,\n  ): NextIndexRangeBuilder<Document, IndexFields, FieldNum>;\n}\n\n/**\n * An {@link IndexRangeBuilder} for the next field of the index.\n *\n * This type is careful to check if adding one to the `FieldNum` will exceed\n * the length of the `IndexFields`.\n */\ntype NextIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFields extends GenericIndexFields,\n  FieldNum extends number,\n> =\n  PlusOne<FieldNum> extends IndexFields[\"length\"]\n    ? IndexRange\n    : IndexRangeBuilder<Document, IndexFields, PlusOne<FieldNum>>;\n\n/**\n * Builder to define the lower bound of an index range.\n *\n * See {@link IndexRangeBuilder}.\n *\n * @public\n */\nexport interface LowerBoundIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFieldName extends string,\n> extends UpperBoundIndexRangeBuilder<Document, IndexFieldName> {\n  /**\n   * Restrict this range to documents where `doc[fieldName] > value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  gt(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): UpperBoundIndexRangeBuilder<Document, IndexFieldName>;\n  /**\n   * Restrict this range to documents where `doc[fieldName] >= value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  gte(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): UpperBoundIndexRangeBuilder<Document, IndexFieldName>;\n}\n\n/**\n * Builder to define the upper bound of an index range.\n *\n * See {@link IndexRangeBuilder}.\n *\n * @public\n */\nexport interface UpperBoundIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFieldName extends string,\n> extends IndexRange {\n  /**\n   * Restrict this range to documents where `doc[fieldName] < value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the same index\n   * field used in the lower bound (`.gt` or `.gte`) or the next field if no\n   * lower bound was specified.\n   * @param value - The value to compare against.\n   */\n  lt(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): IndexRange;\n\n  /**\n   * Restrict this range to documents where `doc[fieldName] <= value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the same index\n   * field used in the lower bound (`.gt` or `.gte`) or the next field if no\n   * lower bound was specified.\n   * @param value - The value to compare against.\n   */\n  lte(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): IndexRange;\n}\n\n/**\n * An expression representing an index range created by\n * {@link IndexRangeBuilder}.\n * @public\n */\nexport abstract class IndexRange {\n  // Property for nominal type support.\n  private _isIndexRange: undefined;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA8KO,MAAe,WAAW;IAAA;;GAAA,GAO/B,aAAc;QALd,qCAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "debugId": null}}, {"offset": {"line": 1875, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/index_range_builder_impl.ts"], "sourcesContent": ["import { JSONValue, Value } from \"../../values/index.js\";\nimport { convexOrUndefinedToJson } from \"../../values/value.js\";\nimport { GenericDocument, GenericIndexFields } from \"../data_model.js\";\nimport {\n  IndexRange,\n  IndexRangeBuilder,\n  LowerBoundIndexRangeBuilder,\n  UpperBoundIndexRangeBuilder,\n} from \"../index_range_builder.js\";\n\nexport type SerializedRangeExpression = {\n  type: \"Eq\" | \"Gt\" | \"Gte\" | \"Lt\" | \"Lte\";\n  fieldPath: string;\n  value: JSONValue;\n};\n\nexport class IndexRangeBuilderImpl\n  extends IndexRange\n  implements\n    IndexRangeBuilder<GenericDocument, GenericIndexFields>,\n    LowerBoundIndexRangeBuilder<GenericDocument, string>,\n    UpperBoundIndexRangeBuilder<GenericDocument, string>\n{\n  private rangeExpressions: ReadonlyArray<SerializedRangeExpression>;\n  private isConsumed: boolean;\n  private constructor(\n    rangeExpressions: ReadonlyArray<SerializedRangeExpression>,\n  ) {\n    super();\n    this.rangeExpressions = rangeExpressions;\n    this.isConsumed = false;\n  }\n\n  static new(): IndexRangeBuilderImpl {\n    return new IndexRangeBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  eq(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  gt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gt\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n  gte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gte\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n  lt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lt\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n  lte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lte\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.rangeExpressions;\n  }\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,+BAA+B;AAExC;;;;;;;;;;;;AAaO,MAAM,8BACH,uOAAA,CAKV;IAGU,YACN,gBAAA,CACA;QACA,KAAA,CAAM;QALR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAKN,IAAA,CAAK,gBAAA,GAAmB;QACxB,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OAAO,MAA6B;QAClC,OAAO,IAAI,sBAAsB,CAAC,CAAC;IACrC;IAEQ,UAAU;QAChB,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,MAAM,IAAI,MACR;QAEJ;QACA,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,WAAO,sOAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IAEA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,WAAO,sOAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IACA,IAAI,SAAA,EAAmB,KAAA,EAAc;QACnC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,WAAO,sOAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IACA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,WAAO,sOAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IACA,IAAI,SAAA,EAAmB,KAAA,EAAc;QACnC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,WAAO,sOAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IAEA,SAAS;QACP,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAA,CAAK,gBAAA;IACd;AACF", "debugId": null}}, {"offset": {"line": 1958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/search_filter_builder.ts"], "sourcesContent": ["import {\n  FieldType<PERSON><PERSON><PERSON><PERSON>Path,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"./data_model.js\";\n\n/**\n * Builder for defining search filters.\n *\n * A search filter is a chained list of:\n * 1. One search expression constructed with `.search`.\n * 2. Zero or more equality expressions constructed with `.eq`.\n *\n * The search expression must search for text in the index's `searchField`. The\n * filter expressions can use any of the `filterFields` defined in the index.\n *\n * For all other filtering use {@link OrderedQuery.filter}.\n *\n * To learn about full text search, see [Indexes](https://docs.convex.dev/text-search).\n * @public\n */\nexport interface SearchFilterBuilder<\n  Document extends GenericDocument,\n  SearchIndexConfig extends GenericSearchIndexConfig,\n> {\n  /**\n   * Search for the terms in `query` within `doc[fieldName]`.\n   *\n   * This will do a full text search that returns results where any word of of\n   * `query` appears in the field.\n   *\n   * Documents will be returned based on their relevance to the query. This\n   * takes into account:\n   * - How many words in the query appear in the text?\n   * - How many times do they appear?\n   * - How long is the text field?\n   *\n   * @param fieldName - The name of the field to search in. This must be listed\n   * as the index's `searchField`.\n   * @param query - The query text to search for.\n   */\n  search(\n    fieldName: SearchIndexConfig[\"searchField\"],\n    query: string,\n  ): SearchFilterFinalizer<Document, SearchIndexConfig>;\n}\n\n/**\n * Builder to define equality expressions as part of a search filter.\n *\n * See {@link SearchFilterBuilder}.\n *\n * @public\n */\nexport interface SearchFilterFinalizer<\n  Document extends GenericDocument,\n  SearchIndexConfig extends GenericSearchIndexConfig,\n> extends SearchFilter {\n  /**\n   * Restrict this query to documents where `doc[fieldName] === value`.\n   *\n   * @param fieldName - The name of the field to compare. This must be listed in\n   * the search index's `filterFields`.\n   * @param value - The value to compare against.\n   */\n  eq<FieldName extends SearchIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<Document, FieldName>,\n  ): SearchFilterFinalizer<Document, SearchIndexConfig>;\n}\n\n/**\n * An expression representing a search filter created by\n * {@link SearchFilterBuilder}.\n *\n * @public\n */\nexport abstract class SearchFilter {\n  // Property for nominal type support.\n  private _isSearchFilter: undefined;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA6EO,MAAe,aAAa;IAAA;;GAAA,GAOjC,aAAc;QALd,qCAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "debugId": null}}, {"offset": {"line": 1983, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/search_filter_builder_impl.ts"], "sourcesContent": ["import { JSONValue, convexOrUndefinedToJson } from \"../../values/value.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"../data_model.js\";\nimport {\n  SearchFilter,\n  SearchFilterBuilder,\n  SearchFilterFinalizer,\n} from \"../search_filter_builder.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport type SerializedSearchFilter =\n  | {\n      type: \"Search\";\n      fieldPath: string;\n      value: string;\n    }\n  | {\n      type: \"Eq\";\n      fieldPath: string;\n      value: JSONValue;\n    };\n\nexport class SearchFilterBuilderImpl\n  extends SearchFilter\n  implements\n    SearchFilterBuilder<GenericDocument, GenericSearchIndexConfig>,\n    SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig>\n{\n  private filters: ReadonlyArray<SerializedSearchFilter>;\n  private isConsumed: boolean;\n  private constructor(filters: ReadonlyArray<SerializedSearchFilter>) {\n    super();\n    this.filters = filters;\n    this.isConsumed = false;\n  }\n\n  static new(): SearchFilterBuilderImpl {\n    return new SearchFilterBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  search(\n    fieldName: string,\n    query: string,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"search\", \"fieldName\");\n    validateArg(query, 2, \"search\", \"query\");\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Search\",\n        fieldPath: fieldName,\n        value: query,\n      }),\n    );\n  }\n  eq<FieldName extends string>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"eq\", \"fieldName\");\n    // when `undefined` is passed explicitly, it is allowed.\n    if (arguments.length !== 2) {\n      validateArg(value, 2, \"search\", \"value\");\n    }\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.filters;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAoB,+BAA+B;AAMnD;AAKA,SAAS,mBAAmB;;;;;;;;;;;;;AAcrB,MAAM,gCACH,2OAAA,CAIV;IAGU,YAAY,OAAA,CAAgD;QAClE,KAAA,CAAM;QAHR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OAAO,MAA+B;QACpC,OAAO,IAAI,wBAAwB,CAAC,CAAC;IACvC;IAEQ,UAAU;QAChB,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,MAAM,IAAI,MACR;QAEJ;QACA,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OACE,SAAA,EACA,KAAA,EACkE;QAClE,IAAA,qOAAA,EAAY,WAAW,GAAG,UAAU,WAAW;QAC/C,IAAA,qOAAA,EAAY,OAAO,GAAG,UAAU,OAAO;QACvC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,wBACT,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO;YAClB,MAAM;YACN,WAAW;YACX,OAAO;QACT,CAAC;IAEL;IACA,GACE,SAAA,EACA,KAAA,EACkE;QAClE,IAAA,qOAAA,EAAY,WAAW,GAAG,MAAM,WAAW;QAE3C,IAAI,UAAU,MAAA,KAAW,GAAG;YAC1B,IAAA,qOAAA,EAAY,OAAO,GAAG,UAAU,OAAO;QACzC;QACA,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,wBACT,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO;YAClB,MAAM;YACN,WAAW;YACX,WAAO,sOAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IAEA,SAAS;QACP,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAA,CAAK,OAAA;IACd;AACF", "debugId": null}}, {"offset": {"line": 2050, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/query_impl.ts"], "sourcesContent": ["import { Value, JSONValue, jsonToConvex } from \"../../values/index.js\";\nimport { PaginationResult, PaginationOptions } from \"../pagination.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  filterBuilderImpl,\n  serializeExpression,\n} from \"./filter_builder_impl.js\";\nimport { Query, QueryInitializer } from \"../query.js\";\nimport { ExpressionOrValue, FilterBuilder } from \"../filter_builder.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  IndexRangeBuilderImpl,\n  SerializedRangeExpression,\n} from \"./index_range_builder_impl.js\";\nimport {\n  SearchFilterBuilderImpl,\n  SerializedSearchFilter,\n} from \"./search_filter_builder_impl.js\";\nimport { validateArg, validateArgIsNonNegativeInteger } from \"./validate.js\";\nimport { version } from \"../../index.js\";\n\nconst MAX_QUERY_OPERATORS = 256;\n\ntype QueryOperator = { filter: JSONValue } | { limit: number };\ntype Source =\n  | { type: \"FullTableScan\"; tableName: string; order: \"asc\" | \"desc\" | null }\n  | {\n      type: \"IndexRange\";\n      indexName: string;\n      range: ReadonlyArray<SerializedRangeExpression>;\n      order: \"asc\" | \"desc\" | null;\n    }\n  | {\n      type: \"Search\";\n      indexName: string;\n      filters: ReadonlyArray<SerializedSearchFilter>;\n    };\n\ntype SerializedQuery = {\n  source: Source;\n  operators: Array<QueryOperator>;\n};\n\nexport class QueryInitializerImpl\n  implements QueryInitializer<GenericTableInfo>\n{\n  private tableName: string;\n\n  constructor(tableName: string) {\n    this.tableName = tableName;\n  }\n\n  withIndex(\n    indexName: string,\n    indexRange?: (q: IndexRangeBuilderImpl) => IndexRangeBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withIndex\", \"indexName\");\n    let rangeBuilder = IndexRangeBuilderImpl.new();\n    if (indexRange !== undefined) {\n      rangeBuilder = indexRange(rangeBuilder);\n    }\n    return new QueryImpl({\n      source: {\n        type: \"IndexRange\",\n        indexName: this.tableName + \".\" + indexName,\n        range: rangeBuilder.export(),\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  withSearchIndex(\n    indexName: string,\n    searchFilter: (q: SearchFilterBuilderImpl) => SearchFilterBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withSearchIndex\", \"indexName\");\n    validateArg(searchFilter, 2, \"withSearchIndex\", \"searchFilter\");\n    const searchFilterBuilder = SearchFilterBuilderImpl.new();\n    return new QueryImpl({\n      source: {\n        type: \"Search\",\n        indexName: this.tableName + \".\" + indexName,\n        filters: searchFilter(searchFilterBuilder).export(),\n      },\n      operators: [],\n    });\n  }\n\n  fullTableScan(): QueryImpl {\n    return new QueryImpl({\n      source: {\n        type: \"FullTableScan\",\n        tableName: this.tableName,\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    return this.fullTableScan().order(order);\n  }\n\n  // This is internal API and should not be exposed to developers yet.\n  async count(): Promise<number> {\n    const syscallJSON = await performAsyncSyscall(\"1.0/count\", {\n      table: this.tableName,\n    });\n    const syscallResult = jsonToConvex(syscallJSON) as number;\n    return syscallResult;\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ) {\n    return this.fullTableScan().filter(predicate);\n  }\n\n  limit(n: number) {\n    return this.fullTableScan().limit(n);\n  }\n\n  collect(): Promise<any[]> {\n    return this.fullTableScan().collect();\n  }\n\n  take(n: number): Promise<Array<any>> {\n    return this.fullTableScan().take(n);\n  }\n\n  paginate(paginationOpts: PaginationOptions): Promise<PaginationResult<any>> {\n    return this.fullTableScan().paginate(paginationOpts);\n  }\n\n  first(): Promise<any> {\n    return this.fullTableScan().first();\n  }\n\n  unique(): Promise<any> {\n    return this.fullTableScan().unique();\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    return this.fullTableScan()[Symbol.asyncIterator]();\n  }\n}\n\n/**\n * @param type Whether the query was consumed or closed.\n * @throws An error indicating the query has been closed.\n */\nfunction throwClosedError(type: \"closed\" | \"consumed\"): never {\n  throw new Error(\n    type === \"consumed\"\n      ? \"This query is closed and can't emit any more values.\"\n      : \"This query has been chained with another operator and can't be reused.\",\n  );\n}\n\nexport class QueryImpl implements Query<GenericTableInfo> {\n  private state:\n    | { type: \"preparing\"; query: SerializedQuery }\n    | { type: \"executing\"; queryId: number }\n    | { type: \"closed\" }\n    | { type: \"consumed\" };\n  private tableNameForErrorMessages: string;\n\n  constructor(query: SerializedQuery) {\n    this.state = { type: \"preparing\", query };\n    if (query.source.type === \"FullTableScan\") {\n      this.tableNameForErrorMessages = query.source.tableName;\n    } else {\n      this.tableNameForErrorMessages = query.source.indexName.split(\".\")[0];\n    }\n  }\n\n  private takeQuery(): SerializedQuery {\n    if (this.state.type !== \"preparing\") {\n      throw new Error(\n        \"A query can only be chained once and can't be chained after iteration begins.\",\n      );\n    }\n    const query = this.state.query;\n    this.state = { type: \"closed\" };\n    return query;\n  }\n\n  private startQuery(): number {\n    if (this.state.type === \"executing\") {\n      throw new Error(\"Iteration can only begin on a query once.\");\n    }\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    const query = this.state.query;\n    const { queryId } = performSyscall(\"1.0/queryStream\", { query, version });\n    this.state = { type: \"executing\", queryId };\n    return queryId;\n  }\n\n  private closeQuery() {\n    if (this.state.type === \"executing\") {\n      const queryId = this.state.queryId;\n      performSyscall(\"1.0/queryCleanup\", { queryId });\n    }\n    this.state = { type: \"consumed\" };\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    validateArg(order, 1, \"order\", \"order\");\n    const query = this.takeQuery();\n    if (query.source.type === \"Search\") {\n      throw new Error(\n        \"Search queries must always be in relevance order. Can not set order manually.\",\n      );\n    }\n    if (query.source.order !== null) {\n      throw new Error(\"Queries may only specify order at most once\");\n    }\n    query.source.order = order;\n    return new QueryImpl(query);\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ): any {\n    validateArg(predicate, 1, \"filter\", \"predicate\");\n    const query = this.takeQuery();\n    if (query.operators.length >= MAX_QUERY_OPERATORS) {\n      throw new Error(\n        `Can't construct query with more than ${MAX_QUERY_OPERATORS} operators`,\n      );\n    }\n    query.operators.push({\n      filter: serializeExpression(predicate(filterBuilderImpl)),\n    });\n    return new QueryImpl(query);\n  }\n\n  limit(n: number): any {\n    validateArg(n, 1, \"limit\", \"n\");\n    const query = this.takeQuery();\n    query.operators.push({ limit: n });\n    return new QueryImpl(query);\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    this.startQuery();\n    return this;\n  }\n\n  async next(): Promise<IteratorResult<any>> {\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    // Allow calling `.next()` when the query is in \"preparing\" state to implicitly start the\n    // query. This allows the developer to call `.next()` on the query without having to use\n    // a `for await` statement.\n    const queryId =\n      this.state.type === \"preparing\" ? this.startQuery() : this.state.queryId;\n    const { value, done } = await performAsyncSyscall(\"1.0/queryStreamNext\", {\n      queryId,\n    });\n    if (done) {\n      this.closeQuery();\n    }\n    const convexValue = jsonToConvex(value);\n    return { value: convexValue, done };\n  }\n\n  return() {\n    this.closeQuery();\n    return Promise.resolve({ done: true, value: undefined });\n  }\n\n  async paginate(\n    paginationOpts: PaginationOptions,\n  ): Promise<PaginationResult<any>> {\n    validateArg(paginationOpts, 1, \"paginate\", \"options\");\n    if (\n      typeof paginationOpts?.numItems !== \"number\" ||\n      paginationOpts.numItems < 0\n    ) {\n      throw new Error(\n        `\\`options.numItems\\` must be a positive number. Received \\`${paginationOpts?.numItems}\\`.`,\n      );\n    }\n    const query = this.takeQuery();\n    const pageSize = paginationOpts.numItems;\n    const cursor = paginationOpts.cursor;\n    const endCursor = paginationOpts?.endCursor ?? null;\n    const maximumRowsRead = paginationOpts.maximumRowsRead ?? null;\n    const { page, isDone, continueCursor, splitCursor, pageStatus } =\n      await performAsyncSyscall(\"1.0/queryPage\", {\n        query,\n        cursor,\n        endCursor,\n        pageSize,\n        maximumRowsRead,\n        maximumBytesRead: paginationOpts.maximumBytesRead,\n        version,\n      });\n    return {\n      page: page.map((json: string) => jsonToConvex(json)),\n      isDone,\n      continueCursor,\n      splitCursor,\n      pageStatus,\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    const out: Value[] = [];\n    for await (const item of this) {\n      out.push(item);\n    }\n    return out;\n  }\n\n  async take(n: number): Promise<Array<any>> {\n    validateArg(n, 1, \"take\", \"n\");\n    validateArgIsNonNegativeInteger(n, 1, \"take\", \"n\");\n    return this.limit(n).collect();\n  }\n\n  async first(): Promise<any | null> {\n    const first_array = await this.take(1);\n    return first_array.length === 0 ? null : first_array[0];\n  }\n\n  async unique(): Promise<any | null> {\n    const first_two_array = await this.take(2);\n    if (first_two_array.length === 0) {\n      return null;\n    }\n    if (first_two_array.length === 2) {\n      throw new Error(`unique() query returned more than one result from table ${this.tableNameForErrorMessages}:\n [${first_two_array[0]._id}, ${first_two_array[1]._id}, ...]`);\n    }\n    return first_two_array[0];\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,SAA2B,oBAAoB;AAE/C,SAAS,qBAAqB,sBAAsB;AACpD;AAOA;AAIA;AAIA,SAAS,aAAa,uCAAuC;AAC7D,SAAS,eAAe;;;;;;;;;;;;;;;;;AAExB,MAAM,sBAAsB;AAsBrB,MAAM,qBAEb;IAGE,YAAY,SAAA,CAAmB;QAF/B,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,SAAA,GAAY;IACnB;IAEA,UACE,SAAA,EACA,UAAA,EACW;QACX,IAAA,qOAAA,EAAY,WAAW,GAAG,aAAa,WAAW;QAClD,IAAI,eAAe,+PAAA,CAAsB,GAAA,CAAI;QAC7C,IAAI,eAAe,KAAA,GAAW;YAC5B,eAAe,WAAW,YAAY;QACxC;QACA,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA,GAAY,MAAM;gBAClC,OAAO,aAAa,MAAA,CAAO;gBAC3B,OAAO;YACT;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,gBACE,SAAA,EACA,YAAA,EACW;QACX,IAAA,qOAAA,EAAY,WAAW,GAAG,mBAAmB,WAAW;QACxD,IAAA,qOAAA,EAAY,cAAc,GAAG,mBAAmB,cAAc;QAC9D,MAAM,sBAAsB,mQAAA,CAAwB,GAAA,CAAI;QACxD,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA,GAAY,MAAM;gBAClC,SAAS,aAAa,mBAAmB,EAAE,MAAA,CAAO;YACpD;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,gBAA2B;QACzB,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA;gBAChB,OAAO;YACT;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,MAAM,KAAA,EAAkC;QACtC,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM,KAAK;IACzC;IAAA,oEAAA;IAGA,MAAM,QAAyB;QAC7B,MAAM,cAAc,UAAM,4OAAA,EAAoB,aAAa;YACzD,OAAO,IAAA,CAAK,SAAA;QACd,CAAC;QACD,MAAM,gBAAgB,+NAAA,EAAa,WAAW;QAC9C,OAAO;IACT;IAEA,OACE,SAAA,EAGA;QACA,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,MAAA,CAAO,SAAS;IAC9C;IAEA,MAAM,CAAA,EAAW;QACf,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM,CAAC;IACrC;IAEA,UAA0B;QACxB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,OAAA,CAAQ;IACtC;IAEA,KAAK,CAAA,EAAgC;QACnC,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,IAAA,CAAK,CAAC;IACpC;IAEA,SAAS,cAAA,EAAmE;QAC1E,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,QAAA,CAAS,cAAc;IACrD;IAEA,QAAsB;QACpB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM;IACpC;IAEA,SAAuB;QACrB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,MAAA,CAAO;IACrC;IAEA,CAAC,OAAO,aAAa,CAAA,GAAgC;QACnD,OAAO,IAAA,CAAK,aAAA,CAAc,CAAA,CAAE,OAAO,aAAa,CAAA,CAAE;IACpD;AACF;AAMA,SAAS,iBAAiB,IAAA,EAAoC;IAC5D,MAAM,IAAI,MACR,SAAS,aACL,yDACA;AAER;AAEO,MAAM,UAA6C;IAQxD,YAAY,KAAA,CAAwB;QAPpC,cAAA,IAAA,EAAQ;QAKR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;YAAa;QAAM;QACxC,IAAI,MAAM,MAAA,CAAO,IAAA,KAAS,iBAAiB;YACzC,IAAA,CAAK,yBAAA,GAA4B,MAAM,MAAA,CAAO,SAAA;QAChD,OAAO;YACL,IAAA,CAAK,yBAAA,GAA4B,MAAM,MAAA,CAAO,SAAA,CAAU,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA;QACtE;IACF;IAEQ,YAA6B;QACnC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,IAAI,MACR;QAEJ;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAS;QAC9B,OAAO;IACT;IAEQ,aAAqB;QAC3B,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,IAAI,MAAM,2CAA2C;QAC7D;QACA,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClE,iBAAiB,IAAA,CAAK,KAAA,CAAM,IAAI;QAClC;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,MAAM,EAAE,OAAA,CAAQ,CAAA,OAAI,uOAAA,EAAe,mBAAmB;YAAE;qBAAO,4MAAA;QAAQ,CAAC;QACxE,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;YAAa;QAAQ;QAC1C,OAAO;IACT;IAEQ,aAAa;QACnB,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,UAAU,IAAA,CAAK,KAAA,CAAM,OAAA;YAC3B,IAAA,uOAAA,EAAe,oBAAoB;gBAAE;YAAQ,CAAC;QAChD;QACA,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAW;IAClC;IAEA,MAAM,KAAA,EAAkC;QACtC,IAAA,qOAAA,EAAY,OAAO,GAAG,SAAS,OAAO;QACtC,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,IAAI,MAAM,MAAA,CAAO,IAAA,KAAS,UAAU;YAClC,MAAM,IAAI,MACR;QAEJ;QACA,IAAI,MAAM,MAAA,CAAO,KAAA,KAAU,MAAM;YAC/B,MAAM,IAAI,MAAM,6CAA6C;QAC/D;QACA,MAAM,MAAA,CAAO,KAAA,GAAQ;QACrB,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,OACE,SAAA,EAGK;QACL,IAAA,qOAAA,EAAY,WAAW,GAAG,UAAU,WAAW;QAC/C,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,IAAI,MAAM,SAAA,CAAU,MAAA,IAAU,qBAAqB;YACjD,MAAM,IAAI,MACR,CAAA,qCAAA,EAAwC,mBAAmB,CAAA,UAAA,CAAA;QAE/D;QACA,MAAM,SAAA,CAAU,IAAA,CAAK;YACnB,YAAQ,wPAAA,EAAoB,UAAU,sPAAiB,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,MAAM,CAAA,EAAgB;QACpB,IAAA,qOAAA,EAAY,GAAG,GAAG,SAAS,GAAG;QAC9B,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,MAAM,SAAA,CAAU,IAAA,CAAK;YAAE,OAAO;QAAE,CAAC;QACjC,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,CAAC,OAAO,aAAa,CAAA,GAAgC;QACnD,IAAA,CAAK,UAAA,CAAW;QAChB,OAAO,IAAA;IACT;IAEA,MAAM,OAAqC;QACzC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClE,iBAAiB,IAAA,CAAK,KAAA,CAAM,IAAI;QAClC;QAIA,MAAM,UACJ,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,cAAc,IAAA,CAAK,UAAA,CAAW,IAAI,IAAA,CAAK,KAAA,CAAM,OAAA;QACnE,MAAM,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,GAAI,UAAM,4OAAA,EAAoB,uBAAuB;YACvE;QACF,CAAC;QACD,IAAI,MAAM;YACR,IAAA,CAAK,UAAA,CAAW;QAClB;QACA,MAAM,kBAAc,2NAAA,EAAa,KAAK;QACtC,OAAO;YAAE,OAAO;YAAa;QAAK;IACpC;IAEA,SAAS;QACP,IAAA,CAAK,UAAA,CAAW;QAChB,OAAO,QAAQ,OAAA,CAAQ;YAAE,MAAM;YAAM,OAAO,KAAA;QAAU,CAAC;IACzD;IAEA,MAAM,SACJ,cAAA,EACgC;QAChC,IAAA,qOAAA,EAAY,gBAAgB,GAAG,YAAY,SAAS;QACpD,IACE,OAAO,gBAAgB,aAAa,YACpC,eAAe,QAAA,GAAW,GAC1B;YACA,MAAM,IAAI,MACR,CAAA,2DAAA,EAA8D,gBAAgB,QAAQ,CAAA,GAAA,CAAA;QAE1F;QACA,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,MAAM,WAAW,eAAe,QAAA;QAChC,MAAM,SAAS,eAAe,MAAA;QAC9B,MAAM,YAAY,gBAAgB,aAAa;QAC/C,MAAM,kBAAkB,eAAe,eAAA,IAAmB;QAC1D,MAAM,EAAE,IAAA,EAAM,MAAA,EAAQ,cAAA,EAAgB,WAAA,EAAa,UAAA,CAAW,CAAA,GAC5D,MAAM,gPAAA,EAAoB,iBAAiB;YACzC;YACA;YACA;YACA;YACA;YACA,kBAAkB,eAAe,gBAAA;qBACjC,4MAAA;QACF,CAAC;QACH,OAAO;YACL,MAAM,KAAK,GAAA,CAAI,CAAC,WAAiB,2NAAA,EAAa,IAAI,CAAC;YACnD;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,UAA+B;QACnC,MAAM,MAAe,CAAC,CAAA;QACtB,WAAA,MAAiB,QAAQ,IAAA,CAAM;YAC7B,IAAI,IAAA,CAAK,IAAI;QACf;QACA,OAAO;IACT;IAEA,MAAM,KAAK,CAAA,EAAgC;QACzC,IAAA,qOAAA,EAAY,GAAG,GAAG,QAAQ,GAAG;QAC7B,IAAA,yPAAA,EAAgC,GAAG,GAAG,QAAQ,GAAG;QACjD,OAAO,IAAA,CAAK,KAAA,CAAM,CAAC,EAAE,OAAA,CAAQ;IAC/B;IAEA,MAAM,QAA6B;QACjC,MAAM,cAAc,MAAM,IAAA,CAAK,IAAA,CAAK,CAAC;QACrC,OAAO,YAAY,MAAA,KAAW,IAAI,OAAO,WAAA,CAAY,CAAC,CAAA;IACxD;IAEA,MAAM,SAA8B;QAClC,MAAM,kBAAkB,MAAM,IAAA,CAAK,IAAA,CAAK,CAAC;QACzC,IAAI,gBAAgB,MAAA,KAAW,GAAG;YAChC,OAAO;QACT;QACA,IAAI,gBAAgB,MAAA,KAAW,GAAG;YAChC,MAAM,IAAI,MAAM,CAAA,wDAAA,EAA2D,IAAA,CAAK,yBAAyB,CAAA;EAAA,EAC3G,eAAA,CAAgB,CAAC,CAAA,CAAE,GAAG,CAAA,EAAA,EAAK,eAAA,CAAgB,CAAC,CAAA,CAAE,GAAG,CAAA,MAAA,CAAQ;QACzD;QACA,OAAO,eAAA,CAAgB,CAAC,CAAA;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 2335, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/database_impl.ts"], "sourcesContent": ["import {\n  convexTo<PERSON><PERSON>,\n  GenericId,\n  jsonToConvex,\n  Value,\n} from \"../../values/index.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  GenericDatabaseReader,\n  GenericDatabaseReaderWithTable,\n  GenericDatabaseWriter,\n  GenericDatabaseWriterWithTable,\n} from \"../database.js\";\nimport { QueryInitializerImpl } from \"./query_impl.js\";\nimport { GenericDataModel, GenericDocument } from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { version } from \"../../index.js\";\nimport { patchValueToJson } from \"../../values/value.js\";\n\nasync function get(\n  table: string | undefined,\n  id: GenericId<string>,\n  isSystem: boolean,\n) {\n  // If the user doesn’t provide any arguments, we use the new signature in the error message.\n  // We don’t do argument validation on the table argument since it’s not provided when using the old signature.\n  validateArg(id, 1, \"get\", \"id\");\n  if (typeof id !== \"string\") {\n    throw new Error(\n      `Invalid argument \\`id\\` for \\`db.get\\`, expected string but got '${typeof id}': ${\n        id as any\n      }`,\n    );\n  }\n  const args = {\n    id: convexToJson(id),\n    isSystem,\n    version,\n    table,\n  };\n  const syscallJSON = await performAsyncSyscall(\"1.0/get\", args);\n\n  return jsonToConvex(syscallJSON) as GenericDocument;\n}\n\nexport function setupReader(): GenericDatabaseReader<GenericDataModel> {\n  const reader = (\n    isSystem = false,\n  ): GenericDatabaseReader<GenericDataModel> &\n    GenericDatabaseReaderWithTable<GenericDataModel> => {\n    return {\n      get: async (arg0: any, arg1?: any) => {\n        return arg1 !== undefined\n          ? await get(arg0, arg1, isSystem)\n          : await get(undefined, arg0, isSystem);\n      },\n      query: (tableName: string) => {\n        return new TableReader(tableName, isSystem).query();\n      },\n      normalizeId: <TableName extends string>(\n        tableName: TableName,\n        id: string,\n      ): GenericId<TableName> | null => {\n        validateArg(tableName, 1, \"normalizeId\", \"tableName\");\n        validateArg(id, 2, \"normalizeId\", \"id\");\n        const accessingSystemTable = tableName.startsWith(\"_\");\n        if (accessingSystemTable !== isSystem) {\n          throw new Error(\n            `${\n              accessingSystemTable ? \"System\" : \"User\"\n            } tables can only be accessed from db.${\n              isSystem ? \"\" : \"system.\"\n            }normalizeId().`,\n          );\n        }\n        const syscallJSON = performSyscall(\"1.0/db/normalizeId\", {\n          table: tableName,\n          idString: id,\n        });\n        const syscallResult = jsonToConvex(syscallJSON) as any;\n        return syscallResult.id;\n      },\n      // We set the system reader on the next line\n      system: null as any,\n      table: (tableName) => {\n        return new TableReader(tableName, isSystem);\n      },\n    };\n  };\n  const { system: _, ...rest } = reader(true);\n  const r = reader();\n  r.system = rest as any;\n  return r;\n}\n\nasync function insert(tableName: string, value: any) {\n  if (tableName.startsWith(\"_\")) {\n    throw new Error(\"System tables (prefixed with `_`) are read-only.\");\n  }\n  validateArg(tableName, 1, \"insert\", \"table\");\n  validateArg(value, 2, \"insert\", \"value\");\n  const syscallJSON = await performAsyncSyscall(\"1.0/insert\", {\n    table: tableName,\n    value: convexToJson(value),\n  });\n  const syscallResult = jsonToConvex(syscallJSON) as any;\n  return syscallResult._id;\n}\n\nasync function patch(table: string | undefined, id: any, value: any) {\n  validateArg(id, 1, \"patch\", \"id\");\n  validateArg(value, 2, \"patch\", \"value\");\n  await performAsyncSyscall(\"1.0/shallowMerge\", {\n    id: convexToJson(id),\n    value: patchValueToJson(value as Value),\n    table,\n  });\n}\n\nasync function replace(table: string | undefined, id: any, value: any) {\n  validateArg(id, 1, \"replace\", \"id\");\n  validateArg(value, 2, \"replace\", \"value\");\n  await performAsyncSyscall(\"1.0/replace\", {\n    id: convexToJson(id),\n    value: convexToJson(value),\n    table,\n  });\n}\n\nasync function delete_(table: string | undefined, id: any) {\n  validateArg(id, 1, \"delete\", \"id\");\n  await performAsyncSyscall(\"1.0/remove\", {\n    id: convexToJson(id),\n    table,\n  });\n}\n\nexport function setupWriter(): GenericDatabaseWriter<GenericDataModel> &\n  GenericDatabaseWriterWithTable<GenericDataModel> {\n  const reader = setupReader();\n  return {\n    get: reader.get,\n    query: reader.query,\n    normalizeId: reader.normalizeId,\n    system: reader.system as any,\n    insert: async (table, value) => {\n      return await insert(table, value);\n    },\n    patch: async (arg0: any, arg1: any, arg2?: any) => {\n      return arg2 !== undefined\n        ? await patch(arg0, arg1, arg2)\n        : await patch(undefined, arg0, arg1);\n    },\n    replace: async (arg0: any, arg1: any, arg2?: any) => {\n      return arg2 !== undefined\n        ? await replace(arg0, arg1, arg2)\n        : await replace(undefined, arg0, arg1);\n    },\n    delete: async (arg0: any, arg1?: any) => {\n      return arg1 !== undefined\n        ? await delete_(arg0, arg1)\n        : await delete_(undefined, arg0);\n    },\n    table: (tableName) => {\n      return new TableWriter(tableName, false);\n    },\n  };\n}\n\nclass TableReader {\n  constructor(\n    protected readonly tableName: string,\n    protected readonly isSystem: boolean,\n  ) {}\n\n  async get(id: GenericId<string>) {\n    return get(this.tableName, id, this.isSystem);\n  }\n\n  query() {\n    const accessingSystemTable = this.tableName.startsWith(\"_\");\n    if (accessingSystemTable !== this.isSystem) {\n      throw new Error(\n        `${\n          accessingSystemTable ? \"System\" : \"User\"\n        } tables can only be accessed from db.${\n          this.isSystem ? \"\" : \"system.\"\n        }query().`,\n      );\n    }\n    return new QueryInitializerImpl(this.tableName);\n  }\n}\n\nclass TableWriter extends TableReader {\n  async insert(value: any) {\n    return insert(this.tableName, value);\n  }\n  async patch(id: any, value: any) {\n    return patch(this.tableName, id, value);\n  }\n  async replace(id: any, value: any) {\n    return replace(this.tableName, id, value);\n  }\n  async delete(id: any) {\n    return delete_(this.tableName, id);\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAMA,SAAS,qBAAqB,sBAAsB;AAOpD,SAAS,4BAA4B;AAErC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;;;;;;;;AAGxB,eAAe,IACb,KAAA,EACA,EAAA,EACA,QAAA,EACA;IAGA,IAAA,qOAAA,EAAY,IAAI,GAAG,OAAO,IAAI;IAC9B,IAAI,OAAO,OAAO,UAAU;QAC1B,MAAM,IAAI,MACR,CAAA,iEAAA,EAAoE,OAAO,EAAE,CAAA,GAAA,EAC3E,EACF,EAAA;IAEJ;IACA,MAAM,OAAO;QACX,QAAI,2NAAA,EAAa,EAAE;QACnB;iBACA,4MAAA;QACA;IACF;IACA,MAAM,cAAc,UAAM,4OAAA,EAAoB,WAAW,IAAI;IAE7D,OAAO,+NAAA,EAAa,WAAW;AACjC;AAEO,SAAS,cAAuD;IACrE,MAAM,SAAS,CACb,WAAW,KAAA,KAEyC;QACpD,OAAO;YACL,KAAK,OAAO,MAAW,SAAe;gBACpC,OAAO,SAAS,KAAA,IACZ,MAAM,IAAI,MAAM,MAAM,QAAQ,IAC9B,MAAM,IAAI,KAAA,GAAW,MAAM,QAAQ;YACzC;YACA,OAAO,CAAC,cAAsB;gBAC5B,OAAO,IAAI,YAAY,WAAW,QAAQ,EAAE,KAAA,CAAM;YACpD;YACA,aAAa,CACX,WACA,OACgC;gBAChC,IAAA,qOAAA,EAAY,WAAW,GAAG,eAAe,WAAW;gBACpD,IAAA,qOAAA,EAAY,IAAI,GAAG,eAAe,IAAI;gBACtC,MAAM,uBAAuB,UAAU,UAAA,CAAW,GAAG;gBACrD,IAAI,yBAAyB,UAAU;oBACrC,MAAM,IAAI,MACR,GACE,uBAAuB,WAAW,MACpC,CAAA,qCAAA,EACE,WAAW,KAAK,SAClB,CAAA,cAAA,CAAA;gBAEJ;gBACA,MAAM,kBAAc,uOAAA,EAAe,sBAAsB;oBACvD,OAAO;oBACP,UAAU;gBACZ,CAAC;gBACD,MAAM,oBAAgB,2NAAA,EAAa,WAAW;gBAC9C,OAAO,cAAc,EAAA;YACvB;YAAA,4CAAA;YAEA,QAAQ;YACR,OAAO,CAAC,cAAc;gBACpB,OAAO,IAAI,YAAY,WAAW,QAAQ;YAC5C;QACF;IACF;IACA,MAAM,EAAE,QAAQ,CAAA,EAAG,GAAG,KAAK,CAAA,GAAI,OAAO,IAAI;IAC1C,MAAM,IAAI,OAAO;IACjB,EAAE,MAAA,GAAS;IACX,OAAO;AACT;AAEA,eAAe,OAAO,SAAA,EAAmB,KAAA,EAAY;IACnD,IAAI,UAAU,UAAA,CAAW,GAAG,GAAG;QAC7B,MAAM,IAAI,MAAM,kDAAkD;IACpE;IACA,IAAA,qOAAA,EAAY,WAAW,GAAG,UAAU,OAAO;IAC3C,IAAA,qOAAA,EAAY,OAAO,GAAG,UAAU,OAAO;IACvC,MAAM,cAAc,UAAM,4OAAA,EAAoB,cAAc;QAC1D,OAAO;QACP,WAAO,2NAAA,EAAa,KAAK;IAC3B,CAAC;IACD,MAAM,oBAAgB,2NAAA,EAAa,WAAW;IAC9C,OAAO,cAAc,GAAA;AACvB;AAEA,eAAe,MAAM,KAAA,EAA2B,EAAA,EAAS,KAAA,EAAY;IACnE,IAAA,qOAAA,EAAY,IAAI,GAAG,SAAS,IAAI;IAChC,IAAA,qOAAA,EAAY,OAAO,GAAG,SAAS,OAAO;IACtC,UAAM,4OAAA,EAAoB,oBAAoB;QAC5C,QAAI,2NAAA,EAAa,EAAE;QACnB,WAAO,+NAAA,EAAiB,KAAc;QACtC;IACF,CAAC;AACH;AAEA,eAAe,QAAQ,KAAA,EAA2B,EAAA,EAAS,KAAA,EAAY;IACrE,IAAA,qOAAA,EAAY,IAAI,GAAG,WAAW,IAAI;IAClC,IAAA,qOAAA,EAAY,OAAO,GAAG,WAAW,OAAO;IACxC,UAAM,4OAAA,EAAoB,eAAe;QACvC,QAAI,2NAAA,EAAa,EAAE;QACnB,WAAO,2NAAA,EAAa,KAAK;QACzB;IACF,CAAC;AACH;AAEA,eAAe,QAAQ,KAAA,EAA2B,EAAA,EAAS;IACzD,IAAA,qOAAA,EAAY,IAAI,GAAG,UAAU,IAAI;IACjC,UAAM,4OAAA,EAAoB,cAAc;QACtC,QAAI,2NAAA,EAAa,EAAE;QACnB;IACF,CAAC;AACH;AAEO,SAAS,cACmC;IACjD,MAAM,SAAS,YAAY;IAC3B,OAAO;QACL,KAAK,OAAO,GAAA;QACZ,OAAO,OAAO,KAAA;QACd,aAAa,OAAO,WAAA;QACpB,QAAQ,OAAO,MAAA;QACf,QAAQ,OAAO,OAAO,UAAU;YAC9B,OAAO,MAAM,OAAO,OAAO,KAAK;QAClC;QACA,OAAO,OAAO,MAAW,MAAW,SAAe;YACjD,OAAO,SAAS,KAAA,IACZ,MAAM,MAAM,MAAM,MAAM,IAAI,IAC5B,MAAM,MAAM,KAAA,GAAW,MAAM,IAAI;QACvC;QACA,SAAS,OAAO,MAAW,MAAW,SAAe;YACnD,OAAO,SAAS,KAAA,IACZ,MAAM,QAAQ,MAAM,MAAM,IAAI,IAC9B,MAAM,QAAQ,KAAA,GAAW,MAAM,IAAI;QACzC;QACA,QAAQ,OAAO,MAAW,SAAe;YACvC,OAAO,SAAS,KAAA,IACZ,MAAM,QAAQ,MAAM,IAAI,IACxB,MAAM,QAAQ,KAAA,GAAW,IAAI;QACnC;QACA,OAAO,CAAC,cAAc;YACpB,OAAO,IAAI,YAAY,WAAW,KAAK;QACzC;IACF;AACF;AAEA,MAAM,YAAY;IAChB,YACqB,SAAA,EACA,QAAA,CACnB;QAFmB,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;IAClB;IAEH,MAAM,IAAI,EAAA,EAAuB;QAC/B,OAAO,IAAI,IAAA,CAAK,SAAA,EAAW,IAAI,IAAA,CAAK,QAAQ;IAC9C;IAEA,QAAQ;QACN,MAAM,uBAAuB,IAAA,CAAK,SAAA,CAAU,UAAA,CAAW,GAAG;QAC1D,IAAI,yBAAyB,IAAA,CAAK,QAAA,EAAU;YAC1C,MAAM,IAAI,MACR,GACE,uBAAuB,WAAW,MACpC,CAAA,qCAAA,EACE,IAAA,CAAK,QAAA,GAAW,KAAK,SACvB,CAAA,QAAA,CAAA;QAEJ;QACA,OAAO,IAAI,gPAAA,CAAqB,IAAA,CAAK,SAAS;IAChD;AACF;AAEA,MAAM,oBAAoB,YAAY;IACpC,MAAM,OAAO,KAAA,EAAY;QACvB,OAAO,OAAO,IAAA,CAAK,SAAA,EAAW,KAAK;IACrC;IACA,MAAM,MAAM,EAAA,EAAS,KAAA,EAAY;QAC/B,OAAO,MAAM,IAAA,CAAK,SAAA,EAAW,IAAI,KAAK;IACxC;IACA,MAAM,QAAQ,EAAA,EAAS,KAAA,EAAY;QACjC,OAAO,QAAQ,IAAA,CAAK,SAAA,EAAW,IAAI,KAAK;IAC1C;IACA,MAAM,OAAO,EAAA,EAAS;QACpB,OAAO,QAAQ,IAAA,CAAK,SAAA,EAAW,EAAE;IACnC;AACF", "debugId": null}}, {"offset": {"line": 2499, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/scheduler_impl.ts"], "sourcesContent": ["import { convexToJson, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { SchedulableFunctionReference, Scheduler } from \"../scheduler.js\";\nimport { Id } from \"../../values/value.js\";\nimport { validateArg } from \"./validate.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nexport function setupMutationScheduler(): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAfterSyscallArgs(delayMs, functionReference, args);\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAtSyscallArgs(\n        ms_since_epoch_or_date,\n        functionReference,\n        args,\n      );\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const args = { id: convexToJson(id) };\n      await performAsyncSyscall(\"1.0/cancel_job\", args);\n    },\n  };\n}\n\nexport function setupActionScheduler(requestId: string): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAfterSyscallArgs(delayMs, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAtSyscallArgs(ms_since_epoch_or_date, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const syscallArgs = { id: convexToJson(id) };\n      return await performAsyncSyscall(\"1.0/actions/cancel_job\", syscallArgs);\n    },\n  };\n}\n\nfunction runAfterSyscallArgs(\n  delayMs: number,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  if (typeof delayMs !== \"number\") {\n    throw new Error(\"`delayMs` must be a number\");\n  }\n  if (!isFinite(delayMs)) {\n    throw new Error(\"`delayMs` must be a finite number\");\n  }\n  if (delayMs < 0) {\n    throw new Error(\"`delayMs` must be non-negative\");\n  }\n  const functionArgs = parseArgs(args);\n  const address = getFunctionAddress(functionReference);\n  // Note the syscall expects a unix timestamp, measured in seconds.\n  const ts = (Date.now() + delayMs) / 1000.0;\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n\nfunction runAtSyscallArgs(\n  ms_since_epoch_or_date: number | Date,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  let ts;\n  if (ms_since_epoch_or_date instanceof Date) {\n    ts = ms_since_epoch_or_date.valueOf() / 1000.0;\n  } else if (typeof ms_since_epoch_or_date === \"number\") {\n    // The timestamp the developer passes is in milliseconds, while the syscall\n    // accepts seconds since the epoch.\n    ts = ms_since_epoch_or_date / 1000;\n  } else {\n    throw new Error(\"The invoke time must a Date or a timestamp\");\n  }\n  const address = getFunctionAddress(functionReference);\n  const functionArgs = parseArgs(args);\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAAA,SAAS,oBAA2B;;AACpC,SAAS,eAAe;AACxB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB;AAG1B,SAAS,mBAAmB;AAC5B,SAAS,0BAA0B;;;;;;;;AAE5B,SAAS,yBAAoC;IAClD,OAAO;QACL,UAAU,OACR,SACA,mBACA,SACG;YACH,MAAM,cAAc,oBAAoB,SAAS,mBAAmB,IAAI;YACxE,OAAO,UAAM,4OAAA,EAAoB,gBAAgB,WAAW;QAC9D;QACA,OAAO,OACL,wBACA,mBACA,SACG;YACH,MAAM,cAAc,iBAClB,wBACA,mBACA;YAEF,OAAO,UAAM,4OAAA,EAAoB,gBAAgB,WAAW;QAC9D;QACA,QAAQ,OAAO,OAAmC;YAChD,IAAA,qOAAA,EAAY,IAAI,GAAG,UAAU,IAAI;YACjC,MAAM,OAAO;gBAAE,QAAI,2NAAA,EAAa,EAAE;YAAE;YACpC,UAAM,4OAAA,EAAoB,kBAAkB,IAAI;QAClD;IACF;AACF;AAEO,SAAS,qBAAqB,SAAA,EAA8B;IACjE,OAAO;QACL,UAAU,OACR,SACA,mBACA,SACG;YACH,MAAM,cAAc;gBAClB;gBACA,GAAG,oBAAoB,SAAS,mBAAmB,IAAI,CAAA;YACzD;YACA,OAAO,UAAM,4OAAA,EAAoB,wBAAwB,WAAW;QACtE;QACA,OAAO,OACL,wBACA,mBACA,SACG;YACH,MAAM,cAAc;gBAClB;gBACA,GAAG,iBAAiB,wBAAwB,mBAAmB,IAAI,CAAA;YACrE;YACA,OAAO,UAAM,4OAAA,EAAoB,wBAAwB,WAAW;QACtE;QACA,QAAQ,OAAO,OAAmC;YAChD,IAAA,qOAAA,EAAY,IAAI,GAAG,UAAU,IAAI;YACjC,MAAM,cAAc;gBAAE,QAAI,2NAAA,EAAa,EAAE;YAAE;YAC3C,OAAO,UAAM,4OAAA,EAAoB,0BAA0B,WAAW;QACxE;IACF;AACF;AAEA,SAAS,oBACP,OAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,IAAI,OAAO,YAAY,UAAU;QAC/B,MAAM,IAAI,MAAM,4BAA4B;IAC9C;IACA,IAAI,CAAC,SAAS,OAAO,GAAG;QACtB,MAAM,IAAI,MAAM,mCAAmC;IACrD;IACA,IAAI,UAAU,GAAG;QACf,MAAM,IAAI,MAAM,gCAAgC;IAClD;IACA,MAAM,mBAAe,wNAAA,EAAU,IAAI;IACnC,MAAM,cAAU,+OAAA,EAAmB,iBAAiB;IAEpD,MAAM,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,OAAA,IAAW;IACpC,OAAO;QACL,GAAG,OAAA;QACH;QACA,UAAM,2NAAA,EAAa,YAAY;iBAC/B,4MAAA;IACF;AACF;AAEA,SAAS,iBACP,sBAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,IAAI;IACJ,IAAI,kCAAkC,MAAM;QAC1C,KAAK,uBAAuB,OAAA,CAAQ,IAAI;IAC1C,OAAA,IAAW,OAAO,2BAA2B,UAAU;QAGrD,KAAK,yBAAyB;IAChC,OAAO;QACL,MAAM,IAAI,MAAM,4CAA4C;IAC9D;IACA,MAAM,cAAU,+OAAA,EAAmB,iBAAiB;IACpD,MAAM,mBAAe,wNAAA,EAAU,IAAI;IACnC,OAAO;QACL,GAAG,OAAA;QACH;QACA,UAAM,2NAAA,EAAa,YAAY;iBAC/B,4MAAA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2605, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/storage_impl.ts"], "sourcesContent": ["import {\n  FileMetadata,\n  StorageActionWriter,\n  FileStorageId,\n  StorageReader,\n  StorageWriter,\n} from \"../storage.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall, performJsSyscall } from \"./syscall.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport function setupStorageReader(requestId: string): StorageReader {\n  return {\n    getUrl: async (storageId: FileStorageId) => {\n      validateArg(storageId, 1, \"getUrl\", \"storageId\");\n      return await performAsyncSyscall(\"1.0/storageGetUrl\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getMetadata: async (storageId: FileStorageId): Promise<FileMetadata> => {\n      return await performAsyncSyscall(\"1.0/storageGetMetadata\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n\nexport function setupStorageWriter(requestId: string): StorageWriter {\n  const reader = setupStorageReader(requestId);\n  return {\n    generateUploadUrl: async () => {\n      return await performAsyncSyscall(\"1.0/storageGenerateUploadUrl\", {\n        requestId,\n        version,\n      });\n    },\n    delete: async (storageId: FileStorageId) => {\n      await performAsyncSyscall(\"1.0/storageDelete\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getUrl: reader.getUrl,\n    getMetadata: reader.getMetadata,\n  };\n}\n\nexport function setupStorageActionWriter(\n  requestId: string,\n): StorageActionWriter {\n  const writer = setupStorageWriter(requestId);\n  return {\n    ...writer,\n    store: async (blob: Blob, options?: { sha256?: string }) => {\n      return await performJsSyscall(\"storage/storeBlob\", {\n        requestId,\n        version,\n        blob,\n        options,\n      });\n    },\n    get: async (storageId: FileStorageId) => {\n      return await performJsSyscall(\"storage/getBlob\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAOA,SAAS,eAAe;AACxB,SAAS,qBAAqB,wBAAwB;AACtD,SAAS,mBAAmB;;;;;AAErB,SAAS,mBAAmB,SAAA,EAAkC;IACnE,OAAO;QACL,QAAQ,OAAO,cAA6B;YAC1C,IAAA,qOAAA,EAAY,WAAW,GAAG,UAAU,WAAW;YAC/C,OAAO,UAAM,4OAAA,EAAoB,qBAAqB;gBACpD;yBACA,4MAAA;gBACA;YACF,CAAC;QACH;QACA,aAAa,OAAO,cAAoD;YACtE,OAAO,UAAM,4OAAA,EAAoB,0BAA0B;gBACzD;yBACA,4MAAA;gBACA;YACF,CAAC;QACH;IACF;AACF;AAEO,SAAS,mBAAmB,SAAA,EAAkC;IACnE,MAAM,SAAS,mBAAmB,SAAS;IAC3C,OAAO;QACL,mBAAmB,YAAY;YAC7B,OAAO,UAAM,4OAAA,EAAoB,gCAAgC;gBAC/D;yBACA,4MAAA;YACF,CAAC;QACH;QACA,QAAQ,OAAO,cAA6B;YAC1C,UAAM,4OAAA,EAAoB,qBAAqB;gBAC7C;yBACA,4MAAA;gBACA;YACF,CAAC;QACH;QACA,QAAQ,OAAO,MAAA;QACf,aAAa,OAAO,WAAA;IACtB;AACF;AAEO,SAAS,yBACd,SAAA,EACqB;IACrB,MAAM,SAAS,mBAAmB,SAAS;IAC3C,OAAO;QACL,GAAG,MAAA;QACH,OAAO,OAAO,MAAY,YAAkC;YAC1D,OAAO,UAAM,yOAAA,EAAiB,qBAAqB;gBACjD;yBACA,4MAAA;gBACA;gBACA;YACF,CAAC;QACH;QACA,KAAK,OAAO,cAA6B;YACvC,OAAO,UAAM,yOAAA,EAAiB,mBAAmB;gBAC/C;yBACA,4MAAA;gBACA;YACF,CAAC;QACH;IACF;AACF", "debugId": null}}, {"offset": {"line": 2684, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/impl/registration_impl.ts"], "sourcesContent": ["import {\n  ConvexError,\n  convexTo<PERSON>son,\n  GenericValidator,\n  jsonToConvex,\n  v,\n  Validator,\n  Value,\n} from \"../../values/index.js\";\nimport { GenericDataModel } from \"../data_model.js\";\nimport {\n  ActionBuilder,\n  DefaultFunctionArgs,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericQueryCtx,\n  MutationBuilder,\n  PublicHttpAction,\n  QueryBuilder,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"../registration.js\";\nimport { setupActionCalls } from \"./actions_impl.js\";\nimport { setupActionVectorSearch } from \"./vector_search_impl.js\";\nimport { setupAuth } from \"./authentication_impl.js\";\nimport { setupReader, setupWriter } from \"./database_impl.js\";\nimport { QueryImpl, QueryInitializerImpl } from \"./query_impl.js\";\nimport {\n  setupActionScheduler,\n  setupMutationScheduler,\n} from \"./scheduler_impl.js\";\nimport {\n  setupStorageActionWriter,\n  setupStorageReader,\n  setupStorageWriter,\n} from \"./storage_impl.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { asObjectValidator } from \"../../values/validator.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nasync function invokeMutation<\n  F extends (ctx: GenericMutationCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const mutationCtx = {\n    db: setupWriter(),\n    auth: setupAuth(requestId),\n    storage: setupStorageWriter(requestId),\n    scheduler: setupMutationScheduler(),\n\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n    runMutation: (reference: any, args?: any) =>\n      runUdf(\"mutation\", reference, args),\n  };\n  const result = await invokeFunction(func, mutationCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\nexport function validateReturnValue(v: any) {\n  if (v instanceof QueryInitializerImpl || v instanceof QueryImpl) {\n    throw new Error(\n      \"Return value is a Query. Results must be retrieved with `.collect()`, `.take(n), `.unique()`, or `.first()`.\",\n    );\n  }\n}\n\nexport async function invokeFunction<\n  Ctx,\n  Args extends any[],\n  F extends (ctx: Ctx, ...args: Args) => any,\n>(func: F, ctx: Ctx, args: Args) {\n  let result;\n  try {\n    result = await Promise.resolve(func(ctx, ...args));\n  } catch (thrown: unknown) {\n    throw serializeConvexErrorData(thrown);\n  }\n  return result;\n}\n\nfunction dontCallDirectly(\n  funcType: string,\n  handler: (ctx: any, args: any) => any,\n): unknown {\n  return (ctx: any, args: any) => {\n    globalThis.console.warn(\n      \"Convex functions should not directly call other Convex functions. Consider calling a helper function instead. \" +\n        `e.g. \\`export const foo = ${funcType}(...); await foo(ctx);\\` is not supported. ` +\n        \"See https://docs.convex.dev/production/best-practices/#use-helper-functions-to-write-shared-code\",\n    );\n    return handler(ctx, args);\n  };\n}\n\n// Keep in sync with node executor\nfunction serializeConvexErrorData(thrown: unknown) {\n  if (\n    typeof thrown === \"object\" &&\n    thrown !== null &&\n    Symbol.for(\"ConvexError\") in thrown\n  ) {\n    const error = thrown as ConvexError<any>;\n    error.data = JSON.stringify(\n      convexToJson(error.data === undefined ? null : error.data),\n    );\n    (error as any).ConvexErrorSymbol = Symbol.for(\"ConvexError\");\n    return error;\n  } else {\n    return thrown;\n  }\n}\n\n/**\n * Guard against Convex functions accidentally getting included in a browser bundle.\n * Convex functions may include secret logic or credentials that should not be\n * send to untrusted clients (browsers).\n */\nfunction assertNotBrowser() {\n  if (\n    typeof window === \"undefined\" ||\n    (window as any).__convexAllowFunctionsInBrowser\n  ) {\n    return;\n  }\n  // JSDom doesn't count, developers are allowed to use JSDom in Convex functions.\n  const isRealBrowser =\n    Object.getOwnPropertyDescriptor(globalThis, \"window\")\n      ?.get?.toString()\n      .includes(\"[native code]\") ?? false;\n  if (isRealBrowser) {\n    // eslint-disable-next-line no-console\n    console.error(\n      \"Convex functions should not be imported in the browser. This will throw an error in future versions of `convex`. If this is a false negative, please report it to Convex support.\",\n    );\n  }\n}\n\ntype FunctionDefinition =\n  | ((ctx: any, args: DefaultFunctionArgs) => any)\n  | {\n      args?: GenericValidator | Record<string, GenericValidator>;\n      returns?: GenericValidator | Record<string, GenericValidator>;\n      handler: (ctx: any, args: DefaultFunctionArgs) => any;\n    };\n\nfunction strictReplacer(key: string, value: any) {\n  if (value === undefined) {\n    throw new Error(\n      `Cannot serialize validator value \\`undefined\\` for ${key}`,\n    );\n  }\n  return value;\n}\nfunction exportArgs(functionDefinition: FunctionDefinition) {\n  return () => {\n    let args: GenericValidator = v.any();\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.args !== undefined\n    ) {\n      args = asObjectValidator(functionDefinition.args);\n    }\n    return JSON.stringify(args.json, strictReplacer);\n  };\n}\n\nfunction exportReturns(functionDefinition: FunctionDefinition) {\n  return () => {\n    let returns: Validator<any, any, any> | undefined;\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.returns !== undefined\n    ) {\n      returns = asObjectValidator(functionDefinition.returns);\n    }\n    return JSON.stringify(returns ? returns.json : null, strictReplacer);\n  };\n}\n\n/**\n * Define a mutation in this Convex app's public API.\n *\n * This function will be allowed to modify your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `mutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const mutationGeneric: MutationBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"mutation\", handler) as RegisteredMutation<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isPublic = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"public\">;\n\n/**\n * Define a mutation that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to modify your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalMutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalMutationGeneric: MutationBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\n    \"internalMutation\",\n    handler,\n  ) as RegisteredMutation<\"internal\", any, any>;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isInternal = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"internal\">;\n\nasync function invokeQuery<\n  F extends (ctx: GenericQueryCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const queryCtx = {\n    db: setupReader(),\n    auth: setupAuth(requestId),\n    storage: setupStorageReader(requestId),\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n  };\n  const result = await invokeFunction(func, queryCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define a query in this Convex app's public API.\n *\n * This function will be allowed to read your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `query` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const queryGeneric: QueryBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"query\", handler) as RegisteredQuery<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isPublic = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"public\">;\n\n/**\n * Define a query that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to read from your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalQuery` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalQueryGeneric: QueryBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalQuery\", handler) as RegisteredQuery<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isInternal = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler as any, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"internal\">;\n\nasync function invokeAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, requestId: string, argsStr: string) {\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    scheduler: setupActionScheduler(requestId),\n    storage: setupStorageActionWriter(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  const result = await invokeFunction(func, ctx, args as any);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define an action in this Convex app's public API.\n *\n * If you're using code generation, use the `action` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const actionGeneric: ActionBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"action\", handler) as RegisteredAction<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isPublic = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"public\">;\n\n/**\n * Define an action that is only accessible from other Convex functions (but not from the client).\n *\n * If you're using code generation, use the `internalAction` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalActionGeneric: ActionBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalAction\", handler) as RegisteredAction<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isInternal = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"internal\">;\n\nasync function invokeHttpAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, request: Request) => any,\n>(func: F, request: Request) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since http endpoints are only running in V8.\n  const requestId = \"\";\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    storage: setupStorageActionWriter(requestId),\n    scheduler: setupActionScheduler(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  return await invokeFunction(func, ctx, [request]);\n}\n\n/**\n * Define a Convex HTTP action.\n *\n * @param func - The function. It receives an {@link GenericActionCtx} as its first argument, and a `Request` object\n * as its second.\n * @returns The wrapped function. Route a URL path to this function in `convex/http.js`.\n *\n * @public\n */\nexport const httpActionGeneric = (\n  func: (\n    ctx: GenericActionCtx<GenericDataModel>,\n    request: Request,\n  ) => Promise<Response>,\n): PublicHttpAction => {\n  const q = dontCallDirectly(\"httpAction\", func) as PublicHttpAction;\n  assertNotBrowser();\n  q.isHttp = true;\n  q.invokeHttpAction = (request) => invokeHttpAction(func as any, request);\n  q._handler = func;\n  return q;\n};\n\nasync function runUdf(\n  udfType: \"query\" | \"mutation\",\n  f: any,\n  args?: Record<string, Value>,\n): Promise<any> {\n  const queryArgs = parseArgs(args);\n  const syscallArgs = {\n    udfType,\n    args: convexToJson(queryArgs),\n    ...getFunctionAddress(f),\n  };\n  const result = await performAsyncSyscall(\"1.0/runUdf\", syscallArgs);\n  return jsonToConvex(result);\n}\n"], "names": ["args", "v"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;AAuBA,SAAS,wBAAwB;AACjC,SAAS,+BAA+B;AACxC,SAAS,iBAAiB;AAC1B,SAAS,aAAa,mBAAmB;AACzC,SAAS,WAAW,4BAA4B;AAChD;AAIA;AAKA,SAAS,iBAAiB;AAC1B,SAAS,2BAA2B;AAEpC,SAAS,0BAA0B;;;;;;;;;;;;;;AAEnC,eAAe,eAEb,IAAA,EAAS,OAAA,EAAiB;IAG1B,MAAM,YAAY;IAClB,MAAM,WAAO,2NAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,cAAc;QAClB,QAAI,0OAAA,CAAY;QAChB,UAAM,8OAAA,EAAU,SAAS;QACzB,aAAS,gPAAA,EAAmB,SAAS;QACrC,eAAW,sPAAA,CAAuB;QAElC,UAAU,CAAC,WAAgBA,QAAe,OAAO,SAAS,WAAWA,KAAI;QACzE,aAAa,CAAC,WAAgBA,QAC5B,OAAO,YAAY,WAAWA,KAAI;IACtC;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,aAAa,IAAW;IAClE,oBAAoB,MAAM;IAC1B,OAAO,KAAK,SAAA,KAAU,2NAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAEO,SAAS,oBAAoBC,EAAAA,EAAQ;IAC1C,IAAIA,cAAa,gPAAA,IAAwBA,cAAa,qOAAA,EAAW;QAC/D,MAAM,IAAI,MACR;IAEJ;AACF;AAEA,eAAsB,eAIpB,IAAA,EAAS,GAAA,EAAU,IAAA,EAAY;IAC/B,IAAI;IACJ,IAAI;QACF,SAAS,MAAM,QAAQ,OAAA,CAAQ,KAAK,KAAK,GAAG,IAAI,CAAC;IACnD,EAAA,OAAS,QAAiB;QACxB,MAAM,yBAAyB,MAAM;IACvC;IACA,OAAO;AACT;AAEA,SAAS,iBACP,QAAA,EACA,OAAA,EACS;IACT,OAAO,CAAC,KAAU,SAAc;QAC9B,WAAW,OAAA,CAAQ,IAAA,CACjB,CAAA,wIAAA,EAC+B,QAAQ,CAAA,2IAAA,CAAA;QAGzC,OAAO,QAAQ,KAAK,IAAI;IAC1B;AACF;AAGA,SAAS,yBAAyB,MAAA,EAAiB;IACjD,IACE,OAAO,WAAW,YAClB,WAAW,QACX,OAAO,GAAA,CAAI,aAAa,KAAK,QAC7B;QACA,MAAM,QAAQ;QACd,MAAM,IAAA,GAAO,KAAK,SAAA,KAChB,2NAAA,EAAa,MAAM,IAAA,KAAS,KAAA,IAAY,OAAO,MAAM,IAAI;QAE1D,MAAc,iBAAA,GAAoB,OAAO,GAAA,CAAI,aAAa;QAC3D,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAOA,SAAS,mBAAmB;IAC1B,IACE,OAAO,WAAW,eACjB,GACD,IADgB;QAEhB;IACF;;;IAEA,MAAM,gBACJ,OAAO,yBAAyB,YAAY,QAAQ,GAChD,KAAK,SAAS,EACf,SAAS,eAAe,KAAK;AAOpC;AAUA,SAAS,eAAe,GAAA,EAAa,KAAA,EAAY;IAC/C,IAAI,UAAU,KAAA,GAAW;QACvB,MAAM,IAAI,MACR,CAAA,mDAAA,EAAsD,GAAG,EAAA;IAE7D;IACA,OAAO;AACT;AACA,SAAS,WAAW,kBAAA,EAAwC;IAC1D,OAAO,MAAM;QACX,IAAI,OAAyB,oNAAA,CAAE,GAAA,CAAI;QACnC,IACE,OAAO,uBAAuB,YAC9B,mBAAmB,IAAA,KAAS,KAAA,GAC5B;YACA,WAAO,oOAAA,EAAkB,mBAAmB,IAAI;QAClD;QACA,OAAO,KAAK,SAAA,CAAU,KAAK,IAAA,EAAM,cAAc;IACjD;AACF;AAEA,SAAS,cAAc,kBAAA,EAAwC;IAC7D,OAAO,MAAM;QACX,IAAI;QACJ,IACE,OAAO,uBAAuB,YAC9B,mBAAmB,OAAA,KAAY,KAAA,GAC/B;YACA,cAAU,oOAAA,EAAkB,mBAAmB,OAAO;QACxD;QACA,OAAO,KAAK,SAAA,CAAU,UAAU,QAAQ,IAAA,GAAO,MAAM,cAAc;IACrE;AACF;AAeO,MAAM,kBAAmD,CAC9D,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,YAAY,OAAO;IAMjD,iBAAiB;IACjB,KAAK,UAAA,GAAa;IAClB,KAAK,QAAA,GAAW;IAChB,KAAK,cAAA,GAAiB,CAAC,UAAY,eAAe,SAAS,OAAO;IAClE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAeO,MAAM,0BAA6D,CACxE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBACX,oBACA;IAGF,iBAAiB;IACjB,KAAK,UAAA,GAAa;IAClB,KAAK,UAAA,GAAa;IAClB,KAAK,cAAA,GAAiB,CAAC,UAAY,eAAe,SAAS,OAAO;IAClE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,YAEb,IAAA,EAAS,OAAA,EAAiB;IAG1B,MAAM,YAAY;IAClB,MAAM,WAAO,2NAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,WAAW;QACf,QAAI,0OAAA,CAAY;QAChB,UAAM,8OAAA,EAAU,SAAS;QACzB,aAAS,gPAAA,EAAmB,SAAS;QACrC,UAAU,CAAC,WAAgBD,QAAe,OAAO,SAAS,WAAWA,KAAI;IAC3E;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,UAAU,IAAW;IAC/D,oBAAoB,MAAM;IAC1B,OAAO,KAAK,SAAA,CAAU,+NAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAeO,MAAM,eAA6C,CACxD,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,SAAS,OAAO;IAM9C,iBAAiB;IACjB,KAAK,OAAA,GAAU;IACf,KAAK,QAAA,GAAW;IAChB,KAAK,WAAA,GAAc,CAAC,UAAY,YAAY,SAAS,OAAO;IAC5D,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAeO,MAAM,uBAAuD,CAClE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,iBAAiB,OAAO;IAMtD,iBAAiB;IACjB,KAAK,OAAA,GAAU;IACf,KAAK,UAAA,GAAa;IAClB,KAAK,WAAA,GAAc,CAAC,UAAY,YAAY,SAAgB,OAAO;IACnE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,aAEb,IAAA,EAAS,SAAA,EAAmB,OAAA,EAAiB;IAC7C,MAAM,WAAO,2NAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,YAAQ,8OAAA,EAAiB,SAAS;IACxC,MAAM,MAAM;QACV,GAAG,KAAA;QACH,MAAM,kPAAA,EAAU,SAAS;QACzB,eAAW,oPAAA,EAAqB,SAAS;QACzC,aAAS,sPAAA,EAAyB,SAAS;QAC3C,kBAAc,2PAAA,EAAwB,SAAS;IACjD;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,KAAK,IAAW;IAC1D,OAAO,KAAK,SAAA,KAAU,2NAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAaO,MAAM,gBAA+C,CAC1D,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,UAAU,OAAO;IAM/C,iBAAiB;IACjB,KAAK,QAAA,GAAW;IAChB,KAAK,QAAA,GAAW;IAChB,KAAK,YAAA,GAAe,CAAC,WAAW,UAC9B,aAAa,SAAS,WAAW,OAAO;IAC1C,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAaO,MAAM,wBAAyD,CACpE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,kBAAkB,OAAO;IAMvD,iBAAiB;IACjB,KAAK,QAAA,GAAW;IAChB,KAAK,UAAA,GAAa;IAClB,KAAK,YAAA,GAAe,CAAC,WAAW,UAC9B,aAAa,SAAS,WAAW,OAAO;IAC1C,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,iBAEb,IAAA,EAAS,OAAA,EAAkB;IAG3B,MAAM,YAAY;IAClB,MAAM,YAAQ,8OAAA,EAAiB,SAAS;IACxC,MAAM,MAAM;QACV,GAAG,KAAA;QACH,UAAM,8OAAA,EAAU,SAAS;QACzB,aAAS,sPAAA,EAAyB,SAAS;QAC3C,eAAW,oPAAA,EAAqB,SAAS;QACzC,kBAAc,2PAAA,EAAwB,SAAS;IACjD;IACA,OAAO,MAAM,eAAe,MAAM,KAAK;QAAC,OAAO;KAAC;AAClD;AAWO,MAAM,oBAAoB,CAC/B,SAIqB;IACrB,MAAM,IAAI,iBAAiB,cAAc,IAAI;IAC7C,iBAAiB;IACjB,EAAE,MAAA,GAAS;IACX,EAAE,gBAAA,GAAmB,CAAC,UAAY,iBAAiB,MAAa,OAAO;IACvE,EAAE,QAAA,GAAW;IACb,OAAO;AACT;AAEA,eAAe,OACb,OAAA,EACA,CAAA,EACA,IAAA,EACc;IACd,MAAM,gBAAY,wNAAA,EAAU,IAAI;IAChC,MAAM,cAAc;QAClB;QACA,UAAM,2NAAA,EAAa,SAAS;QAC5B,GAAG,mPAAA,EAAmB,CAAC,CAAA;IACzB;IACA,MAAM,SAAS,UAAM,4OAAA,EAAoB,cAAc,WAAW;IAClE,WAAO,2NAAA,EAAa,MAAM;AAC5B", "debugId": null}}, {"offset": {"line": 2941, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/pagination.ts"], "sourcesContent": ["import { v } from \"../values/validator.js\";\n\n/**\n * An opaque identifier used for paginating a database query.\n *\n * Cursors are returned from {@link OrderedQuery.paginate} and represent the\n * point of the query where the page of results ended.\n *\n * To continue paginating, pass the cursor back into\n * {@link OrderedQuery.paginate} in the {@link PaginationOptions} object to\n * fetch another page of results.\n *\n * Note: Cursors can only be passed to _exactly_ the same database query that\n * they were generated from. You may not reuse a cursor between different\n * database queries.\n *\n * @public\n */\nexport type Cursor = string;\n\n/**\n * The result of paginating using {@link OrderedQuery.paginate}.\n *\n * @public\n */\nexport interface PaginationResult<T> {\n  /**\n   * The page of results.\n   */\n  page: T[];\n\n  /**\n   * Have we reached the end of the results?\n   */\n  isDone: boolean;\n\n  /**\n   * A {@link Cursor} to continue loading more results.\n   */\n  continueCursor: Cursor;\n\n  /**\n   * A {@link Cursor} to split the page into two, so the page from\n   * (cursor, continueCursor] can be replaced by two pages (cursor, splitCursor]\n   * and (splitCursor, continueCursor].\n   */\n  splitCursor?: Cursor | null;\n\n  /**\n   * When a query reads too much data, it may return 'SplitRecommended' to\n   * indicate that the page should be split into two with `splitCursor`.\n   * When a query reads so much data that `page` might be incomplete, its status\n   * becomes 'SplitRequired'.\n   */\n  pageStatus?: \"SplitRecommended\" | \"SplitRequired\" | null;\n}\n\n/**\n * The options passed to {@link OrderedQuery.paginate}.\n *\n * To use this type in [argument validation](https://docs.convex.dev/functions/validation),\n * use the {@link paginationOptsValidator}.\n *\n * @public\n */\nexport interface PaginationOptions {\n  /**\n   * Number of items to load in this page of results.\n   *\n   * Note: This is only an initial value!\n   *\n   * If you are running this paginated query in a reactive query function, you\n   * may receive more or less items than this if items were added to or removed\n   * from the query range.\n   */\n  numItems: number;\n\n  /**\n   * A {@link Cursor} representing the start of this page or `null` to start\n   * at the beginning of the query results.\n   */\n  cursor: Cursor | null;\n\n  /**\n   * A {@link Cursor} representing the end of this page or `null | undefined` to\n   * use `numItems` instead.\n   *\n   * @internal\n   */\n  endCursor?: Cursor | null;\n\n  /**\n   * The maximum number of rows that should be read from the database.\n   *\n   * This option is different from `numItems` in that it controls the number of rows entering a query's\n   * pipeline, where `numItems` controls the number of rows coming out. For example, a `filter`\n   * may disqualify most of the rows coming in, so setting a low `numItems` would not help\n   * bound its execution time. Instead, set a low `maximumRowsRead` to efficiently paginate\n   * through the filter.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumRowsRead?: number;\n\n  /**\n   * The maximum number of bytes that should be read from the database.\n   *\n   * As with {@link PaginationOptions.maximumRowsRead}, this affects the number\n   * of rows entering a query's pipeline.\n   *\n   * Once a paginated query hits its bytes read budget, an incomplete page\n   * will be returned.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumBytesRead?: number;\n}\n\n/**\n * A {@link values.Validator} for {@link PaginationOptions}.\n *\n * This includes the standard {@link PaginationOptions} properties along with\n * an optional cache-busting `id` property used by {@link react.usePaginatedQuery}.\n *\n * @public\n */\nexport const paginationOptsValidator = v.object({\n  numItems: v.number(),\n  cursor: v.union(v.string(), v.null()),\n  endCursor: v.optional(v.union(v.string(), v.null())),\n  id: v.optional(v.number()),\n  maximumRowsRead: v.optional(v.number()),\n  maximumBytesRead: v.optional(v.number()),\n});\n"], "names": [], "mappings": ";;;;AAAA,SAAS,SAAS;;;AAkIX,MAAM,0BAA0B,oNAAA,CAAE,MAAA,CAAO;IAC9C,UAAU,oNAAA,CAAE,MAAA,CAAO;IACnB,QAAQ,oNAAA,CAAE,KAAA,CAAM,oNAAA,CAAE,MAAA,CAAO,GAAG,oNAAA,CAAE,IAAA,CAAK,CAAC;IACpC,WAAW,oNAAA,CAAE,QAAA,CAAS,oNAAA,CAAE,KAAA,CAAM,oNAAA,CAAE,MAAA,CAAO,GAAG,oNAAA,CAAE,IAAA,CAAK,CAAC,CAAC;IACnD,IAAI,oNAAA,CAAE,QAAA,CAAS,oNAAA,CAAE,MAAA,CAAO,CAAC;IACzB,iBAAiB,oNAAA,CAAE,QAAA,CAAS,oNAAA,CAAE,MAAA,CAAO,CAAC;IACtC,kBAAkB,oNAAA,CAAE,QAAA,CAAS,oNAAA,CAAE,MAAA,CAAO,CAAC;AACzC,CAAC", "debugId": null}}, {"offset": {"line": 2960, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2966, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/api.ts"], "sourcesContent": ["import {\n  EmptyObject,\n  DefaultFunctionArgs,\n  FunctionVisibility,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"./registration.js\";\nimport { Expand, UnionToIntersection } from \"../type_utils.js\";\nimport { PaginationOptions, PaginationResult } from \"./pagination.js\";\nimport { functionName } from \"./functionName.js\";\nimport { getFunctionAddress } from \"./components/paths.js\";\n\n/**\n * The type of a Convex function.\n *\n * @public\n */\nexport type FunctionType = \"query\" | \"mutation\" | \"action\";\n\n/**\n * A reference to a registered Convex function.\n *\n * You can create a {@link FunctionReference} using the generated `api` utility:\n * ```js\n * import { api } from \"../convex/_generated/api\";\n *\n * const reference = api.myModule.myFunction;\n * ```\n *\n * If you aren't using code generation, you can create references using\n * {@link anyApi}:\n * ```js\n * import { anyApi } from \"convex/server\";\n *\n * const reference = anyApi.myModule.myFunction;\n * ```\n *\n * Function references can be used to invoke functions from the client. For\n * example, in React you can pass references to the {@link react.useQuery} hook:\n * ```js\n * const result = useQuery(api.myModule.myFunction);\n * ```\n *\n * @typeParam Type - The type of the function (\"query\", \"mutation\", or \"action\").\n * @typeParam Visibility - The visibility of the function (\"public\" or \"internal\").\n * @typeParam Args - The arguments to this function. This is an object mapping\n * argument names to their types.\n * @typeParam ReturnType - The return type of this function.\n * @public\n */\nexport type FunctionReference<\n  Type extends FunctionType,\n  Visibility extends FunctionVisibility = \"public\",\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n  ComponentPath = string | undefined,\n> = {\n  _type: Type;\n  _visibility: Visibility;\n  _args: Args;\n  _returnType: ReturnType;\n  _componentPath: ComponentPath;\n};\n\n/**\n * Get the name of a function from a {@link FunctionReference}.\n *\n * The name is a string like \"myDir/myModule:myFunction\". If the exported name\n * of the function is `\"default\"`, the function name is omitted\n * (e.g. \"myDir/myModule\").\n *\n * @param functionReference - A {@link FunctionReference} to get the name of.\n * @returns A string of the function's name.\n *\n * @public\n */\nexport function getFunctionName(\n  functionReference: AnyFunctionReference,\n): string {\n  const address = getFunctionAddress(functionReference);\n\n  if (address.name === undefined) {\n    if (address.functionHandle !== undefined) {\n      throw new Error(\n        `Expected function reference like \"api.file.func\" or \"internal.file.func\", but received function handle ${address.functionHandle}`,\n      );\n    } else if (address.reference !== undefined) {\n      throw new Error(\n        `Expected function reference in the current component like \"api.file.func\" or \"internal.file.func\", but received reference ${address.reference}`,\n      );\n    }\n    throw new Error(\n      `Expected function reference like \"api.file.func\" or \"internal.file.func\", but received ${JSON.stringify(address)}`,\n    );\n  }\n  // Both a legacy thing and also a convenience for interactive use:\n  // the types won't check but a string is always allowed at runtime.\n  if (typeof functionReference === \"string\") return functionReference;\n\n  // Two different runtime values for FunctionReference implement this\n  // interface: api objects returned from `createApi()` and standalone\n  // function reference objects returned from makeFunctionReference.\n  const name = (functionReference as any)[functionName];\n  if (!name) {\n    throw new Error(`${functionReference as any} is not a functionReference`);\n  }\n  return name;\n}\n\n/**\n * FunctionReferences generally come from generated code, but in custom clients\n * it may be useful to be able to build one manually.\n *\n * Real function references are empty objects at runtime, but the same interface\n * can be implemented with an object for tests and clients which don't use\n * code generation.\n *\n * @param name - The identifier of the function. E.g. `path/to/file:functionName`\n * @public\n */\nexport function makeFunctionReference<\n  type extends FunctionType,\n  args extends DefaultFunctionArgs = any,\n  ret = any,\n>(name: string): FunctionReference<type, \"public\", args, ret> {\n  return { [functionName]: name } as unknown as FunctionReference<\n    type,\n    \"public\",\n    args,\n    ret\n  >;\n}\n\n/**\n * Create a runtime API object that implements {@link AnyApi}.\n *\n * This allows accessing any path regardless of what directories, modules,\n * or functions are defined.\n *\n * @param pathParts - The path to the current node in the API.\n * @returns An {@link AnyApi}\n * @public\n */\nfunction createApi(pathParts: string[] = []): AnyApi {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createApi(newParts);\n      } else if (prop === functionName) {\n        if (pathParts.length < 2) {\n          const found = [\"api\", ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`api.moduleName.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        const path = pathParts.slice(0, -1).join(\"/\");\n        const exportName = pathParts[pathParts.length - 1];\n        if (exportName === \"default\") {\n          return path;\n        } else {\n          return path + \":\" + exportName;\n        }\n      } else if (prop === Symbol.toStringTag) {\n        return \"FunctionReference\";\n      } else {\n        return undefined;\n      }\n    },\n  };\n\n  return new Proxy({}, handler);\n}\n\n/**\n * Given an export from a module, convert it to a {@link FunctionReference}\n * if it is a Convex function.\n */\nexport type FunctionReferenceFromExport<Export> =\n  Export extends RegisteredQuery<\n    infer Visibility,\n    infer Args,\n    infer ReturnValue\n  >\n    ? FunctionReference<\n        \"query\",\n        Visibility,\n        Args,\n        ConvertReturnType<ReturnValue>\n      >\n    : Export extends RegisteredMutation<\n          infer Visibility,\n          infer Args,\n          infer ReturnValue\n        >\n      ? FunctionReference<\n          \"mutation\",\n          Visibility,\n          Args,\n          ConvertReturnType<ReturnValue>\n        >\n      : Export extends RegisteredAction<\n            infer Visibility,\n            infer Args,\n            infer ReturnValue\n          >\n        ? FunctionReference<\n            \"action\",\n            Visibility,\n            Args,\n            ConvertReturnType<ReturnValue>\n          >\n        : never;\n\n/**\n * Given a module, convert all the Convex functions into\n * {@link FunctionReference}s and remove the other exports.\n *\n * BE CAREFUL WHEN EDITING THIS!\n *\n * This is written carefully to preserve jumping to function definitions using\n * cmd+click. If you edit it, please test that cmd+click still works.\n */\ntype FunctionReferencesInModule<Module extends Record<string, any>> = {\n  -readonly [ExportName in keyof Module as Module[ExportName][\"isConvexFunction\"] extends true\n    ? ExportName\n    : never]: FunctionReferenceFromExport<Module[ExportName]>;\n};\n\n/**\n * Given a path to a module and it's type, generate an API type for this module.\n *\n * This is a nested object according to the module's path.\n */\ntype ApiForModule<\n  ModulePath extends string,\n  Module extends object,\n> = ModulePath extends `${infer First}/${infer Second}`\n  ? {\n      [_ in First]: ApiForModule<Second, Module>;\n    }\n  : { [_ in ModulePath]: FunctionReferencesInModule<Module> };\n\n/**\n * Given the types of all modules in the `convex/` directory, construct the type\n * of `api`.\n *\n * `api` is a utility for constructing {@link FunctionReference}s.\n *\n * @typeParam AllModules - A type mapping module paths (like `\"dir/myModule\"`) to\n * the types of the modules.\n * @public\n */\nexport type ApiFromModules<AllModules extends Record<string, object>> =\n  FilterApi<\n    ApiFromModulesAllowEmptyNodes<AllModules>,\n    FunctionReference<any, any, any, any>\n  >;\n\ntype ApiFromModulesAllowEmptyNodes<AllModules extends Record<string, object>> =\n  ExpandModulesAndDirs<\n    UnionToIntersection<\n      {\n        [ModulePath in keyof AllModules]: ApiForModule<\n          ModulePath & string,\n          AllModules[ModulePath]\n        >;\n      }[keyof AllModules]\n    >\n  >;\n\n/**\n * @public\n *\n * Filter a Convex deployment api object for functions which meet criteria,\n * for example all public queries.\n */\nexport type FilterApi<API, Predicate> = Expand<{\n  [mod in keyof API as API[mod] extends Predicate\n    ? mod\n    : API[mod] extends FunctionReference<any, any, any, any>\n      ? never\n      : FilterApi<API[mod], Predicate> extends Record<string, never>\n        ? never\n        : mod]: API[mod] extends Predicate\n    ? API[mod]\n    : FilterApi<API[mod], Predicate>;\n}>;\n\n/**\n * Given an api of type API and a FunctionReference subtype, return an api object\n * containing only the function references that match.\n *\n * ```ts\n * const q = filterApi<typeof api, FunctionReference<\"query\">>(api)\n * ```\n *\n * @public\n */\nexport function filterApi<API, Predicate>(api: API): FilterApi<API, Predicate> {\n  return api as any;\n}\n\n// These just* API filter helpers require no type parameters so are useable from JavaScript.\n/** @public */\nexport function justInternal<API>(\n  api: API,\n): FilterApi<API, FunctionReference<any, \"internal\", any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justPublic<API>(\n  api: API,\n): FilterApi<API, FunctionReference<any, \"public\", any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justQueries<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"query\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justMutations<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"mutation\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justActions<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"action\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justPaginatedQueries<API>(\n  api: API,\n): FilterApi<\n  API,\n  FunctionReference<\n    \"query\",\n    any,\n    { paginationOpts: PaginationOptions },\n    PaginationResult<any>\n  >\n> {\n  return api as any;\n}\n\n/** @public */\nexport function justSchedulable<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"mutation\" | \"action\", any, any, any>> {\n  return api as any;\n}\n\n/**\n * Like {@link Expand}, this simplifies how TypeScript displays object types.\n * The differences are:\n * 1. This version is recursive.\n * 2. This stops recursing when it hits a {@link FunctionReference}.\n */\ntype ExpandModulesAndDirs<ObjectType> = ObjectType extends AnyFunctionReference\n  ? ObjectType\n  : {\n      [Key in keyof ObjectType]: ExpandModulesAndDirs<ObjectType[Key]>;\n    };\n\n/**\n * A {@link FunctionReference} of any type and any visibility with any\n * arguments and any return type.\n *\n * @public\n */\nexport type AnyFunctionReference = FunctionReference<any, any>;\n\ntype AnyModuleDirOrFunc = {\n  [key: string]: AnyModuleDirOrFunc;\n} & AnyFunctionReference;\n\n/**\n * The type that Convex api objects extend. If you were writing an api from\n * scratch it should extend this type.\n *\n * @public\n */\nexport type AnyApi = Record<string, Record<string, AnyModuleDirOrFunc>>;\n\n/**\n * Recursive partial API, useful for defining a subset of an API when mocking\n * or building custom api objects.\n *\n * @public\n */\nexport type PartialApi<API> = {\n  [mod in keyof API]?: API[mod] extends FunctionReference<any, any, any, any>\n    ? API[mod]\n    : PartialApi<API[mod]>;\n};\n\n/**\n * A utility for constructing {@link FunctionReference}s in projects that\n * are not using code generation.\n *\n * You can create a reference to a function like:\n * ```js\n * const reference = anyApi.myModule.myFunction;\n * ```\n *\n * This supports accessing any path regardless of what directories and modules\n * are in your project. All function references are typed as\n * {@link AnyFunctionReference}.\n *\n *\n * If you're using code generation, use `api` from `convex/_generated/api`\n * instead. It will be more type-safe and produce better auto-complete\n * in your editor.\n *\n * @public\n */\nexport const anyApi: AnyApi = createApi() as any;\n\n/**\n * Given a {@link FunctionReference}, get the return type of the function.\n *\n * This is represented as an object mapping argument names to values.\n * @public\n */\nexport type FunctionArgs<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_args\"];\n\n/**\n * A tuple type of the (maybe optional) arguments to `FuncRef`.\n *\n * This type is used to make methods involving arguments type safe while allowing\n * skipping the arguments for functions that don't require arguments.\n *\n * @public\n */\nexport type OptionalRestArgs<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_args\"] extends EmptyObject\n    ? [args?: EmptyObject]\n    : [args: FuncRef[\"_args\"]];\n\n/**\n * A tuple type of the (maybe optional) arguments to `FuncRef`, followed by an options\n * object of type `Options`.\n *\n * This type is used to make methods like `useQuery` type-safe while allowing\n * 1. Skipping arguments for functions that don't require arguments.\n * 2. Skipping the options object.\n * @public\n */\nexport type ArgsAndOptions<\n  FuncRef extends AnyFunctionReference,\n  Options,\n> = FuncRef[\"_args\"] extends EmptyObject\n  ? [args?: EmptyObject, options?: Options]\n  : [args: FuncRef[\"_args\"], options?: Options];\n\n/**\n * Given a {@link FunctionReference}, get the return type of the function.\n *\n * @public\n */\nexport type FunctionReturnType<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_returnType\"];\n\ntype UndefinedToNull<T> = T extends void ? null : T;\n\ntype NullToUndefinedOrNull<T> = T extends null ? T | undefined | void : T;\n\n/**\n * Convert the return type of a function to it's client-facing format.\n *\n * This means:\n * - Converting `undefined` and `void` to `null`\n * - Removing all `Promise` wrappers\n */\nexport type ConvertReturnType<T> = UndefinedToNull<Awaited<T>>;\n\nexport type ValidatorTypeToReturnType<T> =\n  | Promise<NullToUndefinedOrNull<T>>\n  | NullToUndefinedOrNull<T>;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAS,oBAAoB;AAC7B,SAAS,0BAA0B;;;;AAkE5B,SAAS,gBACd,iBAAA,EACQ;IACR,MAAM,cAAU,+OAAA,EAAmB,iBAAiB;IAEpD,IAAI,QAAQ,IAAA,KAAS,KAAA,GAAW;QAC9B,IAAI,QAAQ,cAAA,KAAmB,KAAA,GAAW;YACxC,MAAM,IAAI,MACR,CAAA,uGAAA,EAA0G,QAAQ,cAAc,EAAA;QAEpI,OAAA,IAAW,QAAQ,SAAA,KAAc,KAAA,GAAW;YAC1C,MAAM,IAAI,MACR,CAAA,0HAAA,EAA6H,QAAQ,SAAS,EAAA;QAElJ;QACA,MAAM,IAAI,MACR,CAAA,uFAAA,EAA0F,KAAK,SAAA,CAAU,OAAO,CAAC,EAAA;IAErH;IAGA,IAAI,OAAO,sBAAsB,SAAU,CAAA,OAAO;IAKlD,MAAM,OAAQ,iBAAA,CAA0B,kOAAY,CAAA;IACpD,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,GAAG,iBAAwB,CAAA,2BAAA,CAA6B;IAC1E;IACA,OAAO;AACT;AAaO,SAAS,sBAId,IAAA,EAA4D;IAC5D,OAAO;QAAE,CAAC,kOAAY,CAAA,EAAG;IAAK;AAMhC;AAYA,SAAS,UAAU,YAAsB,CAAC,CAAA,EAAW;IACnD,MAAM,UAAgC;QACpC,KAAI,CAAA,EAAG,IAAA,EAAuB;YAC5B,IAAI,OAAO,SAAS,UAAU;gBAC5B,MAAM,WAAW,CAAC;uBAAG;oBAAW,IAAI;iBAAA;gBACpC,OAAO,UAAU,QAAQ;YAC3B,OAAA,IAAW,SAAS,kOAAA,EAAc;gBAChC,IAAI,UAAU,MAAA,GAAS,GAAG;oBACxB,MAAM,QAAQ;wBAAC,OAAO;2BAAG,SAAS;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC5C,MAAM,IAAI,MACR,CAAA,iFAAA,EAAoF,KAAK,CAAA,EAAA,CAAA;gBAE7F;gBACA,MAAM,OAAO,UAAU,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,GAAG;gBAC5C,MAAM,aAAa,SAAA,CAAU,UAAU,MAAA,GAAS,CAAC,CAAA;gBACjD,IAAI,eAAe,WAAW;oBAC5B,OAAO;gBACT,OAAO;oBACL,OAAO,OAAO,MAAM;gBACtB;YACF,OAAA,IAAW,SAAS,OAAO,WAAA,EAAa;gBACtC,OAAO;YACT,OAAO;gBACL,OAAO,KAAA;YACT;QACF;IACF;IAEA,OAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AA+HO,SAAS,UAA0B,GAAA,EAAqC;IAC7E,OAAO;AACT;AAIO,SAAS,aACd,GAAA,EAC8D;IAC9D,OAAO;AACT;AAGO,SAAS,WACd,GAAA,EAC4D;IAC5D,OAAO;AACT;AAGO,SAAS,YACd,GAAA,EAC2D;IAC3D,OAAO;AACT;AAGO,SAAS,cACd,GAAA,EAC8D;IAC9D,OAAO;AACT;AAGO,SAAS,YACd,GAAA,EAC4D;IAC5D,OAAO;AACT;AAGO,SAAS,qBACd,GAAA,EASA;IACA,OAAO;AACT;AAGO,SAAS,gBACd,GAAA,EACyE;IACzE,OAAO;AACT;AAkEO,MAAM,SAAiB,UAAU", "debugId": null}}, {"offset": {"line": 3079, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/cron.ts"], "sourcesContent": ["import { getFunctionName, OptionalRestArgs } from \"../server/api.js\";\nimport { parseArgs } from \"../common/index.js\";\nimport { convexTo<PERSON>son, JSONValue, Value } from \"../values/index.js\";\nimport { SchedulableFunctionReference } from \"./scheduler.js\";\n\ntype CronSchedule = {\n  type: \"cron\";\n  cron: string;\n};\n/** @public */\nexport type IntervalSchedule =\n  | { type: \"interval\"; seconds: number }\n  | { type: \"interval\"; minutes: number }\n  | { type: \"interval\"; hours: number };\n/** @public */\nexport type HourlySchedule = {\n  type: \"hourly\";\n  minuteUTC: number;\n};\n/** @public */\nexport type DailySchedule = {\n  type: \"daily\";\n  hourUTC: number;\n  minuteUTC: number;\n};\nconst DAYS_OF_WEEK = [\n  \"sunday\",\n  \"monday\",\n  \"tuesday\",\n  \"wednesday\",\n  \"thursday\",\n  \"friday\",\n  \"saturday\",\n] as const;\ntype DayOfWeek = (typeof DAYS_OF_WEEK)[number];\n/** @public */\nexport type WeeklySchedule = {\n  type: \"weekly\";\n  dayOfWeek: DayOfWeek;\n  hourUTC: number;\n  minuteUTC: number;\n};\n/** @public */\nexport type MonthlySchedule = {\n  type: \"monthly\";\n  day: number;\n  hourUTC: number;\n  minuteUTC: number;\n};\n\n// Duplicating types so docstrings are visible in signatures:\n// `Expand<Omit<MonthlySchedule, \"type\">>` doesn't preserve docstrings.\n// When we get to TypeScript 4.9, `satisfies` would go nicely here.\n\n/** @public */\nexport type Interval =\n  | {\n      /**\n       * Run a job every `seconds` seconds, beginning\n       * when the job is first deployed to Convex.\n       */\n      seconds: number;\n      minutes?: undefined;\n      hours?: undefined;\n    }\n  | {\n      /**\n       * Run a job every `minutes` minutes, beginning\n       * when the job is first deployed to Convex.\n       */\n      minutes: number;\n      seconds?: undefined;\n      hours?: undefined;\n    }\n  | {\n      /**\n       * Run a job every `hours` hours, beginning when\n       * when the job is first deployed to Convex.\n       */\n      hours: number;\n      seconds?: undefined;\n      minutes?: undefined;\n    };\n\n/** @public */\nexport type Hourly = {\n  /**\n   * Minutes past the hour, 0-59.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Daily = {\n  /**\n   * 0-23, hour of day. Remember, this is UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember, this is UTC.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Monthly = {\n  /**\n   * 1-31, day of month. Days greater that 28 will not run every month.\n   */\n  day: number;\n  /**\n   * 0-23, hour of day. Remember to convert from your own time zone to UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember to convert from your own time zone to UTC.\n   */\n  minuteUTC: number;\n};\n/** @public */\nexport type Weekly = {\n  /**\n   * \"monday\", \"tuesday\", etc.\n   */\n  dayOfWeek: DayOfWeek;\n  /**\n   * 0-23, hour of day. Remember to convert from your own time zone to UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember to convert from your own time zone to UTC.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Schedule =\n  | CronSchedule\n  | IntervalSchedule\n  | HourlySchedule\n  | DailySchedule\n  | WeeklySchedule\n  | MonthlySchedule;\n\n/**\n * A schedule to run a Convex mutation or action on.\n * You can schedule Convex functions to run regularly with\n * {@link interval} and exporting it.\n *\n * @public\n **/\nexport interface CronJob {\n  name: string;\n  args: JSONValue;\n  schedule: Schedule;\n}\n\n/**\n * Create a CronJobs object to schedule recurring tasks.\n *\n * ```js\n * // convex/crons.js\n * import { cronJobs } from 'convex/server';\n * import { api } from \"./_generated/api\";\n *\n * const crons = cronJobs();\n * crons.weekly(\n *   \"weekly re-engagement email\",\n *   {\n *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n *     minuteUTC: 30,\n *   },\n *   api.emails.send\n * )\n * export default crons;\n * ```\n *\n * @public\n */\nexport const cronJobs = () => new Crons();\n\n/**\n * @public\n *\n * This is a cron string. They're complicated!\n */\ntype CronString = string;\n\nfunction validateIntervalNumber(n: number) {\n  if (!Number.isInteger(n) || n <= 0) {\n    throw new Error(\"Interval must be an integer greater than 0\");\n  }\n}\n\nfunction validatedDayOfMonth(n: number) {\n  if (!Number.isInteger(n) || n < 1 || n > 31) {\n    throw new Error(\"Day of month must be an integer from 1 to 31\");\n  }\n  return n;\n}\n\nfunction validatedDayOfWeek(s: string) {\n  if (!DAYS_OF_WEEK.includes(s as DayOfWeek)) {\n    throw new Error('Day of week must be a string like \"monday\".');\n  }\n  return s as DayOfWeek;\n}\n\nfunction validatedHourOfDay(n: number) {\n  if (!Number.isInteger(n) || n < 0 || n > 23) {\n    throw new Error(\"Hour of day must be an integer from 0 to 23\");\n  }\n  return n;\n}\n\nfunction validatedMinuteOfHour(n: number) {\n  if (!Number.isInteger(n) || n < 0 || n > 59) {\n    throw new Error(\"Minute of hour must be an integer from 0 to 59\");\n  }\n  return n;\n}\n\nfunction validatedCronString(s: string) {\n  return s;\n}\n\nfunction validatedCronIdentifier(s: string) {\n  if (!s.match(/^[ -~]*$/)) {\n    throw new Error(\n      `Invalid cron identifier ${s}: use ASCII letters that are not control characters`,\n    );\n  }\n  return s;\n}\n\n/**\n * A class for scheduling cron jobs.\n *\n * To learn more see the documentation at https://docs.convex.dev/scheduling/cron-jobs\n *\n * @public\n */\nexport class Crons {\n  crons: Record<string, CronJob>;\n  isCrons: true;\n  constructor() {\n    this.isCrons = true;\n    this.crons = {};\n  }\n\n  /** @internal */\n  schedule(\n    cronIdentifier: string,\n    schedule: Schedule,\n    functionReference: SchedulableFunctionReference,\n    args?: Record<string, Value>,\n  ) {\n    const cronArgs = parseArgs(args);\n    validatedCronIdentifier(cronIdentifier);\n    if (cronIdentifier in this.crons) {\n      throw new Error(`Cron identifier registered twice: ${cronIdentifier}`);\n    }\n    this.crons[cronIdentifier] = {\n      name: getFunctionName(functionReference),\n      args: [convexToJson(cronArgs)],\n      schedule: schedule,\n    };\n  }\n\n  /**\n   * Schedule a mutation or action to run at some interval.\n   *\n   * ```js\n   * crons.interval(\"Clear presence data\", {seconds: 30}, api.presence.clear);\n   * ```\n   *\n   * @param identifier - A unique name for this scheduled job.\n   * @param schedule - The time between runs for this scheduled job.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  interval<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Interval,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const s = schedule;\n    const hasSeconds = +(\"seconds\" in s && s.seconds !== undefined);\n    const hasMinutes = +(\"minutes\" in s && s.minutes !== undefined);\n    const hasHours = +(\"hours\" in s && s.hours !== undefined);\n    const total = hasSeconds + hasMinutes + hasHours;\n    if (total !== 1) {\n      throw new Error(\"Must specify one of seconds, minutes, or hours\");\n    }\n    if (hasSeconds) {\n      validateIntervalNumber(schedule.seconds!);\n    } else if (hasMinutes) {\n      validateIntervalNumber(schedule.minutes!);\n    } else if (hasHours) {\n      validateIntervalNumber(schedule.hours!);\n    }\n    this.schedule(\n      cronIdentifier,\n      { ...schedule, type: \"interval\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on an hourly basis.\n   *\n   * ```js\n   * crons.hourly(\n   *   \"Reset high scores\",\n   *   {\n   *     minuteUTC: 30,\n   *   },\n   *   api.scores.reset\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What time (UTC) each day to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  hourly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Hourly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { minuteUTC, type: \"hourly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a daily basis.\n   *\n   * ```js\n   * crons.daily(\n   *   \"Reset high scores\",\n   *   {\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *   },\n   *   api.scores.reset\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What time (UTC) each day to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  daily<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Daily,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { hourUTC, minuteUTC, type: \"daily\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a weekly basis.\n   *\n   * ```js\n   * crons.weekly(\n   *   \"Weekly re-engagement email\",\n   *   {\n   *     dayOfWeek: \"Tuesday\",\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *   },\n   *   api.emails.send\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What day and time (UTC) each week to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   */\n  weekly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Weekly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const dayOfWeek = validatedDayOfWeek(schedule.dayOfWeek);\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { dayOfWeek, hourUTC, minuteUTC, type: \"weekly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a monthly basis.\n   *\n   * Note that some months have fewer days than others, so e.g. a function\n   * scheduled to run on the 30th will not run in February.\n   *\n   * ```js\n   * crons.monthly(\n   *   \"Bill customers at \",\n   *   {\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *     day: 1,\n   *   },\n   *   api.billing.billCustomers\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What day and time (UTC) each month to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  monthly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Monthly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const day = validatedDayOfMonth(schedule.day);\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { day, hourUTC, minuteUTC, type: \"monthly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a recurring basis.\n   *\n   * Like the unix command `cron`, Sunday is 0, Monday is 1, etc.\n   *\n   * ```\n   *  ┌─ minute (0 - 59)\n   *  │ ┌─ hour (0 - 23)\n   *  │ │ ┌─ day of the month (1 - 31)\n   *  │ │ │ ┌─ month (1 - 12)\n   *  │ │ │ │ ┌─ day of the week (0 - 6) (Sunday to Saturday)\n   * \"* * * * *\"\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param cron - Cron string like `\"15 7 * * *\"` (Every day at 7:15 UTC)\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  cron<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    cron: CronString,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const c = validatedCronString(cron);\n    this.schedule(\n      cronIdentifier,\n      { cron: c, type: \"cron\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /** @internal */\n  export() {\n    return JSON.stringify(this.crons);\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA,SAAS,uBAAyC;AAClD,SAAS,iBAAiB;AAC1B,SAAS,oBAAsC;;;;;;;;;;;;;;AAuB/C,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAkJO,MAAM,WAAW,IAAM,IAAI,MAAM;AASxC,SAAS,uBAAuB,CAAA,EAAW;IACzC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,KAAK,GAAG;QAClC,MAAM,IAAI,MAAM,4CAA4C;IAC9D;AACF;AAEA,SAAS,oBAAoB,CAAA,EAAW;IACtC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,8CAA8C;IAChE;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAW;IACrC,IAAI,CAAC,aAAa,QAAA,CAAS,CAAc,GAAG;QAC1C,MAAM,IAAI,MAAM,6CAA6C;IAC/D;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAW;IACrC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,6CAA6C;IAC/D;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,CAAA,EAAW;IACxC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,gDAAgD;IAClE;IACA,OAAO;AACT;AAEA,SAAS,oBAAoB,CAAA,EAAW;IACtC,OAAO;AACT;AAEA,SAAS,wBAAwB,CAAA,EAAW;IAC1C,IAAI,CAAC,EAAE,KAAA,CAAM,UAAU,GAAG;QACxB,MAAM,IAAI,MACR,CAAA,wBAAA,EAA2B,CAAC,CAAA,mDAAA,CAAA;IAEhC;IACA,OAAO;AACT;AASO,MAAM,MAAM;IAGjB,aAAc;QAFd,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEE,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,KAAA,GAAQ,CAAC;IAChB;IAAA,cAAA,GAGA,SACE,cAAA,EACA,QAAA,EACA,iBAAA,EACA,IAAA,EACA;QACA,MAAM,eAAW,wNAAA,EAAU,IAAI;QAC/B,wBAAwB,cAAc;QACtC,IAAI,kBAAkB,IAAA,CAAK,KAAA,EAAO;YAChC,MAAM,IAAI,MAAM,CAAA,kCAAA,EAAqC,cAAc,EAAE;QACvE;QACA,IAAA,CAAK,KAAA,CAAM,cAAc,CAAA,GAAI;YAC3B,MAAM,gOAAA,EAAgB,iBAAiB;YACvC,MAAM;oBAAC,2NAAA,EAAa,QAAQ,CAAC;aAAA;YAC7B;QACF;IACF;IAAA;;;;;;;;;;;;GAAA,GAeA,SACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,IAAI;QACV,MAAM,aAAa,CAAA,CAAE,aAAa,KAAK,EAAE,OAAA,KAAY,KAAA,CAAA;QACrD,MAAM,aAAa,CAAA,CAAE,aAAa,KAAK,EAAE,OAAA,KAAY,KAAA,CAAA;QACrD,MAAM,WAAW,CAAA,CAAE,WAAW,KAAK,EAAE,KAAA,KAAU,KAAA,CAAA;QAC/C,MAAM,QAAQ,aAAa,aAAa;QACxC,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM,gDAAgD;QAClE;QACA,IAAI,YAAY;YACd,uBAAuB,SAAS,OAAQ;QAC1C,OAAA,IAAW,YAAY;YACrB,uBAAuB,SAAS,OAAQ;QAC1C,OAAA,IAAW,UAAU;YACnB,uBAAuB,SAAS,KAAM;QACxC;QACA,IAAA,CAAK,QAAA,CACH,gBACA;YAAE,GAAG,QAAA;YAAU,MAAM;QAAW,GAChC,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;GAAA,GAqBA,OACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAW,MAAM;QAAS,GAC5B,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,MACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAS;YAAW,MAAM;QAAQ,GACpC,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,OACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,YAAY,mBAAmB,SAAS,SAAS;QACvD,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAW;YAAS;YAAW,MAAM;QAAS,GAChD,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA0BA,QACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,MAAM,oBAAoB,SAAS,GAAG;QAC5C,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAK;YAAS;YAAW,MAAM;QAAU,GAC3C,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,KACE,cAAA,EACA,IAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,IAAI,oBAAoB,IAAI;QAClC,IAAA,CAAK,QAAA,CACH,gBACA;YAAE,MAAM;YAAG,MAAM;QAAO,GACxB,sBACG;IAEP;IAAA,cAAA,GAGA,SAAS;QACP,OAAO,KAAK,SAAA,CAAU,IAAA,CAAK,KAAK;IAClC;AACF", "debugId": null}}, {"offset": {"line": 3354, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/router.ts"], "sourcesContent": ["import { performJsSyscall } from \"./impl/syscall.js\";\nimport { PublicHttpAction } from \"./registration.js\";\n\n// Note: this list is duplicated in the dashboard.\n/**\n * A list of the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport const ROUTABLE_HTTP_METHODS = [\n  \"GET\",\n  \"POST\",\n  \"PUT\",\n  \"DELETE\",\n  \"OPTIONS\",\n  \"PATCH\",\n] as const;\n/**\n * A type representing the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport type RoutableMethod = (typeof ROUTABLE_HTTP_METHODS)[number];\n\nexport function normalizeMethod(\n  method: RoutableMethod | \"HEAD\",\n): RoutableMethod {\n  // This router routes HEAD requests as GETs, letting <PERSON><PERSON><PERSON> strip thee response\n  // bodies are response bodies afterward.\n  if (method === \"HEAD\") return \"GET\";\n  return method;\n}\n\n/**\n * Return a new {@link HttpRouter} object.\n *\n * @public\n */\nexport const httpRouter = () => new HttpRouter();\n\n/**\n * A type representing a route to an HTTP action using an exact request URL path match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPath = {\n  /**\n   * Exact HTTP request path to route.\n   */\n  path: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action using a request URL path prefix match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPathPrefix = {\n  /**\n   * An HTTP request path prefix to route. Requests with a path starting with this value\n   * will be routed to the HTTP action.\n   */\n  pathPrefix: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpec = RouteSpecWithPath | RouteSpecWithPathPrefix;\n\n/**\n * HTTP router for specifying the paths and methods of {@link httpActionGeneric}s\n *\n * An example `convex/http.js` file might look like this.\n *\n * ```js\n * import { httpRouter } from \"convex/server\";\n * import { getMessagesByAuthor } from \"./getMessagesByAuthor\";\n * import { httpAction } from \"./_generated/server\";\n *\n * const http = httpRouter();\n *\n * // HTTP actions can be defined inline...\n * http.route({\n *   path: \"/message\",\n *   method: \"POST\",\n *   handler: httpAction(async ({ runMutation }, request) => {\n *     const { author, body } = await request.json();\n *\n *     await runMutation(api.sendMessage.default, { body, author });\n *     return new Response(null, {\n *       status: 200,\n *     });\n *   })\n * });\n *\n * // ...or they can be imported from other files.\n * http.route({\n *   path: \"/getMessagesByAuthor\",\n *   method: \"GET\",\n *   handler: getMessagesByAuthor,\n * });\n *\n * // Convex expects the router to be the default export of `convex/http.js`.\n * export default http;\n * ```\n *\n * @public\n */\nexport class HttpRouter {\n  exactRoutes: Map<string, Map<RoutableMethod, PublicHttpAction>> = new Map();\n  prefixRoutes: Map<RoutableMethod, Map<string, PublicHttpAction>> = new Map();\n  isRouter: true = true;\n\n  /**\n   * Specify an HttpAction to be used to respond to requests\n   * for an HTTP method (e.g. \"GET\") and a path or pathPrefix.\n   *\n   * Paths must begin with a slash. Path prefixes must also end in a slash.\n   *\n   * ```js\n   * // matches `/profile` (but not `/profile/`)\n   * http.route({ path: \"/profile\", method: \"GET\", handler: getProfile})\n   *\n   * // matches `/profiles/`, `/profiles/abc`, and `/profiles/a/c/b` (but not `/profile`)\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile})\n   * ```\n   */\n  route = (spec: RouteSpec) => {\n    if (!spec.handler) throw new Error(`route requires handler`);\n    if (!spec.method) throw new Error(`route requires method`);\n    const { method, handler } = spec;\n    if (!ROUTABLE_HTTP_METHODS.includes(method)) {\n      throw new Error(\n        `'${method}' is not an allowed HTTP method (like GET, POST, PUT etc.)`,\n      );\n    }\n\n    if (\"path\" in spec) {\n      if (\"pathPrefix\" in spec) {\n        throw new Error(\n          `Invalid httpRouter route: cannot contain both 'path' and 'pathPrefix'`,\n        );\n      }\n      if (!spec.path.startsWith(\"/\")) {\n        throw new Error(`path '${spec.path}' does not start with a /`);\n      }\n      if (spec.path.startsWith(\"/.files/\") || spec.path === \"/.files\") {\n        throw new Error(`path '${spec.path}' is reserved`);\n      }\n      const methods: Map<RoutableMethod, PublicHttpAction> =\n        this.exactRoutes.has(spec.path)\n          ? this.exactRoutes.get(spec.path)!\n          : new Map();\n      if (methods.has(method)) {\n        throw new Error(\n          `Path '${spec.path}' for method ${method} already in use`,\n        );\n      }\n      methods.set(method, handler);\n      this.exactRoutes.set(spec.path, methods);\n    } else if (\"pathPrefix\" in spec) {\n      if (!spec.pathPrefix.startsWith(\"/\")) {\n        throw new Error(\n          `pathPrefix '${spec.pathPrefix}' does not start with a /`,\n        );\n      }\n      if (!spec.pathPrefix.endsWith(\"/\")) {\n        throw new Error(`pathPrefix ${spec.pathPrefix} must end with a /`);\n      }\n      if (spec.pathPrefix.startsWith(\"/.files/\")) {\n        throw new Error(`pathPrefix '${spec.pathPrefix}' is reserved`);\n      }\n      const prefixes =\n        this.prefixRoutes.get(method) || new Map<string, PublicHttpAction>();\n      if (prefixes.has(spec.pathPrefix)) {\n        throw new Error(\n          `${spec.method} pathPrefix ${spec.pathPrefix} is already defined`,\n        );\n      }\n      prefixes.set(spec.pathPrefix, handler);\n      this.prefixRoutes.set(method, prefixes);\n    } else {\n      throw new Error(\n        `Invalid httpRouter route entry: must contain either field 'path' or 'pathPrefix'`,\n      );\n    }\n  };\n\n  /**\n   * Returns a list of routed HTTP actions.\n   *\n   * These are used to populate the list of routes shown in the Functions page of the Convex dashboard.\n   *\n   * @returns - an array of [path, method, endpoint] tuples.\n   */\n  getRoutes = (): Array<\n    Readonly<[string, RoutableMethod, PublicHttpAction]>\n  > => {\n    const exactPaths: string[] = [...this.exactRoutes.keys()].sort();\n    const exact = exactPaths.flatMap((path) =>\n      [...this.exactRoutes.get(path)!.keys()]\n        .sort()\n        .map(\n          (method) =>\n            [path, method, this.exactRoutes.get(path)!.get(method)!] as const,\n        ),\n    );\n\n    const prefixPathMethods = [...this.prefixRoutes.keys()].sort();\n    const prefixes = prefixPathMethods.flatMap((method) =>\n      [...this.prefixRoutes.get(method)!.keys()]\n        .sort()\n        .map(\n          (pathPrefix) =>\n            [\n              `${pathPrefix}*`,\n              method,\n              this.prefixRoutes.get(method)!.get(pathPrefix)!,\n            ] as const,\n        ),\n    );\n\n    return [...exact, ...prefixes];\n  };\n\n  /**\n   * Returns the appropriate HTTP action and its routed request path and method.\n   *\n   * The path and method returned are used for logging and metrics, and should\n   * match up with one of the routes returned by `getRoutes`.\n   *\n   * For example,\n   *\n   * ```js\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile});\n   *\n   * http.lookup(\"/profile/abc\", \"GET\") // returns [getProfile, \"GET\", \"/profile/*\"]\n   *```\n   *\n   * @returns - a tuple [{@link PublicHttpAction}, method, path] or null.\n   */\n  lookup = (\n    path: string,\n    method: RoutableMethod | \"HEAD\",\n  ): Readonly<[PublicHttpAction, RoutableMethod, string]> | null => {\n    method = normalizeMethod(method);\n    const exactMatch = this.exactRoutes.get(path)?.get(method);\n    if (exactMatch) return [exactMatch, method, path];\n\n    const prefixes = this.prefixRoutes.get(method) || new Map();\n    const prefixesSorted = [...prefixes.entries()].sort(\n      ([prefixA, _a], [prefixB, _b]) => prefixB.length - prefixA.length,\n    );\n    for (const [pathPrefix, endpoint] of prefixesSorted) {\n      if (path.startsWith(pathPrefix)) {\n        return [endpoint, method, `${pathPrefix}*`];\n      }\n    }\n    return null;\n  };\n\n  /**\n   * Given a JSON string representation of a Request object, return a Response\n   * by routing the request and running the appropriate endpoint or returning\n   * a 404 Response.\n   *\n   * @param argsStr - a JSON string representing a Request object.\n   *\n   * @returns - a Response object.\n   */\n  runRequest = async (\n    argsStr: string,\n    requestRoute: string,\n  ): Promise<string> => {\n    const request = performJsSyscall(\"requestFromConvexJson\", {\n      convexJson: JSON.parse(argsStr),\n    });\n\n    let pathname = requestRoute;\n    if (!pathname || typeof pathname !== \"string\") {\n      pathname = new URL(request.url).pathname;\n    }\n\n    const method = request.method;\n    const match = this.lookup(pathname, method as RoutableMethod);\n    if (!match) {\n      const response = new Response(`No HttpAction routed for ${pathname}`, {\n        status: 404,\n      });\n      return JSON.stringify(\n        performJsSyscall(\"convexJsonFromResponse\", { response }),\n      );\n    }\n    const [endpoint, _method, _path] = match;\n    const response = await endpoint.invokeHttpAction(request);\n    return JSON.stringify(\n      performJsSyscall(\"convexJsonFromResponse\", { response }),\n    );\n  };\n}\n"], "names": ["response"], "mappings": ";;;;;;;;;;AAAA,SAAS,wBAAwB;;;;;;;;;;;AAa1B,MAAM,wBAAwB;IACnC;IACA;IACA;IACA;IACA;IACA;CACF;AAYO,SAAS,gBACd,MAAA,EACgB;IAGhB,IAAI,WAAW,OAAQ,CAAA,OAAO;IAC9B,OAAO;AACT;AAOO,MAAM,aAAa,IAAM,IAAI,WAAW;AA+FxC,MAAM,WAAW;IAAjB,aAAA;QACL,cAAA,IAAA,EAAA,eAAkE,aAAA,GAAA,IAAI,IAAI;QAC1E,cAAA,IAAA,EAAA,gBAAmE,aAAA,GAAA,IAAI,IAAI;QAC3E,cAAA,IAAA,EAAA,YAAiB;QAgBjB;;;;;;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,SAAQ,CAAC,SAAoB;YAC3B,IAAI,CAAC,KAAK,OAAA,CAAS,CAAA,MAAM,IAAI,MAAM,CAAA,sBAAA,CAAwB;YAC3D,IAAI,CAAC,KAAK,MAAA,CAAQ,CAAA,MAAM,IAAI,MAAM,CAAA,qBAAA,CAAuB;YACzD,MAAM,EAAE,MAAA,EAAQ,OAAA,CAAQ,CAAA,GAAI;YAC5B,IAAI,CAAC,sBAAsB,QAAA,CAAS,MAAM,GAAG;gBAC3C,MAAM,IAAI,MACR,CAAA,CAAA,EAAI,MAAM,CAAA,0DAAA,CAAA;YAEd;YAEA,IAAI,UAAU,MAAM;gBAClB,IAAI,gBAAgB,MAAM;oBACxB,MAAM,IAAI,MACR,CAAA,qEAAA,CAAA;gBAEJ;gBACA,IAAI,CAAC,KAAK,IAAA,CAAK,UAAA,CAAW,GAAG,GAAG;oBAC9B,MAAM,IAAI,MAAM,CAAA,MAAA,EAAS,KAAK,IAAI,CAAA,yBAAA,CAA2B;gBAC/D;gBACA,IAAI,KAAK,IAAA,CAAK,UAAA,CAAW,UAAU,KAAK,KAAK,IAAA,KAAS,WAAW;oBAC/D,MAAM,IAAI,MAAM,CAAA,MAAA,EAAS,KAAK,IAAI,CAAA,aAAA,CAAe;gBACnD;gBACA,MAAM,UACJ,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAI,IAC1B,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAI,IAC9B,aAAA,GAAA,IAAI,IAAI;gBACd,IAAI,QAAQ,GAAA,CAAI,MAAM,GAAG;oBACvB,MAAM,IAAI,MACR,CAAA,MAAA,EAAS,KAAK,IAAI,CAAA,aAAA,EAAgB,MAAM,CAAA,eAAA,CAAA;gBAE5C;gBACA,QAAQ,GAAA,CAAI,QAAQ,OAAO;gBAC3B,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAA,EAAM,OAAO;YACzC,OAAA,IAAW,gBAAgB,MAAM;gBAC/B,IAAI,CAAC,KAAK,UAAA,CAAW,UAAA,CAAW,GAAG,GAAG;oBACpC,MAAM,IAAI,MACR,CAAA,YAAA,EAAe,KAAK,UAAU,CAAA,yBAAA,CAAA;gBAElC;gBACA,IAAI,CAAC,KAAK,UAAA,CAAW,QAAA,CAAS,GAAG,GAAG;oBAClC,MAAM,IAAI,MAAM,CAAA,WAAA,EAAc,KAAK,UAAU,CAAA,kBAAA,CAAoB;gBACnE;gBACA,IAAI,KAAK,UAAA,CAAW,UAAA,CAAW,UAAU,GAAG;oBAC1C,MAAM,IAAI,MAAM,CAAA,YAAA,EAAe,KAAK,UAAU,CAAA,aAAA,CAAe;gBAC/D;gBACA,MAAM,WACJ,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,KAAK,aAAA,GAAA,IAAI,IAA8B;gBACrE,IAAI,SAAS,GAAA,CAAI,KAAK,UAAU,GAAG;oBACjC,MAAM,IAAI,MACR,GAAG,KAAK,MAAM,CAAA,YAAA,EAAe,KAAK,UAAU,CAAA,mBAAA,CAAA;gBAEhD;gBACA,SAAS,GAAA,CAAI,KAAK,UAAA,EAAY,OAAO;gBACrC,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,QAAQ,QAAQ;YACxC,OAAO;gBACL,MAAM,IAAI,MACR,CAAA,gFAAA,CAAA;YAEJ;QACF;QASA;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,aAAY,MAEP;YACH,MAAM,aAAuB,CAAC;mBAAG,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,CAAC;aAAA,CAAE,IAAA,CAAK;YAC/D,MAAM,QAAQ,WAAW,OAAA,CAAQ,CAAC,OAChC,CAAC;uBAAG,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,EAAG,IAAA,CAAK,CAAC;iBAAA,CACnC,IAAA,CAAK,EACL,GAAA,CACC,CAAC,SACC;wBAAC;wBAAM;wBAAQ,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,EAAG,GAAA,CAAI,MAAM,CAAE;qBAAA;YAI/D,MAAM,oBAAoB,CAAC;mBAAG,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,CAAC;aAAA,CAAE,IAAA,CAAK;YAC7D,MAAM,WAAW,kBAAkB,OAAA,CAAQ,CAAC,SAC1C,CAAC;uBAAG,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,EAAG,IAAA,CAAK,CAAC;iBAAA,CACtC,IAAA,CAAK,EACL,GAAA,CACC,CAAC,aACC;wBACE,GAAG,UAAU,CAAA,CAAA,CAAA;wBACb;wBACA,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,EAAG,GAAA,CAAI,UAAU;qBAC/C;YAIR,OAAO,CAAC;mBAAG,OAAO;mBAAG,QAAQ;aAAA;QAC/B;QAkBA;;;;;;;;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,UAAS,CACP,MACA,WACgE;YAChE,SAAS,gBAAgB,MAAM;YAC/B,MAAM,aAAa,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,GAAG,IAAI,MAAM;YACzD,IAAI,WAAY,CAAA,OAAO;gBAAC;gBAAY;gBAAQ,IAAI;aAAA;YAEhD,MAAM,WAAW,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,KAAK,aAAA,GAAA,IAAI,IAAI;YAC1D,MAAM,iBAAiB,CAAC;mBAAG,SAAS,OAAA,CAAQ,CAAC;aAAA,CAAE,IAAA,CAC7C,CAAC,CAAC,SAAS,EAAE,CAAA,EAAG,CAAC,SAAS,EAAE,CAAA,GAAM,QAAQ,MAAA,GAAS,QAAQ,MAAA;YAE7D,KAAA,MAAW,CAAC,YAAY,QAAQ,CAAA,IAAK,eAAgB;gBACnD,IAAI,KAAK,UAAA,CAAW,UAAU,GAAG;oBAC/B,OAAO;wBAAC;wBAAU;wBAAQ,GAAG,UAAU,CAAA,CAAA,CAAG;qBAAA;gBAC5C;YACF;YACA,OAAO;QACT;QAWA;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,cAAa,OACX,SACA,iBACoB;YACpB,MAAM,cAAU,yOAAA,EAAiB,yBAAyB;gBACxD,YAAY,KAAK,KAAA,CAAM,OAAO;YAChC,CAAC;YAED,IAAI,WAAW;YACf,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;gBAC7C,WAAW,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAA;YAClC;YAEA,MAAM,SAAS,QAAQ,MAAA;YACvB,MAAM,QAAQ,IAAA,CAAK,MAAA,CAAO,UAAU,MAAwB;YAC5D,IAAI,CAAC,OAAO;gBACV,MAAMA,YAAW,IAAI,SAAS,CAAA,yBAAA,EAA4B,QAAQ,EAAA,EAAI;oBACpE,QAAQ;gBACV,CAAC;gBACD,OAAO,KAAK,SAAA,KACV,yOAAA,EAAiB,0BAA0B;oBAAE,UAAAA;gBAAS,CAAC;YAE3D;YACA,MAAM,CAAC,UAAU,SAAS,KAAK,CAAA,GAAI;YACnC,MAAM,WAAW,MAAM,SAAS,gBAAA,CAAiB,OAAO;YACxD,OAAO,KAAK,SAAA,KACV,yOAAA,EAAiB,0BAA0B;gBAAE;YAAS,CAAC;QAE3D;IAAA;AACF", "debugId": null}}, {"offset": {"line": 3557, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/components/index.ts"], "sourcesContent": ["import { PropertyValidators, convexToJson } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport {\n  AnyFunctionReference,\n  FunctionReference,\n  FunctionType,\n} from \"../api.js\";\nimport { performAsyncSyscall } from \"../impl/syscall.js\";\nimport { DefaultFunctionArgs } from \"../registration.js\";\nimport {\n  AppDefinitionAnalysis,\n  ComponentDefinitionAnalysis,\n  ComponentDefinitionType,\n} from \"./definition.js\";\nimport {\n  getFunctionAddress,\n  setReferencePath,\n  toReferencePath,\n} from \"./paths.js\";\nexport { getFunctionAddress } from \"./paths.js\";\n\n/**\n * A serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type FunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n> = string & FunctionReference<Type, \"internal\", Args, ReturnType>;\n\n/**\n * Create a serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport async function createFunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs,\n  ReturnType,\n>(\n  functionReference: FunctionReference<\n    Type,\n    \"public\" | \"internal\",\n    Args,\n    ReturnType\n  >,\n): Promise<FunctionHandle<Type, Args, ReturnType>> {\n  const address = getFunctionAddress(functionReference);\n  return await performAsyncSyscall(\"1.0/createFunctionHandle\", {\n    ...address,\n    version,\n  });\n}\n\ninterface ComponentExports {\n  [key: string]: FunctionReference<any, any, any, any> | ComponentExports;\n}\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component definition directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ComponentDefinition<Exports extends ComponentExports = any> = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n\n  /**\n   * Internal type-only property tracking exports provided.\n   *\n   * @deprecated This is a type-only property, don't use it.\n   */\n  __exports: Exports;\n};\n\ntype ComponentDefinitionExports<T extends ComponentDefinition<any>> =\n  T[\"__exports\"];\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component-aware convex directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type AppDefinition = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n};\n\ninterface ExportTree {\n  // Tree with serialized `Reference`s as leaves.\n  [key: string]: string | ExportTree;\n}\n\ntype CommonDefinitionData = {\n  _isRoot: boolean;\n  _childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][];\n  _exportTree: ExportTree;\n};\n\ntype ComponentDefinitionData = CommonDefinitionData & {\n  _args: PropertyValidators;\n  _name: string;\n  _onInitCallbacks: Record<string, (argsStr: string) => string>;\n};\ntype AppDefinitionData = CommonDefinitionData;\n\n/**\n * Used to refer to an already-installed component.\n */\nclass InstalledComponent<Definition extends ComponentDefinition<any>> {\n  /**\n   * @internal\n   */\n  _definition: Definition;\n\n  /**\n   * @internal\n   */\n  _name: string;\n\n  constructor(definition: Definition, name: string) {\n    this._definition = definition;\n    this._name = name;\n    setReferencePath(this, `_reference/childComponent/${name}`);\n  }\n\n  get exports(): ComponentDefinitionExports<Definition> {\n    return createExports(this._name, []);\n  }\n}\n\nfunction createExports(name: string, pathParts: string[]): any {\n  const handler: ProxyHandler<any> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createExports(name, newParts);\n      } else if (prop === toReferencePath) {\n        let reference = `_reference/childComponent/${name}`;\n        for (const part of pathParts) {\n          reference += `/${part}`;\n        }\n        return reference;\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nfunction use<Definition extends ComponentDefinition<any>>(\n  this: CommonDefinitionData,\n  definition: Definition,\n  options?: {\n    name?: string;\n  },\n): InstalledComponent<Definition> {\n  // At runtime an imported component will have this shape.\n  const importedComponentDefinition =\n    definition as unknown as ImportedComponentDefinition;\n  if (typeof importedComponentDefinition.componentDefinitionPath !== \"string\") {\n    throw new Error(\n      \"Component definition does not have the required componentDefinitionPath property. This code only works in Convex runtime.\",\n    );\n  }\n  const name =\n    options?.name ||\n    // added recently\n    importedComponentDefinition.defaultName ||\n    // can be removed once backend is out\n    importedComponentDefinition.componentDefinitionPath.split(\"/\").pop()!;\n  this._childComponents.push([name, importedComponentDefinition, {}]);\n  return new InstalledComponent(definition, name);\n}\n\n/**\n * The runtime type of a ComponentDefinition. TypeScript will claim\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ImportedComponentDefinition = {\n  componentDefinitionPath: string;\n  defaultName: string;\n};\n\nfunction exportAppForAnalysis(\n  this: ComponentDefinition<any> & AppDefinitionData,\n): AppDefinitionAnalysis {\n  const definitionType = { type: \"app\" as const };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\nfunction serializeExportTree(tree: ExportTree): any {\n  const branch: any[] = [];\n  for (const [key, child] of Object.entries(tree)) {\n    let node;\n    if (typeof child === \"string\") {\n      node = { type: \"leaf\", leaf: child };\n    } else {\n      node = serializeExportTree(child);\n    }\n    branch.push([key, node]);\n  }\n  return { type: \"branch\", branch };\n}\n\nfunction serializeChildComponents(\n  childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][],\n): {\n  name: string;\n  path: string;\n  args: [string, { type: \"value\"; value: string }][] | null;\n}[] {\n  return childComponents.map(([name, definition, p]) => {\n    let args: [string, { type: \"value\"; value: string }][] | null = null;\n    if (p !== null) {\n      args = [];\n      for (const [name, value] of Object.entries(p)) {\n        if (value !== undefined) {\n          args.push([\n            name,\n            { type: \"value\", value: JSON.stringify(convexToJson(value)) },\n          ]);\n        }\n      }\n    }\n    // we know that components carry this extra information\n    const path = definition.componentDefinitionPath;\n    if (!path)\n      throw new Error(\n        \"no .componentPath for component definition \" +\n          JSON.stringify(definition, null, 2),\n      );\n\n    return {\n      name: name!,\n      path: path!,\n      args,\n    };\n  });\n}\n\nfunction exportComponentForAnalysis(\n  this: ComponentDefinition<any> & ComponentDefinitionData,\n): ComponentDefinitionAnalysis {\n  const args: [string, { type: \"value\"; value: string }][] = Object.entries(\n    this._args,\n  ).map(([name, validator]) => [\n    name,\n    {\n      type: \"value\",\n      value: JSON.stringify(validator.json),\n    },\n  ]);\n  const definitionType: ComponentDefinitionType = {\n    type: \"childComponent\" as const,\n    name: this._name,\n    args,\n  };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    name: this._name,\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\n// This is what is actually contained in a ComponentDefinition.\ntype RuntimeComponentDefinition = Omit<ComponentDefinition<any>, \"__exports\"> &\n  ComponentDefinitionData & {\n    export: () => ComponentDefinitionAnalysis;\n  };\ntype RuntimeAppDefinition = AppDefinition &\n  AppDefinitionData & {\n    export: () => AppDefinitionAnalysis;\n  };\n\n/**\n * Define a component, a piece of a Convex deployment with namespaced resources.\n *\n * The default\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * @param name Name must be alphanumeric plus underscores. Typically these are\n * lowercase with underscores like `\"onboarding_flow_tracker\"`.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineComponent<Exports extends ComponentExports = any>(\n  name: string,\n): ComponentDefinition<Exports> {\n  const ret: RuntimeComponentDefinition = {\n    _isRoot: false,\n    _name: name,\n    _args: {},\n    _childComponents: [],\n    _exportTree: {},\n    _onInitCallbacks: {},\n\n    export: exportComponentForAnalysis,\n    use,\n\n    // pretend to conform to ComponentDefinition, which temporarily expects __args\n    ...({} as { __args: any; __exports: any }),\n  };\n  return ret as any as ComponentDefinition<Exports>;\n}\n\n/**\n * Attach components, reuseable pieces of a Convex deployment, to this Convex app.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineApp(): AppDefinition {\n  const ret: RuntimeAppDefinition = {\n    _isRoot: true,\n    _childComponents: [],\n    _exportTree: {},\n\n    export: exportAppForAnalysis,\n    use,\n  };\n  return ret as AppDefinition;\n}\n\ntype AnyInterfaceType = {\n  [key: string]: AnyInterfaceType;\n} & AnyFunctionReference;\nexport type AnyComponentReference = Record<string, AnyInterfaceType>;\n\nexport type AnyChildComponents = Record<string, AnyComponentReference>;\n\n/**\n * @internal\n */\nexport function currentSystemUdfInComponent(\n  componentId: string,\n): AnyComponentReference {\n  return {\n    [toReferencePath]: `_reference/currentSystemUdfInComponent/${componentId}`,\n  };\n}\n\nfunction createChildComponents(\n  root: string,\n  pathParts: string[],\n): AnyChildComponents {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createChildComponents(root, newParts);\n      } else if (prop === toReferencePath) {\n        if (pathParts.length < 1) {\n          const found = [root, ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`${root}.childComponent.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        return `_reference/childComponent/` + pathParts.join(\"/\");\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nexport const componentsGeneric = () => createChildComponents(\"components\", []);\n\nexport type AnyComponents = AnyChildComponents;\n"], "names": ["name"], "mappings": ";;;;;;;;;;;;AAAA,SAA6B,oBAAoB;;AACjD,SAAS,eAAe;AAMxB,SAAS,2BAA2B;AAOpC;;;;;;;;;;;;;;;AAuCA,eAAsB,qBAKpB,iBAAA,EAMiD;IACjD,MAAM,cAAU,+OAAA,EAAmB,iBAAiB;IACpD,OAAO,UAAM,4OAAA,EAAoB,4BAA4B;QAC3D,GAAG,OAAA;iBACH,4MAAA;IACF,CAAC;AACH;AAyFA,MAAM,mBAAgE;IAWpE,YAAY,UAAA,EAAwB,IAAA,CAAc;QAPlD;;KAAA,GAAA,cAAA,IAAA,EAAA;QAKA;;KAAA,GAAA,cAAA,IAAA,EAAA;QAGE,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,6OAAA,EAAiB,IAAA,EAAM,CAAA,0BAAA,EAA6B,IAAI,EAAE;IAC5D;IAEA,IAAI,UAAkD;QACpD,OAAO,cAAc,IAAA,CAAK,KAAA,EAAO,CAAC,CAAC;IACrC;AACF;AAEA,SAAS,cAAc,IAAA,EAAc,SAAA,EAA0B;IAC7D,MAAM,UAA6B;QACjC,KAAI,CAAA,EAAG,IAAA,EAAuB;YAC5B,IAAI,OAAO,SAAS,UAAU;gBAC5B,MAAM,WAAW,CAAC;uBAAG;oBAAW,IAAI;iBAAA;gBACpC,OAAO,cAAc,MAAM,QAAQ;YACrC,OAAA,IAAW,SAAS,4OAAA,EAAiB;gBACnC,IAAI,YAAY,CAAA,0BAAA,EAA6B,IAAI,EAAA;gBACjD,KAAA,MAAW,QAAQ,UAAW;oBAC5B,aAAa,CAAA,CAAA,EAAI,IAAI,EAAA;gBACvB;gBACA,OAAO;YACT,OAAO;gBACL,OAAO,KAAA;YACT;QACF;IACF;IACA,OAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEA,SAAS,IAEP,UAAA,EACA,OAAA,EAGgC;IAEhC,MAAM,8BACJ;IACF,IAAI,OAAO,4BAA4B,uBAAA,KAA4B,UAAU;QAC3E,MAAM,IAAI,MACR;IAEJ;IACA,MAAM,OACJ,SAAS,QAAA,iBAAA;IAET,4BAA4B,WAAA,IAAA,qCAAA;IAE5B,4BAA4B,uBAAA,CAAwB,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI;IACrE,IAAA,CAAK,gBAAA,CAAiB,IAAA,CAAK;QAAC;QAAM;QAA6B,CAAC,CAAC;KAAC;IAClE,OAAO,IAAI,mBAAmB,YAAY,IAAI;AAChD;AAgBA,SAAS,uBAEgB;IACvB,MAAM,iBAAiB;QAAE,MAAM;IAAe;IAC9C,MAAM,kBAAkB,yBAAyB,IAAA,CAAK,gBAAgB;IACtE,OAAO;QACL;QACA;QACA,YAAY,CAAC;QACb,SAAS,oBAAoB,IAAA,CAAK,WAAW;IAC/C;AACF;AAEA,SAAS,oBAAoB,IAAA,EAAuB;IAClD,MAAM,SAAgB,CAAC,CAAA;IACvB,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,IAAI,EAAG;QAC/C,IAAI;QACJ,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;gBAAE,MAAM;gBAAQ,MAAM;YAAM;QACrC,OAAO;YACL,OAAO,oBAAoB,KAAK;QAClC;QACA,OAAO,IAAA,CAAK;YAAC;YAAK,IAAI;SAAC;IACzB;IACA,OAAO;QAAE,MAAM;QAAU;IAAO;AAClC;AAEA,SAAS,yBACP,eAAA,EASE;IACF,OAAO,gBAAgB,GAAA,CAAI,CAAC,CAAC,MAAM,YAAY,CAAC,CAAA,KAAM;QACpD,IAAI,OAA4D;QAChE,IAAI,MAAM,MAAM;YACd,OAAO,CAAC,CAAA;YACR,KAAA,MAAW,CAACA,OAAM,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,CAAC,EAAG;gBAC7C,IAAI,UAAU,KAAA,GAAW;oBACvB,KAAK,IAAA,CAAK;wBACRA;wBACA;4BAAE,MAAM;4BAAS,OAAO,KAAK,SAAA,KAAU,2NAAA,EAAa,KAAK,CAAC;wBAAE;qBAC7D;gBACH;YACF;QACF;QAEA,MAAM,OAAO,WAAW,uBAAA;QACxB,IAAI,CAAC,MACH,MAAM,IAAI,MACR,gDACE,KAAK,SAAA,CAAU,YAAY,MAAM,CAAC;QAGxC,OAAO;YACL;YACA;YACA;QACF;IACF,CAAC;AACH;AAEA,SAAS,6BAEsB;IAC7B,MAAM,OAAqD,OAAO,OAAA,CAChE,IAAA,CAAK,KAAA,EACL,GAAA,CAAI,CAAC,CAAC,MAAM,SAAS,CAAA,GAAM;YAC3B;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,SAAA,CAAU,UAAU,IAAI;YACtC;SACD;IACD,MAAM,iBAA0C;QAC9C,MAAM;QACN,MAAM,IAAA,CAAK,KAAA;QACX;IACF;IACA,MAAM,kBAAkB,yBAAyB,IAAA,CAAK,gBAAgB;IACtE,OAAO;QACL,MAAM,IAAA,CAAK,KAAA;QACX;QACA;QACA,YAAY,CAAC;QACb,SAAS,oBAAoB,IAAA,CAAK,WAAW;IAC/C;AACF;AA0BO,SAAS,gBACd,IAAA,EAC8B;IAC9B,MAAM,MAAkC;QACtC,SAAS;QACT,OAAO;QACP,OAAO,CAAC;QACR,kBAAkB,CAAC,CAAA;QACnB,aAAa,CAAC;QACd,kBAAkB,CAAC;QAEnB,QAAQ;QACR;QAAA,8EAAA;QAGA,GAAI,CAAC,CAAA;IACP;IACA,OAAO;AACT;AAQO,SAAS,YAA2B;IACzC,MAAM,MAA4B;QAChC,SAAS;QACT,kBAAkB,CAAC,CAAA;QACnB,aAAa,CAAC;QAEd,QAAQ;QACR;IACF;IACA,OAAO;AACT;AAYO,SAAS,4BACd,WAAA,EACuB;IACvB,OAAO;QACL,CAAC,4OAAe,CAAA,EAAG,CAAA,uCAAA,EAA0C,WAAW,EAAA;IAC1E;AACF;AAEA,SAAS,sBACP,IAAA,EACA,SAAA,EACoB;IACpB,MAAM,UAAgC;QACpC,KAAI,CAAA,EAAG,IAAA,EAAuB;YAC5B,IAAI,OAAO,SAAS,UAAU;gBAC5B,MAAM,WAAW,CAAC;uBAAG;oBAAW,IAAI;iBAAA;gBACpC,OAAO,sBAAsB,MAAM,QAAQ;YAC7C,OAAA,IAAW,SAAS,4OAAA,EAAiB;gBACnC,IAAI,UAAU,MAAA,GAAS,GAAG;oBACxB,MAAM,QAAQ;wBAAC,MAAM;2BAAG,SAAS;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC3C,MAAM,IAAI,MACR,CAAA,yCAAA,EAA4C,IAAI,CAAA,yCAAA,EAA4C,KAAK,CAAA,EAAA,CAAA;gBAErG;gBACA,OAAO,CAAA,0BAAA,CAAA,GAA+B,UAAU,IAAA,CAAK,GAAG;YAC1D,OAAO;gBACL,OAAO,KAAA;YACT;QACF;IACF;IACA,OAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEO,MAAM,oBAAoB,IAAM,sBAAsB,cAAc,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 3790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/schema.ts"], "sourcesContent": ["/**\n * Utilities for defining the schema of your Convex project.\n *\n * ## Usage\n *\n * Schemas should be placed in a `schema.ts` file in your `convex/` directory.\n *\n * Schema definitions should be built using {@link defineSchema},\n * {@link defineTable}, and {@link values.v}. Make sure to export the schema as the\n * default export.\n *\n * ```ts\n * import { defineSchema, defineTable } from \"convex/server\";\n * import { v } from \"convex/values\";\n *\n *  export default defineSchema({\n *    messages: defineTable({\n *      body: v.string(),\n *      user: v.id(\"users\"),\n *    }),\n *    users: defineTable({\n *      name: v.string(),\n *    }),\n *  });\n * ```\n *\n * To learn more about schemas, see [Defining a Schema](https://docs.convex.dev/using/schemas).\n * @module\n */\nimport {\n  AnyDataModel,\n  GenericDataModel,\n  GenericTableIndexes,\n  GenericTableSearchIndexes,\n  GenericTableVectorIndexes,\n  TableNamesInDataModel,\n} from \"../server/data_model.js\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  IndexTiebreakerField,\n  SystemFields,\n  SystemIndexes,\n} from \"../server/system_fields.js\";\nimport { Expand } from \"../type_utils.js\";\nimport {\n  GenericValidator,\n  ObjectType,\n  isValidator,\n  v,\n} from \"../values/validator.js\";\nimport { VObject, Validator } from \"../values/validators.js\";\n\n/**\n * Extract all of the index field paths within a {@link Validator}.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractFieldPaths<T extends Validator<any, any, any>> =\n  // Add in the system fields available in index definitions.\n  // This should be everything except for `_id` because thats added to indexes\n  // automatically.\n  T[\"fieldPaths\"] | keyof SystemFields;\n\n/**\n * Extract the {@link GenericDocument} within a {@link Validator} and\n * add on the system fields.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractDocument<T extends Validator<any, any, any>> =\n  // Add the system fields to `Value` (except `_id` because it depends on\n  //the table name) and trick TypeScript into expanding them.\n  Expand<SystemFields & T[\"type\"]>;\n\nexport interface DbIndexConfig<\n  FirstFieldPath extends string,\n  RestFieldPaths extends string[],\n> {\n  /**\n   * The fields to index, in order. Must specify at least one field.\n   */\n  fields: [FirstFieldPath, ...RestFieldPaths];\n}\n\n/**\n * The configuration for a full text search index.\n *\n * @public\n */\nexport interface SearchIndexConfig<\n  SearchField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for full text search.\n   *\n   * This must be a field of type `string`.\n   */\n  searchField: SearchField;\n\n  /**\n   * Additional fields to index for fast filtering when running search queries.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * The configuration for a vector index.\n *\n * @public\n */\nexport interface VectorIndexConfig<\n  VectorField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for vector search.\n   *\n   * This must be a field of type `v.array(v.float64())` (or a union)\n   */\n  vectorField: VectorField;\n  /**\n   * The length of the vectors indexed. This must be between 2 and 2048 inclusive.\n   */\n  dimensions: number;\n  /**\n   * Additional fields to index for fast filtering when running vector searches.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * @internal\n */\nexport type VectorIndex = {\n  indexDescriptor: string;\n  vectorField: string;\n  dimensions: number;\n  filterFields: string[];\n};\n\n/**\n * @internal\n */\nexport type Index = {\n  indexDescriptor: string;\n  fields: string[];\n};\n\n/**\n * @internal\n */\nexport type SearchIndex = {\n  indexDescriptor: string;\n  searchField: string;\n  filterFields: string[];\n};\n\n/**\n * Options for defining an index.\n *\n * @public\n */\nexport interface IndexOptions {\n  /**\n   * Whether the index should be staged.\n   *\n   * For large tables, index backfill can be slow. Staging an index allows you\n   * to push the schema and enable the index later.\n   *\n   * If `staged` is `true`, the index will be staged and will not be enabled\n   * until the staged flag is removed. Staged indexes do not block push\n   * completion. Staged indexes cannot be used in queries.\n   */\n  staged?: boolean;\n}\n\n/**\n * The definition of a table within a schema.\n *\n * This should be produced by using {@link defineTable}.\n * @public\n */\nexport class TableDefinition<\n  DocumentType extends Validator<any, any, any> = Validator<any, any, any>,\n  Indexes extends GenericTableIndexes = {},\n  SearchIndexes extends GenericTableSearchIndexes = {},\n  VectorIndexes extends GenericTableVectorIndexes = {},\n> {\n  private indexes: Index[];\n  private stagedDbIndexes: Index[];\n  private searchIndexes: SearchIndex[];\n  private stagedSearchIndexes: SearchIndex[];\n  private vectorIndexes: VectorIndex[];\n  private stagedVectorIndexes: VectorIndex[];\n  // The type of documents stored in this table.\n  validator: DocumentType;\n\n  /**\n   * @internal\n   */\n  constructor(documentType: DocumentType) {\n    this.indexes = [];\n    this.stagedDbIndexes = [];\n    this.searchIndexes = [];\n    this.stagedSearchIndexes = [];\n    this.vectorIndexes = [];\n    this.stagedVectorIndexes = [];\n    this.validator = documentType;\n  }\n\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * Returns indexes defined on this table.\n   * Intended for the advanced use cases of dynamically deciding which index to use for a query.\n   * If you think you need this, please chime in on ths issue in the Convex JS GitHub repo.\n   * https://github.com/get-convex/convex-js/issues/49\n   */\n  \" indexes\"(): { indexDescriptor: string; fields: string[] }[] {\n    return this.indexes;\n  }\n\n  /**\n   * Define an index on this table.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The index configuration object.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      DbIndexConfig<FirstFieldPath, RestFieldPaths> &\n        IndexOptions & { staged?: false }\n    >,\n  ): TableDefinition<\n    DocumentType,\n    Expand<\n      Indexes &\n        Record<\n          IndexName,\n          [FirstFieldPath, ...RestFieldPaths, IndexTiebreakerField]\n        >\n    >,\n    SearchIndexes,\n    VectorIndexes\n  >;\n\n  /**\n   * Define an index on this table.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param fields - The fields to index, in order. Must specify at least one\n   * field.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    fields: [FirstFieldPath, ...RestFieldPaths],\n  ): TableDefinition<\n    DocumentType,\n    Expand<\n      Indexes &\n        Record<\n          IndexName,\n          [FirstFieldPath, ...RestFieldPaths, IndexTiebreakerField]\n        >\n    >,\n    SearchIndexes,\n    VectorIndexes\n  >;\n\n  /**\n   * Define a staged index on this table.\n   *\n   * For large tables, index backfill can be slow. Staging an index allows you\n   * to push the schema and enable the index later.\n   *\n   * If `staged` is `true`, the index will be staged and will not be enabled\n   * until the staged flag is removed. Staged indexes do not block push\n   * completion. Staged indexes cannot be used in queries.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The index configuration object.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      DbIndexConfig<FirstFieldPath, RestFieldPaths> &\n        IndexOptions & { staged: true }\n    >,\n  ): TableDefinition<DocumentType, Indexes, SearchIndexes, VectorIndexes>;\n\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    indexConfig:\n      | Expand<DbIndexConfig<FirstFieldPath, RestFieldPaths> & IndexOptions>\n      | [FirstFieldPath, ...RestFieldPaths],\n  ) {\n    if (Array.isArray(indexConfig)) {\n      // indexConfig is [FirstFieldPath, ...RestFieldPaths]\n      this.indexes.push({\n        indexDescriptor: name,\n        fields: indexConfig,\n      });\n    } else if (indexConfig.staged) {\n      // indexConfig is object with fields and staged: true\n      this.stagedDbIndexes.push({\n        indexDescriptor: name,\n        fields: indexConfig.fields,\n      });\n    } else {\n      // indexConfig is object with fields (and maybe staged: false/undefined)\n      this.indexes.push({\n        indexDescriptor: name,\n        fields: indexConfig.fields,\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Define a search index on this table.\n   *\n   * To learn about search indexes, see [Search](https://docs.convex.dev/text-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The search index configuration object.\n   * @returns A {@link TableDefinition} with this search index included.\n   */\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      SearchIndexConfig<SearchField, FilterFields> &\n        IndexOptions & { staged?: false }\n    >,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    // Update `SearchIndexes` to include the new index and use `Expand` to make\n    // the types look pretty in editors.\n    Expand<\n      SearchIndexes &\n        Record<\n          IndexName,\n          {\n            searchField: SearchField;\n            filterFields: FilterFields;\n          }\n        >\n    >,\n    VectorIndexes\n  >;\n\n  /**\n   * Define a staged search index on this table.\n   *\n   * For large tables, index backfill can be slow. Staging an index allows you\n   * to push the schema and enable the index later.\n   *\n   * If `staged` is `true`, the index will be staged and will not be enabled\n   * until the staged flag is removed. Staged indexes do not block push\n   * completion. Staged indexes cannot be used in queries.\n   *\n   * To learn about search indexes, see [Search](https://docs.convex.dev/text-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The search index configuration object.\n   * @returns A {@link TableDefinition} with this search index included.\n   */\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      SearchIndexConfig<SearchField, FilterFields> &\n        IndexOptions & { staged: true }\n    >,\n  ): TableDefinition<DocumentType, Indexes, SearchIndexes, VectorIndexes>;\n\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      SearchIndexConfig<SearchField, FilterFields> & IndexOptions\n    >,\n  ) {\n    if (indexConfig.staged) {\n      this.stagedSearchIndexes.push({\n        indexDescriptor: name,\n        searchField: indexConfig.searchField,\n        filterFields: indexConfig.filterFields || [],\n      });\n    } else {\n      this.searchIndexes.push({\n        indexDescriptor: name,\n        searchField: indexConfig.searchField,\n        filterFields: indexConfig.filterFields || [],\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Define a vector index on this table.\n   *\n   * To learn about vector indexes, see [Vector Search](https://docs.convex.dev/vector-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The vector index configuration object.\n   * @returns A {@link TableDefinition} with this vector index included.\n   */\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      VectorIndexConfig<VectorField, FilterFields> &\n        IndexOptions & { staged?: false }\n    >,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    Expand<\n      VectorIndexes &\n        Record<\n          IndexName,\n          {\n            vectorField: VectorField;\n            dimensions: number;\n            filterFields: FilterFields;\n          }\n        >\n    >\n  >;\n\n  /**\n   * Define a staged vector index on this table.\n   *\n   * For large tables, index backfill can be slow. Staging an index allows you\n   * to push the schema and enable the index later.\n   *\n   * If `staged` is `true`, the index will be staged and will not be enabled\n   * until the staged flag is removed. Staged indexes do not block push\n   * completion. Staged indexes cannot be used in queries.\n   *\n   * To learn about vector indexes, see [Vector Search](https://docs.convex.dev/vector-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The vector index configuration object.\n   * @returns A {@link TableDefinition} with this vector index included.\n   */\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      VectorIndexConfig<VectorField, FilterFields> &\n        IndexOptions & { staged: true }\n    >,\n  ): TableDefinition<DocumentType, Indexes, SearchIndexes, VectorIndexes>;\n\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<\n      VectorIndexConfig<VectorField, FilterFields> & IndexOptions\n    >,\n  ) {\n    if (indexConfig.staged) {\n      this.stagedVectorIndexes.push({\n        indexDescriptor: name,\n        vectorField: indexConfig.vectorField,\n        dimensions: indexConfig.dimensions,\n        filterFields: indexConfig.filterFields || [],\n      });\n    } else {\n      this.vectorIndexes.push({\n        indexDescriptor: name,\n        vectorField: indexConfig.vectorField,\n        dimensions: indexConfig.dimensions,\n        filterFields: indexConfig.filterFields || [],\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Work around for https://github.com/microsoft/TypeScript/issues/57035\n   */\n  protected self(): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    return this;\n  }\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export() {\n    const documentType = this.validator.json;\n    if (typeof documentType !== \"object\") {\n      throw new Error(\n        \"Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)\",\n      );\n    }\n\n    return {\n      indexes: this.indexes,\n      stagedDbIndexes: this.stagedDbIndexes,\n      searchIndexes: this.searchIndexes,\n      stagedSearchIndexes: this.stagedSearchIndexes,\n      vectorIndexes: this.vectorIndexes,\n      stagedVectorIndexes: this.stagedVectorIndexes,\n      documentType,\n    };\n  }\n}\n\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Validator<Record<string, any>, \"required\", any>,\n>(documentSchema: DocumentSchema): TableDefinition<DocumentSchema>;\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Record<string, GenericValidator>,\n>(\n  documentSchema: DocumentSchema,\n): TableDefinition<VObject<ObjectType<DocumentSchema>, DocumentSchema>>;\nexport function defineTable<\n  DocumentSchema extends\n    | Validator<Record<string, any>, \"required\", any>\n    | Record<string, GenericValidator>,\n>(documentSchema: DocumentSchema): TableDefinition<any, any, any> {\n  if (isValidator(documentSchema)) {\n    return new TableDefinition(documentSchema);\n  } else {\n    return new TableDefinition(v.object(documentSchema));\n  }\n}\n\n/**\n * A type describing the schema of a Convex project.\n *\n * This should be constructed using {@link defineSchema}, {@link defineTable},\n * and {@link v}.\n * @public\n */\nexport type GenericSchema = Record<string, TableDefinition>;\n\n/**\n *\n * The definition of a Convex project schema.\n *\n * This should be produced by using {@link defineSchema}.\n * @public\n */\nexport class SchemaDefinition<\n  Schema extends GenericSchema,\n  StrictTableTypes extends boolean,\n> {\n  public tables: Schema;\n  public strictTableNameTypes!: StrictTableTypes;\n  public readonly schemaValidation: boolean;\n\n  /**\n   * @internal\n   */\n  constructor(tables: Schema, options?: DefineSchemaOptions<StrictTableTypes>) {\n    this.tables = tables;\n    this.schemaValidation =\n      options?.schemaValidation === undefined ? true : options.schemaValidation;\n  }\n\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export(): string {\n    return JSON.stringify({\n      tables: Object.entries(this.tables).map(([tableName, definition]) => {\n        const {\n          indexes,\n          stagedDbIndexes,\n          searchIndexes,\n          stagedSearchIndexes,\n          vectorIndexes,\n          stagedVectorIndexes,\n          documentType,\n        } = definition.export();\n        return {\n          tableName,\n          indexes,\n          stagedDbIndexes,\n          searchIndexes,\n          stagedSearchIndexes,\n          vectorIndexes,\n          stagedVectorIndexes,\n          documentType,\n        };\n      }),\n      schemaValidation: this.schemaValidation,\n    });\n  }\n}\n\n/**\n * Options for {@link defineSchema}.\n *\n * @public\n */\nexport interface DefineSchemaOptions<StrictTableNameTypes extends boolean> {\n  /**\n   * Whether Convex should validate at runtime that all documents match\n   * your schema.\n   *\n   * If `schemaValidation` is `true`, Convex will:\n   * 1. Check that all existing documents match your schema when your schema\n   * is pushed.\n   * 2. Check that all insertions and updates match your schema during mutations.\n   *\n   * If `schemaValidation` is `false`, Convex will not validate that new or\n   * existing documents match your schema. You'll still get schema-specific\n   * TypeScript types, but there will be no validation at runtime that your\n   * documents match those types.\n   *\n   * By default, `schemaValidation` is `true`.\n   */\n  schemaValidation?: boolean;\n\n  /**\n   * Whether the TypeScript types should allow accessing tables not in the schema.\n   *\n   * If `strictTableNameTypes` is `true`, using tables not listed in the schema\n   * will generate a TypeScript compilation error.\n   *\n   * If `strictTableNameTypes` is `false`, you'll be able to access tables not\n   * listed in the schema and their document type will be `any`.\n   *\n   * `strictTableNameTypes: false` is useful for rapid prototyping.\n   *\n   * Regardless of the value of `strictTableNameTypes`, your schema will only\n   * validate documents in the tables listed in the schema. You can still create\n   * and modify other tables on the dashboard or in JavaScript mutations.\n   *\n   * By default, `strictTableNameTypes` is `true`.\n   */\n  strictTableNameTypes?: StrictTableNameTypes;\n}\n\n/**\n * Define the schema of this Convex project.\n *\n * This should be exported from a `schema.ts` file in your `convex/` directory\n * like:\n *\n * ```ts\n * export default defineSchema({\n *   ...\n * });\n * ```\n *\n * @param schema - A map from table name to {@link TableDefinition} for all of\n * the tables in this project.\n * @param options - Optional configuration. See {@link DefineSchemaOptions} for\n * a full description.\n * @returns The schema.\n *\n * @public\n */\nexport function defineSchema<\n  Schema extends GenericSchema,\n  StrictTableNameTypes extends boolean = true,\n>(\n  schema: Schema,\n  options?: DefineSchemaOptions<StrictTableNameTypes>,\n): SchemaDefinition<Schema, StrictTableNameTypes> {\n  return new SchemaDefinition(schema, options);\n}\n\n/**\n * Internal type used in Convex code generation!\n *\n * Convert a {@link SchemaDefinition} into a {@link server.GenericDataModel}.\n *\n * @public\n */\nexport type DataModelFromSchemaDefinition<\n  SchemaDef extends SchemaDefinition<any, boolean>,\n> = MaybeMakeLooseDataModel<\n  {\n    [TableName in keyof SchemaDef[\"tables\"] &\n      string]: SchemaDef[\"tables\"][TableName] extends TableDefinition<\n      infer DocumentType,\n      infer Indexes,\n      infer SearchIndexes,\n      infer VectorIndexes\n    >\n      ? {\n          // We've already added all of the system fields except for `_id`.\n          // Add that here.\n          document: Expand<IdField<TableName> & ExtractDocument<DocumentType>>;\n          fieldPaths:\n            | keyof IdField<TableName>\n            | ExtractFieldPaths<DocumentType>;\n          indexes: Expand<Indexes & SystemIndexes>;\n          searchIndexes: SearchIndexes;\n          vectorIndexes: VectorIndexes;\n        }\n      : never;\n  },\n  SchemaDef[\"strictTableNameTypes\"]\n>;\n\ntype MaybeMakeLooseDataModel<\n  DataModel extends GenericDataModel,\n  StrictTableNameTypes extends boolean,\n> = StrictTableNameTypes extends true\n  ? DataModel\n  : Expand<DataModel & AnyDataModel>;\n\nconst _systemSchema = defineSchema({\n  _scheduled_functions: defineTable({\n    name: v.string(),\n    args: v.array(v.any()),\n    scheduledTime: v.float64(),\n    completedTime: v.optional(v.float64()),\n    state: v.union(\n      v.object({ kind: v.literal(\"pending\") }),\n      v.object({ kind: v.literal(\"inProgress\") }),\n      v.object({ kind: v.literal(\"success\") }),\n      v.object({ kind: v.literal(\"failed\"), error: v.string() }),\n      v.object({ kind: v.literal(\"canceled\") }),\n    ),\n  }),\n  _storage: defineTable({\n    sha256: v.string(),\n    size: v.float64(),\n    contentType: v.optional(v.string()),\n  }),\n});\n\nexport interface SystemDataModel\n  extends DataModelFromSchemaDefinition<typeof _systemSchema> {}\n\nexport type SystemTableNames = TableNamesInDataModel<SystemDataModel>;\n"], "names": [], "mappings": ";;;;;;;;;;AA4CA;;;;;;;;;;;AA6IO,MAAM,gBAKX;IAAA;;GAAA,GAaA,YAAY,YAAA,CAA4B;QAZxC,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAER,8CAAA;QAAA,cAAA,IAAA,EAAA;QAME,IAAA,CAAK,OAAA,GAAU,CAAC,CAAA;QAChB,IAAA,CAAK,eAAA,GAAkB,CAAC,CAAA;QACxB,IAAA,CAAK,aAAA,GAAgB,CAAC,CAAA;QACtB,IAAA,CAAK,mBAAA,GAAsB,CAAC,CAAA;QAC5B,IAAA,CAAK,aAAA,GAAgB,CAAC,CAAA;QACtB,IAAA,CAAK,mBAAA,GAAsB,CAAC,CAAA;QAC5B,IAAA,CAAK,SAAA,GAAY;IACnB;IAAA;;;;;;;GAAA,GAUA,aAA8D;QAC5D,OAAO,IAAA,CAAK,OAAA;IACd;IA4FA,MAKE,IAAA,EACA,WAAA,EAGA;QACA,IAAI,MAAM,OAAA,CAAQ,WAAW,GAAG;YAE9B,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK;gBAChB,iBAAiB;gBACjB,QAAQ;YACV,CAAC;QACH,OAAA,IAAW,YAAY,MAAA,EAAQ;YAE7B,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK;gBACxB,iBAAiB;gBACjB,QAAQ,YAAY,MAAA;YACtB,CAAC;QACH,OAAO;YAEL,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK;gBAChB,iBAAiB;gBACjB,QAAQ,YAAY,MAAA;YACtB,CAAC;QACH;QACA,OAAO,IAAA;IACT;IAmEA,YAKE,IAAA,EACA,WAAA,EAGA;QACA,IAAI,YAAY,MAAA,EAAQ;YACtB,IAAA,CAAK,mBAAA,CAAoB,IAAA,CAAK;gBAC5B,iBAAiB;gBACjB,aAAa,YAAY,WAAA;gBACzB,cAAc,YAAY,YAAA,IAAgB,CAAC,CAAA;YAC7C,CAAC;QACH,OAAO;YACL,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK;gBACtB,iBAAiB;gBACjB,aAAa,YAAY,WAAA;gBACzB,cAAc,YAAY,YAAA,IAAgB,CAAC,CAAA;YAC7C,CAAC;QACH;QACA,OAAO,IAAA;IACT;IAkEA,YAKE,IAAA,EACA,WAAA,EAGA;QACA,IAAI,YAAY,MAAA,EAAQ;YACtB,IAAA,CAAK,mBAAA,CAAoB,IAAA,CAAK;gBAC5B,iBAAiB;gBACjB,aAAa,YAAY,WAAA;gBACzB,YAAY,YAAY,UAAA;gBACxB,cAAc,YAAY,YAAA,IAAgB,CAAC,CAAA;YAC7C,CAAC;QACH,OAAO;YACL,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK;gBACtB,iBAAiB;gBACjB,aAAa,YAAY,WAAA;gBACzB,YAAY,YAAY,UAAA;gBACxB,cAAc,YAAY,YAAA,IAAgB,CAAC,CAAA;YAC7C,CAAC;QACH;QACA,OAAO,IAAA;IACT;IAAA;;GAAA,GAKU,OAKR;QACA,OAAO,IAAA;IACT;IAAA;;;;;GAAA,GAOA,SAAS;QACP,MAAM,eAAe,IAAA,CAAK,SAAA,CAAU,IAAA;QACpC,IAAI,OAAO,iBAAiB,UAAU;YACpC,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,SAAS,IAAA,CAAK,OAAA;YACd,iBAAiB,IAAA,CAAK,eAAA;YACtB,eAAe,IAAA,CAAK,aAAA;YACpB,qBAAqB,IAAA,CAAK,mBAAA;YAC1B,eAAe,IAAA,CAAK,aAAA;YACpB,qBAAqB,IAAA,CAAK,mBAAA;YAC1B;QACF;IACF;AACF;AA4DO,SAAS,YAId,cAAA,EAAgE;IAChE,QAAI,8NAAA,EAAY,cAAc,GAAG;QAC/B,OAAO,IAAI,gBAAgB,cAAc;IAC3C,OAAO;QACL,OAAO,IAAI,gBAAgB,oNAAA,CAAE,MAAA,CAAO,cAAc,CAAC;IACrD;AACF;AAkBO,MAAM,iBAGX;IAAA;;GAAA,GAQA,YAAY,MAAA,EAAgB,OAAA,CAAiD;QAP7E,cAAA,IAAA,EAAO;QACP,cAAA,IAAA,EAAO;QACP,cAAA,IAAA,EAAgB;QAMd,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,gBAAA,GACH,SAAS,qBAAqB,KAAA,IAAY,OAAO,QAAQ,gBAAA;IAC7D;IAAA;;;;;GAAA,GAQA,SAAiB;QACf,OAAO,KAAK,SAAA,CAAU;YACpB,QAAQ,OAAO,OAAA,CAAQ,IAAA,CAAK,MAAM,EAAE,GAAA,CAAI,CAAC,CAAC,WAAW,UAAU,CAAA,KAAM;gBACnE,MAAM,EACJ,OAAA,EACA,eAAA,EACA,aAAA,EACA,mBAAA,EACA,aAAA,EACA,mBAAA,EACA,YAAA,EACF,GAAI,WAAW,MAAA,CAAO;gBACtB,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;gBACF;YACF,CAAC;YACD,kBAAkB,IAAA,CAAK,gBAAA;QACzB,CAAC;IACH;AACF;AAkEO,SAAS,aAId,MAAA,EACA,OAAA,EACgD;IAChD,OAAO,IAAI,iBAAiB,QAAQ,OAAO;AAC7C;AA2CA,MAAM,gBAAgB,aAAa;IACjC,sBAAsB,YAAY;QAChC,MAAM,oNAAA,CAAE,MAAA,CAAO;QACf,MAAM,oNAAA,CAAE,KAAA,CAAM,oNAAA,CAAE,GAAA,CAAI,CAAC;QACrB,eAAe,oNAAA,CAAE,OAAA,CAAQ;QACzB,eAAe,oNAAA,CAAE,QAAA,CAAS,oNAAA,CAAE,OAAA,CAAQ,CAAC;QACrC,OAAO,oNAAA,CAAE,KAAA,CACP,oNAAA,CAAE,MAAA,CAAO;YAAE,MAAM,oNAAA,CAAE,OAAA,CAAQ,SAAS;QAAE,CAAC,GACvC,oNAAA,CAAE,MAAA,CAAO;YAAE,MAAM,oNAAA,CAAE,OAAA,CAAQ,YAAY;QAAE,CAAC,GAC1C,oNAAA,CAAE,MAAA,CAAO;YAAE,MAAM,oNAAA,CAAE,OAAA,CAAQ,SAAS;QAAE,CAAC,GACvC,oNAAA,CAAE,MAAA,CAAO;YAAE,MAAM,oNAAA,CAAE,OAAA,CAAQ,QAAQ;YAAG,OAAO,oNAAA,CAAE,MAAA,CAAO;QAAE,CAAC,GACzD,oNAAA,CAAE,MAAA,CAAO;YAAE,MAAM,oNAAA,CAAE,OAAA,CAAQ,UAAU;QAAE,CAAC;IAE5C,CAAC;IACD,UAAU,YAAY;QACpB,QAAQ,oNAAA,CAAE,MAAA,CAAO;QACjB,MAAM,oNAAA,CAAE,OAAA,CAAQ;QAChB,aAAa,oNAAA,CAAE,QAAA,CAAS,oNAAA,CAAE,MAAA,CAAO,CAAC;IACpC,CAAC;AACH,CAAC", "debugId": null}}, {"offset": {"line": 3993, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/Fireflies-Sales/node_modules/.bun/convex%401.26.2/node_modules/convex/src/server/index.ts"], "sourcesContent": ["/**\n * Utilities for implementing server-side Convex query and mutation functions.\n *\n * ## Usage\n *\n * ### Code Generation\n *\n * This module is typically used alongside generated server code.\n *\n * To generate the server code, run `npx convex dev` in your Convex project.\n * This will create a `convex/_generated/server.js` file with the following\n * functions, typed for your schema:\n * - [query](https://docs.convex.dev/generated-api/server#query)\n * - [mutation](https://docs.convex.dev/generated-api/server#mutation)\n *\n * If you aren't using TypeScript and code generation, you can use these untyped\n * functions instead:\n * - {@link queryGeneric}\n * - {@link mutationGeneric}\n *\n * ### Example\n *\n * Convex functions are defined by using either the `query` or\n * `mutation` wrappers.\n *\n * Queries receive a `db` that implements the {@link GenericDatabaseReader} interface.\n *\n * ```js\n * import { query } from \"./_generated/server\";\n *\n * export default query({\n *   handler: async ({ db }, { arg1, arg2 }) => {\n *     // Your (read-only) code here!\n *   },\n * });\n * ```\n *\n * If your function needs to write to the database, such as inserting, updating,\n * or deleting documents, use `mutation` instead which provides a `db` that\n * implements the {@link GenericDatabaseWriter} interface.\n *\n * ```js\n * import { mutation } from \"./_generated/server\";\n *\n * export default mutation({\n *   handler: async ({ db }, { arg1, arg2 }) => {\n *     // Your mutation code here!\n *   },\n * });\n * ```\n * @module\n */\n\nexport type {\n  Auth,\n  UserIdentity,\n  UserIdentityAttributes,\n} from \"./authentication.js\";\nexport * from \"./database.js\";\nexport type {\n  GenericDocument,\n  GenericFieldPaths,\n  GenericIndexFields,\n  GenericTableIndexes,\n  GenericSearchIndexConfig,\n  GenericTableSearchIndexes,\n  GenericVectorIndexConfig,\n  GenericTableVectorIndexes,\n  FieldTypeFromFieldPath,\n  FieldTypeFromFieldPathInner,\n  GenericTableInfo,\n  DocumentByInfo,\n  FieldPaths,\n  Indexes,\n  IndexNames,\n  NamedIndex,\n  SearchIndexes,\n  SearchIndexNames,\n  NamedSearchIndex,\n  VectorIndexes,\n  VectorIndexNames,\n  NamedVectorIndex,\n  GenericDataModel,\n  AnyDataModel,\n  TableNamesInDataModel,\n  NamedTableInfo,\n  DocumentByName,\n} from \"./data_model.js\";\n\nexport type {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"./filter_builder.js\";\nexport {\n  actionGeneric,\n  httpActionGeneric,\n  mutationGeneric,\n  queryGeneric,\n  internalActionGeneric,\n  internalMutationGeneric,\n  internalQueryGeneric,\n} from \"./impl/registration_impl.js\";\nexport type { IndexRange, IndexRangeBuilder } from \"./index_range_builder.js\";\nexport * from \"./pagination.js\";\nexport type { OrderedQuery, Query, QueryInitializer } from \"./query.js\";\nexport type {\n  ArgsArray,\n  DefaultFunctionArgs,\n  FunctionVisibility,\n  ActionBuilder,\n  MutationBuilder,\n  MutationBuilderWithTable,\n  QueryBuilder,\n  QueryBuilderWithTable,\n  HttpActionBuilder,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericMutationCtxWithTable,\n  GenericQueryCtx,\n  GenericQueryCtxWithTable,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n  PublicHttpAction,\n  UnvalidatedFunction,\n  ValidatedFunction,\n  ReturnValueForOptionalValidator,\n  ArgsArrayForOptionalValidator,\n  ArgsArrayToObject,\n  DefaultArgsForOptionalValidator,\n} from \"./registration.js\";\nexport * from \"./search_filter_builder.js\";\nexport * from \"./storage.js\";\nexport type { Scheduler, SchedulableFunctionReference } from \"./scheduler.js\";\nexport { cronJobs } from \"./cron.js\";\nexport type { CronJob, Crons } from \"./cron.js\";\nexport type {\n  SystemFields,\n  IdField,\n  WithoutSystemFields,\n  WithOptionalSystemFields,\n  SystemIndexes,\n  IndexTiebreakerField,\n} from \"./system_fields.js\";\nexport { httpRouter, HttpRouter, ROUTABLE_HTTP_METHODS } from \"./router.js\";\nexport type {\n  RoutableMethod,\n  RouteSpec,\n  RouteSpecWithPath,\n  RouteSpecWithPathPrefix,\n} from \"./router.js\";\nexport {\n  anyApi,\n  getFunctionName,\n  makeFunctionReference,\n  filterApi,\n} from \"./api.js\";\nexport type {\n  ApiFromModules,\n  AnyApi,\n  FilterApi,\n  FunctionType,\n  FunctionReference,\n  FunctionArgs,\n  OptionalRestArgs,\n  PartialApi,\n  ArgsAndOptions,\n  FunctionReturnType,\n} from \"./api.js\";\nexport {\n  defineApp,\n  defineComponent,\n  componentsGeneric,\n  createFunctionHandle,\n  type AnyChildComponents,\n} from \"./components/index.js\";\n/**\n * @internal\n */\nexport { currentSystemUdfInComponent } from \"./components/index.js\";\nexport { getFunctionAddress } from \"./components/index.js\";\nexport type {\n  ComponentDefinition,\n  AnyComponents,\n  FunctionHandle,\n} from \"./components/index.js\";\n\n/**\n * @internal\n */\nexport type { Index, SearchIndex, VectorIndex } from \"./schema.js\";\n\nexport type {\n  SearchIndexConfig,\n  VectorIndexConfig,\n  TableDefinition,\n  SchemaDefinition,\n  DefineSchemaOptions,\n  GenericSchema,\n  DataModelFromSchemaDefinition,\n  SystemDataModel,\n  SystemTableNames,\n} from \"./schema.js\";\nexport { defineTable, defineSchema } from \"./schema.js\";\n\nexport type {\n  VectorSearch,\n  VectorSearchQuery,\n  VectorFilterBuilder,\n  FilterExpression,\n} from \"./vector_search.js\";\n\n/**\n * @public\n */\nexport type { BetterOmit, Expand } from \"../type_utils.js\";\n"], "names": [], "mappings": ";AA0DA,cAAc;AAoCd;AAUA,cAAc;AA4Bd,cAAc;AACd,cAAc;AAEd,SAAS,gBAAgB;AAUzB,SAAS,YAAY,YAAY,6BAA6B;AAO9D;AAkBA;AAkCA,SAAS,aAAa,oBAAoB", "debugId": null}}]}