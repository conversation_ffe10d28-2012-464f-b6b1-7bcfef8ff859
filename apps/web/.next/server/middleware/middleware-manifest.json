{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_6df2740d._.js", "server/edge/chunks/f34ad_@opentelemetry_api_build_esm_aaf6a9c6._.js", "server/edge/chunks/91b4f_@clerk_shared_dist_145f4f4c._.js", "server/edge/chunks/d38ee_@clerk_backend_dist_86651530._.js", "server/edge/chunks/0bf16_@clerk_nextjs_dist_esm_57614958._.js", "server/edge/chunks/31880_cookie_dist_index_55d5ff24.js", "server/edge/chunks/[root-of-the-server]__8bd37c4c._.js", "server/edge/chunks/turbopack-apps_web_edge-wrapper_21894957.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*){(\\\\.json)}?", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "x4GhiFiFoaIn1npFXNZgHEFPxvHRHaphbkakjNUzsjI=", "__NEXT_PREVIEW_MODE_ID": "ceda213b990e7178a86115ce04b41c98", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "49d0a21f34725a28eff804cfeb344015c3e9a7942207bf2c6c5b1eb49ed61c77", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7de1ff8e41faa9871aa2ad0a4f9544f0d40a58a53d637e75404cd349a0f32dec"}}}, "instrumentation": null, "functions": {}}