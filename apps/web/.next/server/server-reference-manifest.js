self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f365a7491d8a7ff5f6e6f175df0f6aad9fe604443\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/fireflies/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/fireflies/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"createOrReadKeylessAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\",\n        \"app/fireflies/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\",\n      \"exportedName\": \"createOrReadKeylessAction\"\n    },\n    \"7f3b4eacba48d7f1b320f11016dde2dc22dec1660d\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/fireflies/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/fireflies/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"syncKeylessConfigAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\",\n        \"app/fireflies/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\",\n      \"exportedName\": \"syncKeylessConfigAction\"\n    },\n    \"7f60a46fa2752f49a37d4e985576e8554a5a39a573\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/fireflies/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/fireflies/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"deleteKeylessAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\",\n        \"app/fireflies/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\",\n      \"exportedName\": \"deleteKeylessAction\"\n    },\n    \"7fba3f34d55092832929857b3b77f01f25c1fd102b\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        },\n        \"app/fireflies/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/fireflies/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"detectKeylessEnvDriftAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\",\n        \"app/fireflies/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\",\n      \"exportedName\": \"detectKeylessEnvDriftAction\"\n    },\n    \"7f6611e75cf2663384c0113dce838946993dffc35b\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        },\n        \"app/fireflies/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/fireflies/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"invalidateCacheAction\",\n          \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\"\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\",\n        \"app/fireflies/page\": \"action-browser\"\n      },\n      \"filename\": \"node_modules/.bun/@clerk+nextjs@6.31.6+04be5137c725fb89/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\",\n      \"exportedName\": \"invalidateCacheAction\"\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"x4GhiFiFoaIn1npFXNZgHEFPxvHRHaphbkakjNUzsjI=\"\n}"