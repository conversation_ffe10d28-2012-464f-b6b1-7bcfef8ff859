import { auth } from '@clerk/nextjs/server';
import type { NextRequest } from 'next/server';
import { z } from 'zod';
import { extractTranscriptId, fetchTranscriptById } from '@/lib/fireflies';

export const runtime = 'nodejs';

const bodySchema = z.object({
  source: z
    .string()
    .describe('Fireflies transcript ID or full Fireflies view URL'),
});

export async function POST(req: NextRequest) {
  try {
    const { userId } = auth();
    if (!userId) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const json = await req.json();
    const args = bodySchema.parse(json);

    const transcriptId = extractTranscriptId(args.source);
    if (!transcriptId) {
      return Response.json(
        { error: 'Source must be a Fireflies transcript ID or view URL.' },
        { status: 400 }
      );
    }

    const t = await fetchTranscriptById(transcriptId);
    return Response.json({
      id: t.id,
      title: t.title ?? null,
      transcript_url: t.transcript_url ?? null,
      total_sentences: t.sentences?.length ?? 0,
    });
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : String(err);
    return Response.json({ error: message }, { status: 500 });
  }
}
