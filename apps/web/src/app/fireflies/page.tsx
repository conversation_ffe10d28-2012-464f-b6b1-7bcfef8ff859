'use client';

import { api } from '@Fireflies-Sales/backend/convex/_generated/api';
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs';
import { useMutation } from 'convex/react';
import { useState } from 'react';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

type MatchItem = {
  index: number;
  speaker: string | null;
  text: string;
  start_time: string;
  end_time: string;
  start_seconds: number | null;
  end_seconds: number | null;
  hits?: number;
  reason?: string;
  confidence?: number;
};

const responseSchema = z.object({
  transcript_id: z.string(),
  transcript_title: z.string().nullable().optional(),
  transcript_url: z.string().nullable().optional(),
  speaker: z.string().nullable(),
  total_sentences: z.number(),
  word: z.string().nullable(),
  literal_word: z
    .object({
      count: z.number(),
      matches: z.array(
        z.object({
          index: z.number(),
          speaker: z.string().nullable(),
          text: z.string(),
          start_time: z.string(),
          end_time: z.string(),
          start_seconds: z.number().nullable(),
          end_seconds: z.number().nullable(),
          hits: z.number(),
        })
      ),
    })
    .nullable()
    .optional(),
  concept: z.string().nullable(),
  concept_result: z
    .object({
      count: z.number(),
      matches: z.array(
        z.object({
          index: z.number(),
          speaker: z.string().nullable(),
          text: z.string(),
          start_time: z.string(),
          end_time: z.string(),
          start_seconds: z.number().nullable(),
          end_seconds: z.number().nullable(),
          reason: z.string().optional(),
          confidence: z.number().optional(),
        })
      ),
      samples: z.array(z.string()),
    })
    .nullable()
    .optional(),
});

const PRESET_CONCEPTS = [
  'biggest twitter spaces account in the world',
  'endorsement from elon musk',
  'investment',
  'media',
  'exposure',
  'brand awareness',
  'community building',
  'connections',
  'OTC',
  'post-tge setup',
  'biggest KOLs in the world',
  'tge',
  'tge launches',
  'founder brand building',
  'trust',
  // additional topics from PRD addendum
  'dealflow partnership',
  'collaboration with our existing projects',
  '600+ projects in our captable',
  'listing help',
  'mm (market making) help',
  'VC connections',
  'KOL connections',
] as const;

// UI constants
const DEFAULT_TOPK = 25;
const MAX_PRESET_CONCEPT_CHIPS = 8;
const MIN_TOPK = 1;
const MAX_TOPK = 100;
const DECIMAL_RADIX = 10;
const PERCENT = 100;

export default function FirefliesPage() {
  const [source, setSource] = useState('');
  const [word, setWord] = useState('');
  const [concept, setConcept] = useState('');
  const [speaker, setSpeaker] = useState('');
  const [topK, setTopK] = useState(DEFAULT_TOPK);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<z.infer<typeof responseSchema> | null>(null);
  const [meta, setMeta] = useState<{
    id: string;
    title: string | null;
    transcript_url: string | null;
    total_sentences: number;
  } | null>(null);
  const logSearch = useMutation(api.searches.log);

  const buildRequestBody = (): Record<string, unknown> => {
    const body: Record<string, unknown> = { source, topK };
    if (word.trim()) {
      body.word = word.trim();
    }
    if (concept.trim()) {
      body.concept = concept.trim();
    }
    if (speaker.trim()) {
      body.speaker = speaker.trim();
    }
    return body;
  };

  const onFetchTranscript = async () => {
    setError(null);
    setMeta(null);
    try {
      const resp = await fetch('/api/fireflies/transcript', {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({ source }),
      });
      const json = await resp.json();
      if (resp.ok) {
        setMeta(json);
      } else {
        setError(json?.error ?? 'Request failed');
      }
    } catch (err) {
      const msg = err instanceof Error ? err.message : String(err);
      setError(msg);
    }
  };

  const handleResponse = (
    json: unknown,
    ok: boolean
  ): z.infer<typeof responseSchema> | string => {
    if (!ok) {
      const err = json as { error?: string };
      return err?.error ?? 'Request failed';
    }
    const parsed = responseSchema.safeParse(json);
    if (!parsed.success) {
      return 'Unexpected response format';
    }
    return parsed.data;
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setData(null);
    try {
      const body = buildRequestBody();

      const resp = await fetch('/api/fireflies/search', {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(body),
      });
      const json = await resp.json();
      const result = handleResponse(json, resp.ok);
      if (typeof result === 'string') {
        setError(result);
      } else {
        setData(result);
        // Log to Convex (authenticated)
        try {
          await logSearch({
            source,
            word: word.trim() || undefined,
            concept: concept.trim() || undefined,
            speaker: speaker.trim() || undefined,
          });
        } catch {
          // ignore log errors
        }
      }
    } catch (err) {
      const msg = err instanceof Error ? err.message : String(err);
      setError(msg);
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className="min-h-[calc(100svh-3rem)] w-full bg-background px-4 py-8">
      <div className="mx-auto max-w-3xl">
        <div className="mb-6 flex items-center justify-between">
          <h1 className="font-semibold text-2xl text-foreground">
            Fireflies Search
          </h1>
          <UserButton />
        </div>
        <p className="mb-8 text-muted-foreground">
          Enter a Fireflies transcript ID or view URL. Search for a literal word
          and/or a broader concept. Results include timestamps and reasoning for
          concept matches.
        </p>

        <Card>
          <CardHeader>
            <CardTitle>Search Parameters</CardTitle>
          </CardHeader>
          <CardContent>
            <SignedIn>
              <form className="grid gap-6" onSubmit={onSubmit}>
                <div className="grid gap-2">
                  <Label htmlFor="source">Transcript Source</Label>
                  <Input
                    autoComplete="off"
                    id="source"
                    name="source"
                    onChange={(e) => setSource(e.target.value)}
                    placeholder="abc123DEF or https://app.fireflies.ai/view/org::abc123DEF"
                    required
                    value={source}
                  />
                  <div className="flex items-center gap-3 pt-2">
                    <Button
                      disabled={!source.trim()}
                      onClick={onFetchTranscript}
                      type="button"
                    >
                      Fetch Transcript Info
                    </Button>
                    <Button disabled={loading || !source.trim()} type="submit">
                      {loading ? 'Searching…' : 'Search'}
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="word">Exact Word (optional)</Label>
                    <Input
                      autoComplete="off"
                      id="word"
                      name="word"
                      onChange={(e) => setWord(e.target.value)}
                      placeholder="e.g. pricing"
                      value={word}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="speaker">Speaker (optional)</Label>
                    <Input
                      autoComplete="off"
                      id="speaker"
                      name="speaker"
                      onChange={(e) => setSpeaker(e.target.value)}
                      placeholder="Exact speaker name"
                      value={speaker}
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="concept">Concept (optional)</Label>
                  <Input
                    autoComplete="off"
                    id="concept"
                    name="concept"
                    onChange={(e) => setConcept(e.target.value)}
                    placeholder="e.g. expressions of apology or regret about a delay"
                    value={concept}
                  />
                  <div className="flex flex-wrap gap-2 pt-2">
                    {PRESET_CONCEPTS.slice(0, MAX_PRESET_CONCEPT_CHIPS).map(
                      (c) => (
                        <Button
                          aria-label={`Use concept: ${c}`}
                          className="h-8 rounded-full px-3 text-xs"
                          key={c}
                          onClick={() => setConcept(c)}
                          type="button"
                        >
                          {c}
                        </Button>
                      )
                    )}
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="topK">Max Concept Matches</Label>
                  <Input
                    id="topK"
                    max={MAX_TOPK}
                    min={MIN_TOPK}
                    name="topK"
                    onChange={(e) =>
                      setTopK(
                        Number.parseInt(e.target.value, DECIMAL_RADIX) ||
                          DEFAULT_TOPK
                      )
                    }
                    type="number"
                    value={topK}
                  />
                </div>

                <div className="flex items-center gap-4">
                  {data?.transcript_url ? (
                    <a
                      className="underline"
                      href={data.transcript_url}
                      rel="noopener"
                      target="_blank"
                    >
                      View on Fireflies
                    </a>
                  ) : null}
                </div>
              </form>
            </SignedIn>
            <SignedOut>
              <div className="flex items-center gap-4">
                <SignInButton mode="modal">
                  <Button type="button">Sign in to search</Button>
                </SignInButton>
              </div>
            </SignedOut>
          </CardContent>
        </Card>

        {error ? (
          <p className="mt-6 text-red-600" role="alert">
            {error}
          </p>
        ) : null}

        {meta ? (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Transcript</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-1">
                <div className="font-medium">{meta.title || 'Untitled'}</div>
                <div className="text-muted-foreground text-sm">
                  ID: {meta.id} · Sentences: {meta.total_sentences}
                </div>
                {meta.transcript_url ? (
                  <a
                    className="text-sm underline"
                    href={meta.transcript_url}
                    rel="noopener"
                    target="_blank"
                  >
                    Open in Fireflies
                  </a>
                ) : null}
              </div>
            </CardContent>
          </Card>
        ) : null}

        {data ? (
          <div className="mt-8 grid gap-8">
            {data.word && data.literal_word ? (
              <section aria-labelledby="word-results" className="grid gap-4">
                <h2
                  className="font-semibold text-foreground text-xl"
                  id="word-results"
                >
                  Word Matches: “{data.word}” ({data.literal_word.count} hits)
                </h2>
                <ul className="grid gap-2">
                  {data.literal_word.matches.map((m: MatchItem) => (
                    <li className="rounded-md border p-3" key={`w-${m.index}`}>
                      <div className="flex flex-wrap items-baseline justify-between gap-2">
                        <div className="font-mono text-sm">#{m.index}</div>
                        <div className="text-muted-foreground text-sm">
                          {m.start_time} – {m.end_time}
                        </div>
                      </div>
                      <p className="mt-2">{m.text}</p>
                      {m.speaker ? (
                        <p className="mt-1 text-muted-foreground text-sm">
                          Speaker: {m.speaker}
                        </p>
                      ) : null}
                    </li>
                  ))}
                </ul>
              </section>
            ) : null}

            {data.concept && data.concept_result ? (
              <section aria-labelledby="concept-results" className="grid gap-4">
                <h2
                  className="font-semibold text-foreground text-xl"
                  id="concept-results"
                >
                  Concept Matches: “{data.concept}” ({data.concept_result.count}
                  )
                </h2>
                <ul className="grid gap-2">
                  {data.concept_result.matches.map((m: MatchItem) => (
                    <li className="rounded-md border p-3" key={`c-${m.index}`}>
                      <div className="flex flex-wrap items-baseline justify-between gap-2">
                        <div className="font-mono text-sm">#{m.index}</div>
                        <div className="text-muted-foreground text-sm">
                          {m.start_time} – {m.end_time}
                        </div>
                      </div>
                      <p className="mt-2">{m.text}</p>
                      <div className="mt-1 text-muted-foreground text-sm">
                        {m.speaker ? (
                          <span>Speaker: {m.speaker} · </span>
                        ) : null}
                        {typeof m.confidence === 'number' ? (
                          <span>
                            Confidence: {(m.confidence * PERCENT).toFixed(0)}%
                          </span>
                        ) : null}
                      </div>
                      {m.reason ? (
                        <p className="mt-2 text-muted-foreground text-sm">
                          Reason: {m.reason}
                        </p>
                      ) : null}
                    </li>
                  ))}
                </ul>
              </section>
            ) : null}
          </div>
        ) : null}
      </div>
    </main>
  );
}
