{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start"}, "dependencies": {"@Fireflies-Sales/backend": "workspace:*", "@clerk/nextjs": "^6.31.5", "@openrouter/ai-sdk-provider": "^1.1.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "ai": "^5.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.4", "next": "15.5.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^4.0.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.1.10", "typescript": "^5"}}