{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/functionSpec.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { ConvexTool } from \"./index.js\";\nimport { loadSelectedDeploymentCredentials } from \"../../api.js\";\nimport { runSystemQuery } from \"../../run.js\";\nimport { getDeploymentSelection } from \"../../deploymentSelection.js\";\n\nconst inputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\n      \"Deployment selector (from the status tool) to get function metadata from.\",\n    ),\n});\n\nconst outputSchema = z\n  .any()\n  .describe(\"Function metadata including arguments and return values\");\n\nconst description = `\nGet the function metadata from a Convex deployment.\n\nReturns an array of structured objects for each function the deployment. Each function's\nmetadata contains its identifier (which is its path within the convex/ folder joined\nwith its exported name), its argument validator, its return value validator, its type\n(i.e. is it a query, mutation, or action), and its visibility (i.e. is it public or\ninternal).\n`.trim();\n\nexport const FunctionSpecTool: ConvexTool<\n  typeof inputSchema,\n  typeof outputSchema\n> = {\n  name: \"functionSpec\",\n  description,\n  inputSchema,\n  outputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n    const functions = await runSystemQuery(ctx, {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n      functionName: \"_system/cli/modules:apiSpec\",\n      componentPath: undefined,\n      args: {},\n    });\n    return functions;\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAElB,iBAAkD;AAClD,iBAA+B;AAC/B,iCAAuC;AAEvC,MAAM,cAAc,aAAE,OAAO;AAAA,EAC3B,oBAAoB,aACjB,OAAO,EACP;AAAA,IACC;AAAA,EACF;AACJ,CAAC;AAED,MAAM,eAAe,aAClB,IAAI,EACJ,SAAS,yDAAyD;AAErE,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlB,KAAK;AAEA,MAAM,mBAGT;AAAA,EACF,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,UAAM,mDAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,UAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,YAAY,UAAM,2BAAe,KAAK;AAAA,MAC1C,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,MACtB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,MAAM,CAAC;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACT;AACF;", "names": []}