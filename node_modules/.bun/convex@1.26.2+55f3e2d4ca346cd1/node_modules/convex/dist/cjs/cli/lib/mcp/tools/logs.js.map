{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/logs.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { ConvexTool } from \"./index.js\";\nimport { loadSelectedDeploymentCredentials } from \"../../api.js\";\nimport { getDeploymentSelection } from \"../../deploymentSelection.js\";\nimport { deploymentFetch } from \"../../utils/utils.js\";\n\nconst inputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\"Deployment selector (from the status tool) to read logs from.\"),\n  cursor: z\n    .number()\n    .optional()\n    .describe(\n      \"Optional cursor (in ms) to start reading from. Use 0 to read from the beginning.\",\n    ),\n  limit: z\n    .number()\n    .int()\n    .positive()\n    .max(1000)\n    .optional()\n    .describe(\n      \"Maximum number of log entries to return. If omitted, returns all available in this chunk.\",\n    ),\n});\n\nconst outputSchema = z.object({\n  entries: z.array(z.any()),\n  newCursor: z.number(),\n});\n\nconst description = `\nFetch a chunk of recent log entries from your Convex deployment.\n\nReturns a batch of UDF execution log entries and a new cursor you can use to\nrequest the next batch. This tool does not tail; it performs a single fetch.\n`.trim();\n\nexport const LogsTool: ConvexTool<typeof inputSchema, typeof outputSchema> = {\n  name: \"logs\",\n  description,\n  inputSchema,\n  outputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n\n    const fetch = deploymentFetch(ctx, {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n    });\n\n    const cursor = args.cursor ?? 0;\n    const response = await fetch(`/api/stream_function_logs?cursor=${cursor}`, {\n      method: \"GET\",\n    });\n    if (!response.ok) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `HTTP error ${response.status}: ${await response.text()}`,\n      });\n    }\n    const { entries, newCursor } = (await response.json()) as {\n      entries: unknown[];\n      newCursor: number;\n    };\n\n    // Optionally limit the number of entries returned from the end.\n    const limitedEntries =\n      typeof args.limit === \"number\" && entries.length > args.limit\n        ? entries.slice(entries.length - args.limit)\n        : entries;\n\n    const parsed = outputSchema.parse({ entries: limitedEntries, newCursor });\n    return parsed;\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAElB,iBAAkD;AAClD,iCAAuC;AACvC,mBAAgC;AAEhC,MAAM,cAAc,aAAE,OAAO;AAAA,EAC3B,oBAAoB,aACjB,OAAO,EACP,SAAS,+DAA+D;AAAA,EAC3E,QAAQ,aACL,OAAO,EACP,SAAS,EACT;AAAA,IACC;AAAA,EACF;AAAA,EACF,OAAO,aACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,IAAI,GAAI,EACR,SAAS,EACT;AAAA,IACC;AAAA,EACF;AACJ,CAAC;AAED,MAAM,eAAe,aAAE,OAAO;AAAA,EAC5B,SAAS,aAAE,MAAM,aAAE,IAAI,CAAC;AAAA,EACxB,WAAW,aAAE,OAAO;AACtB,CAAC;AAED,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB,KAAK;AAEA,MAAM,WAAgE;AAAA,EAC3E,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,UAAM,mDAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,UAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,YAAQ,8BAAgB,KAAK;AAAA,MACjC,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,IACxB,CAAC;AAED,UAAM,SAAS,KAAK,UAAU;AAC9B,UAAM,WAAW,MAAM,MAAM,oCAAoC,MAAM,IAAI;AAAA,MACzE,QAAQ;AAAA,IACV,CAAC;AACD,QAAI,CAAC,SAAS,IAAI;AAChB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,cAAc,SAAS,MAAM,KAAK,MAAM,SAAS,KAAK,CAAC;AAAA,MACzE,CAAC;AAAA,IACH;AACA,UAAM,EAAE,SAAS,UAAU,IAAK,MAAM,SAAS,KAAK;AAMpD,UAAM,iBACJ,OAAO,KAAK,UAAU,YAAY,QAAQ,SAAS,KAAK,QACpD,QAAQ,MAAM,QAAQ,SAAS,KAAK,KAAK,IACzC;AAEN,UAAM,SAAS,aAAa,MAAM,EAAE,SAAS,gBAAgB,UAAU,CAAC;AACxE,WAAO;AAAA,EACT;AACF;", "names": []}