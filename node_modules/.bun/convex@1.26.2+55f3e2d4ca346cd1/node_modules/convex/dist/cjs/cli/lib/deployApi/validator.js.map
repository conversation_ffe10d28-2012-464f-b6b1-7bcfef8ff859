{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/validator.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { looseObject } from \"./utils.js\";\n\nconst baseConvexValidator = z.discriminatedUnion(\"type\", [\n  looseObject({ type: z.literal(\"null\") }),\n  looseObject({ type: z.literal(\"number\") }),\n  looseObject({ type: z.literal(\"bigint\") }),\n  looseObject({ type: z.literal(\"boolean\") }),\n  looseObject({ type: z.literal(\"string\") }),\n  looseObject({ type: z.literal(\"bytes\") }),\n  looseObject({ type: z.literal(\"any\") }),\n  looseObject({ type: z.literal(\"literal\"), value: z.any() }),\n  looseObject({ type: z.literal(\"id\"), tableName: z.string() }),\n]);\nexport type ConvexValidator =\n  | z.infer<typeof baseConvexValidator>\n  | { type: \"array\"; value: ConvexValidator }\n  | {\n      type: \"record\";\n      keys: ConvexValidator;\n      values: { fieldType: ConvexValidator; optional: false };\n    }\n  | { type: \"union\"; value: ConvexValidator[] }\n  | {\n      type: \"object\";\n      value: Record<string, { fieldType: ConvexValidator; optional: boolean }>;\n    };\nexport const convexValidator: z.ZodType<ConvexValidator> = z.lazy(() =>\n  z.union([\n    baseConvexValidator,\n    looseObject({ type: z.literal(\"array\"), value: convexValidator }),\n    looseObject({\n      type: z.literal(\"record\"),\n      keys: convexValidator,\n      values: z.object({\n        fieldType: convexValidator,\n        optional: z.literal(false),\n      }),\n    }),\n    looseObject({\n      type: z.literal(\"union\"),\n      value: z.array(convexValidator),\n    }),\n    looseObject({\n      type: z.literal(\"object\"),\n      value: z.record(\n        looseObject({\n          fieldType: convexValidator,\n          optional: z.boolean(),\n        }),\n      ),\n    }),\n  ]),\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAClB,mBAA4B;AAE5B,MAAM,sBAAsB,aAAE,mBAAmB,QAAQ;AAAA,MACvD,0BAAY,EAAE,MAAM,aAAE,QAAQ,MAAM,EAAE,CAAC;AAAA,MACvC,0BAAY,EAAE,MAAM,aAAE,QAAQ,QAAQ,EAAE,CAAC;AAAA,MACzC,0BAAY,EAAE,MAAM,aAAE,QAAQ,QAAQ,EAAE,CAAC;AAAA,MACzC,0BAAY,EAAE,MAAM,aAAE,QAAQ,SAAS,EAAE,CAAC;AAAA,MAC1C,0BAAY,EAAE,MAAM,aAAE,QAAQ,QAAQ,EAAE,CAAC;AAAA,MACzC,0BAAY,EAAE,MAAM,aAAE,QAAQ,OAAO,EAAE,CAAC;AAAA,MACxC,0BAAY,EAAE,MAAM,aAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,MACtC,0BAAY,EAAE,MAAM,aAAE,QAAQ,SAAS,GAAG,OAAO,aAAE,IAAI,EAAE,CAAC;AAAA,MAC1D,0BAAY,EAAE,MAAM,aAAE,QAAQ,IAAI,GAAG,WAAW,aAAE,OAAO,EAAE,CAAC;AAC9D,CAAC;AAcM,MAAM,kBAA8C,aAAE;AAAA,EAAK,MAChE,aAAE,MAAM;AAAA,IACN;AAAA,QACA,0BAAY,EAAE,MAAM,aAAE,QAAQ,OAAO,GAAG,OAAO,gBAAgB,CAAC;AAAA,QAChE,0BAAY;AAAA,MACV,MAAM,aAAE,QAAQ,QAAQ;AAAA,MACxB,MAAM;AAAA,MACN,QAAQ,aAAE,OAAO;AAAA,QACf,WAAW;AAAA,QACX,UAAU,aAAE,QAAQ,KAAK;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAAA,QACD,0BAAY;AAAA,MACV,MAAM,aAAE,QAAQ,OAAO;AAAA,MACvB,OAAO,aAAE,MAAM,eAAe;AAAA,IAChC,CAAC;AAAA,QACD,0BAAY;AAAA,MACV,MAAM,aAAE,QAAQ,QAAQ;AAAA,MACxB,OAAO,aAAE;AAAA,YACP,0BAAY;AAAA,UACV,WAAW;AAAA,UACX,UAAU,aAAE,QAAQ;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;", "names": []}