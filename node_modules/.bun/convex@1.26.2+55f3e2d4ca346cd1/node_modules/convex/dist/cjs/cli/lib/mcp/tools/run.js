"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var run_exports = {};
__export(run_exports, {
  RunTool: () => RunTool
});
module.exports = __toCommonJS(run_exports);
var import_zod = require("zod");
var import_api = require("../../api.js");
var import_run = require("../../run.js");
var import_config = require("../../config.js");
var import_browser = require("../../../../browser/index.js");
var import_logging = require("../../../../browser/logging.js");
var import_deploymentSelection = require("../../deploymentSelection.js");
const inputSchema = import_zod.z.object({
  deploymentSelector: import_zod.z.string().describe(
    "Deployment selector (from the status tool) to run the function on."
  ),
  functionName: import_zod.z.string().describe(
    "The name of the function to run (e.g. 'path/to/my/module.js:myFunction')."
  ),
  args: import_zod.z.string().describe(
    "The argument object to pass to the function, JSON-encoded as a string."
  )
});
const outputSchema = import_zod.z.object({
  result: import_zod.z.any().describe("The result returned by the function"),
  logLines: import_zod.z.array(import_zod.z.string()).describe("The log lines generated by the function")
});
const description = `
Run a Convex function (query, mutation, or action) on your deployment.

Returns the result and any log lines generated by the function.
`.trim();
const RunTool = {
  name: "run",
  description,
  inputSchema,
  outputSchema,
  handler: async (ctx, args) => {
    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(
      args.deploymentSelector
    );
    process.chdir(projectDir);
    const metadata = await (0, import_deploymentSelection.getDeploymentSelection)(ctx, ctx.options);
    const credentials = await (0, import_api.loadSelectedDeploymentCredentials)(
      ctx,
      metadata,
      deployment
    );
    const parsedArgs = await (0, import_run.parseArgs)(ctx, args.args);
    const { projectConfig } = await (0, import_config.readProjectConfig)(ctx);
    const parsedFunctionName = await (0, import_run.parseFunctionName)(
      ctx,
      args.functionName,
      projectConfig.functions
    );
    const logger = new import_logging.DefaultLogger({ verbose: true });
    const logLines = [];
    logger.addLogLineListener((level, ...args2) => {
      logLines.push(`${level}: ${args2.join(" ")}`);
    });
    const client = new import_browser.ConvexHttpClient(credentials.url, {
      logger
    });
    client.setAdminAuth(credentials.adminKey);
    let result;
    try {
      result = await client.function(parsedFunctionName, void 0, parsedArgs);
    } catch (err) {
      return await ctx.crash({
        exitCode: 1,
        errorType: "invalid filesystem or env vars",
        printedMessage: `Failed to run function "${args.functionName}":
${err.toString().trim()}`
      });
    }
    return {
      result,
      logLines
    };
  }
};
//# sourceMappingURL=run.js.map
