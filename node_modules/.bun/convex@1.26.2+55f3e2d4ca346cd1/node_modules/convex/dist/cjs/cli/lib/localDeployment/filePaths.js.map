{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/filePaths.ts"], "sourcesContent": ["/*\n~/.cache/convex\n  binaries\n    0.0.1\n      convex-local-backend[.exe] // convex-local-backend.exe on windows\n    0.0.2\n      convex-local-backend[.exe]\n  dashboard\n    config.json\n    out\n    // if present, output files from building the self-hosted dashboard which can\n    // be served using `npx serve`\n    index.html\n\n\n~/.convex\n  convex-backend-state\n    local-my_team-chess\n      config.json // contains `LocalDeploymentConfig`\n      convex_local_storage\n      convex_local_backend.sqlite3\n    local-my_team-whisper\n      config.json\n      convex_local_storage\n      convex_local_backend.sqlite3\n    anonymous-convex-backend-state\n      config.json // contains { uuid: <uuid> }, used to identify the anonymous user\n      anonymous-chess\n        config.json\n        convex_local_storage\n        convex_local_backend.sqlite3\n*/\n\nimport path from \"path\";\nimport { cacheDir, rootDirectory } from \"../utils/utils.js\";\nimport { Context } from \"../../../bundler/context.js\";\nimport { logVerbose } from \"../../../bundler/log.js\";\nimport { recursivelyDelete } from \"../fsUtils.js\";\nimport crypto from \"crypto\";\n\n// Naming is hard, but \"local\" refers to deployments linked to a Convex project\n// and \"anonymous\" refers to deployments that are not linked to a Convex project\n// (but in both cases they are running locally).\nexport type LocalDeploymentKind = \"local\" | \"anonymous\";\n\nexport function rootDeploymentStateDir(kind: LocalDeploymentKind) {\n  return path.join(\n    rootDirectory(),\n    kind === \"local\"\n      ? \"convex-backend-state\"\n      : \"anonymous-convex-backend-state\",\n  );\n}\n\nexport function deploymentStateDir(\n  deploymentKind: LocalDeploymentKind,\n  deploymentName: string,\n) {\n  return path.join(rootDeploymentStateDir(deploymentKind), deploymentName);\n}\n\nexport type LocalDeploymentConfig = {\n  ports: {\n    cloud: number;\n    site: number;\n  };\n  backendVersion: string;\n  adminKey: string;\n  // If not present, use the default instance secret for local backends\n  instanceSecret?: string;\n};\nexport function loadDeploymentConfig(\n  ctx: Context,\n  deploymentKind: LocalDeploymentKind,\n  deploymentName: string,\n): LocalDeploymentConfig | null {\n  const dir = deploymentStateDir(deploymentKind, deploymentName);\n  const configFile = path.join(dir, \"config.json\");\n  if (!ctx.fs.exists(dir) || !ctx.fs.stat(dir).isDirectory()) {\n    logVerbose(`Deployment ${deploymentName} not found`);\n    return null;\n  }\n  if (ctx.fs.exists(configFile)) {\n    const content = ctx.fs.readUtf8File(configFile);\n    try {\n      return JSON.parse(content);\n    } catch (e) {\n      logVerbose(`Failed to parse local deployment config: ${e as any}`);\n      return null;\n    }\n  }\n  return null;\n}\n\nexport function saveDeploymentConfig(\n  ctx: Context,\n  deploymentKind: LocalDeploymentKind,\n  deploymentName: string,\n  config: LocalDeploymentConfig,\n) {\n  const dir = deploymentStateDir(deploymentKind, deploymentName);\n  const configFile = path.join(dir, \"config.json\");\n  if (!ctx.fs.exists(dir)) {\n    ctx.fs.mkdir(dir, { recursive: true });\n  }\n  ctx.fs.writeUtf8File(configFile, JSON.stringify(config));\n}\n\nexport function binariesDir() {\n  return path.join(cacheDir(), \"binaries\");\n}\n\nexport function dashboardZip() {\n  return path.join(dashboardDir(), \"dashboard.zip\");\n}\n\nexport function versionedBinaryDir(version: string) {\n  return path.join(binariesDir(), version);\n}\n\nexport function executablePath(version: string) {\n  return path.join(versionedBinaryDir(version), executableName());\n}\n\nexport function executableName() {\n  const ext = process.platform === \"win32\" ? \".exe\" : \"\";\n  return `convex-local-backend${ext}`;\n}\n\nexport function dashboardDir() {\n  return path.join(cacheDir(), \"dashboard\");\n}\n\nexport async function resetDashboardDir(ctx: Context) {\n  const dir = dashboardDir();\n  if (ctx.fs.exists(dir)) {\n    await recursivelyDelete(ctx, dir);\n  }\n  ctx.fs.mkdir(dir, { recursive: true });\n}\n\nexport function dashboardOutDir() {\n  return path.join(dashboardDir(), \"out\");\n}\n\nexport type DashboardConfig = {\n  port: number;\n  apiPort: number;\n  version: string;\n};\nexport function loadDashboardConfig(ctx: Context) {\n  const configFile = path.join(dashboardDir(), \"config.json\");\n  if (!ctx.fs.exists(configFile)) {\n    return null;\n  }\n  const content = ctx.fs.readUtf8File(configFile);\n  try {\n    return JSON.parse(content);\n  } catch (e) {\n    logVerbose(`Failed to parse dashboard config: ${e as any}`);\n    return null;\n  }\n}\n\nexport function saveDashboardConfig(ctx: Context, config: DashboardConfig) {\n  const configFile = path.join(dashboardDir(), \"config.json\");\n  if (!ctx.fs.exists(dashboardDir())) {\n    ctx.fs.mkdir(dashboardDir(), { recursive: true });\n  }\n  ctx.fs.writeUtf8File(configFile, JSON.stringify(config));\n}\n\nexport function loadUuidForAnonymousUser(ctx: Context) {\n  const configFile = path.join(\n    rootDeploymentStateDir(\"anonymous\"),\n    \"config.json\",\n  );\n  if (!ctx.fs.exists(configFile)) {\n    return null;\n  }\n  const content = ctx.fs.readUtf8File(configFile);\n  try {\n    const config = JSON.parse(content);\n    return config.uuid ?? null;\n  } catch (e) {\n    logVerbose(`Failed to parse uuid for anonymous user: ${e as any}`);\n    return null;\n  }\n}\n\nexport function ensureUuidForAnonymousUser(ctx: Context) {\n  const uuid = loadUuidForAnonymousUser(ctx);\n  if (uuid) {\n    return uuid;\n  }\n  const newUuid = crypto.randomUUID();\n  const anonymousDir = rootDeploymentStateDir(\"anonymous\");\n  if (!ctx.fs.exists(anonymousDir)) {\n    ctx.fs.mkdir(anonymousDir, { recursive: true });\n  }\n  ctx.fs.writeUtf8File(\n    path.join(anonymousDir, \"config.json\"),\n    JSON.stringify({ uuid: newUuid }),\n  );\n  return newUuid;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiCA,kBAAiB;AACjB,mBAAwC;AAExC,iBAA2B;AAC3B,qBAAkC;AAClC,oBAAmB;AAOZ,SAAS,uBAAuB,MAA2B;AAChE,SAAO,YAAAA,QAAK;AAAA,QACV,4BAAc;AAAA,IACd,SAAS,UACL,yBACA;AAAA,EACN;AACF;AAEO,SAAS,mBACd,gBACA,gBACA;AACA,SAAO,YAAAA,QAAK,KAAK,uBAAuB,cAAc,GAAG,cAAc;AACzE;AAYO,SAAS,qBACd,KACA,gBACA,gBAC8B;AAC9B,QAAM,MAAM,mBAAmB,gBAAgB,cAAc;AAC7D,QAAM,aAAa,YAAAA,QAAK,KAAK,KAAK,aAAa;AAC/C,MAAI,CAAC,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE,YAAY,GAAG;AAC1D,+BAAW,cAAc,cAAc,YAAY;AACnD,WAAO;AAAA,EACT;AACA,MAAI,IAAI,GAAG,OAAO,UAAU,GAAG;AAC7B,UAAM,UAAU,IAAI,GAAG,aAAa,UAAU;AAC9C,QAAI;AACF,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B,SAAS,GAAG;AACV,iCAAW,4CAA4C,CAAQ,EAAE;AACjE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEO,SAAS,qBACd,KACA,gBACA,gBACA,QACA;AACA,QAAM,MAAM,mBAAmB,gBAAgB,cAAc;AAC7D,QAAM,aAAa,YAAAA,QAAK,KAAK,KAAK,aAAa;AAC/C,MAAI,CAAC,IAAI,GAAG,OAAO,GAAG,GAAG;AACvB,QAAI,GAAG,MAAM,KAAK,EAAE,WAAW,KAAK,CAAC;AAAA,EACvC;AACA,MAAI,GAAG,cAAc,YAAY,KAAK,UAAU,MAAM,CAAC;AACzD;AAEO,SAAS,cAAc;AAC5B,SAAO,YAAAA,QAAK,SAAK,uBAAS,GAAG,UAAU;AACzC;AAEO,SAAS,eAAe;AAC7B,SAAO,YAAAA,QAAK,KAAK,aAAa,GAAG,eAAe;AAClD;AAEO,SAAS,mBAAmB,SAAiB;AAClD,SAAO,YAAAA,QAAK,KAAK,YAAY,GAAG,OAAO;AACzC;AAEO,SAAS,eAAe,SAAiB;AAC9C,SAAO,YAAAA,QAAK,KAAK,mBAAmB,OAAO,GAAG,eAAe,CAAC;AAChE;AAEO,SAAS,iBAAiB;AAC/B,QAAM,MAAM,QAAQ,aAAa,UAAU,SAAS;AACpD,SAAO,uBAAuB,GAAG;AACnC;AAEO,SAAS,eAAe;AAC7B,SAAO,YAAAA,QAAK,SAAK,uBAAS,GAAG,WAAW;AAC1C;AAEA,eAAsB,kBAAkB,KAAc;AACpD,QAAM,MAAM,aAAa;AACzB,MAAI,IAAI,GAAG,OAAO,GAAG,GAAG;AACtB,cAAM,kCAAkB,KAAK,GAAG;AAAA,EAClC;AACA,MAAI,GAAG,MAAM,KAAK,EAAE,WAAW,KAAK,CAAC;AACvC;AAEO,SAAS,kBAAkB;AAChC,SAAO,YAAAA,QAAK,KAAK,aAAa,GAAG,KAAK;AACxC;AAOO,SAAS,oBAAoB,KAAc;AAChD,QAAM,aAAa,YAAAA,QAAK,KAAK,aAAa,GAAG,aAAa;AAC1D,MAAI,CAAC,IAAI,GAAG,OAAO,UAAU,GAAG;AAC9B,WAAO;AAAA,EACT;AACA,QAAM,UAAU,IAAI,GAAG,aAAa,UAAU;AAC9C,MAAI;AACF,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B,SAAS,GAAG;AACV,+BAAW,qCAAqC,CAAQ,EAAE;AAC1D,WAAO;AAAA,EACT;AACF;AAEO,SAAS,oBAAoB,KAAc,QAAyB;AACzE,QAAM,aAAa,YAAAA,QAAK,KAAK,aAAa,GAAG,aAAa;AAC1D,MAAI,CAAC,IAAI,GAAG,OAAO,aAAa,CAAC,GAAG;AAClC,QAAI,GAAG,MAAM,aAAa,GAAG,EAAE,WAAW,KAAK,CAAC;AAAA,EAClD;AACA,MAAI,GAAG,cAAc,YAAY,KAAK,UAAU,MAAM,CAAC;AACzD;AAEO,SAAS,yBAAyB,KAAc;AACrD,QAAM,aAAa,YAAAA,QAAK;AAAA,IACtB,uBAAuB,WAAW;AAAA,IAClC;AAAA,EACF;AACA,MAAI,CAAC,IAAI,GAAG,OAAO,UAAU,GAAG;AAC9B,WAAO;AAAA,EACT;AACA,QAAM,UAAU,IAAI,GAAG,aAAa,UAAU;AAC9C,MAAI;AACF,UAAM,SAAS,KAAK,MAAM,OAAO;AACjC,WAAO,OAAO,QAAQ;AAAA,EACxB,SAAS,GAAG;AACV,+BAAW,4CAA4C,CAAQ,EAAE;AACjE,WAAO;AAAA,EACT;AACF;AAEO,SAAS,2BAA2B,KAAc;AACvD,QAAM,OAAO,yBAAyB,GAAG;AACzC,MAAI,MAAM;AACR,WAAO;AAAA,EACT;AACA,QAAM,UAAU,cAAAC,QAAO,WAAW;AAClC,QAAM,eAAe,uBAAuB,WAAW;AACvD,MAAI,CAAC,IAAI,GAAG,OAAO,YAAY,GAAG;AAChC,QAAI,GAAG,MAAM,cAAc,EAAE,WAAW,KAAK,CAAC;AAAA,EAChD;AACA,MAAI,GAAG;AAAA,IACL,YAAAD,QAAK,KAAK,cAAc,aAAa;AAAA,IACrC,KAAK,UAAU,EAAE,MAAM,QAAQ,CAAC;AAAA,EAClC;AACA,SAAO;AACT;", "names": ["path", "crypto"]}