{"version": 3, "sources": ["../../../../../src/cli/lib/utils/hash.ts"], "sourcesContent": ["import { createHash } from \"crypto\";\n\nexport function hashSha256(value: string): string {\n  return createHash(\"sha256\").update(value, \"utf8\").digest(\"hex\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAEpB,SAAS,WAAW,OAAuB;AAChD,aAAO,0BAAW,QAAQ,EAAE,OAAO,OAAO,MAAM,EAAE,OAAO,KAAK;AAChE;", "names": []}