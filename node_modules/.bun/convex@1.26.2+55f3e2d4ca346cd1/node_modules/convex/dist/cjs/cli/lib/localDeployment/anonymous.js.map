{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/anonymous.ts"], "sourcesContent": ["// ----------------------------------------------------------------------------\n// Anonymous (No account)\n\nimport path from \"path\";\nimport { Context } from \"../../../bundler/context.js\";\nimport {\n  logFinishedStep,\n  logMessage,\n  logVerbose,\n  logWarning,\n} from \"../../../bundler/log.js\";\nimport { promptSearch, promptString, promptYesNo } from \"../utils/prompts.js\";\nimport {\n  bigBrainGenerateAdminKeyForAnonymousDeployment,\n  bigBrainPause,\n  bigBrainStart,\n} from \"./bigBrain.js\";\nimport { LocalDeploymentError, printLocalDeploymentOnError } from \"./errors.js\";\nimport {\n  LocalDeploymentKind,\n  deploymentStateDir,\n  ensureUuidForAnonymousUser,\n  loadDeploymentConfig,\n  saveDeploymentConfig,\n} from \"./filePaths.js\";\nimport { rootDeploymentStateDir } from \"./filePaths.js\";\nimport { LocalDeploymentConfig } from \"./filePaths.js\";\nimport { DeploymentDetails } from \"./localDeployment.js\";\nimport { ensureBackendStopped, localDeploymentUrl } from \"./run.js\";\nimport { ensureBackendRunning } from \"./run.js\";\nimport { handlePotentialUpgrade } from \"./upgrade.js\";\nimport {\n  isOffline,\n  generateInstanceSecret,\n  choosePorts,\n  LOCAL_BACKEND_INSTANCE_SECRET,\n} from \"./utils.js\";\nimport { handleDashboard } from \"./dashboard.js\";\nimport crypto from \"crypto\";\nimport { recursivelyDelete, recursivelyCopy } from \"../fsUtils.js\";\nimport { ensureBackendBinaryDownloaded } from \"./download.js\";\nimport { isAnonymousDeployment } from \"../deployment.js\";\nimport { createProject } from \"../api.js\";\nimport { removeAnonymousPrefix } from \"../deployment.js\";\nimport { nodeFs } from \"../../../bundler/fs.js\";\nimport { doCodegenForNewProject } from \"../codegen.js\";\n\nexport async function handleAnonymousDeployment(\n  ctx: Context,\n  options: {\n    ports?: {\n      cloud: number;\n      site: number;\n    };\n    backendVersion?: string;\n    dashboardVersion?: string;\n    forceUpgrade: boolean;\n    deploymentName: string | null;\n    chosenConfiguration: \"new\" | \"existing\" | \"ask\" | null;\n  },\n): Promise<DeploymentDetails> {\n  if (await isOffline()) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Cannot run a local deployment while offline\",\n    });\n  }\n\n  const deployment = await chooseDeployment(ctx, {\n    deploymentName: options.deploymentName,\n    chosenConfiguration: options.chosenConfiguration,\n  });\n  if (\n    deployment.kind === \"first\" &&\n    process.env.CONVEX_AGENT_MODE !== \"anonymous\"\n  ) {\n    logMessage(\n      \"This command, `npx convex dev`, will run your Convex backend locally and update it with the function you write in the `convex/` directory.\",\n    );\n    logMessage(\n      \"Use `npx convex dashboard` to view and interact with your project from a web UI.\",\n    );\n    logMessage(\n      \"Use `npx convex docs` to read the docs and `npx convex help` to see other commands.\",\n    );\n    ensureUuidForAnonymousUser(ctx);\n    if (process.stdin.isTTY) {\n      const result = await promptYesNo(ctx, {\n        message: \"Continue?\",\n        default: true,\n      });\n      if (!result) {\n        return ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: \"Exiting\",\n        });\n      }\n    }\n  }\n  ctx.registerCleanup(async (_exitCode, err) => {\n    if (err instanceof LocalDeploymentError) {\n      printLocalDeploymentOnError();\n    }\n  });\n  const { binaryPath, version } = await ensureBackendBinaryDownloaded(\n    ctx,\n    options.backendVersion === undefined\n      ? {\n          kind: \"latest\",\n        }\n      : { kind: \"version\", version: options.backendVersion },\n  );\n  await handleDashboard(ctx, version);\n  let adminKey: string;\n  let instanceSecret: string;\n  if (deployment.kind === \"existing\") {\n    adminKey = deployment.config.adminKey;\n    instanceSecret =\n      deployment.config.instanceSecret ?? LOCAL_BACKEND_INSTANCE_SECRET;\n    // If it's still running for some reason, exit and tell the user to kill it.\n    // It's fine if a different backend is running on these ports though since we'll\n    // pick new ones.\n    await ensureBackendStopped(ctx, {\n      ports: {\n        cloud: deployment.config.ports.cloud,\n      },\n      maxTimeSecs: 5,\n      deploymentName: deployment.deploymentName,\n      allowOtherDeployments: true,\n    });\n  } else {\n    instanceSecret = generateInstanceSecret();\n    const data = await bigBrainGenerateAdminKeyForAnonymousDeployment(ctx, {\n      instanceName: deployment.deploymentName,\n      instanceSecret,\n    });\n    adminKey = data.adminKey;\n  }\n\n  const [cloudPort, sitePort] = await choosePorts(ctx, {\n    count: 2,\n    startPort: 3210,\n    requestedPorts: [options.ports?.cloud ?? null, options.ports?.site ?? null],\n  });\n  const onActivity = async (isOffline: boolean, _wasOffline: boolean) => {\n    await ensureBackendRunning(ctx, {\n      cloudPort,\n      deploymentName: deployment.deploymentName,\n      maxTimeSecs: 5,\n    });\n    if (isOffline) {\n      return;\n    }\n  };\n\n  const { cleanupHandle } = await handlePotentialUpgrade(ctx, {\n    deploymentName: deployment.deploymentName,\n    deploymentKind: \"anonymous\",\n    oldVersion:\n      deployment.kind === \"existing\" ? deployment.config.backendVersion : null,\n    newBinaryPath: binaryPath,\n    newVersion: version,\n    ports: { cloud: cloudPort, site: sitePort },\n    adminKey,\n    instanceSecret,\n    forceUpgrade: options.forceUpgrade,\n  });\n\n  const cleanupFunc = ctx.removeCleanup(cleanupHandle);\n  ctx.registerCleanup(async (exitCode, err) => {\n    if (cleanupFunc !== null) {\n      await cleanupFunc(exitCode, err);\n    }\n  });\n\n  if (deployment.kind === \"new\") {\n    await doCodegenForNewProject(ctx);\n  }\n  return {\n    adminKey,\n    deploymentName: deployment.deploymentName,\n    deploymentUrl: localDeploymentUrl(cloudPort),\n    onActivity,\n  };\n}\n\nexport async function loadAnonymousDeployment(\n  ctx: Context,\n  deploymentName: string,\n): Promise<LocalDeploymentConfig> {\n  const config = loadDeploymentConfig(ctx, \"anonymous\", deploymentName);\n  if (config === null) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Could not find deployment with name ${deploymentName}!`,\n    });\n  }\n  return config;\n}\n\nexport async function listExistingAnonymousDeployments(ctx: Context): Promise<\n  Array<{\n    deploymentName: string;\n    config: LocalDeploymentConfig;\n  }>\n> {\n  const dir = rootDeploymentStateDir(\"anonymous\");\n  if (!ctx.fs.exists(dir)) {\n    return [];\n  }\n  const deploymentNames = ctx.fs\n    .listDir(dir)\n    .map((d) => d.name)\n    .filter((d) => isAnonymousDeployment(d));\n  return deploymentNames.flatMap((deploymentName) => {\n    const config = loadDeploymentConfig(ctx, \"anonymous\", deploymentName);\n    if (config !== null) {\n      return [{ deploymentName, config }];\n    }\n    return [];\n  });\n}\n\nasync function chooseDeployment(\n  ctx: Context,\n  options: {\n    deploymentName: string | null;\n    chosenConfiguration: \"new\" | \"existing\" | \"ask\" | null;\n  },\n): Promise<\n  | {\n      kind: \"existing\";\n      deploymentName: string;\n      config: LocalDeploymentConfig;\n    }\n  | {\n      kind: \"new\";\n      deploymentName: string;\n    }\n  | {\n      kind: \"first\";\n      deploymentName: string;\n    }\n> {\n  const deployments = await listExistingAnonymousDeployments(ctx);\n  if (options.deploymentName !== null && options.chosenConfiguration === null) {\n    const existing = deployments.find(\n      (d) => d.deploymentName === options.deploymentName,\n    );\n    if (existing === undefined) {\n      logWarning(`Could not find project with name ${options.deploymentName}!`);\n    } else {\n      return {\n        kind: \"existing\",\n        deploymentName: existing.deploymentName,\n        config: existing.config,\n      };\n    }\n  }\n  if (process.env.CONVEX_AGENT_MODE === \"anonymous\") {\n    const deploymentName = \"anonymous-agent\";\n    const uniqueName = await getUniqueName(\n      ctx,\n      deploymentName,\n      deployments.map((d) => d.deploymentName),\n    );\n    logVerbose(`Deployment name: ${uniqueName}`);\n    return {\n      kind: \"new\",\n      deploymentName: uniqueName,\n    };\n  }\n\n  if (deployments.length === 0) {\n    logMessage(\"Let's set up your first project.\");\n    return await promptForNewDeployment(ctx, []);\n  }\n\n  if (options.chosenConfiguration === \"new\") {\n    const deploymentName = await promptString(ctx, {\n      message: \"Choose a name for your new project:\",\n      default: path.basename(process.cwd()),\n    });\n    const uniqueName = await getUniqueName(\n      ctx,\n      deploymentName,\n      deployments.map((d) => d.deploymentName),\n    );\n    logVerbose(`Deployment name: ${uniqueName}`);\n    return {\n      kind: \"new\",\n      deploymentName: uniqueName,\n    };\n  }\n\n  const newOrExisting = await promptSearch(ctx, {\n    message: \"Which project would you like to use?\",\n    choices: [\n      ...(options.chosenConfiguration === \"existing\"\n        ? []\n        : [\n            {\n              name: \"Create a new one\",\n              value: \"new\",\n            },\n          ]),\n      ...deployments.map((d) => ({\n        name: d.deploymentName,\n        value: d.deploymentName,\n      })),\n    ],\n  });\n\n  if (newOrExisting !== \"new\") {\n    const existingDeployment = deployments.find(\n      (d) => d.deploymentName === newOrExisting,\n    );\n    if (existingDeployment === undefined) {\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Could not find project with name ${newOrExisting}!`,\n      });\n    }\n    return {\n      kind: \"existing\",\n      deploymentName: existingDeployment.deploymentName,\n      config: existingDeployment.config,\n    };\n  }\n  return await promptForNewDeployment(\n    ctx,\n    deployments.map((d) => d.deploymentName),\n  );\n}\n\nasync function promptForNewDeployment(\n  ctx: Context,\n  existingNames: string[],\n): Promise<\n  | {\n      kind: \"first\";\n      deploymentName: string;\n    }\n  | {\n      kind: \"new\";\n      deploymentName: string;\n    }\n> {\n  const isFirstDeployment = existingNames.length === 0;\n  const deploymentName = await promptString(ctx, {\n    message: \"Choose a name:\",\n    default: path.basename(process.cwd()),\n  });\n\n  const uniqueName = await getUniqueName(\n    ctx,\n    `anonymous-${deploymentName}`,\n    existingNames,\n  );\n  logVerbose(`Deployment name: ${uniqueName}`);\n  return isFirstDeployment\n    ? {\n        kind: \"first\",\n        deploymentName: uniqueName,\n      }\n    : {\n        kind: \"new\",\n        deploymentName: uniqueName,\n      };\n}\n\nasync function getUniqueName(\n  ctx: Context,\n  name: string,\n  existingNames: string[],\n) {\n  if (!existingNames.includes(name)) {\n    return name;\n  }\n  for (let i = 1; i <= 5; i++) {\n    const uniqueName = `${name}-${i}`;\n    if (!existingNames.includes(uniqueName)) {\n      return uniqueName;\n    }\n  }\n  const randomSuffix = crypto.randomBytes(4).toString(\"hex\");\n\n  const uniqueName = `${name}-${randomSuffix}`;\n  if (!existingNames.includes(uniqueName)) {\n    return uniqueName;\n  }\n  return ctx.crash({\n    exitCode: 1,\n    errorType: \"fatal\",\n    printedMessage: `Could not generate a unique name for your project, please choose a different name`,\n  });\n}\n/**\n * This takes an \"anonymous\" deployment and makes it a \"local\" deployment\n * that is associated with a project in the given team.\n */\nexport async function handleLinkToProject(\n  ctx: Context,\n  args: {\n    deploymentName: string;\n    teamSlug: string;\n    projectSlug: string | null;\n  },\n): Promise<{\n  deploymentName: string;\n  deploymentUrl: string;\n  projectSlug: string;\n}> {\n  logVerbose(\n    `Linking ${args.deploymentName} to a project in team ${args.teamSlug}`,\n  );\n  const config = loadDeploymentConfig(ctx, \"anonymous\", args.deploymentName);\n  if (config === null) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Failed to load deployment config\",\n    });\n  }\n  await ensureBackendStopped(ctx, {\n    ports: {\n      cloud: config.ports.cloud,\n    },\n    deploymentName: args.deploymentName,\n    allowOtherDeployments: true,\n    maxTimeSecs: 5,\n  });\n  const projectName = removeAnonymousPrefix(args.deploymentName);\n  let projectSlug: string;\n  if (args.projectSlug !== null) {\n    projectSlug = args.projectSlug;\n  } else {\n    const { projectSlug: newProjectSlug } = await createProject(ctx, {\n      teamSlug: args.teamSlug,\n      projectName,\n      deploymentTypeToProvision: \"prod\",\n    });\n    projectSlug = newProjectSlug;\n  }\n  logVerbose(`Creating local deployment in project ${projectSlug}`);\n  // Register it in big brain\n  const { deploymentName: localDeploymentName, adminKey } = await bigBrainStart(\n    ctx,\n    {\n      port: config.ports.cloud,\n      projectSlug,\n      teamSlug: args.teamSlug,\n      instanceName: null,\n    },\n  );\n  const localConfig = loadDeploymentConfig(ctx, \"local\", localDeploymentName);\n  if (localConfig !== null) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Project ${projectSlug} already has a local deployment, so we cannot link this anonymous local deployment to it.`,\n    });\n  }\n  logVerbose(`Moving ${args.deploymentName} to ${localDeploymentName}`);\n  await moveDeployment(\n    ctx,\n    {\n      deploymentKind: \"anonymous\",\n      deploymentName: args.deploymentName,\n    },\n    {\n      deploymentKind: \"local\",\n      deploymentName: localDeploymentName,\n    },\n  );\n  logVerbose(`Saving deployment config for ${localDeploymentName}`);\n  saveDeploymentConfig(ctx, \"local\", localDeploymentName, {\n    adminKey,\n    backendVersion: config.backendVersion,\n    ports: config.ports,\n  });\n  await bigBrainPause(ctx, {\n    projectSlug,\n    teamSlug: args.teamSlug,\n  });\n  logFinishedStep(`Linked ${args.deploymentName} to project ${projectSlug}`);\n  return {\n    projectSlug,\n    deploymentName: localDeploymentName,\n    deploymentUrl: localDeploymentUrl(config.ports.cloud),\n  };\n}\n\nexport async function moveDeployment(\n  ctx: Context,\n  oldDeployment: {\n    deploymentKind: LocalDeploymentKind;\n    deploymentName: string;\n  },\n  newDeployment: {\n    deploymentKind: LocalDeploymentKind;\n    deploymentName: string;\n  },\n) {\n  const oldPath = deploymentStateDir(\n    oldDeployment.deploymentKind,\n    oldDeployment.deploymentName,\n  );\n  const newPath = deploymentStateDir(\n    newDeployment.deploymentKind,\n    newDeployment.deploymentName,\n  );\n  await recursivelyCopy(ctx, nodeFs, oldPath, newPath);\n  recursivelyDelete(ctx, oldPath);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,kBAAiB;AAEjB,iBAKO;AACP,qBAAwD;AACxD,sBAIO;AACP,oBAAkE;AAClE,uBAMO;AACP,IAAAA,oBAAuC;AAGvC,iBAAyD;AACzD,IAAAC,cAAqC;AACrC,qBAAuC;AACvC,mBAKO;AACP,uBAAgC;AAChC,oBAAmB;AACnB,qBAAmD;AACnD,sBAA8C;AAC9C,wBAAsC;AACtC,iBAA8B;AAC9B,IAAAC,qBAAsC;AACtC,gBAAuB;AACvB,qBAAuC;AAEvC,eAAsB,0BACpB,KACA,SAW4B;AAC5B,MAAI,UAAM,wBAAU,GAAG;AACrB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,QAAM,aAAa,MAAM,iBAAiB,KAAK;AAAA,IAC7C,gBAAgB,QAAQ;AAAA,IACxB,qBAAqB,QAAQ;AAAA,EAC/B,CAAC;AACD,MACE,WAAW,SAAS,WACpB,QAAQ,IAAI,sBAAsB,aAClC;AACA;AAAA,MACE;AAAA,IACF;AACA;AAAA,MACE;AAAA,IACF;AACA;AAAA,MACE;AAAA,IACF;AACA,qDAA2B,GAAG;AAC9B,QAAI,QAAQ,MAAM,OAAO;AACvB,YAAM,SAAS,UAAM,4BAAY,KAAK;AAAA,QACpC,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AACD,UAAI,CAAC,QAAQ;AACX,eAAO,IAAI,MAAM;AAAA,UACf,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,MAAI,gBAAgB,OAAO,WAAW,QAAQ;AAC5C,QAAI,eAAe,oCAAsB;AACvC,qDAA4B;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,QAAM,EAAE,YAAY,QAAQ,IAAI,UAAM;AAAA,IACpC;AAAA,IACA,QAAQ,mBAAmB,SACvB;AAAA,MACE,MAAM;AAAA,IACR,IACA,EAAE,MAAM,WAAW,SAAS,QAAQ,eAAe;AAAA,EACzD;AACA,YAAM,kCAAgB,KAAK,OAAO;AAClC,MAAI;AACJ,MAAI;AACJ,MAAI,WAAW,SAAS,YAAY;AAClC,eAAW,WAAW,OAAO;AAC7B,qBACE,WAAW,OAAO,kBAAkB;AAItC,cAAM,iCAAqB,KAAK;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO,WAAW,OAAO,MAAM;AAAA,MACjC;AAAA,MACA,aAAa;AAAA,MACb,gBAAgB,WAAW;AAAA,MAC3B,uBAAuB;AAAA,IACzB,CAAC;AAAA,EACH,OAAO;AACL,yBAAiB,qCAAuB;AACxC,UAAM,OAAO,UAAM,gEAA+C,KAAK;AAAA,MACrE,cAAc,WAAW;AAAA,MACzB;AAAA,IACF,CAAC;AACD,eAAW,KAAK;AAAA,EAClB;AAEA,QAAM,CAAC,WAAW,QAAQ,IAAI,UAAM,0BAAY,KAAK;AAAA,IACnD,OAAO;AAAA,IACP,WAAW;AAAA,IACX,gBAAgB,CAAC,QAAQ,OAAO,SAAS,MAAM,QAAQ,OAAO,QAAQ,IAAI;AAAA,EAC5E,CAAC;AACD,QAAM,aAAa,OAAOC,YAAoB,gBAAyB;AACrE,cAAM,kCAAqB,KAAK;AAAA,MAC9B;AAAA,MACA,gBAAgB,WAAW;AAAA,MAC3B,aAAa;AAAA,IACf,CAAC;AACD,QAAIA,YAAW;AACb;AAAA,IACF;AAAA,EACF;AAEA,QAAM,EAAE,cAAc,IAAI,UAAM,uCAAuB,KAAK;AAAA,IAC1D,gBAAgB,WAAW;AAAA,IAC3B,gBAAgB;AAAA,IAChB,YACE,WAAW,SAAS,aAAa,WAAW,OAAO,iBAAiB;AAAA,IACtE,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,OAAO,EAAE,OAAO,WAAW,MAAM,SAAS;AAAA,IAC1C;AAAA,IACA;AAAA,IACA,cAAc,QAAQ;AAAA,EACxB,CAAC;AAED,QAAM,cAAc,IAAI,cAAc,aAAa;AACnD,MAAI,gBAAgB,OAAO,UAAU,QAAQ;AAC3C,QAAI,gBAAgB,MAAM;AACxB,YAAM,YAAY,UAAU,GAAG;AAAA,IACjC;AAAA,EACF,CAAC;AAED,MAAI,WAAW,SAAS,OAAO;AAC7B,cAAM,uCAAuB,GAAG;AAAA,EAClC;AACA,SAAO;AAAA,IACL;AAAA,IACA,gBAAgB,WAAW;AAAA,IAC3B,mBAAe,+BAAmB,SAAS;AAAA,IAC3C;AAAA,EACF;AACF;AAEA,eAAsB,wBACpB,KACA,gBACgC;AAChC,QAAM,aAAS,uCAAqB,KAAK,aAAa,cAAc;AACpE,MAAI,WAAW,MAAM;AACnB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,uCAAuC,cAAc;AAAA,IACvE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,eAAsB,iCAAiC,KAKrD;AACA,QAAM,UAAM,0CAAuB,WAAW;AAC9C,MAAI,CAAC,IAAI,GAAG,OAAO,GAAG,GAAG;AACvB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,kBAAkB,IAAI,GACzB,QAAQ,GAAG,EACX,IAAI,CAAC,MAAM,EAAE,IAAI,EACjB,OAAO,CAAC,UAAM,yCAAsB,CAAC,CAAC;AACzC,SAAO,gBAAgB,QAAQ,CAAC,mBAAmB;AACjD,UAAM,aAAS,uCAAqB,KAAK,aAAa,cAAc;AACpE,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC,EAAE,gBAAgB,OAAO,CAAC;AAAA,IACpC;AACA,WAAO,CAAC;AAAA,EACV,CAAC;AACH;AAEA,eAAe,iBACb,KACA,SAkBA;AACA,QAAM,cAAc,MAAM,iCAAiC,GAAG;AAC9D,MAAI,QAAQ,mBAAmB,QAAQ,QAAQ,wBAAwB,MAAM;AAC3E,UAAM,WAAW,YAAY;AAAA,MAC3B,CAAC,MAAM,EAAE,mBAAmB,QAAQ;AAAA,IACtC;AACA,QAAI,aAAa,QAAW;AAC1B,iCAAW,oCAAoC,QAAQ,cAAc,GAAG;AAAA,IAC1E,OAAO;AACL,aAAO;AAAA,QACL,MAAM;AAAA,QACN,gBAAgB,SAAS;AAAA,QACzB,QAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,IAAI,sBAAsB,aAAa;AACjD,UAAM,iBAAiB;AACvB,UAAM,aAAa,MAAM;AAAA,MACvB;AAAA,MACA;AAAA,MACA,YAAY,IAAI,CAAC,MAAM,EAAE,cAAc;AAAA,IACzC;AACA,+BAAW,oBAAoB,UAAU,EAAE;AAC3C,WAAO;AAAA,MACL,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB;AAAA,EACF;AAEA,MAAI,YAAY,WAAW,GAAG;AAC5B,+BAAW,kCAAkC;AAC7C,WAAO,MAAM,uBAAuB,KAAK,CAAC,CAAC;AAAA,EAC7C;AAEA,MAAI,QAAQ,wBAAwB,OAAO;AACzC,UAAM,iBAAiB,UAAM,6BAAa,KAAK;AAAA,MAC7C,SAAS;AAAA,MACT,SAAS,YAAAC,QAAK,SAAS,QAAQ,IAAI,CAAC;AAAA,IACtC,CAAC;AACD,UAAM,aAAa,MAAM;AAAA,MACvB;AAAA,MACA;AAAA,MACA,YAAY,IAAI,CAAC,MAAM,EAAE,cAAc;AAAA,IACzC;AACA,+BAAW,oBAAoB,UAAU,EAAE;AAC3C,WAAO;AAAA,MACL,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB;AAAA,EACF;AAEA,QAAM,gBAAgB,UAAM,6BAAa,KAAK;AAAA,IAC5C,SAAS;AAAA,IACT,SAAS;AAAA,MACP,GAAI,QAAQ,wBAAwB,aAChC,CAAC,IACD;AAAA,QACE;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACJ,GAAG,YAAY,IAAI,CAAC,OAAO;AAAA,QACzB,MAAM,EAAE;AAAA,QACR,OAAO,EAAE;AAAA,MACX,EAAE;AAAA,IACJ;AAAA,EACF,CAAC;AAED,MAAI,kBAAkB,OAAO;AAC3B,UAAM,qBAAqB,YAAY;AAAA,MACrC,CAAC,MAAM,EAAE,mBAAmB;AAAA,IAC9B;AACA,QAAI,uBAAuB,QAAW;AACpC,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,oCAAoC,aAAa;AAAA,MACnE,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,gBAAgB,mBAAmB;AAAA,MACnC,QAAQ,mBAAmB;AAAA,IAC7B;AAAA,EACF;AACA,SAAO,MAAM;AAAA,IACX;AAAA,IACA,YAAY,IAAI,CAAC,MAAM,EAAE,cAAc;AAAA,EACzC;AACF;AAEA,eAAe,uBACb,KACA,eAUA;AACA,QAAM,oBAAoB,cAAc,WAAW;AACnD,QAAM,iBAAiB,UAAM,6BAAa,KAAK;AAAA,IAC7C,SAAS;AAAA,IACT,SAAS,YAAAA,QAAK,SAAS,QAAQ,IAAI,CAAC;AAAA,EACtC,CAAC;AAED,QAAM,aAAa,MAAM;AAAA,IACvB;AAAA,IACA,aAAa,cAAc;AAAA,IAC3B;AAAA,EACF;AACA,6BAAW,oBAAoB,UAAU,EAAE;AAC3C,SAAO,oBACH;AAAA,IACE,MAAM;AAAA,IACN,gBAAgB;AAAA,EAClB,IACA;AAAA,IACE,MAAM;AAAA,IACN,gBAAgB;AAAA,EAClB;AACN;AAEA,eAAe,cACb,KACA,MACA,eACA;AACA,MAAI,CAAC,cAAc,SAAS,IAAI,GAAG;AACjC,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,UAAMC,cAAa,GAAG,IAAI,IAAI,CAAC;AAC/B,QAAI,CAAC,cAAc,SAASA,WAAU,GAAG;AACvC,aAAOA;AAAA,IACT;AAAA,EACF;AACA,QAAM,eAAe,cAAAC,QAAO,YAAY,CAAC,EAAE,SAAS,KAAK;AAEzD,QAAM,aAAa,GAAG,IAAI,IAAI,YAAY;AAC1C,MAAI,CAAC,cAAc,SAAS,UAAU,GAAG;AACvC,WAAO;AAAA,EACT;AACA,SAAO,IAAI,MAAM;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,EAClB,CAAC;AACH;AAKA,eAAsB,oBACpB,KACA,MASC;AACD;AAAA,IACE,WAAW,KAAK,cAAc,yBAAyB,KAAK,QAAQ;AAAA,EACtE;AACA,QAAM,aAAS,uCAAqB,KAAK,aAAa,KAAK,cAAc;AACzE,MAAI,WAAW,MAAM;AACnB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,YAAM,iCAAqB,KAAK;AAAA,IAC9B,OAAO;AAAA,MACL,OAAO,OAAO,MAAM;AAAA,IACtB;AAAA,IACA,gBAAgB,KAAK;AAAA,IACrB,uBAAuB;AAAA,IACvB,aAAa;AAAA,EACf,CAAC;AACD,QAAM,kBAAc,0CAAsB,KAAK,cAAc;AAC7D,MAAI;AACJ,MAAI,KAAK,gBAAgB,MAAM;AAC7B,kBAAc,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,EAAE,aAAa,eAAe,IAAI,UAAM,0BAAc,KAAK;AAAA,MAC/D,UAAU,KAAK;AAAA,MACf;AAAA,MACA,2BAA2B;AAAA,IAC7B,CAAC;AACD,kBAAc;AAAA,EAChB;AACA,6BAAW,wCAAwC,WAAW,EAAE;AAEhE,QAAM,EAAE,gBAAgB,qBAAqB,SAAS,IAAI,UAAM;AAAA,IAC9D;AAAA,IACA;AAAA,MACE,MAAM,OAAO,MAAM;AAAA,MACnB;AAAA,MACA,UAAU,KAAK;AAAA,MACf,cAAc;AAAA,IAChB;AAAA,EACF;AACA,QAAM,kBAAc,uCAAqB,KAAK,SAAS,mBAAmB;AAC1E,MAAI,gBAAgB,MAAM;AACxB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,WAAW,WAAW;AAAA,IACxC,CAAC;AAAA,EACH;AACA,6BAAW,UAAU,KAAK,cAAc,OAAO,mBAAmB,EAAE;AACpE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,MACE,gBAAgB;AAAA,MAChB,gBAAgB,KAAK;AAAA,IACvB;AAAA,IACA;AAAA,MACE,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,IAClB;AAAA,EACF;AACA,6BAAW,gCAAgC,mBAAmB,EAAE;AAChE,6CAAqB,KAAK,SAAS,qBAAqB;AAAA,IACtD;AAAA,IACA,gBAAgB,OAAO;AAAA,IACvB,OAAO,OAAO;AAAA,EAChB,CAAC;AACD,YAAM,+BAAc,KAAK;AAAA,IACvB;AAAA,IACA,UAAU,KAAK;AAAA,EACjB,CAAC;AACD,kCAAgB,UAAU,KAAK,cAAc,eAAe,WAAW,EAAE;AACzE,SAAO;AAAA,IACL;AAAA,IACA,gBAAgB;AAAA,IAChB,mBAAe,+BAAmB,OAAO,MAAM,KAAK;AAAA,EACtD;AACF;AAEA,eAAsB,eACpB,KACA,eAIA,eAIA;AACA,QAAM,cAAU;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AACA,QAAM,cAAU;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AACA,YAAM,gCAAgB,KAAK,kBAAQ,SAAS,OAAO;AACnD,wCAAkB,KAAK,OAAO;AAChC;", "names": ["import_filePaths", "import_run", "import_deployment", "isOffline", "path", "uniqueName", "crypto"]}