{"version": 3, "sources": ["../../../../src/cli/lib/versionApi.ts"], "sourcesContent": ["import * as Sentry from \"@sentry/node\";\nimport { version } from \"../version.js\";\n\nconst VERSION_ENDPOINT = \"https://version.convex.dev/v1/version\";\nconst CURSOR_RULES_ENDPOINT = \"https://version.convex.dev/v1/cursor_rules\";\n\nconst HEADERS = {\n  \"Convex-Client\": `npm-cli-${version}`,\n};\n\nexport type VersionResult = {\n  message: string | null;\n  cursorRulesHash: string | null;\n};\n\nexport async function getVersion(): Promise<VersionResult | null> {\n  try {\n    const req = await fetch(VERSION_ENDPOINT, {\n      headers: HEADERS,\n    });\n\n    if (!req.ok) {\n      Sentry.captureException(\n        new Error(`Failed to fetch version: status = ${req.status}`),\n      );\n      return null;\n    }\n\n    const json = await req.json();\n    return validateVersionResult(json);\n  } catch (error) {\n    Sentry.captureException(error);\n    return null;\n  }\n}\n\nexport function validateVersionResult(json: any): VersionResult | null {\n  if (typeof json !== \"object\" || json === null) {\n    Sentry.captureMessage(\"Invalid version result\");\n    return null;\n  }\n\n  if (typeof json.message !== \"string\" && json.message !== null) {\n    Sentry.captureMessage(\"Invalid version.message result\");\n    return null;\n  }\n\n  if (\n    typeof json.cursorRulesHash !== \"string\" &&\n    json.cursorRulesHash !== null\n  ) {\n    Sentry.captureMessage(\"Invalid version.cursorRulesHash result\");\n    return null;\n  }\n\n  return json;\n}\n\nexport async function downloadLatestCursorRules(): Promise<string | null> {\n  try {\n    const req = await fetch(CURSOR_RULES_ENDPOINT, {\n      headers: HEADERS,\n    });\n\n    if (!req.ok) {\n      Sentry.captureMessage(\n        `Failed to fetch Cursor rules: status = ${req.status}`,\n      );\n      return null;\n    }\n\n    const text = await req.text();\n    return text;\n  } catch (error) {\n    Sentry.captureException(error);\n    return null;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAwB;AACxB,qBAAwB;AAExB,MAAM,mBAAmB;AACzB,MAAM,wBAAwB;AAE9B,MAAM,UAAU;AAAA,EACd,iBAAiB,WAAW,sBAAO;AACrC;AAOA,eAAsB,aAA4C;AAChE,MAAI;AACF,UAAM,MAAM,MAAM,MAAM,kBAAkB;AAAA,MACxC,SAAS;AAAA,IACX,CAAC;AAED,QAAI,CAAC,IAAI,IAAI;AACX,aAAO;AAAA,QACL,IAAI,MAAM,qCAAqC,IAAI,MAAM,EAAE;AAAA,MAC7D;AACA,aAAO;AAAA,IACT;AAEA,UAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,WAAO,sBAAsB,IAAI;AAAA,EACnC,SAAS,OAAO;AACd,WAAO,iBAAiB,KAAK;AAC7B,WAAO;AAAA,EACT;AACF;AAEO,SAAS,sBAAsB,MAAiC;AACrE,MAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,WAAO,eAAe,wBAAwB;AAC9C,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,KAAK,YAAY,YAAY,KAAK,YAAY,MAAM;AAC7D,WAAO,eAAe,gCAAgC;AACtD,WAAO;AAAA,EACT;AAEA,MACE,OAAO,KAAK,oBAAoB,YAChC,KAAK,oBAAoB,MACzB;AACA,WAAO,eAAe,wCAAwC;AAC9D,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,eAAsB,4BAAoD;AACxE,MAAI;AACF,UAAM,MAAM,MAAM,MAAM,uBAAuB;AAAA,MAC7C,SAAS;AAAA,IACX,CAAC;AAED,QAAI,CAAC,IAAI,IAAI;AACX,aAAO;AAAA,QACL,0CAA0C,IAAI,MAAM;AAAA,MACtD;AACA,aAAO;AAAA,IACT;AAEA,UAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,WAAO;AAAA,EACT,SAAS,OAAO;AACd,WAAO,iBAAiB,KAAK;AAC7B,WAAO;AAAA,EACT;AACF;", "names": []}