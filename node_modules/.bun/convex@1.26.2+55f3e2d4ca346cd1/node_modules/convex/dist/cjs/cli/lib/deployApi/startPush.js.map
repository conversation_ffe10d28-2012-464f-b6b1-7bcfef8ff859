{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/startPush.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { componentDefinitionPath, componentPath } from \"./paths.js\";\nimport { nodeDependency, sourcePackage } from \"./modules.js\";\nimport { checkedComponent } from \"./checkedComponent.js\";\nimport { evaluatedComponentDefinition } from \"./componentDefinition.js\";\nimport {\n  appDefinitionConfig,\n  componentDefinitionConfig,\n} from \"./definitionConfig.js\";\nimport { authInfo } from \"./types.js\";\nimport { looseObject } from \"./utils.js\";\nimport { indexDiff } from \"./finishPush.js\";\n\nexport const startPushRequest = looseObject({\n  adminKey: z.string(),\n  dryRun: z.boolean(),\n\n  functions: z.string(),\n\n  appDefinition: appDefinitionConfig,\n  componentDefinitions: z.array(componentDefinitionConfig),\n\n  nodeDependencies: z.array(nodeDependency),\n\n  nodeVersion: z.optional(z.string()),\n});\nexport type StartPushRequest = z.infer<typeof startPushRequest>;\n\nexport const schemaChange = looseObject({\n  allocatedComponentIds: z.any(),\n  schemaIds: z.any(),\n  indexDiffs: z.record(componentDefinitionPath, indexDiff).optional(),\n});\nexport type SchemaChange = z.infer<typeof schemaChange>;\n\nexport const startPushResponse = looseObject({\n  environmentVariables: z.record(z.string(), z.string()),\n\n  externalDepsId: z.nullable(z.string()),\n  componentDefinitionPackages: z.record(componentDefinitionPath, sourcePackage),\n\n  appAuth: z.array(authInfo),\n  analysis: z.record(componentDefinitionPath, evaluatedComponentDefinition),\n\n  app: checkedComponent,\n\n  schemaChange,\n});\nexport type StartPushResponse = z.infer<typeof startPushResponse>;\n\nexport const componentSchemaStatus = looseObject({\n  schemaValidationComplete: z.boolean(),\n  indexesComplete: z.number(),\n  indexesTotal: z.number(),\n});\nexport type ComponentSchemaStatus = z.infer<typeof componentSchemaStatus>;\n\nexport const schemaStatus = z.union([\n  looseObject({\n    type: z.literal(\"inProgress\"),\n    components: z.record(componentPath, componentSchemaStatus),\n  }),\n  looseObject({\n    type: z.literal(\"failed\"),\n    error: z.string(),\n    componentPath,\n    tableName: z.nullable(z.string()),\n  }),\n  looseObject({\n    type: z.literal(\"raceDetected\"),\n  }),\n  looseObject({\n    type: z.literal(\"complete\"),\n  }),\n]);\nexport type SchemaStatus = z.infer<typeof schemaStatus>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAClB,mBAAuD;AACvD,qBAA8C;AAC9C,8BAAiC;AACjC,iCAA6C;AAC7C,8BAGO;AACP,mBAAyB;AACzB,mBAA4B;AAC5B,wBAA0B;AAEnB,MAAM,uBAAmB,0BAAY;AAAA,EAC1C,UAAU,aAAE,OAAO;AAAA,EACnB,QAAQ,aAAE,QAAQ;AAAA,EAElB,WAAW,aAAE,OAAO;AAAA,EAEpB,eAAe;AAAA,EACf,sBAAsB,aAAE,MAAM,iDAAyB;AAAA,EAEvD,kBAAkB,aAAE,MAAM,6BAAc;AAAA,EAExC,aAAa,aAAE,SAAS,aAAE,OAAO,CAAC;AACpC,CAAC;AAGM,MAAM,mBAAe,0BAAY;AAAA,EACtC,uBAAuB,aAAE,IAAI;AAAA,EAC7B,WAAW,aAAE,IAAI;AAAA,EACjB,YAAY,aAAE,OAAO,sCAAyB,2BAAS,EAAE,SAAS;AACpE,CAAC;AAGM,MAAM,wBAAoB,0BAAY;AAAA,EAC3C,sBAAsB,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,OAAO,CAAC;AAAA,EAErD,gBAAgB,aAAE,SAAS,aAAE,OAAO,CAAC;AAAA,EACrC,6BAA6B,aAAE,OAAO,sCAAyB,4BAAa;AAAA,EAE5E,SAAS,aAAE,MAAM,qBAAQ;AAAA,EACzB,UAAU,aAAE,OAAO,sCAAyB,uDAA4B;AAAA,EAExE,KAAK;AAAA,EAEL;AACF,CAAC;AAGM,MAAM,4BAAwB,0BAAY;AAAA,EAC/C,0BAA0B,aAAE,QAAQ;AAAA,EACpC,iBAAiB,aAAE,OAAO;AAAA,EAC1B,cAAc,aAAE,OAAO;AACzB,CAAC;AAGM,MAAM,eAAe,aAAE,MAAM;AAAA,MAClC,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,YAAY;AAAA,IAC5B,YAAY,aAAE,OAAO,4BAAe,qBAAqB;AAAA,EAC3D,CAAC;AAAA,MACD,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,QAAQ;AAAA,IACxB,OAAO,aAAE,OAAO;AAAA,IAChB;AAAA,IACA,WAAW,aAAE,SAAS,aAAE,OAAO,CAAC;AAAA,EAClC,CAAC;AAAA,MACD,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,cAAc;AAAA,EAChC,CAAC;AAAA,MACD,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,UAAU;AAAA,EAC5B,CAAC;AACH,CAAC;", "names": []}