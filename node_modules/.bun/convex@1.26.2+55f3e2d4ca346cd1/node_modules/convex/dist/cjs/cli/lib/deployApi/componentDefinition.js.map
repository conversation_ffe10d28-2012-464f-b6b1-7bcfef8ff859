{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/componentDefinition.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { canonicalizedModulePath, componentDefinitionPath } from \"./paths.js\";\nimport { Identifier, Reference, identifier, reference } from \"./types.js\";\nimport { analyzedModule, udfConfig } from \"./modules.js\";\nimport { looseObject } from \"./utils.js\";\nimport { convexValidator } from \"./validator.js\";\n\nexport const componentArgumentValidator = looseObject({\n  type: z.literal(\"value\"),\n  // Validator serialized to JSON.\n  value: z.string(),\n});\n\nexport const componentDefinitionType = z.union([\n  looseObject({ type: z.literal(\"app\") }),\n  looseObject({\n    type: z.literal(\"childComponent\"),\n    name: identifier,\n    args: z.array(z.tuple([identifier, componentArgumentValidator])),\n  }),\n]);\n\nexport const componentArgument = looseObject({\n  type: z.literal(\"value\"),\n  // Value serialized to JSON.\n  value: z.string(),\n});\n\nexport const componentInstantiation = looseObject({\n  name: identifier,\n  path: componentDefinitionPath,\n  args: z.nullable(z.array(z.tuple([identifier, componentArgument]))),\n});\n\nexport type ComponentExports =\n  | { type: \"leaf\"; leaf: Reference }\n  | { type: \"branch\"; branch: [Identifier, ComponentExports][] };\n\nexport const componentExports: z.ZodType<ComponentExports> = z.lazy(() =>\n  z.union([\n    looseObject({\n      type: z.literal(\"leaf\"),\n      leaf: reference,\n    }),\n    looseObject({\n      type: z.literal(\"branch\"),\n      branch: z.array(z.tuple([identifier, componentExports])),\n    }),\n  ]),\n);\n\nexport const componentDefinitionMetadata = looseObject({\n  path: componentDefinitionPath,\n  definitionType: componentDefinitionType,\n  childComponents: z.array(componentInstantiation),\n  httpMounts: z.record(z.string(), reference),\n  exports: looseObject({\n    type: z.literal(\"branch\"),\n    branch: z.array(z.tuple([identifier, componentExports])),\n  }),\n});\n\nexport const indexSchema = looseObject({\n  indexDescriptor: z.string(),\n  fields: z.array(z.string()),\n});\n\nexport const vectorIndexSchema = looseObject({\n  indexDescriptor: z.string(),\n  vectorField: z.string(),\n  dimensions: z.number().optional(),\n  filterFields: z.array(z.string()),\n});\n\nexport const searchIndexSchema = looseObject({\n  indexDescriptor: z.string(),\n  searchField: z.string(),\n  filterFields: z.array(z.string()),\n});\n\nexport const tableDefinition = looseObject({\n  tableName: z.string(),\n  indexes: z.array(indexSchema),\n  searchIndexes: z.array(searchIndexSchema).optional().nullable(),\n  vectorIndexes: z.array(vectorIndexSchema).optional().nullable(),\n  documentType: convexValidator,\n});\nexport type TableDefinition = z.infer<typeof tableDefinition>;\n\nexport const analyzedSchema = looseObject({\n  tables: z.array(tableDefinition),\n  schemaValidation: z.boolean(),\n});\nexport type AnalyzedSchema = z.infer<typeof analyzedSchema>;\n\nexport const evaluatedComponentDefinition = looseObject({\n  definition: componentDefinitionMetadata,\n  schema: analyzedSchema.optional().nullable(),\n  functions: z.record(canonicalizedModulePath, analyzedModule),\n  udfConfig,\n});\nexport type EvaluatedComponentDefinition = z.infer<\n  typeof evaluatedComponentDefinition\n>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAClB,mBAAiE;AACjE,mBAA6D;AAC7D,qBAA0C;AAC1C,mBAA4B;AAC5B,uBAAgC;AAEzB,MAAM,iCAA6B,0BAAY;AAAA,EACpD,MAAM,aAAE,QAAQ,OAAO;AAAA;AAAA,EAEvB,OAAO,aAAE,OAAO;AAClB,CAAC;AAEM,MAAM,0BAA0B,aAAE,MAAM;AAAA,MAC7C,0BAAY,EAAE,MAAM,aAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,MACtC,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,gBAAgB;AAAA,IAChC,MAAM;AAAA,IACN,MAAM,aAAE,MAAM,aAAE,MAAM,CAAC,yBAAY,0BAA0B,CAAC,CAAC;AAAA,EACjE,CAAC;AACH,CAAC;AAEM,MAAM,wBAAoB,0BAAY;AAAA,EAC3C,MAAM,aAAE,QAAQ,OAAO;AAAA;AAAA,EAEvB,OAAO,aAAE,OAAO;AAClB,CAAC;AAEM,MAAM,6BAAyB,0BAAY;AAAA,EAChD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM,aAAE,SAAS,aAAE,MAAM,aAAE,MAAM,CAAC,yBAAY,iBAAiB,CAAC,CAAC,CAAC;AACpE,CAAC;AAMM,MAAM,mBAAgD,aAAE;AAAA,EAAK,MAClE,aAAE,MAAM;AAAA,QACN,0BAAY;AAAA,MACV,MAAM,aAAE,QAAQ,MAAM;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,QACD,0BAAY;AAAA,MACV,MAAM,aAAE,QAAQ,QAAQ;AAAA,MACxB,QAAQ,aAAE,MAAM,aAAE,MAAM,CAAC,yBAAY,gBAAgB,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACH,CAAC;AACH;AAEO,MAAM,kCAA8B,0BAAY;AAAA,EACrD,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,iBAAiB,aAAE,MAAM,sBAAsB;AAAA,EAC/C,YAAY,aAAE,OAAO,aAAE,OAAO,GAAG,sBAAS;AAAA,EAC1C,aAAS,0BAAY;AAAA,IACnB,MAAM,aAAE,QAAQ,QAAQ;AAAA,IACxB,QAAQ,aAAE,MAAM,aAAE,MAAM,CAAC,yBAAY,gBAAgB,CAAC,CAAC;AAAA,EACzD,CAAC;AACH,CAAC;AAEM,MAAM,kBAAc,0BAAY;AAAA,EACrC,iBAAiB,aAAE,OAAO;AAAA,EAC1B,QAAQ,aAAE,MAAM,aAAE,OAAO,CAAC;AAC5B,CAAC;AAEM,MAAM,wBAAoB,0BAAY;AAAA,EAC3C,iBAAiB,aAAE,OAAO;AAAA,EAC1B,aAAa,aAAE,OAAO;AAAA,EACtB,YAAY,aAAE,OAAO,EAAE,SAAS;AAAA,EAChC,cAAc,aAAE,MAAM,aAAE,OAAO,CAAC;AAClC,CAAC;AAEM,MAAM,wBAAoB,0BAAY;AAAA,EAC3C,iBAAiB,aAAE,OAAO;AAAA,EAC1B,aAAa,aAAE,OAAO;AAAA,EACtB,cAAc,aAAE,MAAM,aAAE,OAAO,CAAC;AAClC,CAAC;AAEM,MAAM,sBAAkB,0BAAY;AAAA,EACzC,WAAW,aAAE,OAAO;AAAA,EACpB,SAAS,aAAE,MAAM,WAAW;AAAA,EAC5B,eAAe,aAAE,MAAM,iBAAiB,EAAE,SAAS,EAAE,SAAS;AAAA,EAC9D,eAAe,aAAE,MAAM,iBAAiB,EAAE,SAAS,EAAE,SAAS;AAAA,EAC9D,cAAc;AAChB,CAAC;AAGM,MAAM,qBAAiB,0BAAY;AAAA,EACxC,QAAQ,aAAE,MAAM,eAAe;AAAA,EAC/B,kBAAkB,aAAE,QAAQ;AAC9B,CAAC;AAGM,MAAM,mCAA+B,0BAAY;AAAA,EACtD,YAAY;AAAA,EACZ,QAAQ,eAAe,SAAS,EAAE,SAAS;AAAA,EAC3C,WAAW,aAAE,OAAO,sCAAyB,6BAAc;AAAA,EAC3D;AACF,CAAC;", "names": []}