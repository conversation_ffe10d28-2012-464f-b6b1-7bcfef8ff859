{"version": 3, "sources": ["../../../../../src/cli/lib/utils/prompts.ts"], "sourcesContent": ["import inquirer from \"inquirer\";\nimport { Context } from \"../../../bundler/context.js\";\nimport { logOutput } from \"../../../bundler/log.js\";\nexport const promptString = async (\n  ctx: Context,\n  options: {\n    message: string;\n    default?: string;\n  },\n): Promise<string> => {\n  if (process.stdin.isTTY) {\n    const result = (\n      await inquirer.prompt([\n        {\n          type: \"input\",\n          name: \"result\",\n          message: options.message,\n          default: options.default,\n        },\n      ])\n    ).result;\n    return result;\n  } else {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Cannot prompt for input in non-interactive terminals. (${options.message})`,\n    });\n  }\n};\n\nexport const promptOptions = async <V>(\n  ctx: Context,\n  options: {\n    message: string;\n    choices: Array<{ name: string; value: V }>;\n    default?: V;\n  },\n): Promise<V> => {\n  if (process.stdin.isTTY) {\n    const result = (\n      await inquirer.prompt([\n        {\n          // In the Convex mono-repo, `list` seems to cause the command to not\n          // respond to CTRL+C while `search-list` does not.\n          type: process.env.CONVEX_RUNNING_LIVE_IN_MONOREPO\n            ? \"search-list\"\n            : \"list\",\n          name: \"result\",\n          message: options.message,\n          choices: options.choices,\n          default: options.default,\n        },\n      ])\n    ).result;\n    return result;\n  } else {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Cannot prompt for input in non-interactive terminals. (${options.message})`,\n    });\n  }\n};\n\nexport const promptSearch = async <V>(\n  ctx: Context,\n  options: {\n    message: string;\n    choices: Array<{ name: string; value: V }>;\n    default?: V;\n  },\n): Promise<V> => {\n  if (process.stdin.isTTY) {\n    const result = (\n      await inquirer.prompt([\n        {\n          type: \"search-list\",\n          name: \"result\",\n          message: options.message,\n          choices: options.choices,\n          default: options.default,\n        },\n      ])\n    ).result;\n    return result;\n  } else {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Cannot prompt for input in non-interactive terminals. (${options.message})`,\n    });\n  }\n};\n\nexport const promptYesNo = async (\n  ctx: Context,\n  options: { message: string; default?: boolean },\n): Promise<boolean> => {\n  if (process.stdin.isTTY) {\n    const { result } = await inquirer.prompt([\n      {\n        type: \"confirm\",\n        name: \"result\",\n        message: options.message,\n        default: options.default,\n      },\n    ]);\n    return result;\n  } else {\n    logOutput(options.message);\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Cannot prompt for input in non-interactive terminals. (${options.message})`,\n    });\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAqB;AAErB,iBAA0B;AACnB,MAAM,eAAe,OAC1B,KACA,YAIoB;AACpB,MAAI,QAAQ,MAAM,OAAO;AACvB,UAAM,UACJ,MAAM,gBAAAA,QAAS,OAAO;AAAA,MACpB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,CAAC,GACD;AACF,WAAO;AAAA,EACT,OAAO;AACL,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,0DAA0D,QAAQ,OAAO;AAAA,IAC3F,CAAC;AAAA,EACH;AACF;AAEO,MAAM,gBAAgB,OAC3B,KACA,YAKe;AACf,MAAI,QAAQ,MAAM,OAAO;AACvB,UAAM,UACJ,MAAM,gBAAAA,QAAS,OAAO;AAAA,MACpB;AAAA;AAAA;AAAA,QAGE,MAAM,QAAQ,IAAI,kCACd,gBACA;AAAA,QACJ,MAAM;AAAA,QACN,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,CAAC,GACD;AACF,WAAO;AAAA,EACT,OAAO;AACL,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,0DAA0D,QAAQ,OAAO;AAAA,IAC3F,CAAC;AAAA,EACH;AACF;AAEO,MAAM,eAAe,OAC1B,KACA,YAKe;AACf,MAAI,QAAQ,MAAM,OAAO;AACvB,UAAM,UACJ,MAAM,gBAAAA,QAAS,OAAO;AAAA,MACpB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,CAAC,GACD;AACF,WAAO;AAAA,EACT,OAAO;AACL,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,0DAA0D,QAAQ,OAAO;AAAA,IAC3F,CAAC;AAAA,EACH;AACF;AAEO,MAAM,cAAc,OACzB,KACA,YACqB;AACrB,MAAI,QAAQ,MAAM,OAAO;AACvB,UAAM,EAAE,OAAO,IAAI,MAAM,gBAAAA,QAAS,OAAO;AAAA,MACvC;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,OAAO;AACL,8BAAU,QAAQ,OAAO;AACzB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,0DAA0D,QAAQ,OAAO;AAAA,IAC3F,CAAC;AAAA,EACH;AACF;", "names": ["inquirer"]}