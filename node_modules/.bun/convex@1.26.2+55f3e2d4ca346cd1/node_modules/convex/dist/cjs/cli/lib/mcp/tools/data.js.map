{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/data.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { runSystemQuery } from \"../../run.js\";\nimport { ConvexTool } from \"./index.js\";\nimport { PaginationResult } from \"../../../../server/pagination.js\";\nimport { loadSelectedDeploymentCredentials } from \"../../api.js\";\nimport { getDeploymentSelection } from \"../../deploymentSelection.js\";\n\nconst inputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\"Deployment selector (from the status tool) to read data from.\"),\n  tableName: z.string().describe(\"The name of the table to read from.\"),\n  order: z.enum([\"asc\", \"desc\"]).describe(\"The order to sort the results in.\"),\n  cursor: z.string().optional().describe(\"The cursor to start reading from.\"),\n  limit: z\n    .number()\n    .max(1000)\n    .optional()\n    .describe(\"The maximum number of results to return, defaults to 100.\"),\n});\n\nconst outputSchema = z.object({\n  page: z.array(z.any()),\n  isDone: z.boolean(),\n  continueCursor: z.string(),\n});\n\nconst description = `\nRead a page of data from a table in the project's Convex deployment.\n\nOutput:\n- page: A page of results from the table.\n- isDone: Whether there are more results to read.\n- continueCursor: The cursor to use to read the next page of results.\n`.trim();\n\nexport const DataTool: ConvexTool<typeof inputSchema, typeof outputSchema> = {\n  name: \"data\",\n  description,\n  inputSchema,\n  outputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n    const paginationResult = (await runSystemQuery(ctx, {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n      functionName: \"_system/cli/tableData\",\n      componentPath: undefined,\n      args: {\n        table: args.tableName,\n        order: args.order,\n        paginationOpts: {\n          numItems: args.limit ?? 100,\n          cursor: args.cursor ?? null,\n        },\n      },\n    })) as unknown as PaginationResult<any>;\n    return {\n      page: paginationResult.page,\n      isDone: paginationResult.isDone,\n      continueCursor: paginationResult.continueCursor,\n    };\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAClB,iBAA+B;AAG/B,iBAAkD;AAClD,iCAAuC;AAEvC,MAAM,cAAc,aAAE,OAAO;AAAA,EAC3B,oBAAoB,aACjB,OAAO,EACP,SAAS,+DAA+D;AAAA,EAC3E,WAAW,aAAE,OAAO,EAAE,SAAS,qCAAqC;AAAA,EACpE,OAAO,aAAE,KAAK,CAAC,OAAO,MAAM,CAAC,EAAE,SAAS,mCAAmC;AAAA,EAC3E,QAAQ,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS,mCAAmC;AAAA,EAC1E,OAAO,aACJ,OAAO,EACP,IAAI,GAAI,EACR,SAAS,EACT,SAAS,2DAA2D;AACzE,CAAC;AAED,MAAM,eAAe,aAAE,OAAO;AAAA,EAC5B,MAAM,aAAE,MAAM,aAAE,IAAI,CAAC;AAAA,EACrB,QAAQ,aAAE,QAAQ;AAAA,EAClB,gBAAgB,aAAE,OAAO;AAC3B,CAAC;AAED,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,KAAK;AAEA,MAAM,WAAgE;AAAA,EAC3E,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,UAAM,mDAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,UAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,mBAAoB,UAAM,2BAAe,KAAK;AAAA,MAClD,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,MACtB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK;AAAA,QACZ,gBAAgB;AAAA,UACd,UAAU,KAAK,SAAS;AAAA,UACxB,QAAQ,KAAK,UAAU;AAAA,QACzB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,MAAM,iBAAiB;AAAA,MACvB,QAAQ,iBAAiB;AAAA,MACzB,gBAAgB,iBAAiB;AAAA,IACnC;AAAA,EACF;AACF;", "names": []}