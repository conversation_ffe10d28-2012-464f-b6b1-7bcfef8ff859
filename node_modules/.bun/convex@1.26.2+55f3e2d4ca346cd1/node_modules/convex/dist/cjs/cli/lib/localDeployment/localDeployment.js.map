{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/localDeployment.ts"], "sourcesContent": ["import { Context } from \"../../../bundler/context.js\";\nimport { logVerbose } from \"../../../bundler/log.js\";\nimport {\n  bigBrainPause,\n  bigBrainRecordActivity,\n  bigBrainStart,\n} from \"./bigBrain.js\";\nimport {\n  LocalDeploymentConfig,\n  loadDeploymentConfig,\n  rootDeploymentStateDir,\n  saveDeploymentConfig,\n} from \"./filePaths.js\";\nimport {\n  ensureBackendRunning,\n  ensureBackendStopped,\n  localDeploymentUrl,\n  runLocalBackend,\n} from \"./run.js\";\nimport { handlePotentialUpgrade } from \"./upgrade.js\";\nimport { OnDeploymentActivityFunc } from \"../deployment.js\";\nimport { promptSearch } from \"../utils/prompts.js\";\nimport { LocalDeploymentError, printLocalDeploymentOnError } from \"./errors.js\";\nimport {\n  choosePorts,\n  printLocalDeploymentWelcomeMessage,\n  isOffline,\n  LOCAL_BACKEND_INSTANCE_SECRET,\n} from \"./utils.js\";\nimport { ensureBackendBinaryDownloaded } from \"./download.js\";\nexport type DeploymentDetails = {\n  deploymentName: string;\n  deploymentUrl: string;\n  adminKey: string;\n  onActivity: OnDeploymentActivityFunc;\n};\n\nexport async function handleLocalDeployment(\n  ctx: Context,\n  options: {\n    teamSlug: string;\n    projectSlug: string;\n    ports?: {\n      cloud: number;\n      site: number;\n    };\n    backendVersion?: string;\n    forceUpgrade: boolean;\n  },\n): Promise<DeploymentDetails> {\n  if (await isOffline()) {\n    return handleOffline(ctx, options);\n  }\n\n  const existingDeploymentForProject = await getExistingDeployment(ctx, {\n    projectSlug: options.projectSlug,\n    teamSlug: options.teamSlug,\n  });\n  if (existingDeploymentForProject === null) {\n    printLocalDeploymentWelcomeMessage();\n  }\n  ctx.registerCleanup(async (_exitCode, err) => {\n    if (err instanceof LocalDeploymentError) {\n      printLocalDeploymentOnError();\n    }\n  });\n  if (existingDeploymentForProject !== null) {\n    logVerbose(`Found existing deployment for project ${options.projectSlug}`);\n    // If it's still running for some reason, exit and tell the user to kill it.\n    // It's fine if a different backend is running on these ports though since we'll\n    // pick new ones.\n    await ensureBackendStopped(ctx, {\n      ports: {\n        cloud: existingDeploymentForProject.config.ports.cloud,\n      },\n      maxTimeSecs: 5,\n      deploymentName: existingDeploymentForProject.deploymentName,\n      allowOtherDeployments: true,\n    });\n  }\n\n  const { binaryPath, version } = await ensureBackendBinaryDownloaded(\n    ctx,\n    options.backendVersion === undefined\n      ? {\n          kind: \"latest\",\n          allowedVersion: existingDeploymentForProject?.config.backendVersion,\n        }\n      : { kind: \"version\", version: options.backendVersion },\n  );\n  const [cloudPort, sitePort] = await choosePorts(ctx, {\n    count: 2,\n    startPort: 3210,\n    requestedPorts: [options.ports?.cloud ?? null, options.ports?.site ?? null],\n  });\n  const { deploymentName, adminKey } = await bigBrainStart(ctx, {\n    port: cloudPort,\n    projectSlug: options.projectSlug,\n    teamSlug: options.teamSlug,\n    instanceName: existingDeploymentForProject?.deploymentName ?? null,\n  });\n  const onActivity = async (isOffline: boolean, _wasOffline: boolean) => {\n    await ensureBackendRunning(ctx, {\n      cloudPort,\n      deploymentName,\n      maxTimeSecs: 5,\n    });\n    if (isOffline) {\n      return;\n    }\n    await bigBrainRecordActivity(ctx, {\n      instanceName: deploymentName,\n    });\n  };\n\n  const { cleanupHandle } = await handlePotentialUpgrade(ctx, {\n    deploymentKind: \"local\",\n    deploymentName,\n    oldVersion: existingDeploymentForProject?.config.backendVersion ?? null,\n    newBinaryPath: binaryPath,\n    newVersion: version,\n    ports: { cloud: cloudPort, site: sitePort },\n    adminKey,\n    instanceSecret: LOCAL_BACKEND_INSTANCE_SECRET,\n    forceUpgrade: options.forceUpgrade,\n  });\n\n  const cleanupFunc = ctx.removeCleanup(cleanupHandle);\n  ctx.registerCleanup(async (exitCode, err) => {\n    if (cleanupFunc !== null) {\n      await cleanupFunc(exitCode, err);\n    }\n    await bigBrainPause(ctx, {\n      projectSlug: options.projectSlug,\n      teamSlug: options.teamSlug,\n    });\n  });\n\n  return {\n    adminKey,\n    deploymentName,\n    deploymentUrl: localDeploymentUrl(cloudPort),\n    onActivity,\n  };\n}\n\nexport async function loadLocalDeploymentCredentials(\n  ctx: Context,\n  deploymentName: string,\n): Promise<{\n  deploymentName: string;\n  deploymentUrl: string;\n  adminKey: string;\n}> {\n  const config = loadDeploymentConfig(ctx, \"local\", deploymentName);\n  if (config === null) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Failed to load deployment config\",\n    });\n  }\n  return {\n    deploymentName,\n    deploymentUrl: localDeploymentUrl(config.ports.cloud),\n    adminKey: config.adminKey,\n  };\n}\n\nasync function handleOffline(\n  ctx: Context,\n  options: {\n    teamSlug: string;\n    projectSlug: string;\n    ports?: { cloud: number; site: number };\n  },\n): Promise<DeploymentDetails> {\n  const { deploymentName, config } =\n    await chooseFromExistingLocalDeployments(ctx);\n  const { binaryPath } = await ensureBackendBinaryDownloaded(ctx, {\n    kind: \"version\",\n    version: config.backendVersion,\n  });\n  const [cloudPort, sitePort] = await choosePorts(ctx, {\n    count: 2,\n    startPort: 3210,\n    requestedPorts: [options.ports?.cloud ?? null, options.ports?.site ?? null],\n  });\n  saveDeploymentConfig(ctx, \"local\", deploymentName, config);\n  await runLocalBackend(ctx, {\n    binaryPath,\n    ports: { cloud: cloudPort, site: sitePort },\n    deploymentName,\n    deploymentKind: \"local\",\n    instanceSecret: LOCAL_BACKEND_INSTANCE_SECRET,\n    isLatestVersion: false,\n  });\n  return {\n    adminKey: config.adminKey,\n    deploymentName,\n    deploymentUrl: localDeploymentUrl(cloudPort),\n    onActivity: async (isOffline: boolean, wasOffline: boolean) => {\n      await ensureBackendRunning(ctx, {\n        cloudPort,\n        deploymentName,\n        maxTimeSecs: 5,\n      });\n      if (isOffline) {\n        return;\n      }\n      if (wasOffline) {\n        await bigBrainStart(ctx, {\n          port: cloudPort,\n          projectSlug: options.projectSlug,\n          teamSlug: options.teamSlug,\n          instanceName: deploymentName,\n        });\n      }\n      await bigBrainRecordActivity(ctx, {\n        instanceName: deploymentName,\n      });\n    },\n  };\n}\n\nasync function getExistingDeployment(\n  ctx: Context,\n  options: {\n    projectSlug: string;\n    teamSlug: string;\n  },\n): Promise<{ deploymentName: string; config: LocalDeploymentConfig } | null> {\n  const { projectSlug, teamSlug } = options;\n  const prefix = `local-${teamSlug.replace(/-/g, \"_\")}-${projectSlug.replace(/-/g, \"_\")}`;\n  const localDeployments = await getLocalDeployments(ctx);\n  const existingDeploymentForProject = localDeployments.find((d) =>\n    d.deploymentName.startsWith(prefix),\n  );\n  if (existingDeploymentForProject === undefined) {\n    return null;\n  }\n  return {\n    deploymentName: existingDeploymentForProject.deploymentName,\n    config: existingDeploymentForProject.config,\n  };\n}\n\nasync function getLocalDeployments(ctx: Context): Promise<\n  Array<{\n    deploymentName: string;\n    config: LocalDeploymentConfig;\n  }>\n> {\n  const dir = rootDeploymentStateDir(\"local\");\n  if (!ctx.fs.exists(dir)) {\n    return [];\n  }\n  const deploymentNames = ctx.fs\n    .listDir(dir)\n    .map((d) => d.name)\n    .filter((d) => d.startsWith(\"local-\"));\n  return deploymentNames.flatMap((deploymentName) => {\n    const config = loadDeploymentConfig(ctx, \"local\", deploymentName);\n    if (config !== null) {\n      return [{ deploymentName, config }];\n    }\n    return [];\n  });\n}\n\nasync function chooseFromExistingLocalDeployments(ctx: Context): Promise<{\n  deploymentName: string;\n  config: LocalDeploymentConfig;\n}> {\n  const localDeployments = await getLocalDeployments(ctx);\n  return promptSearch(ctx, {\n    message: \"Choose from an existing local deployment?\",\n    choices: localDeployments.map((d) => ({\n      name: d.deploymentName,\n      value: d,\n    })),\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,iBAA2B;AAC3B,sBAIO;AACP,uBAKO;AACP,iBAKO;AACP,qBAAuC;AAEvC,qBAA6B;AAC7B,oBAAkE;AAClE,mBAKO;AACP,sBAA8C;AAQ9C,eAAsB,sBACpB,KACA,SAU4B;AAC5B,MAAI,UAAM,wBAAU,GAAG;AACrB,WAAO,cAAc,KAAK,OAAO;AAAA,EACnC;AAEA,QAAM,+BAA+B,MAAM,sBAAsB,KAAK;AAAA,IACpE,aAAa,QAAQ;AAAA,IACrB,UAAU,QAAQ;AAAA,EACpB,CAAC;AACD,MAAI,iCAAiC,MAAM;AACzC,yDAAmC;AAAA,EACrC;AACA,MAAI,gBAAgB,OAAO,WAAW,QAAQ;AAC5C,QAAI,eAAe,oCAAsB;AACvC,qDAA4B;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,MAAI,iCAAiC,MAAM;AACzC,+BAAW,yCAAyC,QAAQ,WAAW,EAAE;AAIzE,cAAM,iCAAqB,KAAK;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO,6BAA6B,OAAO,MAAM;AAAA,MACnD;AAAA,MACA,aAAa;AAAA,MACb,gBAAgB,6BAA6B;AAAA,MAC7C,uBAAuB;AAAA,IACzB,CAAC;AAAA,EACH;AAEA,QAAM,EAAE,YAAY,QAAQ,IAAI,UAAM;AAAA,IACpC;AAAA,IACA,QAAQ,mBAAmB,SACvB;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB,8BAA8B,OAAO;AAAA,IACvD,IACA,EAAE,MAAM,WAAW,SAAS,QAAQ,eAAe;AAAA,EACzD;AACA,QAAM,CAAC,WAAW,QAAQ,IAAI,UAAM,0BAAY,KAAK;AAAA,IACnD,OAAO;AAAA,IACP,WAAW;AAAA,IACX,gBAAgB,CAAC,QAAQ,OAAO,SAAS,MAAM,QAAQ,OAAO,QAAQ,IAAI;AAAA,EAC5E,CAAC;AACD,QAAM,EAAE,gBAAgB,SAAS,IAAI,UAAM,+BAAc,KAAK;AAAA,IAC5D,MAAM;AAAA,IACN,aAAa,QAAQ;AAAA,IACrB,UAAU,QAAQ;AAAA,IAClB,cAAc,8BAA8B,kBAAkB;AAAA,EAChE,CAAC;AACD,QAAM,aAAa,OAAOA,YAAoB,gBAAyB;AACrE,cAAM,iCAAqB,KAAK;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AACD,QAAIA,YAAW;AACb;AAAA,IACF;AACA,cAAM,wCAAuB,KAAK;AAAA,MAChC,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,QAAM,EAAE,cAAc,IAAI,UAAM,uCAAuB,KAAK;AAAA,IAC1D,gBAAgB;AAAA,IAChB;AAAA,IACA,YAAY,8BAA8B,OAAO,kBAAkB;AAAA,IACnE,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,OAAO,EAAE,OAAO,WAAW,MAAM,SAAS;AAAA,IAC1C;AAAA,IACA,gBAAgB;AAAA,IAChB,cAAc,QAAQ;AAAA,EACxB,CAAC;AAED,QAAM,cAAc,IAAI,cAAc,aAAa;AACnD,MAAI,gBAAgB,OAAO,UAAU,QAAQ;AAC3C,QAAI,gBAAgB,MAAM;AACxB,YAAM,YAAY,UAAU,GAAG;AAAA,IACjC;AACA,cAAM,+BAAc,KAAK;AAAA,MACvB,aAAa,QAAQ;AAAA,MACrB,UAAU,QAAQ;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,mBAAe,+BAAmB,SAAS;AAAA,IAC3C;AAAA,EACF;AACF;AAEA,eAAsB,+BACpB,KACA,gBAKC;AACD,QAAM,aAAS,uCAAqB,KAAK,SAAS,cAAc;AAChE,MAAI,WAAW,MAAM;AACnB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA,mBAAe,+BAAmB,OAAO,MAAM,KAAK;AAAA,IACpD,UAAU,OAAO;AAAA,EACnB;AACF;AAEA,eAAe,cACb,KACA,SAK4B;AAC5B,QAAM,EAAE,gBAAgB,OAAO,IAC7B,MAAM,mCAAmC,GAAG;AAC9C,QAAM,EAAE,WAAW,IAAI,UAAM,+CAA8B,KAAK;AAAA,IAC9D,MAAM;AAAA,IACN,SAAS,OAAO;AAAA,EAClB,CAAC;AACD,QAAM,CAAC,WAAW,QAAQ,IAAI,UAAM,0BAAY,KAAK;AAAA,IACnD,OAAO;AAAA,IACP,WAAW;AAAA,IACX,gBAAgB,CAAC,QAAQ,OAAO,SAAS,MAAM,QAAQ,OAAO,QAAQ,IAAI;AAAA,EAC5E,CAAC;AACD,6CAAqB,KAAK,SAAS,gBAAgB,MAAM;AACzD,YAAM,4BAAgB,KAAK;AAAA,IACzB;AAAA,IACA,OAAO,EAAE,OAAO,WAAW,MAAM,SAAS;AAAA,IAC1C;AAAA,IACA,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,EACnB,CAAC;AACD,SAAO;AAAA,IACL,UAAU,OAAO;AAAA,IACjB;AAAA,IACA,mBAAe,+BAAmB,SAAS;AAAA,IAC3C,YAAY,OAAOA,YAAoB,eAAwB;AAC7D,gBAAM,iCAAqB,KAAK;AAAA,QAC9B;AAAA,QACA;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AACD,UAAIA,YAAW;AACb;AAAA,MACF;AACA,UAAI,YAAY;AACd,kBAAM,+BAAc,KAAK;AAAA,UACvB,MAAM;AAAA,UACN,aAAa,QAAQ;AAAA,UACrB,UAAU,QAAQ;AAAA,UAClB,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AACA,gBAAM,wCAAuB,KAAK;AAAA,QAChC,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,eAAe,sBACb,KACA,SAI2E;AAC3E,QAAM,EAAE,aAAa,SAAS,IAAI;AAClC,QAAM,SAAS,SAAS,SAAS,QAAQ,MAAM,GAAG,CAAC,IAAI,YAAY,QAAQ,MAAM,GAAG,CAAC;AACrF,QAAM,mBAAmB,MAAM,oBAAoB,GAAG;AACtD,QAAM,+BAA+B,iBAAiB;AAAA,IAAK,CAAC,MAC1D,EAAE,eAAe,WAAW,MAAM;AAAA,EACpC;AACA,MAAI,iCAAiC,QAAW;AAC9C,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,gBAAgB,6BAA6B;AAAA,IAC7C,QAAQ,6BAA6B;AAAA,EACvC;AACF;AAEA,eAAe,oBAAoB,KAKjC;AACA,QAAM,UAAM,yCAAuB,OAAO;AAC1C,MAAI,CAAC,IAAI,GAAG,OAAO,GAAG,GAAG;AACvB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,kBAAkB,IAAI,GACzB,QAAQ,GAAG,EACX,IAAI,CAAC,MAAM,EAAE,IAAI,EACjB,OAAO,CAAC,MAAM,EAAE,WAAW,QAAQ,CAAC;AACvC,SAAO,gBAAgB,QAAQ,CAAC,mBAAmB;AACjD,UAAM,aAAS,uCAAqB,KAAK,SAAS,cAAc;AAChE,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC,EAAE,gBAAgB,OAAO,CAAC;AAAA,IACpC;AACA,WAAO,CAAC;AAAA,EACV,CAAC;AACH;AAEA,eAAe,mCAAmC,KAG/C;AACD,QAAM,mBAAmB,MAAM,oBAAoB,GAAG;AACtD,aAAO,6BAAa,KAAK;AAAA,IACvB,SAAS;AAAA,IACT,SAAS,iBAAiB,IAAI,CAAC,OAAO;AAAA,MACpC,MAAM,EAAE;AAAA,MACR,OAAO;AAAA,IACT,EAAE;AAAA,EACJ,CAAC;AACH;", "names": ["isOffline"]}