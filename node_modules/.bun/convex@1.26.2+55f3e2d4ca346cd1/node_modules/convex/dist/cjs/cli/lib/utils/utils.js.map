{"version": 3, "sources": ["../../../../../src/cli/lib/utils/utils.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport os from \"os\";\nimport path from \"path\";\n\nimport { ProjectConfig } from \"../config.js\";\n\nimport { spawn } from \"child_process\";\nimport { InvalidArgumentError } from \"commander\";\nimport fetchRetryFactory, { RequestInitRetryParams } from \"fetch-retry\";\nimport { Context, ErrorType } from \"../../../bundler/context.js\";\nimport {\n  failExistingSpinner,\n  logError,\n  logMessage,\n  logWarning,\n} from \"../../../bundler/log.js\";\nimport { version } from \"../../version.js\";\nimport { Project } from \"../api.js\";\nimport { promptOptions, promptSearch, promptYesNo } from \"./prompts.js\";\nimport {\n  bigBrainEnableFeatureMetadata,\n  projectHasExistingCloudDev,\n} from \"../localDeployment/bigBrain.js\";\nimport type { paths as ManagementPaths } from \"../../generatedApi.js\";\nimport createClient from \"openapi-fetch\";\n\nconst retryingFetch = fetchRetryFactory(fetch);\n\nexport const productionProvisionHost = \"https://api.convex.dev\";\nexport const provisionHost =\n  process.env.CONVEX_PROVISION_HOST || productionProvisionHost;\nconst BIG_BRAIN_URL = `${provisionHost}/api/`;\nexport const ENV_VAR_FILE_PATH = \".env.local\";\nexport const CONVEX_DEPLOY_KEY_ENV_VAR_NAME = \"CONVEX_DEPLOY_KEY\";\nexport const CONVEX_DEPLOYMENT_ENV_VAR_NAME = \"CONVEX_DEPLOYMENT\";\nexport const CONVEX_SELF_HOSTED_URL_VAR_NAME = \"CONVEX_SELF_HOSTED_URL\";\nexport const CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME =\n  \"CONVEX_SELF_HOSTED_ADMIN_KEY\";\nconst MAX_RETRIES = 6;\n// After 3 retries, log a progress message that we're retrying the request\nconst RETRY_LOG_THRESHOLD = 3;\n\nexport function parsePositiveInteger(value: string) {\n  const parsedValue = parseInteger(value);\n  if (parsedValue <= 0) {\n    // eslint-disable-next-line no-restricted-syntax\n    throw new InvalidArgumentError(\"Not a positive number.\");\n  }\n  return parsedValue;\n}\n\nexport function parseInteger(value: string) {\n  const parsedValue = +value;\n  if (isNaN(parsedValue)) {\n    // eslint-disable-next-line no-restricted-syntax\n    throw new InvalidArgumentError(\"Not a number.\");\n  }\n  return parsedValue;\n}\n\nexport type ErrorData = {\n  code: string;\n  message: string;\n};\n\n/**\n * Error thrown on non-2XX reponse codes to make most `fetch()` error handling\n * follow a single code path.\n */\nexport class ThrowingFetchError extends Error {\n  response: Response;\n  serverErrorData?: ErrorData;\n\n  constructor(\n    msg: string,\n    {\n      code,\n      message,\n      response,\n    }: { cause?: Error; code?: string; message?: string; response: Response },\n  ) {\n    if (code !== undefined && message !== undefined) {\n      super(`${msg}: ${code}: ${message}`);\n      this.serverErrorData = { code, message };\n    } else {\n      super(msg);\n    }\n\n    Object.setPrototypeOf(this, ThrowingFetchError.prototype);\n\n    this.response = response;\n  }\n\n  public static async fromResponse(\n    response: Response,\n    msg?: string,\n  ): Promise<ThrowingFetchError> {\n    msg = `${msg ? `${msg} ` : \"\"}${response.status} ${response.statusText}`;\n    let code, message;\n    try {\n      ({ code, message } = await response.json());\n    } catch {\n      // Do nothing because the non-2XX response code is the primary error here.\n    }\n    return new ThrowingFetchError(msg, { code, message, response });\n  }\n\n  async handle(ctx: Context): Promise<never> {\n    let error_type: ErrorType = \"transient\";\n    await checkFetchErrorForDeprecation(ctx, this.response);\n\n    let msg = this.message;\n\n    if (this.response.status === 400) {\n      error_type = \"invalid filesystem or env vars\";\n    } else if (this.response.status === 401) {\n      error_type = \"fatal\";\n      msg = `${msg}\\nAuthenticate with \\`npx convex dev\\``;\n    } else if (this.response.status === 404) {\n      error_type = \"fatal\";\n      msg = `${msg}: ${this.response.url}`;\n    }\n\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: error_type,\n      errForSentry: this,\n      printedMessage: chalk.red(msg.trim()),\n    });\n  }\n}\n\n/**\n * Thin wrapper around `fetch()` which throws a FetchDataError on non-2XX\n * responses which includes error code and message from the response JSON.\n * (Axios-style)\n *\n * It also accepts retry options from fetch-retry.\n */\nexport async function throwingFetch(\n  resource: RequestInfo | URL,\n  options: (RequestInit & RequestInitRetryParams<typeof fetch>) | undefined,\n): Promise<Response> {\n  const Headers = globalThis.Headers;\n  const headers = new Headers((options || {})[\"headers\"]);\n  if (options?.body) {\n    if (!headers.has(\"Content-Type\")) {\n      headers.set(\"Content-Type\", \"application/json\");\n    }\n  }\n  const response = await retryingFetch(resource, options);\n  if (!response.ok) {\n    // This error must always be handled manually.\n    // eslint-disable-next-line no-restricted-syntax\n    throw await ThrowingFetchError.fromResponse(\n      response,\n      `Error fetching ${options?.method ? options.method + \" \" : \"\"} ${\n        typeof resource === \"string\"\n          ? resource\n          : \"url\" in resource\n            ? resource.url\n            : resource.toString()\n      }`,\n    );\n  }\n  return response;\n}\n\n/**\n * Handle an error a fetch error or non-2xx response.\n */\nexport async function logAndHandleFetchError(\n  ctx: Context,\n  err: unknown,\n): Promise<never> {\n  // Fail the spinner so the stderr lines appear\n  failExistingSpinner();\n\n  if (err instanceof ThrowingFetchError) {\n    return await err.handle(ctx);\n  } else {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      errForSentry: err,\n      printedMessage: chalk.red(err),\n    });\n  }\n}\n\nfunction logDeprecationWarning(ctx: Context, deprecationMessage: string) {\n  if (ctx.deprecationMessagePrinted) {\n    return;\n  }\n  ctx.deprecationMessagePrinted = true;\n  logWarning(chalk.yellow(deprecationMessage));\n}\n\nasync function checkFetchErrorForDeprecation(ctx: Context, resp: Response) {\n  const headers = resp.headers;\n  if (headers) {\n    const deprecationState = headers.get(\"x-convex-deprecation-state\");\n    const deprecationMessage = headers.get(\"x-convex-deprecation-message\");\n    switch (deprecationState) {\n      case null:\n        break;\n      case \"Deprecated\":\n        // This version is deprecated. Print a warning and crash.\n\n        // Gotcha:\n        // 1. Don't use `logDeprecationWarning` because we should always print\n        // why this we crashed (even if we printed a warning earlier).\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: chalk.red(deprecationMessage),\n        });\n      default:\n        // The error included a deprecation warning. Print, but handle the\n        // error normally (it was for another reason).\n        logDeprecationWarning(\n          ctx,\n          deprecationMessage || \"(no deprecation message included)\",\n        );\n        break;\n    }\n  }\n}\n\n/// Call this method after a successful API response to conditionally print the\n/// \"please upgrade\" message.\nexport function deprecationCheckWarning(ctx: Context, resp: Response) {\n  const headers = resp.headers;\n  if (headers) {\n    const deprecationState = headers.get(\"x-convex-deprecation-state\");\n    const deprecationMessage = headers.get(\"x-convex-deprecation-message\");\n    switch (deprecationState) {\n      case null:\n        break;\n      case \"Deprecated\":\n        // This should never happen because such states are errors, not warnings.\n        // eslint-disable-next-line no-restricted-syntax\n        throw new Error(\n          \"Called deprecationCheckWarning on a fatal error. This is a bug.\",\n        );\n      default:\n        logDeprecationWarning(\n          ctx,\n          deprecationMessage || \"(no deprecation message included)\",\n        );\n        break;\n    }\n  }\n}\n\ntype Team = {\n  id: number;\n  name: string;\n  slug: string;\n};\n\nexport async function hasTeam(ctx: Context, teamSlug: string) {\n  const teams: Team[] = await bigBrainAPI({ ctx, method: \"GET\", url: \"teams\" });\n  return teams.some((team) => team.slug === teamSlug);\n}\n\nexport async function validateOrSelectTeam(\n  ctx: Context,\n  teamSlug: string | undefined,\n  promptMessage: string,\n): Promise<{ teamSlug: string; chosen: boolean }> {\n  const teams: Team[] = await bigBrainAPI({ ctx, method: \"GET\", url: \"teams\" });\n  if (teams.length === 0) {\n    await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      errForSentry: \"No teams found\",\n      printedMessage: chalk.red(\"Error: No teams found\"),\n    });\n  }\n  if (!teamSlug) {\n    // Prompt the user to select if they belong to more than one team.\n    switch (teams.length) {\n      case 1:\n        return { teamSlug: teams[0].slug, chosen: false };\n      default:\n        return {\n          teamSlug: await promptSearch(ctx, {\n            message: promptMessage,\n            choices: teams.map((team: Team) => ({\n              name: `${team.name} (${team.slug})`,\n              value: team.slug,\n            })),\n          }),\n          chosen: true,\n        };\n    }\n  } else {\n    // Validate the chosen team.\n    if (!teams.find((team) => team.slug === teamSlug)) {\n      await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Error: Team ${teamSlug} not found, fix the --team option or remove it`,\n      });\n    }\n    return { teamSlug, chosen: false };\n  }\n}\n\nexport async function selectDevDeploymentType(\n  ctx: Context,\n  {\n    chosenConfiguration,\n    newOrExisting,\n    teamSlug,\n    projectSlug,\n    userHasChosenSomethingInteractively,\n    // from `--configure --dev-deployment local|cloud`\n    devDeploymentFromFlag,\n    // from `--cloud or --local`\n    forceDevDeployment,\n  }:\n    | {\n        chosenConfiguration: \"new\" | \"existing\" | \"ask\" | null;\n        newOrExisting: \"existing\";\n        teamSlug: string;\n        projectSlug: string;\n        userHasChosenSomethingInteractively: boolean;\n        devDeploymentFromFlag: \"cloud\" | \"local\" | undefined;\n        forceDevDeployment: \"cloud\" | \"local\" | undefined;\n      }\n    | {\n        chosenConfiguration: \"new\" | \"existing\" | \"ask\" | null;\n        newOrExisting: \"new\";\n        teamSlug: string;\n        // For new projects we don't know the project slug yet.\n        projectSlug: undefined;\n        userHasChosenSomethingInteractively: boolean;\n        devDeploymentFromFlag: \"cloud\" | \"local\" | undefined;\n        forceDevDeployment: \"cloud\" | \"local\" | undefined;\n      },\n): Promise<{ devDeployment: \"cloud\" | \"local\" }> {\n  if (forceDevDeployment) return { devDeployment: forceDevDeployment };\n  if (devDeploymentFromFlag) return { devDeployment: devDeploymentFromFlag };\n\n  if (newOrExisting === \"existing\" && chosenConfiguration === null) {\n    // Don't suggest local dev if developer already has a cloud deployment.\n    if (await projectHasExistingCloudDev(ctx, { projectSlug, teamSlug })) {\n      // TODO Expand rollout to offer local dev in this case. ENG-8307\n      return { devDeployment: \"cloud\" };\n    }\n  }\n\n  // To avoid breaking previously non-interactive flows, don't prompt if enough\n  // flags were specified for configure not to already have needed input.\n  if (chosenConfiguration !== \"ask\" && !userHasChosenSomethingInteractively) {\n    return { devDeployment: \"cloud\" };\n  }\n\n  // For creating a first project (no projects exist) or joining a first project\n  // (one project exists), always use cloud since it's a smoother experience.\n  const isFirstProject =\n    (await bigBrainEnableFeatureMetadata(ctx)).totalProjects.kind !==\n    \"multiple\";\n  if (isFirstProject) {\n    return { devDeployment: \"cloud\" };\n  }\n\n  // For now default is always cloud.\n  const devDeployment: \"cloud\" | \"local\" = await promptOptions(ctx, {\n    message:\n      \"Use cloud or local dev deployment? For more see https://docs.convex.dev/cli/local-deployments\",\n    default: \"cloud\",\n    choices: [\n      { name: \"cloud deployment\", value: \"cloud\" },\n      { name: \"local deployment (BETA)\", value: \"local\" },\n    ],\n  });\n  return { devDeployment };\n}\n\nexport async function hasProject(\n  ctx: Context,\n  teamSlug: string,\n  projectSlug: string,\n) {\n  try {\n    const projects: Project[] = (\n      await typedBigBrainClient(ctx).GET(\"/teams/{team_slug}/projects\", {\n        params: {\n          path: {\n            team_slug: teamSlug,\n          },\n        },\n      })\n    ).data!;\n    return !!projects.find((project) => project.slug === projectSlug);\n  } catch {\n    return false;\n  }\n}\n\nexport async function hasProjects(ctx: Context) {\n  return !!(await bigBrainAPI({ ctx, method: \"GET\", url: `has_projects` }));\n}\n\nexport async function validateOrSelectProject(\n  ctx: Context,\n  projectSlug: string | undefined,\n  teamSlug: string,\n  singleProjectPrompt: string,\n  multiProjectPrompt: string,\n): Promise<string | null> {\n  const projects: Project[] = (\n    await typedBigBrainClient(ctx).GET(\"/teams/{team_slug}/projects\", {\n      params: {\n        path: {\n          team_slug: teamSlug,\n        },\n      },\n    })\n  ).data!;\n  if (projects.length === 0) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `No existing projects! Run this command again and choose \"create a new project.\"`,\n    });\n  }\n  if (!projectSlug) {\n    const nonDemoProjects = projects.filter((project) => !project.isDemo);\n    if (nonDemoProjects.length === 0) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `No existing non-demo projects! Run this command again and choose \"create a new project.\"`,\n      });\n    }\n    // Prompt the user to select project.\n    switch (nonDemoProjects.length) {\n      case 1: {\n        const project = nonDemoProjects[0];\n        const confirmed = await promptYesNo(ctx, {\n          message: `${singleProjectPrompt} ${project.name} (${project.slug})?`,\n        });\n\n        if (!confirmed) {\n          return null;\n        }\n        return nonDemoProjects[0].slug;\n      }\n      default:\n        return await promptSearch(ctx, {\n          message: multiProjectPrompt,\n          choices: nonDemoProjects.map((project: Project) => ({\n            name: `${project.name} (${project.slug})`,\n            value: project.slug,\n          })),\n        });\n    }\n  } else {\n    // Validate the chosen project.\n    if (!projects.find((project) => project.slug === projectSlug)) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Error: Project ${projectSlug} not found, fix the --project option or remove it`,\n      });\n    }\n    return projectSlug;\n  }\n}\n\n/**\n * @param ctx\n * @returns a Record of dependency name to dependency version for dependencies\n * and devDependencies\n */\nexport async function loadPackageJson(\n  ctx: Context,\n  includePeerDeps = false,\n): Promise<Record<string, string>> {\n  let packageJson;\n  try {\n    packageJson = ctx.fs.readUtf8File(\"package.json\");\n  } catch (err) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `Unable to read your package.json: ${\n        err as any\n      }. Make sure you're running this command from the root directory of a Convex app that contains the package.json`,\n    });\n  }\n  let obj;\n  try {\n    obj = JSON.parse(packageJson);\n  } catch (err) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      errForSentry: err,\n      printedMessage: `Unable to parse package.json: ${err as any}`,\n    });\n  }\n  if (typeof obj !== \"object\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: \"Expected to parse an object from package.json\",\n    });\n  }\n  const packages = {\n    ...(includePeerDeps ? (obj.peerDependencies ?? {}) : {}),\n    ...(obj.dependencies ?? {}),\n    ...(obj.devDependencies ?? {}),\n  };\n  return packages;\n}\n\nexport async function ensureHasConvexDependency(ctx: Context, cmd: string) {\n  const packages = await loadPackageJson(ctx, true);\n  const hasConvexDependency = \"convex\" in packages;\n  if (!hasConvexDependency) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `In order to ${cmd}, add \\`convex\\` to your package.json dependencies.`,\n    });\n  }\n}\n\n/** Return a new array with elements of the passed in array sorted by a key lambda */\nexport const sorted = <T>(arr: T[], key: (el: T) => any): T[] => {\n  const newArr = [...arr];\n  const cmp = (a: T, b: T) => {\n    if (key(a) < key(b)) return -1;\n    if (key(a) > key(b)) return 1;\n    return 0;\n  };\n  return newArr.sort(cmp);\n};\n\nexport function functionsDir(\n  configPath: string,\n  projectConfig: ProjectConfig,\n): string {\n  return path.join(path.dirname(configPath), projectConfig.functions);\n}\n\nfunction convexName() {\n  // Use a different directory for config files generated for tests\n  if (process.env.CONVEX_PROVISION_HOST) {\n    const port = process.env.CONVEX_PROVISION_HOST.split(\":\")[2];\n    if (port === undefined || port === \"8050\") {\n      return `convex-test`;\n    } else {\n      return `convex-test-${port}`;\n    }\n  }\n  return \"convex\";\n}\n\nexport function rootDirectory(): string {\n  return path.join(os.homedir(), `.${convexName()}`);\n}\n\nexport function cacheDir() {\n  const name = convexName();\n  const platform = process.platform;\n  if (platform === \"win32\") {\n    // On Windows, `LOCALAPPDATA` is usually set, but fall back to\n    // `USERPROFILE` if not, and fall back to homedir if all else fails.\n    if (process.env.LOCALAPPDATA) {\n      return path.join(process.env.LOCALAPPDATA, name);\n    }\n    if (process.env.USERPROFILE) {\n      return path.join(process.env.USERPROFILE, \"AppData\", \"Local\", name);\n    }\n    return path.join(os.homedir(), \"AppData\", \"Local\", name);\n  }\n  return path.join(os.homedir(), \".cache\", name);\n}\n\n/**\n * Fetch with appropriate headers for the Convex Management API.\n *\n * This fetch() also has retries and throws if the response is not ok.\n */\nexport async function bigBrainFetch(ctx: Context): Promise<typeof fetch> {\n  const authHeader = ctx.bigBrainAuth()?.header;\n  const bigBrainHeaders: Record<string, string> = authHeader\n    ? {\n        Authorization: authHeader,\n        \"Convex-Client\": `npm-cli-${version}`,\n      }\n    : {\n        \"Convex-Client\": `npm-cli-${version}`,\n      };\n  return (resource: RequestInfo | URL, options: RequestInit | undefined) => {\n    const { headers: optionsHeaders, ...rest } = options || {};\n    const headers = {\n      ...bigBrainHeaders,\n      ...(optionsHeaders || {}),\n    };\n    const opts = {\n      retries: MAX_RETRIES,\n      retryDelay,\n      headers,\n      ...rest,\n    };\n\n    const url =\n      resource instanceof URL\n        ? resource.pathname\n        : typeof resource === \"string\"\n          ? new URL(resource, BIG_BRAIN_URL)\n          : new URL(resource.url, BIG_BRAIN_URL);\n    return throwingFetch(url, opts);\n  };\n}\n\nexport async function bigBrainAPI<T = any>({\n  ctx,\n  method,\n  url,\n  data,\n}: {\n  ctx: Context;\n  method: \"GET\" | \"POST\" | \"HEAD\";\n  url: string;\n  data?: any;\n}): Promise<T> {\n  const dataString =\n    data === undefined\n      ? undefined\n      : typeof data === \"string\"\n        ? data\n        : JSON.stringify(data);\n  try {\n    return await bigBrainAPIMaybeThrows({\n      ctx,\n      method,\n      url,\n      data: dataString,\n    });\n  } catch (err: unknown) {\n    return await logAndHandleFetchError(ctx, err);\n  }\n}\n\n/**\n * Typed API client with a fetch() implemention that includes retries and crashes on errors.\n * It is always safe to call `.data!` on the response: any error would throw or crash.\n *\n * Pass { throw: true } to throw ThrowingFetchErrors instead of exiting the process.\n */\nexport function typedBigBrainClient(\n  ctx: Context,\n  options: { throw?: boolean } = {},\n) {\n  const bigBrainClient = createClient<ManagementPaths>({\n    baseUrl: BIG_BRAIN_URL,\n    fetch: async (\n      resource: Request,\n      options?: RequestInit,\n    ): Promise<Response> => {\n      const fetch = await bigBrainFetch(ctx);\n      return fetch(resource, options);\n    },\n  });\n\n  // Wrap the client with error handling - go back to proxy since middleware doesn't catch parsing errors\n  return new Proxy(bigBrainClient, {\n    get(target, prop) {\n      const originalMethod = target[prop as keyof typeof target];\n\n      if (\n        prop === \"GET\" ||\n        prop === \"POST\" ||\n        prop === \"HEAD\" ||\n        prop === \"OPTIONS\" ||\n        prop === \"PUT\" ||\n        prop === \"DELETE\" ||\n        prop === \"PATCH\" ||\n        prop === \"TRACE\"\n      ) {\n        return async (...args: any[]) => {\n          try {\n            return await (originalMethod as Function).apply(target, args);\n          } catch (err: unknown) {\n            if (options.throw) {\n              // eslint-disable-next-line no-restricted-syntax\n              throw err;\n            }\n            return await logAndHandleFetchError(ctx, err);\n          }\n        };\n      }\n\n      return originalMethod;\n    },\n  });\n}\n\nexport async function bigBrainAPIMaybeThrows({\n  ctx,\n  method,\n  url,\n  data,\n}: {\n  ctx: Context;\n  method: \"GET\" | \"POST\" | \"HEAD\";\n  url: string;\n  data?: any;\n}): Promise<any> {\n  const fetch = await bigBrainFetch(ctx);\n  const dataString =\n    data === undefined\n      ? method === \"POST\"\n        ? JSON.stringify({})\n        : undefined\n      : typeof data === \"string\"\n        ? data\n        : JSON.stringify(data);\n  const res = await fetch(url, {\n    method,\n    ...(dataString ? { body: dataString } : {}),\n    headers:\n      method === \"POST\"\n        ? {\n            \"Content-Type\": \"application/json\",\n          }\n        : {},\n  });\n  deprecationCheckWarning(ctx, res);\n  if (res.status === 200) {\n    return await res.json();\n  }\n}\n\n/**\n * Polls an arbitrary function until a condition is met.\n *\n * @param fetch Function performing a fetch, returning resulting data.\n * @param condition This function will terminate polling when it returns `true`.\n * @param waitMs How long to wait in between fetches.\n * @returns The resulting data from `fetch`.\n */\nexport const poll = async function <Result>(\n  fetch: () => Promise<Result>,\n  condition: (data: Result) => boolean,\n  waitMs = 1000,\n) {\n  let result = await fetch();\n  while (!condition(result)) {\n    await wait(waitMs);\n    result = await fetch();\n  }\n  return result;\n};\n\nconst wait = function (waitMs: number) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, waitMs);\n  });\n};\n\nexport function waitForever() {\n  // This never resolves\n  return new Promise((_) => {\n    // ignore\n  });\n}\n\n// Returns a promise and a function that resolves the promise.\nexport function waitUntilCalled(): [Promise<unknown>, () => void] {\n  let onCalled: (v: unknown) => void;\n  const waitPromise = new Promise((resolve) => (onCalled = resolve));\n  return [waitPromise, () => onCalled(null)];\n}\n\n// We can eventually switch to something like `filesize` for i18n and\n// more robust formatting, but let's keep our CLI bundle small for now.\nexport function formatSize(n: number): string {\n  if (n < 1024) {\n    return `${n} B`;\n  }\n  if (n < 1024 * 1024) {\n    return `${(n / 1024).toFixed(1)} KB`;\n  }\n  if (n < 1024 * 1024 * 1024) {\n    return `${(n / 1024 / 1024).toFixed(1)} MB`;\n  }\n  return `${(n / 1024 / 1024 / 1024).toFixed(2)} GB`;\n}\n\nexport function formatDuration(ms: number): string {\n  const twoDigits = (n: number, unit: string) =>\n    `${n.toLocaleString(\"en-US\", { maximumFractionDigits: 2 })}${unit}`;\n\n  if (ms < 1e-3) {\n    return twoDigits(ms * 1e9, \"ns\");\n  }\n  if (ms < 1) {\n    return twoDigits(ms * 1e3, \"µs\");\n  }\n  if (ms < 1e3) {\n    return twoDigits(ms, \"ms\");\n  }\n  const s = ms / 1e3;\n  if (s < 60) {\n    return twoDigits(ms / 1e3, \"s\");\n  }\n  return twoDigits(s / 60, \"m\");\n}\n\nexport function getCurrentTimeString() {\n  const now = new Date();\n  const hours = String(now.getHours()).padStart(2, \"0\");\n  const minutes = String(now.getMinutes()).padStart(2, \"0\");\n  const seconds = String(now.getSeconds()).padStart(2, \"0\");\n  return `${hours}:${minutes}:${seconds}`;\n}\n\n// We don't allow running commands in project subdirectories yet,\n// but we can provide better errors if we look around.\nexport async function findParentConfigs(ctx: Context): Promise<{\n  parentPackageJson: string;\n  parentConvexJson?: string;\n}> {\n  const parentPackageJson = findUp(ctx, \"package.json\");\n  if (!parentPackageJson) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage:\n        \"No package.json found. To create a new project using Convex, see https://docs.convex.dev/home#quickstarts\",\n    });\n  }\n  const candidateConvexJson =\n    parentPackageJson &&\n    path.join(path.dirname(parentPackageJson), \"convex.json\");\n  const parentConvexJson =\n    candidateConvexJson && ctx.fs.exists(candidateConvexJson)\n      ? candidateConvexJson\n      : undefined;\n  return {\n    parentPackageJson,\n    parentConvexJson,\n  };\n}\n\n/**\n * Finds a file in the current working directory or a parent.\n *\n * @returns The absolute path of the first file found or undefined.\n */\nfunction findUp(ctx: Context, filename: string): string | undefined {\n  let curDir = path.resolve(\".\");\n  let parentDir = curDir;\n  do {\n    const candidate = path.join(curDir, filename);\n    if (ctx.fs.exists(candidate)) {\n      return candidate;\n    }\n    curDir = parentDir;\n    parentDir = path.dirname(curDir);\n  } while (parentDir !== curDir);\n  return;\n}\n\n/**\n * Returns whether there's an existing project config. Throws\n * if this is not a valid directory for a project config.\n */\nexport async function isInExistingProject(ctx: Context) {\n  const { parentPackageJson, parentConvexJson } = await findParentConfigs(ctx);\n  if (parentPackageJson !== path.resolve(\"package.json\")) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: \"Run this command from the root directory of a project.\",\n    });\n  }\n  return !!parentConvexJson;\n}\n\n// `spawnAsync` is the async version of Node's `spawnSync` (and `spawn`).\n//\n// By default, this returns the produced `stdout` and `stderror` and\n// an error if one was encountered (to mirror `spawnSync`).\n//\n// If `stdio` is set to `\"inherit\"`, pipes `stdout` and `stderror` (\n// pausing the spinner if one is running) and rejects the promise\n// on errors (to mirror `execFileSync`).\nexport function spawnAsync(\n  ctx: Context,\n  command: string,\n  args: ReadonlyArray<string>,\n): Promise<{\n  stdout: string;\n  stderr: string;\n  status: null | number;\n  error?: Error | undefined;\n}>;\nexport function spawnAsync(\n  ctx: Context,\n  command: string,\n  args: ReadonlyArray<string>,\n  options: { stdio: \"inherit\"; shell?: boolean },\n): Promise<void>;\nexport function spawnAsync(\n  ctx: Context,\n  command: string,\n  args: ReadonlyArray<string>,\n  options?: { stdio: \"inherit\"; shell?: boolean },\n) {\n  return new Promise((resolve, reject) => {\n    const child = spawn(command, args, { shell: options?.shell });\n    let stdout = \"\";\n    let stderr = \"\";\n\n    const pipeOutput = options?.stdio === \"inherit\";\n\n    if (pipeOutput) {\n      child.stdout.on(\"data\", (text) =>\n        logMessage(text.toString(\"utf-8\").trimEnd()),\n      );\n      child.stderr.on(\"data\", (text) =>\n        logError(text.toString(\"utf-8\").trimEnd()),\n      );\n    } else {\n      child.stdout.on(\"data\", (data) => {\n        stdout += data.toString(\"utf-8\");\n      });\n\n      child.stderr.on(\"data\", (data) => {\n        stderr += data.toString(\"utf-8\");\n      });\n    }\n\n    const completionListener = (code: number | null) => {\n      child.removeListener(\"error\", errorListener);\n      const result = pipeOutput\n        ? { status: code }\n        : { stdout, stderr, status: code };\n      if (code !== 0) {\n        const argumentString =\n          args && args.length > 0 ? ` ${args.join(\" \")}` : \"\";\n        const error = new Error(\n          `\\`${command}${argumentString}\\` exited with non-zero code: ${code}`,\n        );\n        if (pipeOutput) {\n          reject({ ...result, error });\n        } else {\n          resolve({ ...result, error });\n        }\n      } else {\n        resolve(result);\n      }\n    };\n\n    const errorListener = (error: Error) => {\n      child.removeListener(\"exit\", completionListener);\n      child.removeListener(\"close\", completionListener);\n      if (pipeOutput) {\n        reject({ error, status: null });\n      } else {\n        resolve({ error, status: null });\n      }\n    };\n\n    if (pipeOutput) {\n      child.once(\"exit\", completionListener);\n    } else {\n      child.once(\"close\", completionListener);\n    }\n    child.once(\"error\", errorListener);\n  });\n}\n\nconst IDEMPOTENT_METHODS = [\"GET\", \"HEAD\", \"PUT\", \"DELETE\", \"OPTIONS\", \"TRACE\"];\n\nfunction retryDelay(\n  attempt: number,\n  _error: Error | null,\n  _response: Response | null,\n): number {\n  // immediate, 1s delay, 2s delay, 4s delay, etc.\n  const delay = attempt === 0 ? 1 : 2 ** (attempt - 1) * 1000;\n  const randomSum = delay * 0.2 * Math.random();\n  return delay + randomSum;\n}\n\nfunction deploymentFetchRetryOn(\n  onError?: (err: any, attempt: number) => void,\n  method?: string,\n) {\n  const shouldRetry = function (\n    attempt: number,\n    error: Error | null,\n    response: Response | null,\n  ): { kind: \"retry\"; error: any } | { kind: \"stop\" } {\n    // Retry on network errors.\n    if (error !== null) {\n      // TODO filter out all SSL errors\n      // https://github.com/nodejs/node/blob/8a41d9b636be86350cd32847c3f89d327c4f6ff7/src/crypto/crypto_common.cc#L218-L245\n      return { kind: \"retry\", error: error };\n    }\n    // Retry on 404s since these can sometimes happen with newly created\n    // deployments for POSTs.\n    if (response?.status === 404) {\n      return {\n        kind: \"retry\",\n        error: `Received response with status ${response.status}`,\n      };\n    }\n\n    // Whatever the error code it doesn't hurt to retry idempotent requests.\n    if (\n      response &&\n      !response.ok &&\n      method &&\n      IDEMPOTENT_METHODS.includes(method.toUpperCase())\n    ) {\n      // ...but it's a bit annoying to wait for things we know won't succced\n      if (\n        [\n          400, // Bad Request\n          401, // Unauthorized\n          402, // PaymentRequired\n          403, // Forbidden\n          405, // Method Not Allowed\n          406, // Not Acceptable\n          412, // Precondition Failed\n          413, // Payload Too Large\n          414, // URI Too Long\n          415, // Unsupported Media Type\n          416, // Range Not Satisfiable\n        ].includes(response.status)\n      ) {\n        return {\n          kind: \"stop\",\n        };\n      }\n      return {\n        kind: \"retry\",\n        error: `Received response with status ${response.status}`,\n      };\n    }\n\n    return { kind: \"stop\" };\n  };\n\n  return function (\n    attempt: number,\n    error: Error | null,\n    response: Response | null,\n  ) {\n    const result = shouldRetry(attempt, error, response);\n    if (result.kind === \"retry\") {\n      onError?.(result.error, attempt);\n    }\n    if (attempt >= MAX_RETRIES) {\n      // Stop retrying if we've exhausted all retries, but do this after we've\n      // called `onError` so that the caller can still log the error.\n      return false;\n    }\n    return result.kind === \"retry\";\n  };\n}\n\n/**\n * Unlike `deploymentFetch`, this does not add on any headers, so the caller\n * must supply any headers.\n */\nexport function bareDeploymentFetch(\n  ctx: Context,\n  options: {\n    deploymentUrl: string;\n    onError?: (err: any) => void;\n  },\n): typeof throwingFetch {\n  const { deploymentUrl, onError } = options;\n  const onErrorWithAttempt = (err: any, attempt: number) => {\n    onError?.(err);\n    if (attempt >= RETRY_LOG_THRESHOLD) {\n      logMessage(\n        chalk.gray(`Retrying request (attempt ${attempt}/${MAX_RETRIES})...`),\n      );\n    }\n  };\n  return (resource: RequestInfo | URL, options: RequestInit | undefined) => {\n    const url =\n      resource instanceof URL\n        ? resource.pathname\n        : typeof resource === \"string\"\n          ? new URL(resource, deploymentUrl)\n          : new URL(resource.url, deploymentUrl);\n    const func = throwingFetch(url, {\n      retryDelay,\n      retryOn: deploymentFetchRetryOn(onErrorWithAttempt, options?.method),\n      ...options,\n    });\n    return func;\n  };\n}\n\n/**\n * This returns a `fetch` function that will fetch against `deploymentUrl`.\n *\n * It will also set the `Authorization` header, `Content-Type` header, and\n * the `Convex-Client` header if they are not set in the `fetch`.\n */\nexport function deploymentFetch(\n  ctx: Context,\n  options: {\n    deploymentUrl: string;\n    adminKey: string;\n    onError?: (err: any) => void;\n  },\n): typeof throwingFetch {\n  const { deploymentUrl, adminKey, onError } = options;\n  const onErrorWithAttempt = (err: any, attempt: number) => {\n    onError?.(err);\n    if (attempt >= RETRY_LOG_THRESHOLD) {\n      logMessage(\n        chalk.gray(`Retrying request (attempt ${attempt}/${MAX_RETRIES})...`),\n      );\n    }\n  };\n  return (resource: RequestInfo | URL, options: RequestInit | undefined) => {\n    const url =\n      resource instanceof URL\n        ? resource.pathname\n        : typeof resource === \"string\"\n          ? new URL(resource, deploymentUrl)\n          : new URL(resource.url, deploymentUrl);\n\n    const headers = new Headers(options?.headers || {});\n    if (!headers.has(\"Authorization\")) {\n      headers.set(\"Authorization\", `Convex ${adminKey}`);\n    }\n    if (!headers.has(\"Content-Type\")) {\n      headers.set(\"Content-Type\", \"application/json\");\n    }\n    if (!headers.has(\"Convex-Client\")) {\n      headers.set(\"Convex-Client\", `npm-cli-${version}`);\n    }\n    const func = throwingFetch(url, {\n      retryDelay,\n      retryOn: deploymentFetchRetryOn(onErrorWithAttempt, options?.method),\n      ...options,\n      headers,\n    });\n    return func;\n  };\n}\n\n/**\n * Whether this is likely to be a WebContainer,\n * WebContainers can't complete the WorkOS  login but where that login flow\n * fails has changed with the environment.\n */\nexport function isWebContainer(): boolean {\n  // Dynamic require as used here doesn't work with tsx\n  if (process.env.CONVEX_RUNNING_LIVE_IN_MONOREPO) {\n    return false;\n  }\n  const dynamicRequire = require;\n  if (process.versions.webcontainer === undefined) {\n    return false;\n  }\n  let blitzInternalEnv: unknown;\n  try {\n    blitzInternalEnv = dynamicRequire(\"@blitz/internal/env\");\n    // totally fine for this require to fail\n    // eslint-disable-next-line no-empty\n  } catch {}\n  return blitzInternalEnv !== null && blitzInternalEnv !== undefined;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,gBAAe;AACf,kBAAiB;AAIjB,2BAAsB;AACtB,uBAAqC;AACrC,yBAA0D;AAE1D,iBAKO;AACP,qBAAwB;AAExB,qBAAyD;AACzD,sBAGO;AAEP,2BAAyB;AAEzB,MAAM,oBAAgB,mBAAAA,SAAkB,KAAK;AAEtC,MAAM,0BAA0B;AAChC,MAAM,gBACX,QAAQ,IAAI,yBAAyB;AACvC,MAAM,gBAAgB,GAAG,aAAa;AAC/B,MAAM,oBAAoB;AAC1B,MAAM,iCAAiC;AACvC,MAAM,iCAAiC;AACvC,MAAM,kCAAkC;AACxC,MAAM,wCACX;AACF,MAAM,cAAc;AAEpB,MAAM,sBAAsB;AAErB,SAAS,qBAAqB,OAAe;AAClD,QAAM,cAAc,aAAa,KAAK;AACtC,MAAI,eAAe,GAAG;AAEpB,UAAM,IAAI,sCAAqB,wBAAwB;AAAA,EACzD;AACA,SAAO;AACT;AAEO,SAAS,aAAa,OAAe;AAC1C,QAAM,cAAc,CAAC;AACrB,MAAI,MAAM,WAAW,GAAG;AAEtB,UAAM,IAAI,sCAAqB,eAAe;AAAA,EAChD;AACA,SAAO;AACT;AAWO,MAAM,2BAA2B,MAAM;AAAA,EAI5C,YACE,KACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF,GACA;AAAA;AAAA;AAVF;AACA;AASE;AAAA;AACA,QAAI,SAAS,UAAa,YAAY,QAAW;AAC/C,cAAM,GAAG,GAAG,KAAK,IAAI,KAAK,OAAO,EAAE;AACnC,WAAK,kBAAkB,EAAE,MAAM,QAAQ;AAAA,IACzC,OAAO;AACL,cAAM,GAAG;AAAA,IACX;AAEA,WAAO,eAAe,MAAM,mBAAmB,SAAS;AAExD,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,aAAoB,aAClB,UACA,KAC6B;AAC7B,UAAM,GAAG,MAAM,GAAG,GAAG,MAAM,EAAE,GAAG,SAAS,MAAM,IAAI,SAAS,UAAU;AACtE,QAAI,MAAM;AACV,QAAI;AACF,OAAC,EAAE,MAAM,QAAQ,IAAI,MAAM,SAAS,KAAK;AAAA,IAC3C,QAAQ;AAAA,IAER;AACA,WAAO,IAAI,mBAAmB,KAAK,EAAE,MAAM,SAAS,SAAS,CAAC;AAAA,EAChE;AAAA,EAEA,MAAM,OAAO,KAA8B;AACzC,QAAI,aAAwB;AAC5B,UAAM,8BAA8B,KAAK,KAAK,QAAQ;AAEtD,QAAI,MAAM,KAAK;AAEf,QAAI,KAAK,SAAS,WAAW,KAAK;AAChC,mBAAa;AAAA,IACf,WAAW,KAAK,SAAS,WAAW,KAAK;AACvC,mBAAa;AACb,YAAM,GAAG,GAAG;AAAA;AAAA,IACd,WAAW,KAAK,SAAS,WAAW,KAAK;AACvC,mBAAa;AACb,YAAM,GAAG,GAAG,KAAK,KAAK,SAAS,GAAG;AAAA,IACpC;AAEA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB,aAAAC,QAAM,IAAI,IAAI,KAAK,CAAC;AAAA,IACtC,CAAC;AAAA,EACH;AACF;AASA,eAAsB,cACpB,UACA,SACmB;AACnB,QAAMC,WAAU,WAAW;AAC3B,QAAM,UAAU,IAAIA,UAAS,WAAW,CAAC,GAAG,SAAS,CAAC;AACtD,MAAI,SAAS,MAAM;AACjB,QAAI,CAAC,QAAQ,IAAI,cAAc,GAAG;AAChC,cAAQ,IAAI,gBAAgB,kBAAkB;AAAA,IAChD;AAAA,EACF;AACA,QAAM,WAAW,MAAM,cAAc,UAAU,OAAO;AACtD,MAAI,CAAC,SAAS,IAAI;AAGhB,UAAM,MAAM,mBAAmB;AAAA,MAC7B;AAAA,MACA,kBAAkB,SAAS,SAAS,QAAQ,SAAS,MAAM,EAAE,IAC3D,OAAO,aAAa,WAChB,WACA,SAAS,WACP,SAAS,MACT,SAAS,SAAS,CAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAKA,eAAsB,uBACpB,KACA,KACgB;AAEhB,sCAAoB;AAEpB,MAAI,eAAe,oBAAoB;AACrC,WAAO,MAAM,IAAI,OAAO,GAAG;AAAA,EAC7B,OAAO;AACL,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB,aAAAD,QAAM,IAAI,GAAG;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAEA,SAAS,sBAAsB,KAAc,oBAA4B;AACvE,MAAI,IAAI,2BAA2B;AACjC;AAAA,EACF;AACA,MAAI,4BAA4B;AAChC,6BAAW,aAAAA,QAAM,OAAO,kBAAkB,CAAC;AAC7C;AAEA,eAAe,8BAA8B,KAAc,MAAgB;AACzE,QAAM,UAAU,KAAK;AACrB,MAAI,SAAS;AACX,UAAM,mBAAmB,QAAQ,IAAI,4BAA4B;AACjE,UAAM,qBAAqB,QAAQ,IAAI,8BAA8B;AACrE,YAAQ,kBAAkB;AAAA,MACxB,KAAK;AACH;AAAA,MACF,KAAK;AAMH,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,aAAAA,QAAM,IAAI,kBAAkB;AAAA,QAC9C,CAAC;AAAA,MACH;AAGE;AAAA,UACE;AAAA,UACA,sBAAsB;AAAA,QACxB;AACA;AAAA,IACJ;AAAA,EACF;AACF;AAIO,SAAS,wBAAwB,KAAc,MAAgB;AACpE,QAAM,UAAU,KAAK;AACrB,MAAI,SAAS;AACX,UAAM,mBAAmB,QAAQ,IAAI,4BAA4B;AACjE,UAAM,qBAAqB,QAAQ,IAAI,8BAA8B;AACrE,YAAQ,kBAAkB;AAAA,MACxB,KAAK;AACH;AAAA,MACF,KAAK;AAGH,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AACE;AAAA,UACE;AAAA,UACA,sBAAsB;AAAA,QACxB;AACA;AAAA,IACJ;AAAA,EACF;AACF;AAQA,eAAsB,QAAQ,KAAc,UAAkB;AAC5D,QAAM,QAAgB,MAAM,YAAY,EAAE,KAAK,QAAQ,OAAO,KAAK,QAAQ,CAAC;AAC5E,SAAO,MAAM,KAAK,CAAC,SAAS,KAAK,SAAS,QAAQ;AACpD;AAEA,eAAsB,qBACpB,KACA,UACA,eACgD;AAChD,QAAM,QAAgB,MAAM,YAAY,EAAE,KAAK,QAAQ,OAAO,KAAK,QAAQ,CAAC;AAC5E,MAAI,MAAM,WAAW,GAAG;AACtB,UAAM,IAAI,MAAM;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB,aAAAA,QAAM,IAAI,uBAAuB;AAAA,IACnD,CAAC;AAAA,EACH;AACA,MAAI,CAAC,UAAU;AAEb,YAAQ,MAAM,QAAQ;AAAA,MACpB,KAAK;AACH,eAAO,EAAE,UAAU,MAAM,CAAC,EAAE,MAAM,QAAQ,MAAM;AAAA,MAClD;AACE,eAAO;AAAA,UACL,UAAU,UAAM,6BAAa,KAAK;AAAA,YAChC,SAAS;AAAA,YACT,SAAS,MAAM,IAAI,CAAC,UAAgB;AAAA,cAClC,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI;AAAA,cAChC,OAAO,KAAK;AAAA,YACd,EAAE;AAAA,UACJ,CAAC;AAAA,UACD,QAAQ;AAAA,QACV;AAAA,IACJ;AAAA,EACF,OAAO;AAEL,QAAI,CAAC,MAAM,KAAK,CAAC,SAAS,KAAK,SAAS,QAAQ,GAAG;AACjD,YAAM,IAAI,MAAM;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,eAAe,QAAQ;AAAA,MACzC,CAAC;AAAA,IACH;AACA,WAAO,EAAE,UAAU,QAAQ,MAAM;AAAA,EACnC;AACF;AAEA,eAAsB,wBACpB,KACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AACF,GAoB+C;AAC/C,MAAI,mBAAoB,QAAO,EAAE,eAAe,mBAAmB;AACnE,MAAI,sBAAuB,QAAO,EAAE,eAAe,sBAAsB;AAEzE,MAAI,kBAAkB,cAAc,wBAAwB,MAAM;AAEhE,QAAI,UAAM,4CAA2B,KAAK,EAAE,aAAa,SAAS,CAAC,GAAG;AAEpE,aAAO,EAAE,eAAe,QAAQ;AAAA,IAClC;AAAA,EACF;AAIA,MAAI,wBAAwB,SAAS,CAAC,qCAAqC;AACzE,WAAO,EAAE,eAAe,QAAQ;AAAA,EAClC;AAIA,QAAM,kBACH,UAAM,+CAA8B,GAAG,GAAG,cAAc,SACzD;AACF,MAAI,gBAAgB;AAClB,WAAO,EAAE,eAAe,QAAQ;AAAA,EAClC;AAGA,QAAM,gBAAmC,UAAM,8BAAc,KAAK;AAAA,IAChE,SACE;AAAA,IACF,SAAS;AAAA,IACT,SAAS;AAAA,MACP,EAAE,MAAM,oBAAoB,OAAO,QAAQ;AAAA,MAC3C,EAAE,MAAM,2BAA2B,OAAO,QAAQ;AAAA,IACpD;AAAA,EACF,CAAC;AACD,SAAO,EAAE,cAAc;AACzB;AAEA,eAAsB,WACpB,KACA,UACA,aACA;AACA,MAAI;AACF,UAAM,YACJ,MAAM,oBAAoB,GAAG,EAAE,IAAI,+BAA+B;AAAA,MAChE,QAAQ;AAAA,QACN,MAAM;AAAA,UACJ,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC,GACD;AACF,WAAO,CAAC,CAAC,SAAS,KAAK,CAAC,YAAY,QAAQ,SAAS,WAAW;AAAA,EAClE,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,eAAsB,YAAY,KAAc;AAC9C,SAAO,CAAC,CAAE,MAAM,YAAY,EAAE,KAAK,QAAQ,OAAO,KAAK,eAAe,CAAC;AACzE;AAEA,eAAsB,wBACpB,KACA,aACA,UACA,qBACA,oBACwB;AACxB,QAAM,YACJ,MAAM,oBAAoB,GAAG,EAAE,IAAI,+BAA+B;AAAA,IAChE,QAAQ;AAAA,MACN,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,CAAC,GACD;AACF,MAAI,SAAS,WAAW,GAAG;AACzB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,MAAI,CAAC,aAAa;AAChB,UAAM,kBAAkB,SAAS,OAAO,CAAC,YAAY,CAAC,QAAQ,MAAM;AACpE,QAAI,gBAAgB,WAAW,GAAG;AAChC,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAEA,YAAQ,gBAAgB,QAAQ;AAAA,MAC9B,KAAK,GAAG;AACN,cAAM,UAAU,gBAAgB,CAAC;AACjC,cAAM,YAAY,UAAM,4BAAY,KAAK;AAAA,UACvC,SAAS,GAAG,mBAAmB,IAAI,QAAQ,IAAI,KAAK,QAAQ,IAAI;AAAA,QAClE,CAAC;AAED,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AACA,eAAO,gBAAgB,CAAC,EAAE;AAAA,MAC5B;AAAA,MACA;AACE,eAAO,UAAM,6BAAa,KAAK;AAAA,UAC7B,SAAS;AAAA,UACT,SAAS,gBAAgB,IAAI,CAAC,aAAsB;AAAA,YAClD,MAAM,GAAG,QAAQ,IAAI,KAAK,QAAQ,IAAI;AAAA,YACtC,OAAO,QAAQ;AAAA,UACjB,EAAE;AAAA,QACJ,CAAC;AAAA,IACL;AAAA,EACF,OAAO;AAEL,QAAI,CAAC,SAAS,KAAK,CAAC,YAAY,QAAQ,SAAS,WAAW,GAAG;AAC7D,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,kBAAkB,WAAW;AAAA,MAC/C,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AAOA,eAAsB,gBACpB,KACA,kBAAkB,OACe;AACjC,MAAI;AACJ,MAAI;AACF,kBAAc,IAAI,GAAG,aAAa,cAAc;AAAA,EAClD,SAAS,KAAK;AACZ,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,qCACd,GACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI;AACJ,MAAI;AACF,UAAM,KAAK,MAAM,WAAW;AAAA,EAC9B,SAAS,KAAK;AACZ,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB,iCAAiC,GAAU;AAAA,IAC7D,CAAC;AAAA,EACH;AACA,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,WAAW;AAAA,IACf,GAAI,kBAAmB,IAAI,oBAAoB,CAAC,IAAK,CAAC;AAAA,IACtD,GAAI,IAAI,gBAAgB,CAAC;AAAA,IACzB,GAAI,IAAI,mBAAmB,CAAC;AAAA,EAC9B;AACA,SAAO;AACT;AAEA,eAAsB,0BAA0B,KAAc,KAAa;AACzE,QAAM,WAAW,MAAM,gBAAgB,KAAK,IAAI;AAChD,QAAM,sBAAsB,YAAY;AACxC,MAAI,CAAC,qBAAqB;AACxB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,eAAe,GAAG;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAGO,MAAM,SAAS,CAAI,KAAU,QAA6B;AAC/D,QAAM,SAAS,CAAC,GAAG,GAAG;AACtB,QAAM,MAAM,CAAC,GAAM,MAAS;AAC1B,QAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAG,QAAO;AAC5B,QAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAG,QAAO;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,OAAO,KAAK,GAAG;AACxB;AAEO,SAAS,aACd,YACA,eACQ;AACR,SAAO,YAAAE,QAAK,KAAK,YAAAA,QAAK,QAAQ,UAAU,GAAG,cAAc,SAAS;AACpE;AAEA,SAAS,aAAa;AAEpB,MAAI,QAAQ,IAAI,uBAAuB;AACrC,UAAM,OAAO,QAAQ,IAAI,sBAAsB,MAAM,GAAG,EAAE,CAAC;AAC3D,QAAI,SAAS,UAAa,SAAS,QAAQ;AACzC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,eAAe,IAAI;AAAA,IAC5B;AAAA,EACF;AACA,SAAO;AACT;AAEO,SAAS,gBAAwB;AACtC,SAAO,YAAAA,QAAK,KAAK,UAAAC,QAAG,QAAQ,GAAG,IAAI,WAAW,CAAC,EAAE;AACnD;AAEO,SAAS,WAAW;AACzB,QAAM,OAAO,WAAW;AACxB,QAAM,WAAW,QAAQ;AACzB,MAAI,aAAa,SAAS;AAGxB,QAAI,QAAQ,IAAI,cAAc;AAC5B,aAAO,YAAAD,QAAK,KAAK,QAAQ,IAAI,cAAc,IAAI;AAAA,IACjD;AACA,QAAI,QAAQ,IAAI,aAAa;AAC3B,aAAO,YAAAA,QAAK,KAAK,QAAQ,IAAI,aAAa,WAAW,SAAS,IAAI;AAAA,IACpE;AACA,WAAO,YAAAA,QAAK,KAAK,UAAAC,QAAG,QAAQ,GAAG,WAAW,SAAS,IAAI;AAAA,EACzD;AACA,SAAO,YAAAD,QAAK,KAAK,UAAAC,QAAG,QAAQ,GAAG,UAAU,IAAI;AAC/C;AAOA,eAAsB,cAAc,KAAqC;AACvE,QAAM,aAAa,IAAI,aAAa,GAAG;AACvC,QAAM,kBAA0C,aAC5C;AAAA,IACE,eAAe;AAAA,IACf,iBAAiB,WAAW,sBAAO;AAAA,EACrC,IACA;AAAA,IACE,iBAAiB,WAAW,sBAAO;AAAA,EACrC;AACJ,SAAO,CAAC,UAA6B,YAAqC;AACxE,UAAM,EAAE,SAAS,gBAAgB,GAAG,KAAK,IAAI,WAAW,CAAC;AACzD,UAAM,UAAU;AAAA,MACd,GAAG;AAAA,MACH,GAAI,kBAAkB,CAAC;AAAA,IACzB;AACA,UAAM,OAAO;AAAA,MACX,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL;AAEA,UAAM,MACJ,oBAAoB,MAChB,SAAS,WACT,OAAO,aAAa,WAClB,IAAI,IAAI,UAAU,aAAa,IAC/B,IAAI,IAAI,SAAS,KAAK,aAAa;AAC3C,WAAO,cAAc,KAAK,IAAI;AAAA,EAChC;AACF;AAEA,eAAsB,YAAqB;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKe;AACb,QAAM,aACJ,SAAS,SACL,SACA,OAAO,SAAS,WACd,OACA,KAAK,UAAU,IAAI;AAC3B,MAAI;AACF,WAAO,MAAM,uBAAuB;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,KAAc;AACrB,WAAO,MAAM,uBAAuB,KAAK,GAAG;AAAA,EAC9C;AACF;AAQO,SAAS,oBACd,KACA,UAA+B,CAAC,GAChC;AACA,QAAM,qBAAiB,qBAAAC,SAA8B;AAAA,IACnD,SAAS;AAAA,IACT,OAAO,OACL,UACAC,aACsB;AACtB,YAAMC,SAAQ,MAAM,cAAc,GAAG;AACrC,aAAOA,OAAM,UAAUD,QAAO;AAAA,IAChC;AAAA,EACF,CAAC;AAGD,SAAO,IAAI,MAAM,gBAAgB;AAAA,IAC/B,IAAI,QAAQ,MAAM;AAChB,YAAM,iBAAiB,OAAO,IAA2B;AAEzD,UACE,SAAS,SACT,SAAS,UACT,SAAS,UACT,SAAS,aACT,SAAS,SACT,SAAS,YACT,SAAS,WACT,SAAS,SACT;AACA,eAAO,UAAU,SAAgB;AAC/B,cAAI;AACF,mBAAO,MAAO,eAA4B,MAAM,QAAQ,IAAI;AAAA,UAC9D,SAAS,KAAc;AACrB,gBAAI,QAAQ,OAAO;AAEjB,oBAAM;AAAA,YACR;AACA,mBAAO,MAAM,uBAAuB,KAAK,GAAG;AAAA,UAC9C;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AAEA,eAAsB,uBAAuB;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKiB;AACf,QAAMC,SAAQ,MAAM,cAAc,GAAG;AACrC,QAAM,aACJ,SAAS,SACL,WAAW,SACT,KAAK,UAAU,CAAC,CAAC,IACjB,SACF,OAAO,SAAS,WACd,OACA,KAAK,UAAU,IAAI;AAC3B,QAAM,MAAM,MAAMA,OAAM,KAAK;AAAA,IAC3B;AAAA,IACA,GAAI,aAAa,EAAE,MAAM,WAAW,IAAI,CAAC;AAAA,IACzC,SACE,WAAW,SACP;AAAA,MACE,gBAAgB;AAAA,IAClB,IACA,CAAC;AAAA,EACT,CAAC;AACD,0BAAwB,KAAK,GAAG;AAChC,MAAI,IAAI,WAAW,KAAK;AACtB,WAAO,MAAM,IAAI,KAAK;AAAA,EACxB;AACF;AAUO,MAAM,OAAO,eAClBA,QACA,WACA,SAAS,KACT;AACA,MAAI,SAAS,MAAMA,OAAM;AACzB,SAAO,CAAC,UAAU,MAAM,GAAG;AACzB,UAAM,KAAK,MAAM;AACjB,aAAS,MAAMA,OAAM;AAAA,EACvB;AACA,SAAO;AACT;AAEA,MAAM,OAAO,SAAU,QAAgB;AACrC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,SAAS,MAAM;AAAA,EAC5B,CAAC;AACH;AAEO,SAAS,cAAc;AAE5B,SAAO,IAAI,QAAQ,CAAC,MAAM;AAAA,EAE1B,CAAC;AACH;AAGO,SAAS,kBAAkD;AAChE,MAAI;AACJ,QAAM,cAAc,IAAI,QAAQ,CAAC,YAAa,WAAW,OAAQ;AACjE,SAAO,CAAC,aAAa,MAAM,SAAS,IAAI,CAAC;AAC3C;AAIO,SAAS,WAAW,GAAmB;AAC5C,MAAI,IAAI,MAAM;AACZ,WAAO,GAAG,CAAC;AAAA,EACb;AACA,MAAI,IAAI,OAAO,MAAM;AACnB,WAAO,IAAI,IAAI,MAAM,QAAQ,CAAC,CAAC;AAAA,EACjC;AACA,MAAI,IAAI,OAAO,OAAO,MAAM;AAC1B,WAAO,IAAI,IAAI,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,EACxC;AACA,SAAO,IAAI,IAAI,OAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAC/C;AAEO,SAAS,eAAe,IAAoB;AACjD,QAAM,YAAY,CAAC,GAAW,SAC5B,GAAG,EAAE,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC,CAAC,GAAG,IAAI;AAEnE,MAAI,KAAK,MAAM;AACb,WAAO,UAAU,KAAK,KAAK,IAAI;AAAA,EACjC;AACA,MAAI,KAAK,GAAG;AACV,WAAO,UAAU,KAAK,KAAK,OAAI;AAAA,EACjC;AACA,MAAI,KAAK,KAAK;AACZ,WAAO,UAAU,IAAI,IAAI;AAAA,EAC3B;AACA,QAAM,IAAI,KAAK;AACf,MAAI,IAAI,IAAI;AACV,WAAO,UAAU,KAAK,KAAK,GAAG;AAAA,EAChC;AACA,SAAO,UAAU,IAAI,IAAI,GAAG;AAC9B;AAEO,SAAS,uBAAuB;AACrC,QAAM,MAAM,oBAAI,KAAK;AACrB,QAAM,QAAQ,OAAO,IAAI,SAAS,CAAC,EAAE,SAAS,GAAG,GAAG;AACpD,QAAM,UAAU,OAAO,IAAI,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG;AACxD,QAAM,UAAU,OAAO,IAAI,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG;AACxD,SAAO,GAAG,KAAK,IAAI,OAAO,IAAI,OAAO;AACvC;AAIA,eAAsB,kBAAkB,KAGrC;AACD,QAAM,oBAAoB,OAAO,KAAK,cAAc;AACpD,MAAI,CAAC,mBAAmB;AACtB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AACA,QAAM,sBACJ,qBACA,YAAAJ,QAAK,KAAK,YAAAA,QAAK,QAAQ,iBAAiB,GAAG,aAAa;AAC1D,QAAM,mBACJ,uBAAuB,IAAI,GAAG,OAAO,mBAAmB,IACpD,sBACA;AACN,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,OAAO,KAAc,UAAsC;AAClE,MAAI,SAAS,YAAAA,QAAK,QAAQ,GAAG;AAC7B,MAAI,YAAY;AAChB,KAAG;AACD,UAAM,YAAY,YAAAA,QAAK,KAAK,QAAQ,QAAQ;AAC5C,QAAI,IAAI,GAAG,OAAO,SAAS,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,aAAS;AACT,gBAAY,YAAAA,QAAK,QAAQ,MAAM;AAAA,EACjC,SAAS,cAAc;AACvB;AACF;AAMA,eAAsB,oBAAoB,KAAc;AACtD,QAAM,EAAE,mBAAmB,iBAAiB,IAAI,MAAM,kBAAkB,GAAG;AAC3E,MAAI,sBAAsB,YAAAA,QAAK,QAAQ,cAAc,GAAG;AACtD,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO,CAAC,CAAC;AACX;AA0BO,SAAS,WACd,KACA,SACA,MACA,SACA;AACA,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,YAAQ,4BAAM,SAAS,MAAM,EAAE,OAAO,SAAS,MAAM,CAAC;AAC5D,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,UAAM,aAAa,SAAS,UAAU;AAEtC,QAAI,YAAY;AACd,YAAM,OAAO;AAAA,QAAG;AAAA,QAAQ,CAAC,aACvB,uBAAW,KAAK,SAAS,OAAO,EAAE,QAAQ,CAAC;AAAA,MAC7C;AACA,YAAM,OAAO;AAAA,QAAG;AAAA,QAAQ,CAAC,aACvB,qBAAS,KAAK,SAAS,OAAO,EAAE,QAAQ,CAAC;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,YAAM,OAAO,GAAG,QAAQ,CAAC,SAAS;AAChC,kBAAU,KAAK,SAAS,OAAO;AAAA,MACjC,CAAC;AAED,YAAM,OAAO,GAAG,QAAQ,CAAC,SAAS;AAChC,kBAAU,KAAK,SAAS,OAAO;AAAA,MACjC,CAAC;AAAA,IACH;AAEA,UAAM,qBAAqB,CAAC,SAAwB;AAClD,YAAM,eAAe,SAAS,aAAa;AAC3C,YAAM,SAAS,aACX,EAAE,QAAQ,KAAK,IACf,EAAE,QAAQ,QAAQ,QAAQ,KAAK;AACnC,UAAI,SAAS,GAAG;AACd,cAAM,iBACJ,QAAQ,KAAK,SAAS,IAAI,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK;AACnD,cAAM,QAAQ,IAAI;AAAA,UAChB,KAAK,OAAO,GAAG,cAAc,iCAAiC,IAAI;AAAA,QACpE;AACA,YAAI,YAAY;AACd,iBAAO,EAAE,GAAG,QAAQ,MAAM,CAAC;AAAA,QAC7B,OAAO;AACL,kBAAQ,EAAE,GAAG,QAAQ,MAAM,CAAC;AAAA,QAC9B;AAAA,MACF,OAAO;AACL,gBAAQ,MAAM;AAAA,MAChB;AAAA,IACF;AAEA,UAAM,gBAAgB,CAAC,UAAiB;AACtC,YAAM,eAAe,QAAQ,kBAAkB;AAC/C,YAAM,eAAe,SAAS,kBAAkB;AAChD,UAAI,YAAY;AACd,eAAO,EAAE,OAAO,QAAQ,KAAK,CAAC;AAAA,MAChC,OAAO;AACL,gBAAQ,EAAE,OAAO,QAAQ,KAAK,CAAC;AAAA,MACjC;AAAA,IACF;AAEA,QAAI,YAAY;AACd,YAAM,KAAK,QAAQ,kBAAkB;AAAA,IACvC,OAAO;AACL,YAAM,KAAK,SAAS,kBAAkB;AAAA,IACxC;AACA,UAAM,KAAK,SAAS,aAAa;AAAA,EACnC,CAAC;AACH;AAEA,MAAM,qBAAqB,CAAC,OAAO,QAAQ,OAAO,UAAU,WAAW,OAAO;AAE9E,SAAS,WACP,SACA,QACA,WACQ;AAER,QAAM,QAAQ,YAAY,IAAI,IAAI,MAAM,UAAU,KAAK;AACvD,QAAM,YAAY,QAAQ,MAAM,KAAK,OAAO;AAC5C,SAAO,QAAQ;AACjB;AAEA,SAAS,uBACP,SACA,QACA;AACA,QAAM,cAAc,SAClB,SACA,OACA,UACkD;AAElD,QAAI,UAAU,MAAM;AAGlB,aAAO,EAAE,MAAM,SAAS,MAAa;AAAA,IACvC;AAGA,QAAI,UAAU,WAAW,KAAK;AAC5B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO,iCAAiC,SAAS,MAAM;AAAA,MACzD;AAAA,IACF;AAGA,QACE,YACA,CAAC,SAAS,MACV,UACA,mBAAmB,SAAS,OAAO,YAAY,CAAC,GAChD;AAEA,UACE;AAAA,QACE;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF,EAAE,SAAS,SAAS,MAAM,GAC1B;AACA,eAAO;AAAA,UACL,MAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO,iCAAiC,SAAS,MAAM;AAAA,MACzD;AAAA,IACF;AAEA,WAAO,EAAE,MAAM,OAAO;AAAA,EACxB;AAEA,SAAO,SACL,SACA,OACA,UACA;AACA,UAAM,SAAS,YAAY,SAAS,OAAO,QAAQ;AACnD,QAAI,OAAO,SAAS,SAAS;AAC3B,gBAAU,OAAO,OAAO,OAAO;AAAA,IACjC;AACA,QAAI,WAAW,aAAa;AAG1B,aAAO;AAAA,IACT;AACA,WAAO,OAAO,SAAS;AAAA,EACzB;AACF;AAMO,SAAS,oBACd,KACA,SAIsB;AACtB,QAAM,EAAE,eAAe,QAAQ,IAAI;AACnC,QAAM,qBAAqB,CAAC,KAAU,YAAoB;AACxD,cAAU,GAAG;AACb,QAAI,WAAW,qBAAqB;AAClC;AAAA,QACE,aAAAF,QAAM,KAAK,6BAA6B,OAAO,IAAI,WAAW,MAAM;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,UAA6BK,aAAqC;AACxE,UAAM,MACJ,oBAAoB,MAChB,SAAS,WACT,OAAO,aAAa,WAClB,IAAI,IAAI,UAAU,aAAa,IAC/B,IAAI,IAAI,SAAS,KAAK,aAAa;AAC3C,UAAM,OAAO,cAAc,KAAK;AAAA,MAC9B;AAAA,MACA,SAAS,uBAAuB,oBAAoBA,UAAS,MAAM;AAAA,MACnE,GAAGA;AAAA,IACL,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAQO,SAAS,gBACd,KACA,SAKsB;AACtB,QAAM,EAAE,eAAe,UAAU,QAAQ,IAAI;AAC7C,QAAM,qBAAqB,CAAC,KAAU,YAAoB;AACxD,cAAU,GAAG;AACb,QAAI,WAAW,qBAAqB;AAClC;AAAA,QACE,aAAAL,QAAM,KAAK,6BAA6B,OAAO,IAAI,WAAW,MAAM;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,UAA6BK,aAAqC;AACxE,UAAM,MACJ,oBAAoB,MAChB,SAAS,WACT,OAAO,aAAa,WAClB,IAAI,IAAI,UAAU,aAAa,IAC/B,IAAI,IAAI,SAAS,KAAK,aAAa;AAE3C,UAAM,UAAU,IAAI,QAAQA,UAAS,WAAW,CAAC,CAAC;AAClD,QAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AACjC,cAAQ,IAAI,iBAAiB,UAAU,QAAQ,EAAE;AAAA,IACnD;AACA,QAAI,CAAC,QAAQ,IAAI,cAAc,GAAG;AAChC,cAAQ,IAAI,gBAAgB,kBAAkB;AAAA,IAChD;AACA,QAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AACjC,cAAQ,IAAI,iBAAiB,WAAW,sBAAO,EAAE;AAAA,IACnD;AACA,UAAM,OAAO,cAAc,KAAK;AAAA,MAC9B;AAAA,MACA,SAAS,uBAAuB,oBAAoBA,UAAS,MAAM;AAAA,MACnE,GAAGA;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAOO,SAAS,iBAA0B;AAExC,MAAI,QAAQ,IAAI,iCAAiC;AAC/C,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB;AACvB,MAAI,QAAQ,SAAS,iBAAiB,QAAW;AAC/C,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AACF,uBAAmB,eAAe,qBAAqB;AAAA,EAGzD,QAAQ;AAAA,EAAC;AACT,SAAO,qBAAqB,QAAQ,qBAAqB;AAC3D;", "names": ["fetchRetryFactory", "chalk", "Headers", "path", "os", "createClient", "options", "fetch"]}