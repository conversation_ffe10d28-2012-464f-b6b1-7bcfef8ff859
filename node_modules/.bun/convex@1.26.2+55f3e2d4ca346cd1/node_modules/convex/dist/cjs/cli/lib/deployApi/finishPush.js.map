{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/finishPush.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { looseObject } from \"./utils.js\";\n\nexport const authDiff = looseObject({\n  added: z.array(z.string()),\n  removed: z.array(z.string()),\n});\nexport type AuthDiff = z.infer<typeof authDiff>;\n\nexport const componentDefinitionDiff = looseObject({});\nexport type ComponentDefinitionDiff = z.infer<typeof componentDefinitionDiff>;\n\nexport const componentDiffType = z.discriminatedUnion(\"type\", [\n  looseObject({\n    type: z.literal(\"create\"),\n  }),\n  looseObject({\n    type: z.literal(\"modify\"),\n  }),\n  looseObject({\n    type: z.literal(\"unmount\"),\n  }),\n  looseObject({\n    type: z.literal(\"remount\"),\n  }),\n]);\nexport type ComponentDiffType = z.infer<typeof componentDiffType>;\n\nexport const moduleDiff = looseObject({\n  added: z.array(z.string()),\n  removed: z.array(z.string()),\n});\nexport type ModuleDiff = z.infer<typeof moduleDiff>;\n\nexport const udfConfigDiff = looseObject({\n  previous_version: z.string(),\n  next_version: z.string(),\n});\nexport type UdfConfigDiff = z.infer<typeof udfConfigDiff>;\n\nexport const cronDiff = looseObject({\n  added: z.array(z.string()),\n  updated: z.array(z.string()),\n  deleted: z.array(z.string()),\n});\nexport type CronDiff = z.infer<typeof cronDiff>;\n\nconst developerIndexConfig = z.intersection(\n  z.discriminatedUnion(\"type\", [\n    looseObject({\n      name: z.string(),\n      type: z.literal(\"database\"),\n      fields: z.array(z.string()),\n    }),\n    looseObject({\n      name: z.string(),\n      type: z.literal(\"search\"),\n      searchField: z.string(),\n      filterFields: z.array(z.string()),\n    }),\n    looseObject({\n      name: z.string(),\n      type: z.literal(\"vector\"),\n      dimensions: z.number(),\n      vectorField: z.string(),\n      filterFields: z.array(z.string()),\n    }),\n  ]),\n  z.object({ staged: z.boolean().optional() }),\n);\nexport type DeveloperIndexConfig = z.infer<typeof developerIndexConfig>;\n\nexport const indexDiff = looseObject({\n  added_indexes: z.array(developerIndexConfig),\n  removed_indexes: z.array(developerIndexConfig),\n  enabled_indexes: z.array(developerIndexConfig).optional(),\n  disabled_indexes: z.array(developerIndexConfig).optional(),\n});\nexport type IndexDiff = z.infer<typeof indexDiff>;\n\nexport const schemaDiff = looseObject({\n  previous_schema: z.nullable(z.string()),\n  next_schema: z.nullable(z.string()),\n});\nexport type SchemaDiff = z.infer<typeof schemaDiff>;\n\nexport const componentDiff = looseObject({\n  diffType: componentDiffType,\n  moduleDiff,\n  udfConfigDiff: z.nullable(udfConfigDiff),\n  cronDiff,\n  indexDiff,\n  schemaDiff: z.nullable(schemaDiff),\n});\nexport type ComponentDiff = z.infer<typeof componentDiff>;\n\nexport const finishPushDiff = looseObject({\n  authDiff,\n  definitionDiffs: z.record(z.string(), componentDefinitionDiff),\n  componentDiffs: z.record(z.string(), componentDiff),\n});\nexport type FinishPushDiff = z.infer<typeof finishPushDiff>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAClB,mBAA4B;AAErB,MAAM,eAAW,0BAAY;AAAA,EAClC,OAAO,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,EACzB,SAAS,aAAE,MAAM,aAAE,OAAO,CAAC;AAC7B,CAAC;AAGM,MAAM,8BAA0B,0BAAY,CAAC,CAAC;AAG9C,MAAM,oBAAoB,aAAE,mBAAmB,QAAQ;AAAA,MAC5D,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,QAAQ;AAAA,EAC1B,CAAC;AAAA,MACD,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,QAAQ;AAAA,EAC1B,CAAC;AAAA,MACD,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,SAAS;AAAA,EAC3B,CAAC;AAAA,MACD,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,SAAS;AAAA,EAC3B,CAAC;AACH,CAAC;AAGM,MAAM,iBAAa,0BAAY;AAAA,EACpC,OAAO,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,EACzB,SAAS,aAAE,MAAM,aAAE,OAAO,CAAC;AAC7B,CAAC;AAGM,MAAM,oBAAgB,0BAAY;AAAA,EACvC,kBAAkB,aAAE,OAAO;AAAA,EAC3B,cAAc,aAAE,OAAO;AACzB,CAAC;AAGM,MAAM,eAAW,0BAAY;AAAA,EAClC,OAAO,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,EACzB,SAAS,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,EAC3B,SAAS,aAAE,MAAM,aAAE,OAAO,CAAC;AAC7B,CAAC;AAGD,MAAM,uBAAuB,aAAE;AAAA,EAC7B,aAAE,mBAAmB,QAAQ;AAAA,QAC3B,0BAAY;AAAA,MACV,MAAM,aAAE,OAAO;AAAA,MACf,MAAM,aAAE,QAAQ,UAAU;AAAA,MAC1B,QAAQ,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,IAC5B,CAAC;AAAA,QACD,0BAAY;AAAA,MACV,MAAM,aAAE,OAAO;AAAA,MACf,MAAM,aAAE,QAAQ,QAAQ;AAAA,MACxB,aAAa,aAAE,OAAO;AAAA,MACtB,cAAc,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,IAClC,CAAC;AAAA,QACD,0BAAY;AAAA,MACV,MAAM,aAAE,OAAO;AAAA,MACf,MAAM,aAAE,QAAQ,QAAQ;AAAA,MACxB,YAAY,aAAE,OAAO;AAAA,MACrB,aAAa,aAAE,OAAO;AAAA,MACtB,cAAc,aAAE,MAAM,aAAE,OAAO,CAAC;AAAA,IAClC,CAAC;AAAA,EACH,CAAC;AAAA,EACD,aAAE,OAAO,EAAE,QAAQ,aAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC7C;AAGO,MAAM,gBAAY,0BAAY;AAAA,EACnC,eAAe,aAAE,MAAM,oBAAoB;AAAA,EAC3C,iBAAiB,aAAE,MAAM,oBAAoB;AAAA,EAC7C,iBAAiB,aAAE,MAAM,oBAAoB,EAAE,SAAS;AAAA,EACxD,kBAAkB,aAAE,MAAM,oBAAoB,EAAE,SAAS;AAC3D,CAAC;AAGM,MAAM,iBAAa,0BAAY;AAAA,EACpC,iBAAiB,aAAE,SAAS,aAAE,OAAO,CAAC;AAAA,EACtC,aAAa,aAAE,SAAS,aAAE,OAAO,CAAC;AACpC,CAAC;AAGM,MAAM,oBAAgB,0BAAY;AAAA,EACvC,UAAU;AAAA,EACV;AAAA,EACA,eAAe,aAAE,SAAS,aAAa;AAAA,EACvC;AAAA,EACA;AAAA,EACA,YAAY,aAAE,SAAS,UAAU;AACnC,CAAC;AAGM,MAAM,qBAAiB,0BAAY;AAAA,EACxC;AAAA,EACA,iBAAiB,aAAE,OAAO,aAAE,OAAO,GAAG,uBAAuB;AAAA,EAC7D,gBAAgB,aAAE,OAAO,aAAE,OAAO,GAAG,aAAa;AACpD,CAAC;", "names": []}