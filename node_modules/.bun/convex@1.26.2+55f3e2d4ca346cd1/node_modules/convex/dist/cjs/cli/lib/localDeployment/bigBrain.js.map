{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/bigBrain.ts"], "sourcesContent": ["import { Context } from \"../../../bundler/context.js\";\nimport { bigBrainAPI } from \"../utils/utils.js\";\n\nexport async function bigBrainStart(\n  ctx: Context,\n  data: {\n    // cloud port\n    port: number;\n    projectSlug: string;\n    teamSlug: string;\n    instanceName: string | null;\n  },\n): Promise<{ deploymentName: string; adminKey: string }> {\n  return bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"local_deployment/start\",\n    data,\n  });\n}\n\nexport async function bigBrainPause(\n  ctx: Context,\n  data: {\n    projectSlug: string;\n    teamSlug: string;\n  },\n): Promise<void> {\n  return bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"local_deployment/pause\",\n    data,\n  });\n}\n\nexport async function bigBrainRecordActivity(\n  ctx: Context,\n  data: {\n    instanceName: string;\n  },\n) {\n  return bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"local_deployment/record_activity\",\n    data,\n  });\n}\n\nexport async function bigBrainEnableFeatureMetadata(\n  ctx: Context,\n): Promise<{ totalProjects: { kind: \"none\" | \"one\" | \"multiple\" } }> {\n  return bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"local_deployment/enable_feature_metadata\",\n    data: {},\n  });\n}\n\nexport async function bigBrainGenerateAdminKeyForAnonymousDeployment(\n  ctx: Context,\n  data: {\n    instanceName: string;\n    instanceSecret: string;\n  },\n) {\n  return bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"local_deployment/generate_admin_key\",\n    data,\n  });\n}\n/** Whether a project already has a cloud dev deployment for this user. */\nexport async function projectHasExistingCloudDev(\n  ctx: Context,\n  {\n    projectSlug,\n    teamSlug,\n  }: {\n    projectSlug: string;\n    teamSlug: string;\n  },\n) {\n  const response = await bigBrainAPI<\n    | {\n        kind: \"Exists\";\n      }\n    | {\n        kind: \"DoesNotExist\";\n      }\n  >({\n    ctx,\n    method: \"POST\",\n    url: \"deployment/existing_dev\",\n    data: { projectSlug, teamSlug },\n  });\n  if (response.kind === \"Exists\") {\n    return true;\n  } else if (response.kind === \"DoesNotExist\") {\n    return false;\n  }\n  return await ctx.crash({\n    exitCode: 1,\n    errorType: \"fatal\",\n    printedMessage: `Unexpected /api/deployment/existing_dev response: ${JSON.stringify(response, null, 2)}`,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mBAA4B;AAE5B,eAAsB,cACpB,KACA,MAOuD;AACvD,aAAO,0BAAY;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL;AAAA,EACF,CAAC;AACH;AAEA,eAAsB,cACpB,KACA,MAIe;AACf,aAAO,0BAAY;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL;AAAA,EACF,CAAC;AACH;AAEA,eAAsB,uBACpB,KACA,MAGA;AACA,aAAO,0BAAY;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL;AAAA,EACF,CAAC;AACH;AAEA,eAAsB,8BACpB,KACmE;AACnE,aAAO,0BAAY;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM,CAAC;AAAA,EACT,CAAC;AACH;AAEA,eAAsB,+CACpB,KACA,MAIA;AACA,aAAO,0BAAY;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL;AAAA,EACF,CAAC;AACH;AAEA,eAAsB,2BACpB,KACA;AAAA,EACE;AAAA,EACA;AACF,GAIA;AACA,QAAM,WAAW,UAAM,0BAOrB;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM,EAAE,aAAa,SAAS;AAAA,EAChC,CAAC;AACD,MAAI,SAAS,SAAS,UAAU;AAC9B,WAAO;AAAA,EACT,WAAW,SAAS,SAAS,gBAAgB;AAC3C,WAAO;AAAA,EACT;AACA,SAAO,MAAM,IAAI,MAAM;AAAA,IACrB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB,qDAAqD,KAAK,UAAU,UAAU,MAAM,CAAC,CAAC;AAAA,EACxG,CAAC;AACH;", "names": []}