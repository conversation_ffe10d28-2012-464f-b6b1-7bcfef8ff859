{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/runOneoffQuery.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { ConvexTool } from \"./index.js\";\nimport { loadSelectedDeploymentCredentials } from \"../../api.js\";\nimport { getDeploymentSelection } from \"../../deploymentSelection.js\";\n\nconst inputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\n      \"Deployment selector (from the status tool) to run the query on.\",\n    ),\n  query: z\n    .string()\n    .describe(\n      \"The query to run. This should be valid JavaScript code that returns a value.\",\n    ),\n});\n\nconst outputSchema = z.object({\n  result: z.any().describe(\"The result returned by the query\"),\n  logLines: z\n    .array(z.string())\n    .describe(\"The log lines generated by the query\"),\n});\n\nconst description = `\nRun a one-off readonly query on your Convex deployment.\n\nThis tool executes a JavaScript string as a query in your Convex deployment.\nThe query should follow Convex guidelines and use the following setup:\n\n\\`\\`\\`js\nimport { query, internalQuery } from \"convex:/_system/repl/wrappers.js\";\n\nexport default query({\n  handler: async (ctx) => {\n    console.log(\"Write and test your query function here!\");\n  },\n});\n\\`\\`\\`\n\nNote that there are no imports available in this environment. The only import\nyou can use is the built-in \"convex:/_system/repl/wrappers.js\" module in the\ntemplate.\n\nThe function call is also completely sandboxed, so it can only read data and\ncannot modify the database or access the network.\n\nReturns the result and any log lines generated by the query.\n`.trim();\n\nexport const RunOneoffQueryTool: ConvexTool<\n  typeof inputSchema,\n  typeof outputSchema\n> = {\n  name: \"runOneoffQuery\",\n  description,\n  inputSchema,\n  outputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n    try {\n      const response = await fetch(`${credentials.url}/api/run_test_function`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          adminKey: credentials.adminKey,\n          args: {},\n          bundle: {\n            path: \"testQuery.js\",\n            source: args.query,\n          },\n          format: \"convex_encoded_json\",\n        }),\n      });\n      if (!response.ok) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `HTTP error ${response.status}: ${await response.text()}`,\n        });\n      }\n      const result = await response.json();\n      if (result.status !== \"success\") {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `Query failed: ${JSON.stringify(result)}`,\n        });\n      }\n      return {\n        result: result.value,\n        logLines: result.logLines,\n      };\n    } catch (err) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Failed to run query: ${(err as Error).toString().trim()}`,\n      });\n    }\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAElB,iBAAkD;AAClD,iCAAuC;AAEvC,MAAM,cAAc,aAAE,OAAO;AAAA,EAC3B,oBAAoB,aACjB,OAAO,EACP;AAAA,IACC;AAAA,EACF;AAAA,EACF,OAAO,aACJ,OAAO,EACP;AAAA,IACC;AAAA,EACF;AACJ,CAAC;AAED,MAAM,eAAe,aAAE,OAAO;AAAA,EAC5B,QAAQ,aAAE,IAAI,EAAE,SAAS,kCAAkC;AAAA,EAC3D,UAAU,aACP,MAAM,aAAE,OAAO,CAAC,EAChB,SAAS,sCAAsC;AACpD,CAAC;AAED,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBlB,KAAK;AAEA,MAAM,qBAGT;AAAA,EACF,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,UAAM,mDAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,UAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,GAAG,YAAY,GAAG,0BAA0B;AAAA,QACvE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,UAAU,YAAY;AAAA,UACtB,MAAM,CAAC;AAAA,UACP,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,QAAQ,KAAK;AAAA,UACf;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AACD,UAAI,CAAC,SAAS,IAAI;AAChB,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,cAAc,SAAS,MAAM,KAAK,MAAM,SAAS,KAAK,CAAC;AAAA,QACzE,CAAC;AAAA,MACH;AACA,YAAM,SAAS,MAAM,SAAS,KAAK;AACnC,UAAI,OAAO,WAAW,WAAW;AAC/B,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,iBAAiB,KAAK,UAAU,MAAM,CAAC;AAAA,QACzD,CAAC;AAAA,MACH;AACA,aAAO;AAAA,QACL,QAAQ,OAAO;AAAA,QACf,UAAU,OAAO;AAAA,MACnB;AAAA,IACF,SAAS,KAAK;AACZ,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,wBAAyB,IAAc,SAAS,EAAE,KAAK,CAAC;AAAA,MAC1E,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": []}