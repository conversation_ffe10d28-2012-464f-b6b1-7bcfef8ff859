{"version": 3, "sources": ["../../../../src/browser/sync/web_socket_manager.ts"], "sourcesContent": ["import { Logger } from \"../logging.js\";\nimport {\n  ClientMessage,\n  encodeClientMessage,\n  parseServerMessage,\n  ServerMessage,\n} from \"./protocol.js\";\n\nconst CLOSE_NORMAL = 1000;\nconst CLOSE_GOING_AWAY = 1001;\nconst CLOSE_NO_STATUS = 1005;\n/** Convex-specific close code representing a \"404 Not Found\".\n * The edge Onramp accepts websocket upgrades before confirming that the\n * intended destination exists, so this code is sent once we've discovered that\n * the destination does not exist.\n */\nconst CLOSE_NOT_FOUND = 4040;\n\n/**\n * The various states our WebSocket can be in:\n *\n * - \"disconnected\": We don't have a WebSocket, but plan to create one.\n * - \"connecting\": We have created the WebSocket and are waiting for the\n *   `onOpen` callback.\n * - \"ready\": We have an open WebSocket.\n * - \"stopped\": The WebSocket was closed and a new one can be created via `.restart()`.\n * - \"terminated\": We have closed the WebSocket and will never create a new one.\n *\n *\n * WebSocket State Machine\n * -----------------------\n * initialState: disconnected\n * validTransitions:\n *   disconnected:\n *     new WebSocket() -> connecting\n *     terminate() -> terminated\n *   connecting:\n *     onopen -> ready\n *     close() -> disconnected\n *     terminate() -> terminated\n *   ready:\n *     close() -> disconnected\n *     stop() -> stopped\n *     terminate() -> terminated\n *   stopped:\n *     restart() -> connecting\n *     terminate() -> terminated\n * terminalStates:\n *   terminated\n *\n *\n *\n *                                        ┌────────────────┐\n *                ┌────terminate()────────│  disconnected  │◀─┐\n *                │                       └────────────────┘  │\n *                ▼                            │       ▲      │\n *       ┌────────────────┐           new WebSocket()  │      │\n *    ┌─▶│   terminated   │◀──────┐            │       │      │\n *    │  └────────────────┘       │            │       │      │\n *    │           ▲          terminate()       │    close() close()\n *    │      terminate()          │            │       │      │\n *    │           │               │            ▼       │      │\n *    │  ┌────────────────┐       └───────┌────────────────┐  │\n *    │  │    stopped     │──restart()───▶│   connecting   │  │\n *    │  └────────────────┘               └────────────────┘  │\n *    │           ▲                                │          │\n *    │           │                               onopen      │\n *    │           │                                │          │\n *    │           │                                ▼          │\n * terminate()    │                       ┌────────────────┐  │\n *    │           └────────stop()─────────│     ready      │──┘\n *    │                                   └────────────────┘\n *    │                                            │\n *    │                                            │\n *    └────────────────────────────────────────────┘\n *\n * The `connecting` and `ready` state have a sub-state-machine for pausing.\n */\n\ntype Socket =\n  | { state: \"disconnected\" }\n  | { state: \"connecting\"; ws: WebSocket; paused: \"yes\" | \"no\" }\n  | { state: \"ready\"; ws: WebSocket; paused: \"yes\" | \"no\" | \"uninitialized\" }\n  | { state: \"stopped\" }\n  | { state: \"terminated\" };\n\nexport type ReconnectMetadata = {\n  connectionCount: number;\n  lastCloseReason: string | null;\n};\n\nexport type OnMessageResponse = {\n  hasSyncedPastLastReconnect: boolean;\n};\n\nconst serverDisconnectErrors = {\n  // A known error, e.g. during a restart or push\n  InternalServerError: { timeout: 1000 },\n  // ErrorMetadata::overloaded() messages that we realy should back off\n  SubscriptionsWorkerFullError: { timeout: 3000 },\n  TooManyConcurrentRequests: { timeout: 3000 },\n  CommitterFullError: { timeout: 3000 },\n  AwsTooManyRequestsException: { timeout: 3000 },\n  ExecuteFullError: { timeout: 3000 },\n  SystemTimeoutError: { timeout: 3000 },\n  ExpiredInQueue: { timeout: 3000 },\n  // ErrorMetadata::feature_temporarily_unavailable() that typically indicate a deploy just happened\n  VectorIndexesUnavailable: { timeout: 1000 },\n  SearchIndexesUnavailable: { timeout: 1000 },\n  TableSummariesUnavailable: { timeout: 1000 },\n  // More ErrorMeatadata::overloaded()\n  VectorIndexTooLarge: { timeout: 3000 },\n  SearchIndexTooLarge: { timeout: 3000 },\n  TooManyWritesInTimePeriod: { timeout: 3000 },\n} as const satisfies Record<string, { timeout: number }>;\n\ntype ServerDisconnectError = keyof typeof serverDisconnectErrors | \"Unknown\";\n\nfunction classifyDisconnectError(s?: string): ServerDisconnectError {\n  if (s === undefined) return \"Unknown\";\n  // startsWith so more info could be at the end (although currently there isn't)\n\n  for (const prefix of Object.keys(\n    serverDisconnectErrors,\n  ) as ServerDisconnectError[]) {\n    if (s.startsWith(prefix)) {\n      return prefix;\n    }\n  }\n  return \"Unknown\";\n}\n\n/**\n * A wrapper around a websocket that handles errors, reconnection, and message\n * parsing.\n */\nexport class WebSocketManager {\n  private socket: Socket;\n\n  private connectionCount: number;\n  private _hasEverConnected: boolean = false;\n  private lastCloseReason:\n    | \"InitialConnect\"\n    | \"OnCloseInvoked\"\n    | (string & {}) // a full serverErrorReason (not just the prefix) or a new one\n    | null;\n\n  /** Upon HTTPS/WSS failure, the first jittered backoff duration, in ms. */\n  private readonly defaultInitialBackoff: number;\n\n  /** We backoff exponentially, but we need to cap that--this is the jittered max. */\n  private readonly maxBackoff: number;\n\n  /** How many times have we failed consecutively? */\n  private retries: number;\n\n  /** How long before lack of server response causes us to initiate a reconnect,\n   * in ms */\n  private readonly serverInactivityThreshold: number;\n\n  private reconnectDueToServerInactivityTimeout: ReturnType<\n    typeof setTimeout\n  > | null;\n\n  private readonly uri: string;\n  private readonly onOpen: (reconnectMetadata: ReconnectMetadata) => void;\n  private readonly onResume: () => void;\n  private readonly onMessage: (message: ServerMessage) => OnMessageResponse;\n  private readonly webSocketConstructor: typeof WebSocket;\n  private readonly logger: Logger;\n  private readonly onServerDisconnectError:\n    | ((message: string) => void)\n    | undefined;\n\n  constructor(\n    uri: string,\n    callbacks: {\n      onOpen: (reconnectMetadata: ReconnectMetadata) => void;\n      onResume: () => void;\n      onMessage: (message: ServerMessage) => OnMessageResponse;\n      onServerDisconnectError?: (message: string) => void;\n    },\n    webSocketConstructor: typeof WebSocket,\n    logger: Logger,\n    private readonly markConnectionStateDirty: () => void,\n  ) {\n    this.webSocketConstructor = webSocketConstructor;\n    this.socket = { state: \"disconnected\" };\n    this.connectionCount = 0;\n    this.lastCloseReason = \"InitialConnect\";\n\n    // backoff for unknown errors\n    this.defaultInitialBackoff = 1000;\n    this.maxBackoff = 16000;\n    this.retries = 0;\n\n    // Ping messages (sync protocol Pings, not WebSocket protocol Pings) are\n    // sent every 15s in the absence of other messages. But a single large\n    // Transition or other downstream message can hog the line so this\n    // threshold is set higher to prevent clients from giving up.\n    this.serverInactivityThreshold = 60000;\n    this.reconnectDueToServerInactivityTimeout = null;\n\n    this.uri = uri;\n    this.onOpen = callbacks.onOpen;\n    this.onResume = callbacks.onResume;\n    this.onMessage = callbacks.onMessage;\n    this.onServerDisconnectError = callbacks.onServerDisconnectError;\n    this.logger = logger;\n\n    this.connect();\n  }\n\n  private setSocketState(state: Socket) {\n    this.socket = state;\n    this._logVerbose(\n      `socket state changed: ${this.socket.state}, paused: ${\n        \"paused\" in this.socket ? this.socket.paused : undefined\n      }`,\n    );\n    this.markConnectionStateDirty();\n  }\n\n  private connect() {\n    if (this.socket.state === \"terminated\") {\n      return;\n    }\n    if (\n      this.socket.state !== \"disconnected\" &&\n      this.socket.state !== \"stopped\"\n    ) {\n      throw new Error(\n        \"Didn't start connection from disconnected state: \" + this.socket.state,\n      );\n    }\n\n    const ws = new this.webSocketConstructor(this.uri);\n    this._logVerbose(\"constructed WebSocket\");\n    this.setSocketState({\n      state: \"connecting\",\n      ws,\n      paused: \"no\",\n    });\n\n    // Kick off server inactivity timer before WebSocket connection is established\n    // so we can detect cases where handshake fails.\n    // The `onopen` event only fires after the connection is established:\n    // Source: https://datatracker.ietf.org/doc/html/rfc6455#page-19:~:text=_The%20WebSocket%20Connection%20is%20Established_,-and\n    this.resetServerInactivityTimeout();\n\n    ws.onopen = () => {\n      this.logger.logVerbose(\"begin ws.onopen\");\n      if (this.socket.state !== \"connecting\") {\n        throw new Error(\"onopen called with socket not in connecting state\");\n      }\n      this.setSocketState({\n        state: \"ready\",\n        ws,\n        paused: this.socket.paused === \"yes\" ? \"uninitialized\" : \"no\",\n      });\n      this.resetServerInactivityTimeout();\n      if (this.socket.paused === \"no\") {\n        this._hasEverConnected = true;\n        this.onOpen({\n          connectionCount: this.connectionCount,\n          lastCloseReason: this.lastCloseReason,\n        });\n      }\n\n      if (this.lastCloseReason !== \"InitialConnect\") {\n        this.logger.log(\"WebSocket reconnected\");\n      }\n\n      this.connectionCount += 1;\n      this.lastCloseReason = null;\n    };\n    // NB: The WebSocket API calls `onclose` even if connection fails, so we can route all error paths through `onclose`.\n    ws.onerror = (error) => {\n      const message = (error as ErrorEvent).message;\n      this.logger.log(`WebSocket error: ${message}`);\n    };\n    ws.onmessage = (message) => {\n      this.resetServerInactivityTimeout();\n      const serverMessage = parseServerMessage(JSON.parse(message.data));\n      this._logVerbose(`received ws message with type ${serverMessage.type}`);\n      const response = this.onMessage(serverMessage);\n      if (response.hasSyncedPastLastReconnect) {\n        // Reset backoff to 0 once all outstanding requests are complete.\n        this.retries = 0;\n        this.markConnectionStateDirty();\n      }\n    };\n    ws.onclose = (event) => {\n      this._logVerbose(\"begin ws.onclose\");\n      if (this.lastCloseReason === null) {\n        this.lastCloseReason = event.reason ?? \"OnCloseInvoked\";\n      }\n      if (\n        event.code !== CLOSE_NORMAL &&\n        event.code !== CLOSE_GOING_AWAY && // This commonly gets fired on mobile apps when the app is backgrounded\n        event.code !== CLOSE_NO_STATUS &&\n        event.code !== CLOSE_NOT_FOUND // Note that we want to retry on a 404, as it can be transient during a push.\n      ) {\n        let msg = `WebSocket closed with code ${event.code}`;\n        if (event.reason) {\n          msg += `: ${event.reason}`;\n        }\n        this.logger.log(msg);\n        if (this.onServerDisconnectError && event.reason) {\n          // This callback is a unstable API, InternalServerErrors in particular may be removed\n          // since they reflect expected temporary downtime. But until a quantitative measure\n          // of uptime is reported this unstable API errs on the inclusive side.\n          this.onServerDisconnectError(msg);\n        }\n      }\n      const reason = classifyDisconnectError(event.reason);\n      this.scheduleReconnect(reason);\n      return;\n    };\n  }\n\n  /**\n   * @returns The state of the {@link Socket}.\n   */\n  socketState(): string {\n    return this.socket.state;\n  }\n\n  /**\n   * @param message - A ClientMessage to send.\n   * @returns Whether the message (might have been) sent.\n   */\n  sendMessage(message: ClientMessage) {\n    const messageForLog = {\n      type: message.type,\n      ...(message.type === \"Authenticate\" && message.tokenType === \"User\"\n        ? {\n            value: `...${message.value.slice(-7)}`,\n          }\n        : {}),\n    };\n    if (this.socket.state === \"ready\" && this.socket.paused === \"no\") {\n      const encodedMessage = encodeClientMessage(message);\n      const request = JSON.stringify(encodedMessage);\n      try {\n        this.socket.ws.send(request);\n      } catch (error: any) {\n        this.logger.log(\n          `Failed to send message on WebSocket, reconnecting: ${error}`,\n        );\n        this.closeAndReconnect(\"FailedToSendMessage\");\n      }\n      // We are not sure if this was sent or not.\n      this._logVerbose(\n        `sent message with type ${message.type}: ${JSON.stringify(\n          messageForLog,\n        )}`,\n      );\n      return true;\n    }\n    this._logVerbose(\n      `message not sent (socket state: ${this.socket.state}, paused: ${\"paused\" in this.socket ? this.socket.paused : undefined}): ${JSON.stringify(\n        messageForLog,\n      )}`,\n    );\n\n    return false;\n  }\n\n  private resetServerInactivityTimeout() {\n    if (this.socket.state === \"terminated\") {\n      // Don't reset any timers if we were trying to terminate.\n      return;\n    }\n    if (this.reconnectDueToServerInactivityTimeout !== null) {\n      clearTimeout(this.reconnectDueToServerInactivityTimeout);\n      this.reconnectDueToServerInactivityTimeout = null;\n    }\n    this.reconnectDueToServerInactivityTimeout = setTimeout(() => {\n      this.closeAndReconnect(\"InactiveServer\");\n    }, this.serverInactivityThreshold);\n  }\n\n  private scheduleReconnect(reason: \"client\" | ServerDisconnectError) {\n    this.socket = { state: \"disconnected\" };\n    const backoff = this.nextBackoff(reason);\n    this.markConnectionStateDirty();\n    this.logger.log(`Attempting reconnect in ${backoff}ms`);\n    setTimeout(() => this.connect(), backoff);\n  }\n\n  /**\n   * Close the WebSocket and schedule a reconnect.\n   *\n   * This should be used when we hit an error and would like to restart the session.\n   */\n  private closeAndReconnect(closeReason: string) {\n    this._logVerbose(`begin closeAndReconnect with reason ${closeReason}`);\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"terminated\":\n      case \"stopped\":\n        // Nothing to do if we don't have a WebSocket.\n        return;\n      case \"connecting\":\n      case \"ready\": {\n        this.lastCloseReason = closeReason;\n        // Close the old socket asynchronously, we'll open a new socket in reconnect.\n        void this.close();\n        this.scheduleReconnect(\"client\");\n        return;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n  }\n\n  /**\n   * Close the WebSocket, being careful to clear the onclose handler to avoid re-entrant\n   * calls. Use this instead of directly calling `ws.close()`\n   *\n   * It is the callers responsibility to update the state after this method is called so that the\n   * closed socket is not accessible or used again after this method is called\n   */\n  private close(): Promise<void> {\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"terminated\":\n      case \"stopped\":\n        // Nothing to do if we don't have a WebSocket.\n        return Promise.resolve();\n      case \"connecting\": {\n        const ws = this.socket.ws;\n        return new Promise((r) => {\n          ws.onclose = () => {\n            this._logVerbose(\"Closed after connecting\");\n            r();\n          };\n          ws.onopen = () => {\n            this._logVerbose(\"Opened after connecting\");\n            ws.close();\n          };\n        });\n      }\n      case \"ready\": {\n        this._logVerbose(\"ws.close called\");\n        const ws = this.socket.ws;\n        const result: Promise<void> = new Promise((r) => {\n          ws.onclose = () => {\n            r();\n          };\n        });\n        ws.close();\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return Promise.resolve();\n      }\n    }\n  }\n\n  /**\n   * Close the WebSocket and do not reconnect.\n   * @returns A Promise that resolves when the WebSocket `onClose` callback is called.\n   */\n  terminate(): Promise<void> {\n    if (this.reconnectDueToServerInactivityTimeout) {\n      clearTimeout(this.reconnectDueToServerInactivityTimeout);\n    }\n    switch (this.socket.state) {\n      case \"terminated\":\n      case \"stopped\":\n      case \"disconnected\":\n      case \"connecting\":\n      case \"ready\": {\n        const result = this.close();\n        this.setSocketState({ state: \"terminated\" });\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        throw new Error(\n          `Invalid websocket state: ${(this.socket as any).state}`,\n        );\n      }\n    }\n  }\n\n  stop(): Promise<void> {\n    switch (this.socket.state) {\n      case \"terminated\":\n        // If we're terminating we ignore stop\n        return Promise.resolve();\n      case \"connecting\":\n      case \"stopped\":\n      case \"disconnected\":\n      case \"ready\": {\n        const result = this.close();\n        this.socket = { state: \"stopped\" };\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return Promise.resolve();\n      }\n    }\n  }\n\n  /**\n   * Create a new WebSocket after a previous `stop()`, unless `terminate()` was\n   * called before.\n   */\n  tryRestart(): void {\n    switch (this.socket.state) {\n      case \"stopped\":\n        break;\n      case \"terminated\":\n      case \"connecting\":\n      case \"ready\":\n      case \"disconnected\":\n        this.logger.logVerbose(\"Restart called without stopping first\");\n        return;\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n    this.connect();\n  }\n\n  pause(): void {\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"stopped\":\n      case \"terminated\":\n        // If already stopped or stopping ignore.\n        return;\n      case \"connecting\":\n      case \"ready\": {\n        this.socket = { ...this.socket, paused: \"yes\" };\n        return;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return;\n      }\n    }\n  }\n\n  /**\n   * Resume the state machine if previously paused.\n   */\n  resume(): void {\n    switch (this.socket.state) {\n      case \"connecting\":\n        this.socket = { ...this.socket, paused: \"no\" };\n        return;\n      case \"ready\":\n        if (this.socket.paused === \"uninitialized\") {\n          this.socket = { ...this.socket, paused: \"no\" };\n          this.onOpen({\n            connectionCount: this.connectionCount,\n            lastCloseReason: this.lastCloseReason,\n          });\n        } else if (this.socket.paused === \"yes\") {\n          this.socket = { ...this.socket, paused: \"no\" };\n          this.onResume();\n        }\n        return;\n      case \"terminated\":\n      case \"stopped\":\n      case \"disconnected\":\n        // Ignore resume if not paused, perhaps we already resumed.\n        return;\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n    this.connect();\n  }\n\n  connectionState(): {\n    isConnected: boolean;\n    hasEverConnected: boolean;\n    connectionCount: number;\n    connectionRetries: number;\n  } {\n    return {\n      isConnected: this.socket.state === \"ready\",\n      hasEverConnected: this._hasEverConnected,\n      connectionCount: this.connectionCount,\n      connectionRetries: this.retries,\n    };\n  }\n\n  private _logVerbose(message: string) {\n    this.logger.logVerbose(message);\n  }\n\n  private nextBackoff(reason: \"client\" | ServerDisconnectError): number {\n    const initialBackoff: number =\n      reason === \"client\"\n        ? 100 // There's no evidence of a server problem, retry quickly\n        : reason === \"Unknown\"\n          ? this.defaultInitialBackoff\n          : serverDisconnectErrors[reason].timeout;\n\n    const baseBackoff = initialBackoff * Math.pow(2, this.retries);\n    this.retries += 1;\n    const actualBackoff = Math.min(baseBackoff, this.maxBackoff);\n    const jitter = actualBackoff * (Math.random() - 0.5);\n    return actualBackoff + jitter;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAKO;AAEP,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,kBAAkB;AAMxB,MAAM,kBAAkB;AA+ExB,MAAM,yBAAyB;AAAA;AAAA,EAE7B,qBAAqB,EAAE,SAAS,IAAK;AAAA;AAAA,EAErC,8BAA8B,EAAE,SAAS,IAAK;AAAA,EAC9C,2BAA2B,EAAE,SAAS,IAAK;AAAA,EAC3C,oBAAoB,EAAE,SAAS,IAAK;AAAA,EACpC,6BAA6B,EAAE,SAAS,IAAK;AAAA,EAC7C,kBAAkB,EAAE,SAAS,IAAK;AAAA,EAClC,oBAAoB,EAAE,SAAS,IAAK;AAAA,EACpC,gBAAgB,EAAE,SAAS,IAAK;AAAA;AAAA,EAEhC,0BAA0B,EAAE,SAAS,IAAK;AAAA,EAC1C,0BAA0B,EAAE,SAAS,IAAK;AAAA,EAC1C,2BAA2B,EAAE,SAAS,IAAK;AAAA;AAAA,EAE3C,qBAAqB,EAAE,SAAS,IAAK;AAAA,EACrC,qBAAqB,EAAE,SAAS,IAAK;AAAA,EACrC,2BAA2B,EAAE,SAAS,IAAK;AAC7C;AAIA,SAAS,wBAAwB,GAAmC;AAClE,MAAI,MAAM,OAAW,QAAO;AAG5B,aAAW,UAAU,OAAO;AAAA,IAC1B;AAAA,EACF,GAA8B;AAC5B,QAAI,EAAE,WAAW,MAAM,GAAG;AACxB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAMO,MAAM,iBAAiB;AAAA,EAsC5B,YACE,KACA,WAMA,sBACA,QACiB,0BACjB;AADiB;AA/CnB,wBAAQ;AAER,wBAAQ;AACR,wBAAQ,qBAA6B;AACrC,wBAAQ;AAOR;AAAA,wBAAiB;AAGjB;AAAA,wBAAiB;AAGjB;AAAA,wBAAQ;AAIR;AAAA;AAAA,wBAAiB;AAEjB,wBAAQ;AAIR,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AAgBf,SAAK,uBAAuB;AAC5B,SAAK,SAAS,EAAE,OAAO,eAAe;AACtC,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAGvB,SAAK,wBAAwB;AAC7B,SAAK,aAAa;AAClB,SAAK,UAAU;AAMf,SAAK,4BAA4B;AACjC,SAAK,wCAAwC;AAE7C,SAAK,MAAM;AACX,SAAK,SAAS,UAAU;AACxB,SAAK,WAAW,UAAU;AAC1B,SAAK,YAAY,UAAU;AAC3B,SAAK,0BAA0B,UAAU;AACzC,SAAK,SAAS;AAEd,SAAK,QAAQ;AAAA,EACf;AAAA,EAEQ,eAAe,OAAe;AACpC,SAAK,SAAS;AACd,SAAK;AAAA,MACH,yBAAyB,KAAK,OAAO,KAAK,aACxC,YAAY,KAAK,SAAS,KAAK,OAAO,SAAS,MACjD;AAAA,IACF;AACA,SAAK,yBAAyB;AAAA,EAChC;AAAA,EAEQ,UAAU;AAChB,QAAI,KAAK,OAAO,UAAU,cAAc;AACtC;AAAA,IACF;AACA,QACE,KAAK,OAAO,UAAU,kBACtB,KAAK,OAAO,UAAU,WACtB;AACA,YAAM,IAAI;AAAA,QACR,sDAAsD,KAAK,OAAO;AAAA,MACpE;AAAA,IACF;AAEA,UAAM,KAAK,IAAI,KAAK,qBAAqB,KAAK,GAAG;AACjD,SAAK,YAAY,uBAAuB;AACxC,SAAK,eAAe;AAAA,MAClB,OAAO;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,IACV,CAAC;AAMD,SAAK,6BAA6B;AAElC,OAAG,SAAS,MAAM;AAChB,WAAK,OAAO,WAAW,iBAAiB;AACxC,UAAI,KAAK,OAAO,UAAU,cAAc;AACtC,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AACA,WAAK,eAAe;AAAA,QAClB,OAAO;AAAA,QACP;AAAA,QACA,QAAQ,KAAK,OAAO,WAAW,QAAQ,kBAAkB;AAAA,MAC3D,CAAC;AACD,WAAK,6BAA6B;AAClC,UAAI,KAAK,OAAO,WAAW,MAAM;AAC/B,aAAK,oBAAoB;AACzB,aAAK,OAAO;AAAA,UACV,iBAAiB,KAAK;AAAA,UACtB,iBAAiB,KAAK;AAAA,QACxB,CAAC;AAAA,MACH;AAEA,UAAI,KAAK,oBAAoB,kBAAkB;AAC7C,aAAK,OAAO,IAAI,uBAAuB;AAAA,MACzC;AAEA,WAAK,mBAAmB;AACxB,WAAK,kBAAkB;AAAA,IACzB;AAEA,OAAG,UAAU,CAAC,UAAU;AACtB,YAAM,UAAW,MAAqB;AACtC,WAAK,OAAO,IAAI,oBAAoB,OAAO,EAAE;AAAA,IAC/C;AACA,OAAG,YAAY,CAAC,YAAY;AAC1B,WAAK,6BAA6B;AAClC,YAAM,oBAAgB,oCAAmB,KAAK,MAAM,QAAQ,IAAI,CAAC;AACjE,WAAK,YAAY,iCAAiC,cAAc,IAAI,EAAE;AACtE,YAAM,WAAW,KAAK,UAAU,aAAa;AAC7C,UAAI,SAAS,4BAA4B;AAEvC,aAAK,UAAU;AACf,aAAK,yBAAyB;AAAA,MAChC;AAAA,IACF;AACA,OAAG,UAAU,CAAC,UAAU;AACtB,WAAK,YAAY,kBAAkB;AACnC,UAAI,KAAK,oBAAoB,MAAM;AACjC,aAAK,kBAAkB,MAAM,UAAU;AAAA,MACzC;AACA,UACE,MAAM,SAAS,gBACf,MAAM,SAAS;AAAA,MACf,MAAM,SAAS,mBACf,MAAM,SAAS,iBACf;AACA,YAAI,MAAM,8BAA8B,MAAM,IAAI;AAClD,YAAI,MAAM,QAAQ;AAChB,iBAAO,KAAK,MAAM,MAAM;AAAA,QAC1B;AACA,aAAK,OAAO,IAAI,GAAG;AACnB,YAAI,KAAK,2BAA2B,MAAM,QAAQ;AAIhD,eAAK,wBAAwB,GAAG;AAAA,QAClC;AAAA,MACF;AACA,YAAM,SAAS,wBAAwB,MAAM,MAAM;AACnD,WAAK,kBAAkB,MAAM;AAC7B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,cAAsB;AACpB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAwB;AAClC,UAAM,gBAAgB;AAAA,MACpB,MAAM,QAAQ;AAAA,MACd,GAAI,QAAQ,SAAS,kBAAkB,QAAQ,cAAc,SACzD;AAAA,QACE,OAAO,MAAM,QAAQ,MAAM,MAAM,EAAE,CAAC;AAAA,MACtC,IACA,CAAC;AAAA,IACP;AACA,QAAI,KAAK,OAAO,UAAU,WAAW,KAAK,OAAO,WAAW,MAAM;AAChE,YAAM,qBAAiB,qCAAoB,OAAO;AAClD,YAAM,UAAU,KAAK,UAAU,cAAc;AAC7C,UAAI;AACF,aAAK,OAAO,GAAG,KAAK,OAAO;AAAA,MAC7B,SAAS,OAAY;AACnB,aAAK,OAAO;AAAA,UACV,sDAAsD,KAAK;AAAA,QAC7D;AACA,aAAK,kBAAkB,qBAAqB;AAAA,MAC9C;AAEA,WAAK;AAAA,QACH,0BAA0B,QAAQ,IAAI,KAAK,KAAK;AAAA,UAC9C;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AACA,SAAK;AAAA,MACH,mCAAmC,KAAK,OAAO,KAAK,aAAa,YAAY,KAAK,SAAS,KAAK,OAAO,SAAS,MAAS,MAAM,KAAK;AAAA,QAClI;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,+BAA+B;AACrC,QAAI,KAAK,OAAO,UAAU,cAAc;AAEtC;AAAA,IACF;AACA,QAAI,KAAK,0CAA0C,MAAM;AACvD,mBAAa,KAAK,qCAAqC;AACvD,WAAK,wCAAwC;AAAA,IAC/C;AACA,SAAK,wCAAwC,WAAW,MAAM;AAC5D,WAAK,kBAAkB,gBAAgB;AAAA,IACzC,GAAG,KAAK,yBAAyB;AAAA,EACnC;AAAA,EAEQ,kBAAkB,QAA0C;AAClE,SAAK,SAAS,EAAE,OAAO,eAAe;AACtC,UAAM,UAAU,KAAK,YAAY,MAAM;AACvC,SAAK,yBAAyB;AAC9B,SAAK,OAAO,IAAI,2BAA2B,OAAO,IAAI;AACtD,eAAW,MAAM,KAAK,QAAQ,GAAG,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,kBAAkB,aAAqB;AAC7C,SAAK,YAAY,uCAAuC,WAAW,EAAE;AACrE,YAAQ,KAAK,OAAO,OAAO;AAAA,MACzB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH;AAAA,MACF,KAAK;AAAA,MACL,KAAK,SAAS;AACZ,aAAK,kBAAkB;AAEvB,aAAK,KAAK,MAAM;AAChB,aAAK,kBAAkB,QAAQ;AAC/B;AAAA,MACF;AAAA,MACA,SAAS;AAEP,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,QAAuB;AAC7B,YAAQ,KAAK,OAAO,OAAO;AAAA,MACzB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK,cAAc;AACjB,cAAM,KAAK,KAAK,OAAO;AACvB,eAAO,IAAI,QAAQ,CAAC,MAAM;AACxB,aAAG,UAAU,MAAM;AACjB,iBAAK,YAAY,yBAAyB;AAC1C,cAAE;AAAA,UACJ;AACA,aAAG,SAAS,MAAM;AAChB,iBAAK,YAAY,yBAAyB;AAC1C,eAAG,MAAM;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,KAAK,SAAS;AACZ,aAAK,YAAY,iBAAiB;AAClC,cAAM,KAAK,KAAK,OAAO;AACvB,cAAM,SAAwB,IAAI,QAAQ,CAAC,MAAM;AAC/C,aAAG,UAAU,MAAM;AACjB,cAAE;AAAA,UACJ;AAAA,QACF,CAAC;AACD,WAAG,MAAM;AACT,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AAEP,aAAK;AACL,eAAO,QAAQ,QAAQ;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAA2B;AACzB,QAAI,KAAK,uCAAuC;AAC9C,mBAAa,KAAK,qCAAqC;AAAA,IACzD;AACA,YAAQ,KAAK,OAAO,OAAO;AAAA,MACzB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,SAAS;AACZ,cAAM,SAAS,KAAK,MAAM;AAC1B,aAAK,eAAe,EAAE,OAAO,aAAa,CAAC;AAC3C,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AAEP,aAAK;AACL,cAAM,IAAI;AAAA,UACR,4BAA6B,KAAK,OAAe,KAAK;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAsB;AACpB,YAAQ,KAAK,OAAO,OAAO;AAAA,MACzB,KAAK;AAEH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,SAAS;AACZ,cAAM,SAAS,KAAK,MAAM;AAC1B,aAAK,SAAS,EAAE,OAAO,UAAU;AACjC,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AAEP,aAAK;AACL,eAAO,QAAQ,QAAQ;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAmB;AACjB,YAAQ,KAAK,OAAO,OAAO;AAAA,MACzB,KAAK;AACH;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,OAAO,WAAW,uCAAuC;AAC9D;AAAA,MACF,SAAS;AAEP,aAAK;AAAA,MACP;AAAA,IACF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,QAAc;AACZ,YAAQ,KAAK,OAAO,OAAO;AAAA,MACzB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH;AAAA,MACF,KAAK;AAAA,MACL,KAAK,SAAS;AACZ,aAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,QAAQ,MAAM;AAC9C;AAAA,MACF;AAAA,MACA,SAAS;AAEP,aAAK;AACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAe;AACb,YAAQ,KAAK,OAAO,OAAO;AAAA,MACzB,KAAK;AACH,aAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,QAAQ,KAAK;AAC7C;AAAA,MACF,KAAK;AACH,YAAI,KAAK,OAAO,WAAW,iBAAiB;AAC1C,eAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,QAAQ,KAAK;AAC7C,eAAK,OAAO;AAAA,YACV,iBAAiB,KAAK;AAAA,YACtB,iBAAiB,KAAK;AAAA,UACxB,CAAC;AAAA,QACH,WAAW,KAAK,OAAO,WAAW,OAAO;AACvC,eAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,QAAQ,KAAK;AAC7C,eAAK,SAAS;AAAA,QAChB;AACA;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH;AAAA,MACF,SAAS;AAEP,aAAK;AAAA,MACP;AAAA,IACF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,kBAKE;AACA,WAAO;AAAA,MACL,aAAa,KAAK,OAAO,UAAU;AAAA,MACnC,kBAAkB,KAAK;AAAA,MACvB,iBAAiB,KAAK;AAAA,MACtB,mBAAmB,KAAK;AAAA,IAC1B;AAAA,EACF;AAAA,EAEQ,YAAY,SAAiB;AACnC,SAAK,OAAO,WAAW,OAAO;AAAA,EAChC;AAAA,EAEQ,YAAY,QAAkD;AACpE,UAAM,iBACJ,WAAW,WACP,MACA,WAAW,YACT,KAAK,wBACL,uBAAuB,MAAM,EAAE;AAEvC,UAAM,cAAc,iBAAiB,KAAK,IAAI,GAAG,KAAK,OAAO;AAC7D,SAAK,WAAW;AAChB,UAAM,gBAAgB,KAAK,IAAI,aAAa,KAAK,UAAU;AAC3D,UAAM,SAAS,iBAAiB,KAAK,OAAO,IAAI;AAChD,WAAO,gBAAgB;AAAA,EACzB;AACF;", "names": []}