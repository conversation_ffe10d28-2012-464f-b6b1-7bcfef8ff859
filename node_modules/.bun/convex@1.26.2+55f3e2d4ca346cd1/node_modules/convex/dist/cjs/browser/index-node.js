"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var browser_exports = {};
__export(browser_exports, {
  BaseConvexClient: () => import_client.BaseConvexClient,
  ConvexClient: () => import_simple_client.ConvexClient,
  ConvexHttpClient: () => import_http_client.ConvexHttpClient
});
module.exports = __toCommonJS(browser_exports);
var import_client = require("./sync/client.js");
var import_simple_client = require("./simple_client-node.js");
var import_http_client = require("./http_client.js");
//# sourceMappingURL=index.js.map
