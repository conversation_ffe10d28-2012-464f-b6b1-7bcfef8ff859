{"version": 3, "sources": ["../../../../src/browser/sync/request_manager.ts"], "sourcesContent": ["import { jsonToConvex } from \"../../values/index.js\";\nimport { logForFunction, Logger } from \"../logging.js\";\nimport { Long } from \"../long.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport {\n  ActionRequest,\n  ActionResponse,\n  ClientMessage,\n  MutationRequest,\n  MutationResponse,\n  RequestId,\n} from \"./protocol.js\";\n\ntype RequestStatus =\n  | {\n      status: \"Requested\" | \"NotSent\";\n      onResult: (result: FunctionResult) => void;\n      requestedAt: Date;\n    }\n  | {\n      status: \"Completed\";\n      result: FunctionResult;\n      onResolve: () => void;\n      ts: Long;\n    };\n\nexport class RequestManager {\n  private inflightRequests: Map<\n    RequestId,\n    {\n      message: MutationRequest | ActionRequest;\n      status: RequestStatus;\n    }\n  >;\n  private requestsOlderThanRestart: Set<RequestId>;\n  private inflightMutationsCount: number = 0;\n  private inflightActionsCount: number = 0;\n  constructor(\n    private readonly logger: Logger,\n    private readonly markConnectionStateDirty: () => void,\n  ) {\n    this.inflightRequests = new Map();\n    this.requestsOlderThanRestart = new Set();\n  }\n\n  request(\n    message: MutationRequest | ActionRequest,\n    sent: boolean,\n  ): Promise<FunctionResult> {\n    const result = new Promise<FunctionResult>((resolve) => {\n      const status = sent ? \"Requested\" : \"NotSent\";\n      this.inflightRequests.set(message.requestId, {\n        message,\n        status: { status, requestedAt: new Date(), onResult: resolve },\n      });\n\n      if (message.type === \"Mutation\") {\n        this.inflightMutationsCount++;\n      } else if (message.type === \"Action\") {\n        this.inflightActionsCount++;\n      }\n    });\n\n    this.markConnectionStateDirty();\n    return result;\n  }\n\n  /**\n   * Update the state after receiving a response.\n   *\n   * @returns A RequestId if the request is complete and its optimistic update\n   * can be dropped, null otherwise.\n   */\n  onResponse(\n    response: MutationResponse | ActionResponse,\n  ): { requestId: RequestId; result: FunctionResult } | null {\n    const requestInfo = this.inflightRequests.get(response.requestId);\n    if (requestInfo === undefined) {\n      // Annoyingly we can occasionally get responses to mutations that we're no\n      // longer tracking. One flow where this happens is:\n      // 1. Client sends mutation 1\n      // 2. Client gets response for mutation 1. The sever says that it was committed at ts=10.\n      // 3. Client is disconnected\n      // 4. Client reconnects and re-issues queries and this mutation.\n      // 5. Server sends transition message to ts=20\n      // 6. Client drops mutation because it's already been observed.\n      // 7. Client receives a second response for mutation 1 but doesn't know about it anymore.\n\n      // The right fix for this is probably to add a reconciliation phase on\n      // reconnection where we receive responses to all the mutations before\n      // the transition message so this flow could never happen (CX-1513).\n\n      // For now though, we can just ignore this message.\n      return null;\n    }\n\n    // Because `.restart()` re-requests completed requests, we may get some\n    // responses for requests that are already in the \"Completed\" state.\n    // We can safely ignore those because we've already notified the UI about\n    // their results.\n    if (requestInfo.status.status === \"Completed\") {\n      return null;\n    }\n\n    const udfType =\n      requestInfo.message.type === \"Mutation\" ? \"mutation\" : \"action\";\n    const udfPath = requestInfo.message.udfPath;\n\n    for (const line of response.logLines) {\n      logForFunction(this.logger, \"info\", udfType, udfPath, line);\n    }\n\n    const status = requestInfo.status;\n    let result: FunctionResult;\n    let onResolve;\n    if (response.success) {\n      result = {\n        success: true,\n        logLines: response.logLines,\n        value: jsonToConvex(response.result),\n      };\n      onResolve = () => status.onResult(result);\n    } else {\n      const errorMessage = response.result as string;\n      const { errorData } = response;\n      logForFunction(this.logger, \"error\", udfType, udfPath, errorMessage);\n      result = {\n        success: false,\n        errorMessage,\n        errorData:\n          errorData !== undefined ? jsonToConvex(errorData) : undefined,\n        logLines: response.logLines,\n      };\n      onResolve = () => status.onResult(result);\n    }\n\n    // We can resolve Mutation failures immediately since they don't have any\n    // side effects. Actions are intentionally decoupled from\n    // queries/mutations here on the sync protocol since they have different\n    // guarantees.\n    if (response.type === \"ActionResponse\" || !response.success) {\n      onResolve();\n      this.inflightRequests.delete(response.requestId);\n      this.requestsOlderThanRestart.delete(response.requestId);\n\n      if (requestInfo.message.type === \"Action\") {\n        this.inflightActionsCount--;\n      } else if (requestInfo.message.type === \"Mutation\") {\n        this.inflightMutationsCount--;\n      }\n\n      this.markConnectionStateDirty();\n      return { requestId: response.requestId, result };\n    }\n\n    // We have to wait to resolve the request promise until after we transition\n    // past this timestamp so clients can read their own writes.\n    requestInfo.status = {\n      status: \"Completed\",\n      result,\n      ts: response.ts,\n      onResolve,\n    };\n\n    return null;\n  }\n\n  // Remove and returns completed requests.\n  removeCompleted(ts: Long): Map<RequestId, FunctionResult> {\n    const completeRequests: Map<RequestId, FunctionResult> = new Map();\n    for (const [requestId, requestInfo] of this.inflightRequests.entries()) {\n      const status = requestInfo.status;\n      if (status.status === \"Completed\" && status.ts.lessThanOrEqual(ts)) {\n        status.onResolve();\n        completeRequests.set(requestId, status.result);\n\n        if (requestInfo.message.type === \"Mutation\") {\n          this.inflightMutationsCount--;\n        } else if (requestInfo.message.type === \"Action\") {\n          this.inflightActionsCount--;\n        }\n\n        this.inflightRequests.delete(requestId);\n        this.requestsOlderThanRestart.delete(requestId);\n      }\n    }\n    if (completeRequests.size > 0) {\n      this.markConnectionStateDirty();\n    }\n    return completeRequests;\n  }\n\n  restart(): ClientMessage[] {\n    // When we reconnect to the backend, re-request all requests that are safe\n    // to be resend.\n\n    this.requestsOlderThanRestart = new Set(this.inflightRequests.keys());\n    const allMessages = [];\n    for (const [requestId, value] of this.inflightRequests) {\n      if (value.status.status === \"NotSent\") {\n        value.status.status = \"Requested\";\n        allMessages.push(value.message);\n        continue;\n      }\n\n      if (value.message.type === \"Mutation\") {\n        // This includes ones that have already been completed because we still\n        // want to tell the backend to transition the client past the completed\n        // timestamp. This is safe since mutations are idempotent.\n        allMessages.push(value.message);\n      } else if (value.message.type === \"Action\") {\n        // Unlike mutations, actions are not idempotent. When we reconnect to the\n        // backend, we don't know if it is safe to resend in-flight actions, so we\n        // cancel them and consider them failed.\n        this.inflightRequests.delete(requestId);\n        this.requestsOlderThanRestart.delete(requestId);\n        this.inflightActionsCount--;\n        if (value.status.status === \"Completed\") {\n          throw new Error(\"Action should never be in 'Completed' state\");\n        }\n        value.status.onResult({\n          success: false,\n          errorMessage: \"Connection lost while action was in flight\",\n          logLines: [],\n        });\n      }\n    }\n    this.markConnectionStateDirty();\n    return allMessages;\n  }\n\n  resume(): ClientMessage[] {\n    const allMessages = [];\n    for (const [, value] of this.inflightRequests) {\n      if (value.status.status === \"NotSent\") {\n        value.status.status = \"Requested\";\n        allMessages.push(value.message);\n        continue;\n      }\n    }\n    return allMessages;\n  }\n\n  /**\n   * @returns true if there are any requests that have been requested but have\n   * not be completed yet.\n   */\n  hasIncompleteRequests(): boolean {\n    for (const requestInfo of this.inflightRequests.values()) {\n      if (requestInfo.status.status === \"Requested\") {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @returns true if there are any inflight requests, including ones that have\n   * completed on the server, but have not been applied.\n   */\n  hasInflightRequests(): boolean {\n    return this.inflightRequests.size > 0;\n  }\n\n  /**\n   * @returns true if there are any inflight requests, that have been hanging around\n   * since prior to the most recent restart.\n   */\n  hasSyncedPastLastReconnect(): boolean {\n    return this.requestsOlderThanRestart.size === 0;\n  }\n\n  timeOfOldestInflightRequest(): Date | null {\n    if (this.inflightRequests.size === 0) {\n      return null;\n    }\n    let oldestInflightRequest = Date.now();\n    for (const request of this.inflightRequests.values()) {\n      if (request.status.status !== \"Completed\") {\n        if (request.status.requestedAt.getTime() < oldestInflightRequest) {\n          oldestInflightRequest = request.status.requestedAt.getTime();\n        }\n      }\n    }\n    return new Date(oldestInflightRequest);\n  }\n\n  /**\n   * @returns The number of mutations currently in flight.\n   */\n  inflightMutations(): number {\n    return this.inflightMutationsCount;\n  }\n\n  /**\n   * @returns The number of actions currently in flight.\n   */\n  inflightActions(): number {\n    return this.inflightActionsCount;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA6B;AAC7B,qBAAuC;AAyBhC,MAAM,eAAe;AAAA,EAW1B,YACmB,QACA,0BACjB;AAFiB;AACA;AAZnB,wBAAQ;AAOR,wBAAQ;AACR,wBAAQ,0BAAiC;AACzC,wBAAQ,wBAA+B;AAKrC,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,2BAA2B,oBAAI,IAAI;AAAA,EAC1C;AAAA,EAEA,QACE,SACA,MACyB;AACzB,UAAM,SAAS,IAAI,QAAwB,CAAC,YAAY;AACtD,YAAM,SAAS,OAAO,cAAc;AACpC,WAAK,iBAAiB,IAAI,QAAQ,WAAW;AAAA,QAC3C;AAAA,QACA,QAAQ,EAAE,QAAQ,aAAa,oBAAI,KAAK,GAAG,UAAU,QAAQ;AAAA,MAC/D,CAAC;AAED,UAAI,QAAQ,SAAS,YAAY;AAC/B,aAAK;AAAA,MACP,WAAW,QAAQ,SAAS,UAAU;AACpC,aAAK;AAAA,MACP;AAAA,IACF,CAAC;AAED,SAAK,yBAAyB;AAC9B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WACE,UACyD;AACzD,UAAM,cAAc,KAAK,iBAAiB,IAAI,SAAS,SAAS;AAChE,QAAI,gBAAgB,QAAW;AAgB7B,aAAO;AAAA,IACT;AAMA,QAAI,YAAY,OAAO,WAAW,aAAa;AAC7C,aAAO;AAAA,IACT;AAEA,UAAM,UACJ,YAAY,QAAQ,SAAS,aAAa,aAAa;AACzD,UAAM,UAAU,YAAY,QAAQ;AAEpC,eAAW,QAAQ,SAAS,UAAU;AACpC,yCAAe,KAAK,QAAQ,QAAQ,SAAS,SAAS,IAAI;AAAA,IAC5D;AAEA,UAAM,SAAS,YAAY;AAC3B,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,SAAS;AACpB,eAAS;AAAA,QACP,SAAS;AAAA,QACT,UAAU,SAAS;AAAA,QACnB,WAAO,4BAAa,SAAS,MAAM;AAAA,MACrC;AACA,kBAAY,MAAM,OAAO,SAAS,MAAM;AAAA,IAC1C,OAAO;AACL,YAAM,eAAe,SAAS;AAC9B,YAAM,EAAE,UAAU,IAAI;AACtB,yCAAe,KAAK,QAAQ,SAAS,SAAS,SAAS,YAAY;AACnE,eAAS;AAAA,QACP,SAAS;AAAA,QACT;AAAA,QACA,WACE,cAAc,aAAY,4BAAa,SAAS,IAAI;AAAA,QACtD,UAAU,SAAS;AAAA,MACrB;AACA,kBAAY,MAAM,OAAO,SAAS,MAAM;AAAA,IAC1C;AAMA,QAAI,SAAS,SAAS,oBAAoB,CAAC,SAAS,SAAS;AAC3D,gBAAU;AACV,WAAK,iBAAiB,OAAO,SAAS,SAAS;AAC/C,WAAK,yBAAyB,OAAO,SAAS,SAAS;AAEvD,UAAI,YAAY,QAAQ,SAAS,UAAU;AACzC,aAAK;AAAA,MACP,WAAW,YAAY,QAAQ,SAAS,YAAY;AAClD,aAAK;AAAA,MACP;AAEA,WAAK,yBAAyB;AAC9B,aAAO,EAAE,WAAW,SAAS,WAAW,OAAO;AAAA,IACjD;AAIA,gBAAY,SAAS;AAAA,MACnB,QAAQ;AAAA,MACR;AAAA,MACA,IAAI,SAAS;AAAA,MACb;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,gBAAgB,IAA0C;AACxD,UAAM,mBAAmD,oBAAI,IAAI;AACjE,eAAW,CAAC,WAAW,WAAW,KAAK,KAAK,iBAAiB,QAAQ,GAAG;AACtE,YAAM,SAAS,YAAY;AAC3B,UAAI,OAAO,WAAW,eAAe,OAAO,GAAG,gBAAgB,EAAE,GAAG;AAClE,eAAO,UAAU;AACjB,yBAAiB,IAAI,WAAW,OAAO,MAAM;AAE7C,YAAI,YAAY,QAAQ,SAAS,YAAY;AAC3C,eAAK;AAAA,QACP,WAAW,YAAY,QAAQ,SAAS,UAAU;AAChD,eAAK;AAAA,QACP;AAEA,aAAK,iBAAiB,OAAO,SAAS;AACtC,aAAK,yBAAyB,OAAO,SAAS;AAAA,MAChD;AAAA,IACF;AACA,QAAI,iBAAiB,OAAO,GAAG;AAC7B,WAAK,yBAAyB;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,UAA2B;AAIzB,SAAK,2BAA2B,IAAI,IAAI,KAAK,iBAAiB,KAAK,CAAC;AACpE,UAAM,cAAc,CAAC;AACrB,eAAW,CAAC,WAAW,KAAK,KAAK,KAAK,kBAAkB;AACtD,UAAI,MAAM,OAAO,WAAW,WAAW;AACrC,cAAM,OAAO,SAAS;AACtB,oBAAY,KAAK,MAAM,OAAO;AAC9B;AAAA,MACF;AAEA,UAAI,MAAM,QAAQ,SAAS,YAAY;AAIrC,oBAAY,KAAK,MAAM,OAAO;AAAA,MAChC,WAAW,MAAM,QAAQ,SAAS,UAAU;AAI1C,aAAK,iBAAiB,OAAO,SAAS;AACtC,aAAK,yBAAyB,OAAO,SAAS;AAC9C,aAAK;AACL,YAAI,MAAM,OAAO,WAAW,aAAa;AACvC,gBAAM,IAAI,MAAM,6CAA6C;AAAA,QAC/D;AACA,cAAM,OAAO,SAAS;AAAA,UACpB,SAAS;AAAA,UACT,cAAc;AAAA,UACd,UAAU,CAAC;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,yBAAyB;AAC9B,WAAO;AAAA,EACT;AAAA,EAEA,SAA0B;AACxB,UAAM,cAAc,CAAC;AACrB,eAAW,CAAC,EAAE,KAAK,KAAK,KAAK,kBAAkB;AAC7C,UAAI,MAAM,OAAO,WAAW,WAAW;AACrC,cAAM,OAAO,SAAS;AACtB,oBAAY,KAAK,MAAM,OAAO;AAC9B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAiC;AAC/B,eAAW,eAAe,KAAK,iBAAiB,OAAO,GAAG;AACxD,UAAI,YAAY,OAAO,WAAW,aAAa;AAC7C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAA+B;AAC7B,WAAO,KAAK,iBAAiB,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAAsC;AACpC,WAAO,KAAK,yBAAyB,SAAS;AAAA,EAChD;AAAA,EAEA,8BAA2C;AACzC,QAAI,KAAK,iBAAiB,SAAS,GAAG;AACpC,aAAO;AAAA,IACT;AACA,QAAI,wBAAwB,KAAK,IAAI;AACrC,eAAW,WAAW,KAAK,iBAAiB,OAAO,GAAG;AACpD,UAAI,QAAQ,OAAO,WAAW,aAAa;AACzC,YAAI,QAAQ,OAAO,YAAY,QAAQ,IAAI,uBAAuB;AAChE,kCAAwB,QAAQ,OAAO,YAAY,QAAQ;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AACA,WAAO,IAAI,KAAK,qBAAqB;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAA4B;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,kBAA0B;AACxB,WAAO,KAAK;AAAA,EACd;AACF;", "names": []}