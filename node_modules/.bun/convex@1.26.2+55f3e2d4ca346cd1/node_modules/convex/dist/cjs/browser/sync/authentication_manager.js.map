{"version": 3, "sources": ["../../../../src/browser/sync/authentication_manager.ts"], "sourcesContent": ["import { Logger } from \"../logging.js\";\nimport { LocalSyncState } from \"./local_state.js\";\nimport { AuthError, IdentityVersion, Transition } from \"./protocol.js\";\nimport { jwtDecode } from \"jwt-decode\";\n\n// setTimout uses 32 bit integer, so it can only\n// schedule about 24 days in the future.\nconst MAXIMUM_REFRESH_DELAY = 20 * 24 * 60 * 60 * 1000; // 20 days\n\nconst MAX_TOKEN_CONFIRMATION_ATTEMPTS = 2;\n\n/**\n * An async function returning a JWT. Depending on the auth providers\n * configured in convex/auth.config.ts, this may be a JWT-encoded OpenID\n * Connect Identity Token or a traditional JWT.\n *\n * `forceRefreshToken` is `true` if the server rejected a previously\n * returned token or the token is anticipated to expiring soon\n * based on its `exp` time.\n *\n * See {@link ConvexReactClient.setAuth}.\n *\n * @public\n */\nexport type AuthTokenFetcher = (args: {\n  forceRefreshToken: boolean;\n}) => Promise<string | null | undefined>;\n\n/**\n * What is provided to the client.\n */\ntype AuthConfig = {\n  fetchToken: AuthTokenFetcher;\n  onAuthChange: (isAuthenticated: boolean) => void;\n};\n\n/**\n * In general we take 3 steps:\n *   1. Fetch a possibly cached token\n *   2. Immediately fetch a fresh token without using a cache\n *   3. Repeat step 2 before the end of the fresh token's lifetime\n *\n * When we fetch without using a cache we know when the token\n * will expire, and can schedule refetching it.\n *\n * If we get an error before a scheduled refetch, we go back\n * to step 2.\n */\ntype AuthState =\n  | { state: \"noAuth\" }\n  | {\n      state: \"waitingForServerConfirmationOfCachedToken\";\n      config: AuthConfig;\n      hasRetried: boolean;\n    }\n  | {\n      state: \"initialRefetch\";\n      config: AuthConfig;\n    }\n  | {\n      state: \"waitingForServerConfirmationOfFreshToken\";\n      config: AuthConfig;\n      hadAuth: boolean;\n      token: string;\n    }\n  | {\n      state: \"waitingForScheduledRefetch\";\n      config: AuthConfig;\n      refetchTokenTimeoutId: ReturnType<typeof setTimeout>;\n    }\n  // Special/weird state when we got a valid token\n  // but could not fetch a new one.\n  | {\n      state: \"notRefetching\";\n      config: AuthConfig;\n    };\n\n/**\n * Handles the state transitions for auth. The server is the source\n * of truth.\n */\nexport class AuthenticationManager {\n  private authState: AuthState = { state: \"noAuth\" };\n  // Used to detect races involving `setConfig` calls\n  // while a token is being fetched.\n  private configVersion = 0;\n  // Shared by the BaseClient so that the auth manager can easily inspect it\n  private readonly syncState: LocalSyncState;\n  // Passed down by BaseClient, sends a message to the server\n  private readonly authenticate: (token: string) => IdentityVersion;\n  private readonly stopSocket: () => Promise<void>;\n  private readonly tryRestartSocket: () => void;\n  private readonly pauseSocket: () => void;\n  private readonly resumeSocket: () => void;\n  // Passed down by BaseClient, sends a message to the server\n  private readonly clearAuth: () => void;\n  private readonly logger: Logger;\n  private readonly refreshTokenLeewaySeconds: number;\n  // Number of times we have attempted to confirm the latest token. We retry up\n  // to `MAX_TOKEN_CONFIRMATION_ATTEMPTS` times.\n  private tokenConfirmationAttempts = 0;\n  constructor(\n    syncState: LocalSyncState,\n    callbacks: {\n      authenticate: (token: string) => IdentityVersion;\n      stopSocket: () => Promise<void>;\n      tryRestartSocket: () => void;\n      pauseSocket: () => void;\n      resumeSocket: () => void;\n      clearAuth: () => void;\n    },\n    config: {\n      refreshTokenLeewaySeconds: number;\n      logger: Logger;\n    },\n  ) {\n    this.syncState = syncState;\n    this.authenticate = callbacks.authenticate;\n    this.stopSocket = callbacks.stopSocket;\n    this.tryRestartSocket = callbacks.tryRestartSocket;\n    this.pauseSocket = callbacks.pauseSocket;\n    this.resumeSocket = callbacks.resumeSocket;\n    this.clearAuth = callbacks.clearAuth;\n    this.logger = config.logger;\n    this.refreshTokenLeewaySeconds = config.refreshTokenLeewaySeconds;\n  }\n\n  async setConfig(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ) {\n    this.resetAuthState();\n    this._logVerbose(\"pausing WS for auth token fetch\");\n    this.pauseSocket();\n    const token = await this.fetchTokenAndGuardAgainstRace(fetchToken, {\n      forceRefreshToken: false,\n    });\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n    if (token.value) {\n      this.setAuthState({\n        state: \"waitingForServerConfirmationOfCachedToken\",\n        config: { fetchToken, onAuthChange: onChange },\n        hasRetried: false,\n      });\n      this.authenticate(token.value);\n    } else {\n      this.setAuthState({\n        state: \"initialRefetch\",\n        config: { fetchToken, onAuthChange: onChange },\n      });\n      // Try again with `forceRefreshToken: true`\n      await this.refetchToken();\n    }\n    this._logVerbose(\"resuming WS after auth token fetch\");\n    this.resumeSocket();\n  }\n\n  onTransition(serverMessage: Transition) {\n    if (\n      !this.syncState.isCurrentOrNewerAuthVersion(\n        serverMessage.endVersion.identity,\n      )\n    ) {\n      // This is a stale transition - client has moved on to\n      // a newer auth version.\n      return;\n    }\n    if (\n      serverMessage.endVersion.identity <= serverMessage.startVersion.identity\n    ) {\n      // This transition did not change auth - it is not a response to Authenticate.\n      return;\n    }\n\n    if (this.authState.state === \"waitingForServerConfirmationOfCachedToken\") {\n      this._logVerbose(\"server confirmed auth token is valid\");\n      void this.refetchToken();\n      this.authState.config.onAuthChange(true);\n      return;\n    }\n    if (this.authState.state === \"waitingForServerConfirmationOfFreshToken\") {\n      this._logVerbose(\"server confirmed new auth token is valid\");\n      this.scheduleTokenRefetch(this.authState.token);\n      this.tokenConfirmationAttempts = 0;\n      if (!this.authState.hadAuth) {\n        this.authState.config.onAuthChange(true);\n      }\n    }\n  }\n\n  onAuthError(serverMessage: AuthError) {\n    // If the AuthError is not due to updating the token, and we're currently\n    // waiting on the result of a token update, ignore.\n    if (\n      serverMessage.authUpdateAttempted === false &&\n      (this.authState.state === \"waitingForServerConfirmationOfFreshToken\" ||\n        this.authState.state === \"waitingForServerConfirmationOfCachedToken\")\n    ) {\n      this._logVerbose(\"ignoring non-auth token expired error\");\n      return;\n    }\n    const { baseVersion } = serverMessage;\n    // Versioned AuthErrors are ignored if the client advanced to\n    // a newer auth identity\n    // Error are reporting the previous version, since the server\n    // didn't advance, hence `+ 1`.\n    if (!this.syncState.isCurrentOrNewerAuthVersion(baseVersion + 1)) {\n      this._logVerbose(\"ignoring auth error for previous auth attempt\");\n      return;\n    }\n    void this.tryToReauthenticate(serverMessage);\n    return;\n  }\n\n  // This is similar to `refetchToken` defined below, in fact we\n  // don't represent them as different states, but it is different\n  // in that we pause the WebSocket so that mutations\n  // don't retry with bad auth.\n  private async tryToReauthenticate(serverMessage: AuthError) {\n    this._logVerbose(`attempting to reauthenticate: ${serverMessage.error}`);\n    if (\n      // No way to fetch another token, kaboom\n      this.authState.state === \"noAuth\" ||\n      // We failed on a fresh token. After a small number of retries, we give up\n      // and clear the auth state to avoid infinite retries.\n      (this.authState.state === \"waitingForServerConfirmationOfFreshToken\" &&\n        this.tokenConfirmationAttempts >= MAX_TOKEN_CONFIRMATION_ATTEMPTS)\n    ) {\n      this.logger.error(\n        `Failed to authenticate: \"${serverMessage.error}\", check your server auth config`,\n      );\n      if (this.syncState.hasAuth()) {\n        this.syncState.clearAuth();\n      }\n      if (this.authState.state !== \"noAuth\") {\n        this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n      }\n      return;\n    }\n    if (this.authState.state === \"waitingForServerConfirmationOfFreshToken\") {\n      this.tokenConfirmationAttempts++;\n      this._logVerbose(\n        `retrying reauthentication, ${MAX_TOKEN_CONFIRMATION_ATTEMPTS - this.tokenConfirmationAttempts} attempts remaining`,\n      );\n    }\n\n    await this.stopSocket();\n    const token = await this.fetchTokenAndGuardAgainstRace(\n      this.authState.config.fetchToken,\n      {\n        forceRefreshToken: true,\n      },\n    );\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n\n    if (token.value && this.syncState.isNewAuth(token.value)) {\n      this.authenticate(token.value);\n      this.setAuthState({\n        state: \"waitingForServerConfirmationOfFreshToken\",\n        config: this.authState.config,\n        token: token.value,\n        hadAuth:\n          this.authState.state === \"notRefetching\" ||\n          this.authState.state === \"waitingForScheduledRefetch\",\n      });\n    } else {\n      this._logVerbose(\"reauthentication failed, could not fetch a new token\");\n      if (this.syncState.hasAuth()) {\n        this.syncState.clearAuth();\n      }\n      this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n    }\n    this.tryRestartSocket();\n  }\n\n  // Force refetch the token and schedule another refetch\n  // before the token expires - an active client should never\n  // need to reauthenticate.\n  private async refetchToken() {\n    if (this.authState.state === \"noAuth\") {\n      return;\n    }\n    this._logVerbose(\"refetching auth token\");\n    const token = await this.fetchTokenAndGuardAgainstRace(\n      this.authState.config.fetchToken,\n      {\n        forceRefreshToken: true,\n      },\n    );\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n\n    if (token.value) {\n      if (this.syncState.isNewAuth(token.value)) {\n        this.setAuthState({\n          state: \"waitingForServerConfirmationOfFreshToken\",\n          hadAuth: this.syncState.hasAuth(),\n          token: token.value,\n          config: this.authState.config,\n        });\n        this.authenticate(token.value);\n      } else {\n        this.setAuthState({\n          state: \"notRefetching\",\n          config: this.authState.config,\n        });\n      }\n    } else {\n      this._logVerbose(\"refetching token failed\");\n      if (this.syncState.hasAuth()) {\n        this.clearAuth();\n      }\n      this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n    }\n    // Restart in case this refetch was triggered via schedule during\n    // a reauthentication attempt.\n    this._logVerbose(\n      \"restarting WS after auth token fetch (if currently stopped)\",\n    );\n    this.tryRestartSocket();\n  }\n\n  private scheduleTokenRefetch(token: string) {\n    if (this.authState.state === \"noAuth\") {\n      return;\n    }\n    const decodedToken = this.decodeToken(token);\n    if (!decodedToken) {\n      // This is no longer really possible, because\n      // we wait on server response before scheduling token refetch,\n      // and the server currently requires JWT tokens.\n      this.logger.error(\n        \"Auth token is not a valid JWT, cannot refetch the token\",\n      );\n      return;\n    }\n    // iat: issued at time, UTC seconds timestamp at which the JWT was issued\n    // exp: expiration time, UTC seconds timestamp at which the JWT will expire\n    const { iat, exp } = decodedToken as { iat?: number; exp?: number };\n    if (!iat || !exp) {\n      this.logger.error(\n        \"Auth token does not have required fields, cannot refetch the token\",\n      );\n      return;\n    }\n    // Because the client and server clocks may be out of sync,\n    // we only know that the token will expire after `exp - iat`,\n    // and since we just fetched a fresh one we know when that\n    // will happen.\n    const tokenValiditySeconds = exp - iat;\n    if (tokenValiditySeconds <= 2) {\n      this.logger.error(\n        \"Auth token does not live long enough, cannot refetch the token\",\n      );\n      return;\n    }\n    // Attempt to refresh the token `refreshTokenLeewaySeconds` before it expires,\n    // or immediately if the token is already expiring soon.\n    let delay = Math.min(\n      MAXIMUM_REFRESH_DELAY,\n      (tokenValiditySeconds - this.refreshTokenLeewaySeconds) * 1000,\n    );\n    if (delay <= 0) {\n      // Refetch immediately, but this might be due to configuring a `refreshTokenLeewaySeconds`\n      // that is too large compared to the token's actual lifetime.\n      this.logger.warn(\n        `Refetching auth token immediately, configured leeway ${this.refreshTokenLeewaySeconds}s is larger than the token's lifetime ${tokenValiditySeconds}s`,\n      );\n      delay = 0;\n    }\n    const refetchTokenTimeoutId = setTimeout(() => {\n      this._logVerbose(\"running scheduled token refetch\");\n      void this.refetchToken();\n    }, delay);\n    this.setAuthState({\n      state: \"waitingForScheduledRefetch\",\n      refetchTokenTimeoutId,\n      config: this.authState.config,\n    });\n    this._logVerbose(\n      `scheduled preemptive auth token refetching in ${delay}ms`,\n    );\n  }\n\n  // Protects against simultaneous calls to `setConfig`\n  // while we're fetching a token\n  private async fetchTokenAndGuardAgainstRace(\n    fetchToken: AuthTokenFetcher,\n    fetchArgs: {\n      forceRefreshToken: boolean;\n    },\n  ) {\n    const originalConfigVersion = ++this.configVersion;\n    this._logVerbose(\n      `fetching token with config version ${originalConfigVersion}`,\n    );\n    const token = await fetchToken(fetchArgs);\n    if (this.configVersion !== originalConfigVersion) {\n      // This is a stale config\n      this._logVerbose(\n        `stale config version, expected ${originalConfigVersion}, got ${this.configVersion}`,\n      );\n      return { isFromOutdatedConfig: true };\n    }\n    return { isFromOutdatedConfig: false, value: token };\n  }\n\n  stop() {\n    this.resetAuthState();\n    // Bump this in case we are mid-token-fetch when we get stopped\n    this.configVersion++;\n    this._logVerbose(`config version bumped to ${this.configVersion}`);\n  }\n\n  private setAndReportAuthFailed(\n    onAuthChange: (authenticated: boolean) => void,\n  ) {\n    onAuthChange(false);\n    this.resetAuthState();\n  }\n\n  private resetAuthState() {\n    this.setAuthState({ state: \"noAuth\" });\n  }\n\n  private setAuthState(newAuth: AuthState) {\n    const authStateForLog =\n      newAuth.state === \"waitingForServerConfirmationOfFreshToken\"\n        ? {\n            hadAuth: newAuth.hadAuth,\n            state: newAuth.state,\n            token: `...${newAuth.token.slice(-7)}`,\n          }\n        : { state: newAuth.state };\n    this._logVerbose(\n      `setting auth state to ${JSON.stringify(authStateForLog)}`,\n    );\n    switch (newAuth.state) {\n      case \"waitingForScheduledRefetch\":\n      case \"notRefetching\":\n      case \"noAuth\":\n        this.tokenConfirmationAttempts = 0;\n        break;\n      case \"waitingForServerConfirmationOfFreshToken\":\n      case \"waitingForServerConfirmationOfCachedToken\":\n      case \"initialRefetch\":\n        break;\n      default: {\n        newAuth satisfies never;\n      }\n    }\n    if (this.authState.state === \"waitingForScheduledRefetch\") {\n      clearTimeout(this.authState.refetchTokenTimeoutId);\n\n      // The waitingForScheduledRefetch state is the most quiesced authed state.\n      // Let the syncState know that auth is in a good state, so it can reset failure backoffs\n      this.syncState.markAuthCompletion();\n    }\n    this.authState = newAuth;\n  }\n\n  private decodeToken(token: string) {\n    try {\n      return jwtDecode(token);\n    } catch (e) {\n      this._logVerbose(\n        `Error decoding token: ${e instanceof Error ? e.message : \"Unknown error\"}`,\n      );\n      return null;\n    }\n  }\n\n  private _logVerbose(message: string) {\n    this.logger.logVerbose(`${message} [v${this.configVersion}]`);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,wBAA0B;AAI1B,MAAM,wBAAwB,KAAK,KAAK,KAAK,KAAK;AAElD,MAAM,kCAAkC;AAwEjC,MAAM,sBAAsB;AAAA,EAoBjC,YACE,WACA,WAQA,QAIA;AAjCF,wBAAQ,aAAuB,EAAE,OAAO,SAAS;AAGjD;AAAA;AAAA,wBAAQ,iBAAgB;AAExB;AAAA,wBAAiB;AAEjB;AAAA,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AAEjB;AAAA,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AAGjB;AAAA;AAAA,wBAAQ,6BAA4B;AAgBlC,SAAK,YAAY;AACjB,SAAK,eAAe,UAAU;AAC9B,SAAK,aAAa,UAAU;AAC5B,SAAK,mBAAmB,UAAU;AAClC,SAAK,cAAc,UAAU;AAC7B,SAAK,eAAe,UAAU;AAC9B,SAAK,YAAY,UAAU;AAC3B,SAAK,SAAS,OAAO;AACrB,SAAK,4BAA4B,OAAO;AAAA,EAC1C;AAAA,EAEA,MAAM,UACJ,YACA,UACA;AACA,SAAK,eAAe;AACpB,SAAK,YAAY,iCAAiC;AAClD,SAAK,YAAY;AACjB,UAAM,QAAQ,MAAM,KAAK,8BAA8B,YAAY;AAAA,MACjE,mBAAmB;AAAA,IACrB,CAAC;AACD,QAAI,MAAM,sBAAsB;AAC9B;AAAA,IACF;AACA,QAAI,MAAM,OAAO;AACf,WAAK,aAAa;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ,EAAE,YAAY,cAAc,SAAS;AAAA,QAC7C,YAAY;AAAA,MACd,CAAC;AACD,WAAK,aAAa,MAAM,KAAK;AAAA,IAC/B,OAAO;AACL,WAAK,aAAa;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ,EAAE,YAAY,cAAc,SAAS;AAAA,MAC/C,CAAC;AAED,YAAM,KAAK,aAAa;AAAA,IAC1B;AACA,SAAK,YAAY,oCAAoC;AACrD,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,aAAa,eAA2B;AACtC,QACE,CAAC,KAAK,UAAU;AAAA,MACd,cAAc,WAAW;AAAA,IAC3B,GACA;AAGA;AAAA,IACF;AACA,QACE,cAAc,WAAW,YAAY,cAAc,aAAa,UAChE;AAEA;AAAA,IACF;AAEA,QAAI,KAAK,UAAU,UAAU,6CAA6C;AACxE,WAAK,YAAY,sCAAsC;AACvD,WAAK,KAAK,aAAa;AACvB,WAAK,UAAU,OAAO,aAAa,IAAI;AACvC;AAAA,IACF;AACA,QAAI,KAAK,UAAU,UAAU,4CAA4C;AACvE,WAAK,YAAY,0CAA0C;AAC3D,WAAK,qBAAqB,KAAK,UAAU,KAAK;AAC9C,WAAK,4BAA4B;AACjC,UAAI,CAAC,KAAK,UAAU,SAAS;AAC3B,aAAK,UAAU,OAAO,aAAa,IAAI;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY,eAA0B;AAGpC,QACE,cAAc,wBAAwB,UACrC,KAAK,UAAU,UAAU,8CACxB,KAAK,UAAU,UAAU,8CAC3B;AACA,WAAK,YAAY,uCAAuC;AACxD;AAAA,IACF;AACA,UAAM,EAAE,YAAY,IAAI;AAKxB,QAAI,CAAC,KAAK,UAAU,4BAA4B,cAAc,CAAC,GAAG;AAChE,WAAK,YAAY,+CAA+C;AAChE;AAAA,IACF;AACA,SAAK,KAAK,oBAAoB,aAAa;AAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc,oBAAoB,eAA0B;AAC1D,SAAK,YAAY,iCAAiC,cAAc,KAAK,EAAE;AACvE;AAAA;AAAA,MAEE,KAAK,UAAU,UAAU;AAAA;AAAA,MAGxB,KAAK,UAAU,UAAU,8CACxB,KAAK,6BAA6B;AAAA,MACpC;AACA,WAAK,OAAO;AAAA,QACV,4BAA4B,cAAc,KAAK;AAAA,MACjD;AACA,UAAI,KAAK,UAAU,QAAQ,GAAG;AAC5B,aAAK,UAAU,UAAU;AAAA,MAC3B;AACA,UAAI,KAAK,UAAU,UAAU,UAAU;AACrC,aAAK,uBAAuB,KAAK,UAAU,OAAO,YAAY;AAAA,MAChE;AACA;AAAA,IACF;AACA,QAAI,KAAK,UAAU,UAAU,4CAA4C;AACvE,WAAK;AACL,WAAK;AAAA,QACH,8BAA8B,kCAAkC,KAAK,yBAAyB;AAAA,MAChG;AAAA,IACF;AAEA,UAAM,KAAK,WAAW;AACtB,UAAM,QAAQ,MAAM,KAAK;AAAA,MACvB,KAAK,UAAU,OAAO;AAAA,MACtB;AAAA,QACE,mBAAmB;AAAA,MACrB;AAAA,IACF;AACA,QAAI,MAAM,sBAAsB;AAC9B;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,KAAK,UAAU,UAAU,MAAM,KAAK,GAAG;AACxD,WAAK,aAAa,MAAM,KAAK;AAC7B,WAAK,aAAa;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ,KAAK,UAAU;AAAA,QACvB,OAAO,MAAM;AAAA,QACb,SACE,KAAK,UAAU,UAAU,mBACzB,KAAK,UAAU,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH,OAAO;AACL,WAAK,YAAY,sDAAsD;AACvE,UAAI,KAAK,UAAU,QAAQ,GAAG;AAC5B,aAAK,UAAU,UAAU;AAAA,MAC3B;AACA,WAAK,uBAAuB,KAAK,UAAU,OAAO,YAAY;AAAA,IAChE;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,eAAe;AAC3B,QAAI,KAAK,UAAU,UAAU,UAAU;AACrC;AAAA,IACF;AACA,SAAK,YAAY,uBAAuB;AACxC,UAAM,QAAQ,MAAM,KAAK;AAAA,MACvB,KAAK,UAAU,OAAO;AAAA,MACtB;AAAA,QACE,mBAAmB;AAAA,MACrB;AAAA,IACF;AACA,QAAI,MAAM,sBAAsB;AAC9B;AAAA,IACF;AAEA,QAAI,MAAM,OAAO;AACf,UAAI,KAAK,UAAU,UAAU,MAAM,KAAK,GAAG;AACzC,aAAK,aAAa;AAAA,UAChB,OAAO;AAAA,UACP,SAAS,KAAK,UAAU,QAAQ;AAAA,UAChC,OAAO,MAAM;AAAA,UACb,QAAQ,KAAK,UAAU;AAAA,QACzB,CAAC;AACD,aAAK,aAAa,MAAM,KAAK;AAAA,MAC/B,OAAO;AACL,aAAK,aAAa;AAAA,UAChB,OAAO;AAAA,UACP,QAAQ,KAAK,UAAU;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,WAAK,YAAY,yBAAyB;AAC1C,UAAI,KAAK,UAAU,QAAQ,GAAG;AAC5B,aAAK,UAAU;AAAA,MACjB;AACA,WAAK,uBAAuB,KAAK,UAAU,OAAO,YAAY;AAAA,IAChE;AAGA,SAAK;AAAA,MACH;AAAA,IACF;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EAEQ,qBAAqB,OAAe;AAC1C,QAAI,KAAK,UAAU,UAAU,UAAU;AACrC;AAAA,IACF;AACA,UAAM,eAAe,KAAK,YAAY,KAAK;AAC3C,QAAI,CAAC,cAAc;AAIjB,WAAK,OAAO;AAAA,QACV;AAAA,MACF;AACA;AAAA,IACF;AAGA,UAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAI,CAAC,OAAO,CAAC,KAAK;AAChB,WAAK,OAAO;AAAA,QACV;AAAA,MACF;AACA;AAAA,IACF;AAKA,UAAM,uBAAuB,MAAM;AACnC,QAAI,wBAAwB,GAAG;AAC7B,WAAK,OAAO;AAAA,QACV;AAAA,MACF;AACA;AAAA,IACF;AAGA,QAAI,QAAQ,KAAK;AAAA,MACf;AAAA,OACC,uBAAuB,KAAK,6BAA6B;AAAA,IAC5D;AACA,QAAI,SAAS,GAAG;AAGd,WAAK,OAAO;AAAA,QACV,wDAAwD,KAAK,yBAAyB,yCAAyC,oBAAoB;AAAA,MACrJ;AACA,cAAQ;AAAA,IACV;AACA,UAAM,wBAAwB,WAAW,MAAM;AAC7C,WAAK,YAAY,iCAAiC;AAClD,WAAK,KAAK,aAAa;AAAA,IACzB,GAAG,KAAK;AACR,SAAK,aAAa;AAAA,MAChB,OAAO;AAAA,MACP;AAAA,MACA,QAAQ,KAAK,UAAU;AAAA,IACzB,CAAC;AACD,SAAK;AAAA,MACH,iDAAiD,KAAK;AAAA,IACxD;AAAA,EACF;AAAA;AAAA;AAAA,EAIA,MAAc,8BACZ,YACA,WAGA;AACA,UAAM,wBAAwB,EAAE,KAAK;AACrC,SAAK;AAAA,MACH,sCAAsC,qBAAqB;AAAA,IAC7D;AACA,UAAM,QAAQ,MAAM,WAAW,SAAS;AACxC,QAAI,KAAK,kBAAkB,uBAAuB;AAEhD,WAAK;AAAA,QACH,kCAAkC,qBAAqB,SAAS,KAAK,aAAa;AAAA,MACpF;AACA,aAAO,EAAE,sBAAsB,KAAK;AAAA,IACtC;AACA,WAAO,EAAE,sBAAsB,OAAO,OAAO,MAAM;AAAA,EACrD;AAAA,EAEA,OAAO;AACL,SAAK,eAAe;AAEpB,SAAK;AACL,SAAK,YAAY,4BAA4B,KAAK,aAAa,EAAE;AAAA,EACnE;AAAA,EAEQ,uBACN,cACA;AACA,iBAAa,KAAK;AAClB,SAAK,eAAe;AAAA,EACtB;AAAA,EAEQ,iBAAiB;AACvB,SAAK,aAAa,EAAE,OAAO,SAAS,CAAC;AAAA,EACvC;AAAA,EAEQ,aAAa,SAAoB;AACvC,UAAM,kBACJ,QAAQ,UAAU,6CACd;AAAA,MACE,SAAS,QAAQ;AAAA,MACjB,OAAO,QAAQ;AAAA,MACf,OAAO,MAAM,QAAQ,MAAM,MAAM,EAAE,CAAC;AAAA,IACtC,IACA,EAAE,OAAO,QAAQ,MAAM;AAC7B,SAAK;AAAA,MACH,yBAAyB,KAAK,UAAU,eAAe,CAAC;AAAA,IAC1D;AACA,YAAQ,QAAQ,OAAO;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,4BAA4B;AACjC;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF,SAAS;AACP;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,UAAU,UAAU,8BAA8B;AACzD,mBAAa,KAAK,UAAU,qBAAqB;AAIjD,WAAK,UAAU,mBAAmB;AAAA,IACpC;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EAEQ,YAAY,OAAe;AACjC,QAAI;AACF,iBAAO,6BAAU,KAAK;AAAA,IACxB,SAAS,GAAG;AACV,WAAK;AAAA,QACH,yBAAyB,aAAa,QAAQ,EAAE,UAAU,eAAe;AAAA,MAC3E;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEQ,YAAY,SAAiB;AACnC,SAAK,OAAO,WAAW,GAAG,OAAO,MAAM,KAAK,aAAa,GAAG;AAAA,EAC9D;AACF;", "names": []}