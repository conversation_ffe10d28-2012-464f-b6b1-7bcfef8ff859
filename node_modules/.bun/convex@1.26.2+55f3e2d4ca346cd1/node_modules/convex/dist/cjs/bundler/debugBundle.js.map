{"version": 3, "sources": ["../../../src/bundler/debugBundle.ts"], "sourcesContent": ["import path from \"path\";\nimport esbuild, { BuildFailure, LogLevel, Plugin } from \"esbuild\";\nimport { Context } from \"./context.js\";\nimport {\n  logError,\n  changeSpinner,\n  logFailure,\n  logVerbose,\n  logMessage,\n} from \"./log.js\";\nimport { wasmPlugin } from \"./wasm.js\";\nimport dependencyTrackerPlugin from \"./depgraph.js\";\n\nexport async function innerEsbuild({\n  entryPoints,\n  platform,\n  dir,\n  extraConditions,\n  generateSourceMaps,\n  plugins,\n  chunksFolder,\n  logLevel,\n}: {\n  entryPoints: string[];\n  platform: esbuild.Platform;\n  dir: string;\n  extraConditions: string[];\n  generateSourceMaps: boolean;\n  plugins: Plugin[];\n  chunksFolder: string;\n  logLevel?: LogLevel;\n}) {\n  const result = await esbuild.build({\n    entryPoints,\n    bundle: true,\n    platform: platform,\n    format: \"esm\",\n    target: \"esnext\",\n    jsx: \"automatic\",\n    outdir: \"out\",\n    outbase: dir,\n    conditions: [\"convex\", \"module\", ...extraConditions],\n    plugins,\n    write: false,\n    sourcemap: generateSourceMaps,\n    splitting: true,\n    chunkNames: path.join(chunksFolder, \"[hash]\"),\n    treeShaking: true,\n    minifySyntax: true,\n    minifyIdentifiers: true,\n    // Enabling minifyWhitespace breaks sourcemaps on convex backends.\n    // The sourcemaps produced are valid on https://evanw.github.io/source-map-visualization\n    // but something we're doing (perhaps involving https://github.com/getsentry/rust-sourcemap)\n    // makes everything map to the same line.\n    minifyWhitespace: false, // false is the default, just showing for clarify.\n    keepNames: true,\n    define: {\n      \"process.env.NODE_ENV\": '\"production\"',\n    },\n    metafile: true,\n    logLevel: logLevel || \"warning\",\n  });\n  return result;\n}\n\nexport function isEsbuildBuildError(e: any): e is BuildFailure {\n  return (\n    \"errors\" in e &&\n    \"warnings\" in e &&\n    Array.isArray(e.errors) &&\n    Array.isArray(e.warnings)\n  );\n}\n\n/**\n * Bundle non-\"use node\" entry points one at a time to track down the first file with an error\n * is being imported.\n */\nexport async function debugIsolateBundlesSerially(\n  ctx: Context,\n  {\n    entryPoints,\n    extraConditions,\n    dir,\n  }: {\n    entryPoints: string[];\n    extraConditions: string[];\n    dir: string;\n  },\n): Promise<void> {\n  logMessage(\n    `Bundling convex entry points one at a time to track down things that can't be bundled for the Convex JS runtime.`,\n  );\n  let i = 1;\n  for (const entryPoint of entryPoints) {\n    changeSpinner(\n      `bundling entry point ${entryPoint} (${i++}/${entryPoints.length})...`,\n    );\n\n    const { plugin, tracer } = dependencyTrackerPlugin();\n    try {\n      await innerEsbuild({\n        entryPoints: [entryPoint],\n        platform: \"browser\",\n        generateSourceMaps: true,\n        chunksFolder: \"_deps\",\n        extraConditions,\n        dir,\n        plugins: [plugin, wasmPlugin],\n        logLevel: \"silent\",\n      });\n    } catch (error) {\n      if (!isEsbuildBuildError(error) || !error.errors[0]) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"invalid filesystem data\",\n          printedMessage: null,\n        });\n      }\n\n      const buildError = error.errors[0];\n      const errorFile = buildError.location?.file;\n      if (!errorFile) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"invalid filesystem data\",\n          printedMessage: null,\n        });\n      }\n\n      const importedPath = buildError.text.match(/\"([^\"]+)\"/)?.[1];\n      if (!importedPath) continue;\n\n      const full = path.resolve(errorFile);\n      logError(\"\");\n      logError(\n        `Bundling ${entryPoint} resulted in ${error.errors.length} esbuild errors.`,\n      );\n      logError(`One of the bundling errors occurred while bundling ${full}:\\n`);\n      logError(\n        esbuild\n          .formatMessagesSync([buildError], {\n            kind: \"error\",\n            color: true,\n          })\n          .join(\"\\n\"),\n      );\n      logError(\"It would help to avoid importing this file.\");\n      const chains = tracer.traceImportChains(entryPoint, full);\n      const chain: string[] = chains[0];\n      chain.reverse();\n\n      logError(``);\n      if (chain.length > 0) {\n        const problematicFileRelative = formatFilePath(dir, chain[0]);\n\n        if (chain.length === 1) {\n          logError(`  ${problematicFileRelative}`);\n        } else {\n          logError(`  ${problematicFileRelative} is imported by`);\n\n          for (let i = 1; i < chain.length - 1; i++) {\n            const fileRelative = formatFilePath(dir, chain[i]);\n            logError(`  ${fileRelative}, which is imported by`);\n          }\n\n          const entryPointFile = chain[chain.length - 1];\n          const entryPointRelative = formatFilePath(dir, entryPointFile);\n\n          logError(`  ${entryPointRelative}, which doesn't use \"use node\"\\n`);\n          logError(\n            `  For registered action functions to use Node.js APIs in any code they run they must be defined\\n` +\n              `  in a file with 'use node' at the top. See https://docs.convex.dev/functions/runtimes#nodejs-runtime\\n`,\n          );\n        }\n      }\n\n      logFailure(\"Bundling failed\");\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage: \"Bundling failed.\",\n      });\n    }\n    logVerbose(`${entryPoint} bundled`);\n  }\n}\n\n// Helper function to format file paths consistently\nfunction formatFilePath(baseDir: string, filePath: string): string {\n  // If it's already a relative path like \"./shared\", just return it\n  if (!path.isAbsolute(filePath)) {\n    // For relative paths, ensure they start with \"convex/\"\n    if (!filePath.startsWith(\"convex/\")) {\n      // If it's a path like \"./subdir/file.ts\" or \"subdir/file.ts\"\n      const cleanPath = filePath.replace(/^\\.\\//, \"\");\n      return `convex/${cleanPath}`;\n    }\n    return filePath;\n  }\n\n  // Get the path relative to the base directory\n  const relativePath = path.relative(baseDir, filePath);\n\n  // Remove any leading \"./\" that path.relative might add\n  const cleanPath = relativePath.replace(/^\\.\\//, \"\");\n\n  // Check if this is a path within the convex directory\n  const isConvexPath =\n    cleanPath.startsWith(\"convex/\") ||\n    cleanPath.includes(\"/convex/\") ||\n    path.dirname(cleanPath) === \"convex\";\n\n  if (isConvexPath) {\n    // If it already starts with convex/, return it as is\n    if (cleanPath.startsWith(\"convex/\")) {\n      return cleanPath;\n    }\n\n    // For files in the convex directory\n    if (path.dirname(cleanPath) === \"convex\") {\n      const filename = path.basename(cleanPath);\n      return `convex/${filename}`;\n    }\n\n    // For files in subdirectories of convex\n    const convexIndex = cleanPath.indexOf(\"convex/\");\n    if (convexIndex >= 0) {\n      return cleanPath.substring(convexIndex);\n    }\n  }\n\n  // For any other path, assume it's in the convex directory\n  // This handles cases where the file is in a subdirectory of convex\n  // but the path doesn't include \"convex/\" explicitly\n  return `convex/${cleanPath}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AACjB,qBAAwD;AAExD,iBAMO;AACP,kBAA2B;AAC3B,sBAAoC;AAEpC,eAAsB,aAAa;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GASG;AACD,QAAM,SAAS,MAAM,eAAAA,QAAQ,MAAM;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY,CAAC,UAAU,UAAU,GAAG,eAAe;AAAA,IACnD;AAAA,IACA,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY,YAAAC,QAAK,KAAK,cAAc,QAAQ;AAAA,IAC5C,aAAa;AAAA,IACb,cAAc;AAAA,IACd,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKnB,kBAAkB;AAAA;AAAA,IAClB,WAAW;AAAA,IACX,QAAQ;AAAA,MACN,wBAAwB;AAAA,IAC1B;AAAA,IACA,UAAU;AAAA,IACV,UAAU,YAAY;AAAA,EACxB,CAAC;AACD,SAAO;AACT;AAEO,SAAS,oBAAoB,GAA2B;AAC7D,SACE,YAAY,KACZ,cAAc,KACd,MAAM,QAAQ,EAAE,MAAM,KACtB,MAAM,QAAQ,EAAE,QAAQ;AAE5B;AAMA,eAAsB,4BACpB,KACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AACF,GAKe;AACf;AAAA,IACE;AAAA,EACF;AACA,MAAI,IAAI;AACR,aAAW,cAAc,aAAa;AACpC;AAAA,MACE,wBAAwB,UAAU,KAAK,GAAG,IAAI,YAAY,MAAM;AAAA,IAClE;AAEA,UAAM,EAAE,QAAQ,OAAO,QAAI,gBAAAC,SAAwB;AACnD,QAAI;AACF,YAAM,aAAa;AAAA,QACjB,aAAa,CAAC,UAAU;AAAA,QACxB,UAAU;AAAA,QACV,oBAAoB;AAAA,QACpB,cAAc;AAAA,QACd;AAAA,QACA;AAAA,QACA,SAAS,CAAC,QAAQ,sBAAU;AAAA,QAC5B,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,SAAS,OAAO;AACd,UAAI,CAAC,oBAAoB,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,GAAG;AACnD,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAEA,YAAM,aAAa,MAAM,OAAO,CAAC;AACjC,YAAM,YAAY,WAAW,UAAU;AACvC,UAAI,CAAC,WAAW;AACd,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAEA,YAAM,eAAe,WAAW,KAAK,MAAM,WAAW,IAAI,CAAC;AAC3D,UAAI,CAAC,aAAc;AAEnB,YAAM,OAAO,YAAAD,QAAK,QAAQ,SAAS;AACnC,+BAAS,EAAE;AACX;AAAA,QACE,YAAY,UAAU,gBAAgB,MAAM,OAAO,MAAM;AAAA,MAC3D;AACA,+BAAS,sDAAsD,IAAI;AAAA,CAAK;AACxE;AAAA,QACE,eAAAD,QACG,mBAAmB,CAAC,UAAU,GAAG;AAAA,UAChC,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC,EACA,KAAK,IAAI;AAAA,MACd;AACA,+BAAS,6CAA6C;AACtD,YAAM,SAAS,OAAO,kBAAkB,YAAY,IAAI;AACxD,YAAM,QAAkB,OAAO,CAAC;AAChC,YAAM,QAAQ;AAEd,+BAAS,EAAE;AACX,UAAI,MAAM,SAAS,GAAG;AACpB,cAAM,0BAA0B,eAAe,KAAK,MAAM,CAAC,CAAC;AAE5D,YAAI,MAAM,WAAW,GAAG;AACtB,mCAAS,KAAK,uBAAuB,EAAE;AAAA,QACzC,OAAO;AACL,mCAAS,KAAK,uBAAuB,iBAAiB;AAEtD,mBAASG,KAAI,GAAGA,KAAI,MAAM,SAAS,GAAGA,MAAK;AACzC,kBAAM,eAAe,eAAe,KAAK,MAAMA,EAAC,CAAC;AACjD,qCAAS,KAAK,YAAY,wBAAwB;AAAA,UACpD;AAEA,gBAAM,iBAAiB,MAAM,MAAM,SAAS,CAAC;AAC7C,gBAAM,qBAAqB,eAAe,KAAK,cAAc;AAE7D,mCAAS,KAAK,kBAAkB;AAAA,CAAkC;AAClE;AAAA,YACE;AAAA;AAAA;AAAA,UAEF;AAAA,QACF;AAAA,MACF;AAEA,iCAAW,iBAAiB;AAC5B,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AACA,+BAAW,GAAG,UAAU,UAAU;AAAA,EACpC;AACF;AAGA,SAAS,eAAe,SAAiB,UAA0B;AAEjE,MAAI,CAAC,YAAAF,QAAK,WAAW,QAAQ,GAAG;AAE9B,QAAI,CAAC,SAAS,WAAW,SAAS,GAAG;AAEnC,YAAMG,aAAY,SAAS,QAAQ,SAAS,EAAE;AAC9C,aAAO,UAAUA,UAAS;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAGA,QAAM,eAAe,YAAAH,QAAK,SAAS,SAAS,QAAQ;AAGpD,QAAM,YAAY,aAAa,QAAQ,SAAS,EAAE;AAGlD,QAAM,eACJ,UAAU,WAAW,SAAS,KAC9B,UAAU,SAAS,UAAU,KAC7B,YAAAA,QAAK,QAAQ,SAAS,MAAM;AAE9B,MAAI,cAAc;AAEhB,QAAI,UAAU,WAAW,SAAS,GAAG;AACnC,aAAO;AAAA,IACT;AAGA,QAAI,YAAAA,QAAK,QAAQ,SAAS,MAAM,UAAU;AACxC,YAAM,WAAW,YAAAA,QAAK,SAAS,SAAS;AACxC,aAAO,UAAU,QAAQ;AAAA,IAC3B;AAGA,UAAM,cAAc,UAAU,QAAQ,SAAS;AAC/C,QAAI,eAAe,GAAG;AACpB,aAAO,UAAU,UAAU,WAAW;AAAA,IACxC;AAAA,EACF;AAKA,SAAO,UAAU,SAAS;AAC5B;", "names": ["esbuild", "path", "dependencyTrackerPlugin", "i", "cleanPath"]}