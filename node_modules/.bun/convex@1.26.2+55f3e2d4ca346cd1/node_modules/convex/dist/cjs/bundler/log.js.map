{"version": 3, "sources": ["../../../src/bundler/log.ts"], "sourcesContent": ["import { format } from \"util\";\nimport chalk from \"chalk\";\nimport ProgressBar from \"progress\";\nimport ora, { Ora } from \"ora\";\n\nlet spinner: Ora | null = null;\n\n// console.error before it started being red by default in Node v20\nfunction logToStderr(...args: unknown[]) {\n  process.stderr.write(`${format(...args)}\\n`);\n}\n\n// Handles clearing spinner so that it doesn't get messed up\nexport function logError(message: string) {\n  spinner?.clear();\n  logToStderr(message);\n}\n\n// Handles clearing spinner so that it doesn't get messed up\nexport function logWarning(...logged: any) {\n  spinner?.clear();\n  logToStderr(...logged);\n}\n\n// Handles clearing spinner so that it doesn't get messed up\nexport function logMessage(...logged: any) {\n  spinner?.clear();\n  logToStderr(...logged);\n}\n\n// For the rare case writing output to stdout. Status and error messages\n// (logMessage, logWarning, etc.) should be written to stderr.\nexport function logOutput(...logged: any) {\n  spinner?.clear();\n  // the one spot where we can console.log\n  // eslint-disable-next-line no-console\n  console.log(...logged);\n}\n\nexport function logVerbose(...logged: any) {\n  if (process.env.CONVEX_VERBOSE) {\n    logMessage(`[verbose] ${new Date().toISOString()}`, ...logged);\n  }\n}\n\n/**\n * Returns a ProgressBar instance, and also handles clearing the spinner if necessary.\n *\n * The caller is responsible for calling `progressBar.tick()` and terminating the `progressBar`\n * when it's done.\n */\nexport function startLogProgress(\n  format: string,\n  progressBarOptions: ProgressBar.ProgressBarOptions,\n): ProgressBar {\n  spinner?.clear();\n  return new ProgressBar(format, progressBarOptions);\n}\n\n// Start a spinner.\n// To change its message use changeSpinner.\n// To print warnings/errors while it's running use logError or logWarning.\n// To stop it due to an error use logFailure.\n// To stop it due to success use logFinishedStep.\nexport function showSpinner(message: string) {\n  spinner?.stop();\n  spinner = ora({\n    // Add newline to prevent clobbering when a message\n    // we can't pipe through `logMessage` et al gets printed\n    text: message + \"\\n\",\n    stream: process.stderr,\n    // hideCursor: true doesn't work with `tsx`.\n    // see https://github.com/tapjs/signal-exit/issues/49#issuecomment-1459408082\n    // See CX-6822 for an issue to bring back cursor hiding, probably by upgrading libraries.\n    hideCursor: process.env.CONVEX_RUNNING_LIVE_IN_MONOREPO ? false : true,\n  }).start();\n}\n\nexport function changeSpinner(message: string) {\n  if (spinner) {\n    // Add newline to prevent clobbering\n    spinner.text = message + \"\\n\";\n  } else {\n    logToStderr(message);\n  }\n}\n\nexport function failExistingSpinner() {\n  spinner?.fail();\n  spinner = null;\n}\n\nexport function logFailure(message: string) {\n  if (spinner) {\n    spinner.fail(message);\n    spinner = null;\n  } else {\n    logToStderr(`${chalk.red(`✖`)} ${message}`);\n  }\n}\n\n// Stops and removes spinner if one is active\nexport function logFinishedStep(message: string) {\n  if (spinner) {\n    spinner.succeed(message);\n    spinner = null;\n  } else {\n    logToStderr(`${chalk.green(`✔`)} ${message}`);\n  }\n}\n\nexport function stopSpinner() {\n  if (spinner) {\n    spinner.stop();\n    spinner = null;\n  }\n}\n\n// Only shows the spinner if the async `fn` takes longer than `delayMs`\nexport async function showSpinnerIfSlow(\n  message: string,\n  delayMs: number,\n  fn: () => Promise<any>,\n) {\n  const timeout = setTimeout(() => {\n    showSpinner(message);\n  }, delayMs);\n  await fn();\n  clearTimeout(timeout);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuB;AACvB,mBAAkB;AAClB,sBAAwB;AACxB,iBAAyB;AAEzB,IAAI,UAAsB;AAG1B,SAAS,eAAe,MAAiB;AACvC,UAAQ,OAAO,MAAM,OAAG,oBAAO,GAAG,IAAI,CAAC;AAAA,CAAI;AAC7C;AAGO,SAAS,SAAS,SAAiB;AACxC,WAAS,MAAM;AACf,cAAY,OAAO;AACrB;AAGO,SAAS,cAAc,QAAa;AACzC,WAAS,MAAM;AACf,cAAY,GAAG,MAAM;AACvB;AAGO,SAAS,cAAc,QAAa;AACzC,WAAS,MAAM;AACf,cAAY,GAAG,MAAM;AACvB;AAIO,SAAS,aAAa,QAAa;AACxC,WAAS,MAAM;AAGf,UAAQ,IAAI,GAAG,MAAM;AACvB;AAEO,SAAS,cAAc,QAAa;AACzC,MAAI,QAAQ,IAAI,gBAAgB;AAC9B,eAAW,cAAa,oBAAI,KAAK,GAAE,YAAY,CAAC,IAAI,GAAG,MAAM;AAAA,EAC/D;AACF;AAQO,SAAS,iBACdA,SACA,oBACa;AACb,WAAS,MAAM;AACf,SAAO,IAAI,gBAAAC,QAAYD,SAAQ,kBAAkB;AACnD;AAOO,SAAS,YAAY,SAAiB;AAC3C,WAAS,KAAK;AACd,gBAAU,WAAAE,SAAI;AAAA;AAAA;AAAA,IAGZ,MAAM,UAAU;AAAA,IAChB,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA,IAIhB,YAAY,QAAQ,IAAI,kCAAkC,QAAQ;AAAA,EACpE,CAAC,EAAE,MAAM;AACX;AAEO,SAAS,cAAc,SAAiB;AAC7C,MAAI,SAAS;AAEX,YAAQ,OAAO,UAAU;AAAA,EAC3B,OAAO;AACL,gBAAY,OAAO;AAAA,EACrB;AACF;AAEO,SAAS,sBAAsB;AACpC,WAAS,KAAK;AACd,YAAU;AACZ;AAEO,SAAS,WAAW,SAAiB;AAC1C,MAAI,SAAS;AACX,YAAQ,KAAK,OAAO;AACpB,cAAU;AAAA,EACZ,OAAO;AACL,gBAAY,GAAG,aAAAC,QAAM,IAAI,QAAG,CAAC,IAAI,OAAO,EAAE;AAAA,EAC5C;AACF;AAGO,SAAS,gBAAgB,SAAiB;AAC/C,MAAI,SAAS;AACX,YAAQ,QAAQ,OAAO;AACvB,cAAU;AAAA,EACZ,OAAO;AACL,gBAAY,GAAG,aAAAA,QAAM,MAAM,QAAG,CAAC,IAAI,OAAO,EAAE;AAAA,EAC9C;AACF;AAEO,SAAS,cAAc;AAC5B,MAAI,SAAS;AACX,YAAQ,KAAK;AACb,cAAU;AAAA,EACZ;AACF;AAGA,eAAsB,kBACpB,SACA,SACA,IACA;AACA,QAAM,UAAU,WAAW,MAAM;AAC/B,gBAAY,OAAO;AAAA,EACrB,GAAG,OAAO;AACV,QAAM,GAAG;AACT,eAAa,OAAO;AACtB;", "names": ["format", "ProgressBar", "ora", "chalk"]}