{"version": 3, "sources": ["../../../src/bundler/context.ts"], "sourcesContent": ["import * as Sentry from \"@sentry/node\";\nimport { Ora } from \"ora\";\nimport { Filesystem, nodeFs } from \"./fs.js\";\nimport { initializeBigBrainAuth } from \"../cli/lib/deploymentSelection.js\";\nimport { logFailure, logVerbose } from \"./log.js\";\n// How the error should be handled when running `npx convex dev`.\nexport type ErrorType =\n  // The error was likely caused by the state of the developer's local\n  // file system (e.g. `tsc` fails due to a syntax error). The `convex dev`\n  // command will then print out the error and wait for the file to change before\n  // retrying.\n  | \"invalid filesystem data\"\n  // The error was caused by either the local state (ie schema.ts content)\n  // or the state of the db (ie documents not matching the new schema).\n  // The `convex dev` command will wait for either file OR table data change\n  // to retry (if a table name is specified as the value in this Object).\n  | {\n      \"invalid filesystem or db data\": {\n        tableName: string;\n        componentPath?: string;\n      } | null;\n    }\n  // The error was caused by either the local state (ie schema.ts content)\n  // or the state of the deployment environment variables.\n  // The `convex dev` command will wait for either file OR env var change\n  // before retrying.\n  | \"invalid filesystem or env vars\"\n  // The error was some transient issue (e.g. a network\n  // error). This will then cause a retry after an exponential backoff.\n  | \"transient\"\n  // This error is truly permanent. Exit `npx convex dev` because the\n  // developer will need to take a manual commandline action.\n  | \"fatal\";\n\nexport type BigBrainAuth = {\n  header: string;\n} & (\n  | {\n      kind: \"projectKey\";\n      projectKey: string;\n    }\n  | {\n      kind: \"deploymentKey\";\n      deploymentKey: string;\n    }\n  | {\n      kind: \"previewDeployKey\";\n      previewDeployKey: string;\n    }\n  | {\n      kind: \"accessToken\";\n      accessToken: string;\n    }\n);\n\nexport interface Context {\n  fs: Filesystem;\n  deprecationMessagePrinted: boolean;\n  // Reports to Sentry and either throws FatalError or exits the process.\n  // Prints the `printedMessage` if provided\n  crash(args: {\n    exitCode: number;\n    errorType: ErrorType;\n    errForSentry?: any;\n    printedMessage: string | null;\n  }): Promise<never>;\n  registerCleanup(fn: (exitCode: number, err?: any) => Promise<void>): string;\n  removeCleanup(\n    handle: string,\n  ): (exitCode: number, err?: any) => Promise<void> | null;\n  bigBrainAuth(): BigBrainAuth | null;\n  /**\n   * Prefer using `updateBigBrainAuthAfterLogin` in `deploymentSelection.ts` instead\n   */\n  _updateBigBrainAuth(auth: BigBrainAuth | null): void;\n}\n\nasync function flushAndExit(exitCode: number, err?: any) {\n  if (err) {\n    Sentry.captureException(err);\n  }\n  await Sentry.close();\n  return process.exit(exitCode);\n}\n\nexport type OneoffCtx = Context & {\n  // Generally `ctx.crash` is better to use since it handles printing a message\n  // for the user, and then calls this.\n  //\n  // This function reports to Sentry + exits the process, but does not handle\n  // printing a message for the user.\n  flushAndExit: (exitCode: number, err?: any) => Promise<never>;\n};\n\nclass OneoffContextImpl {\n  private _cleanupFns: Record<\n    string,\n    (exitCode: number, err?: any) => Promise<void>\n  > = {};\n  public fs: Filesystem = nodeFs;\n  public deprecationMessagePrinted: boolean = false;\n  public spinner: Ora | undefined = undefined;\n  private _bigBrainAuth: BigBrainAuth | null = null;\n\n  crash = async (args: {\n    exitCode: number;\n    errorType?: ErrorType;\n    errForSentry?: any;\n    printedMessage: string | null;\n  }) => {\n    if (args.printedMessage !== null) {\n      logFailure(args.printedMessage);\n    }\n    return await this.flushAndExit(args.exitCode, args.errForSentry);\n  };\n  flushAndExit = async (exitCode: number, err?: any) => {\n    logVerbose(\"Flushing and exiting, error:\", err);\n    if (err) {\n      logVerbose(err.stack);\n    }\n    const cleanupFns = this._cleanupFns;\n    // Clear the cleanup functions so that there's no risk of running them twice\n    // if this somehow gets triggered twice.\n    this._cleanupFns = {};\n    const fns = Object.values(cleanupFns);\n    logVerbose(`Running ${fns.length} cleanup functions`);\n    for (const fn of fns) {\n      await fn(exitCode, err);\n    }\n    logVerbose(\"All cleanup functions ran\");\n    return flushAndExit(exitCode, err);\n  };\n  registerCleanup(fn: (exitCode: number, err?: any) => Promise<void>) {\n    const handle = Math.random().toString(36).slice(2);\n    this._cleanupFns[handle] = fn;\n    return handle;\n  }\n  removeCleanup(handle: string) {\n    const value = this._cleanupFns[handle];\n    delete this._cleanupFns[handle];\n    return value ?? null;\n  }\n  bigBrainAuth(): BigBrainAuth | null {\n    return this._bigBrainAuth;\n  }\n  _updateBigBrainAuth(auth: BigBrainAuth | null): void {\n    logVerbose(`Updating big brain auth to ${auth?.kind ?? \"null\"}`);\n    this._bigBrainAuth = auth;\n  }\n}\n\nexport const oneoffContext: (args: {\n  url?: string;\n  adminKey?: string;\n  envFile?: string;\n}) => Promise<OneoffCtx> = async (args) => {\n  const ctx = new OneoffContextImpl();\n  await initializeBigBrainAuth(ctx, {\n    url: args.url,\n    adminKey: args.adminKey,\n    envFile: args.envFile,\n  });\n  return ctx;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAwB;AAExB,gBAAmC;AACnC,iCAAuC;AACvC,iBAAuC;AAyEvC,eAAe,aAAa,UAAkB,KAAW;AACvD,MAAI,KAAK;AACP,WAAO,iBAAiB,GAAG;AAAA,EAC7B;AACA,QAAM,OAAO,MAAM;AACnB,SAAO,QAAQ,KAAK,QAAQ;AAC9B;AAWA,MAAM,kBAAkB;AAAA,EAAxB;AACE,wBAAQ,eAGJ,CAAC;AACL,wBAAO,MAAiB;AACxB,wBAAO,6BAAqC;AAC5C,wBAAO;AACP,wBAAQ,iBAAqC;AAE7C,iCAAQ,OAAO,SAKT;AACJ,UAAI,KAAK,mBAAmB,MAAM;AAChC,mCAAW,KAAK,cAAc;AAAA,MAChC;AACA,aAAO,MAAM,KAAK,aAAa,KAAK,UAAU,KAAK,YAAY;AAAA,IACjE;AACA,wCAAe,OAAO,UAAkB,QAAc;AACpD,iCAAW,gCAAgC,GAAG;AAC9C,UAAI,KAAK;AACP,mCAAW,IAAI,KAAK;AAAA,MACtB;AACA,YAAM,aAAa,KAAK;AAGxB,WAAK,cAAc,CAAC;AACpB,YAAM,MAAM,OAAO,OAAO,UAAU;AACpC,iCAAW,WAAW,IAAI,MAAM,oBAAoB;AACpD,iBAAW,MAAM,KAAK;AACpB,cAAM,GAAG,UAAU,GAAG;AAAA,MACxB;AACA,iCAAW,2BAA2B;AACtC,aAAO,aAAa,UAAU,GAAG;AAAA,IACnC;AAAA;AAAA,EACA,gBAAgB,IAAoD;AAClE,UAAM,SAAS,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AACjD,SAAK,YAAY,MAAM,IAAI;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,cAAc,QAAgB;AAC5B,UAAM,QAAQ,KAAK,YAAY,MAAM;AACrC,WAAO,KAAK,YAAY,MAAM;AAC9B,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,eAAoC;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB,MAAiC;AACnD,+BAAW,8BAA8B,MAAM,QAAQ,MAAM,EAAE;AAC/D,SAAK,gBAAgB;AAAA,EACvB;AACF;AAEO,MAAM,gBAIc,OAAO,SAAS;AACzC,QAAM,MAAM,IAAI,kBAAkB;AAClC,YAAM,mDAAuB,KAAK;AAAA,IAChC,KAAK,KAAK;AAAA,IACV,UAAU,KAAK;AAAA,IACf,SAAS,KAAK;AAAA,EAChB,CAAC;AACD,SAAO;AACT;", "names": []}